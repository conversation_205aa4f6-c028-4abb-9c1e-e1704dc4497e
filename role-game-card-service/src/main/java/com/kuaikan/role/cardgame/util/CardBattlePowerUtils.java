package com.kuaikan.role.cardgame.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 卡牌战斗 战力计算工具类
 *
 *<AUTHOR>
 *@date 2024/9/12
 */
public class CardBattlePowerUtils {

    /**
     * 计算卡牌战力
     *
     * @param cardAtk 卡牌攻击力
     * @param cardHp 卡牌生命值
     * @param selectedCount 选中的卡牌数量
     * @return 卡牌战力
     */
    public static int calculateCardBattlePower(Integer cardAtk, Integer cardHp, Integer selectedCount) {
        if (cardAtk == null || cardHp == null) {
            return 0;
        }
        return BigDecimal.valueOf(Math.pow((double) cardAtk / selectedCount * cardHp, 0.55) / 0.6).setScale(0, RoundingMode.HALF_UP).intValue();
    }

    public static void main(String[] args) {
        double hp = BigDecimal.valueOf(1425.44).multiply(BigDecimal.valueOf(1).add(BigDecimal.valueOf(0.2)).add(BigDecimal.valueOf(0))).doubleValue();
        System.out.println(hp);
        System.out.println(BigDecimal.valueOf(Math.pow( 755.2 * hp , 0.55) / 0.6).setScale(0, RoundingMode.HALF_UP).intValue());
    }
}
