package com.kuaikan.role.cardgame.biz;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.role.game.api.bean.cardbattle.UserBattleActivityRecord;
import com.kuaikan.role.game.api.rpc.result.cardbattle.NewActivityFlagDTO;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.kuaikan.common.bean.RequestInfo;
import com.kuaikan.role.cardgame.component.UserBattleRecordComponent;
import com.kuaikan.role.cardgame.component.activity.AbstractBattleActivityStrategy;
import com.kuaikan.role.cardgame.component.activity.BattleActivityContext;
import com.kuaikan.role.cardgame.component.activity.BattleExploreContext;
import com.kuaikan.role.cardgame.component.activity.BattleExploreManager;
import com.kuaikan.role.cardgame.repository.BattleActivityConfigRepository;
import com.kuaikan.role.cardgame.repository.BattleDungeonConfigRepository;
import com.kuaikan.role.cardgame.repository.UserBattleTaskRepository;
import com.kuaikan.role.game.api.bean.cardbattle.BattleActivityConfig;
import com.kuaikan.role.game.api.bean.cardbattle.BattleDungeonConfig;
import com.kuaikan.role.game.api.bean.cardbattle.UserBattleActivityRecord;
import com.kuaikan.role.game.api.bean.cardbattle.UserCardBattleTaskRecord;
import com.kuaikan.role.game.api.enums.cardbattle.CardBattleExploreTypeEnum;
import com.kuaikan.role.game.api.enums.cardbattle.CardBattleNewFlagType;
import com.kuaikan.role.game.api.rpc.result.cardbattle.CardBattleActivityDTO;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleEntryInfoDTO;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleExploreParam;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleTaskDTO;
import com.kuaikan.role.game.api.rpc.result.cardbattle.CardBattleActivityMainPageDTO;
import com.kuaikan.role.game.api.rpc.result.cardbattle.CardBattleActivityNewFlagDTO;

/**
 * 副本活动业务biz
 *
 *<AUTHOR>
 *@date 2024/10/24
 */
@Slf4j
@Component
public class BattleActivityBiz {

    @Resource
    private BattleExploreManager battleExploreManager;

    @Resource
    private UserBattleRecordComponent userBattleRecordComponent;

    @Resource
    private UserBattleTaskRepository userBattleTaskRepository;
    @Resource
    private BattleActivityConfigRepository battleActivityConfigRepository;
    @Resource
    private BattleDungeonConfigRepository battleDungeonConfigRepository;

    public List<CardBattleActivityDTO> getActivityList(RequestInfo requestInfo, Integer activityType) {
        AbstractBattleActivityStrategy strategy = battleExploreManager.getConditionByType(activityType);
        if (strategy == null) {
            log.warn("getActivityList strategy is null，activityType={}", activityType);
            return new ArrayList<>();
        }
        return strategy.getActivityList(requestInfo, activityType);
    }

    public List<CardBattleActivityDTO> getActivityInfo(RequestInfo requestInfo, Integer activityType) {
        AbstractBattleActivityStrategy strategy = battleExploreManager.getConditionByType(activityType);
        if (strategy == null) {
            log.warn("getActivityInfo strategy is null, activityType={}", activityType);
            return new ArrayList<>();
        }
        return strategy.getActivityInfo(requestInfo, activityType);
    }

    public CardBattleEntryInfoDTO getEntryInfo(RequestInfo requestInfo, CardBattleExploreParam param) {
        AbstractBattleActivityStrategy strategy = battleExploreManager.getConditionByType(param.getActivityType());
        if (strategy == null) {
            log.warn("getEntryInfo strategy is null, param:{}", param);
            return null;
        }
        BattleExploreContext context = strategy.buildExploreContext(requestInfo, param);
        return strategy.getEntry(context);
    }

    public CardBattleTaskDTO doExplore(RequestInfo requestInfo, CardBattleExploreParam param) {
        Integer battleExploreType = param.getActivityType();
        AbstractBattleActivityStrategy strategy = battleExploreManager.getConditionByType(battleExploreType);
        if (strategy == null) {
            log.warn("doExplore strategy is null, param:{}", param);
            return null;
        }
        BattleExploreContext context = strategy.buildExploreContext(requestInfo, param);
        return strategy.doExplore(context);
    }

    public CardBattleActivityMainPageDTO getActivityMainPage(RequestInfo requestInfo, CardBattleExploreParam param) {
        Integer battleExploreType = param.getActivityType();
        AbstractBattleActivityStrategy strategy = battleExploreManager.getConditionByType(battleExploreType);
        if (strategy == null) {
            log.warn("getActivityMainPage strategy is null, param:{}", param);
            return null;
        }
        BattleActivityContext battleActivityContext = strategy.buildActivityContext(requestInfo, param);
        return strategy.getActivityMainPage(requestInfo, battleActivityContext);
    }

    public boolean checkDungeonAvgUnlocked(String dungeonId, long userId, int avgId) {
        return userBattleRecordComponent.checkDungeonAvgUnlock(userId, dungeonId, avgId);
    }

    public CardBattleActivityNewFlagDTO getActivityNewFlag(RequestInfo requestInfo) {
        // new文案 优先级： 限时活动，幻境探索
        // 1、限时活动
        AbstractBattleActivityStrategy strategy = battleExploreManager.getConditionByType(CardBattleExploreTypeEnum.LIMITED_TIME_EXPLORE.getCode());
        NewActivityFlagDTO flag = strategy.checkNewActivityFlag(requestInfo, CardBattleExploreTypeEnum.LIMITED_TIME_EXPLORE.getCode());
        if (flag != null && flag.getNewFlag() != null && flag.getNewFlag()) {
            return new CardBattleActivityNewFlagDTO().setType(CardBattleNewFlagType.LIMITED_TIME_ACTIVITY.getCode()).setContext("活动上新");
        }

        strategy = battleExploreManager.getConditionByType(CardBattleExploreTypeEnum.EXPLORATION.getCode());
        flag = strategy.checkNewActivityFlag(requestInfo, CardBattleExploreTypeEnum.EXPLORATION.getCode());
        if (flag != null && flag.getNewFlag() != null && flag.getNewFlag()) {
            return new CardBattleActivityNewFlagDTO().setType(CardBattleNewFlagType.DREAMLAND.getCode()).setContext("幻镜探索上新啦！");
        }

        return null;
    }

    public CardBattleActivityNewFlagDTO getActivityNewFlagByType(RequestInfo requestInfo, Integer activityType) {
        AbstractBattleActivityStrategy strategy = battleExploreManager.getConditionByType(activityType);
        NewActivityFlagDTO flag = strategy.checkNewActivityFlag(requestInfo, activityType);
        if (flag != null && flag.getNewFlag() != null && flag.getNewFlag()) {
            return new CardBattleActivityNewFlagDTO().setType(activityType).setContext("活动上新").setNewActivityFlags(flag.getNewConfigs());
        }
        return null;
    }

    public long getActivityDungeonProgress(long userId, String activityId, String dungeonId) {
        // 1、获取探索配置信息
        BattleActivityConfig activityConfig = battleActivityConfigRepository.getBattleActivityConfigWithLocalCache(activityId);
        if (activityConfig == null) {
            log.error("getActivityDungeonProgress activityConfig is null, activityId={}", activityId);
            return 0;
        }

        if (CollectionUtils.isEmpty(activityConfig.getBattleDungeonConfig())) {
            log.error("getActivityDungeonProgress battleDungeonConfig is null, activityConfig={}", activityConfig);
            return 0;
        }

        // 2、查询用户活动信息及探索记录
        UserBattleActivityRecord userBattleActivityRecord = userBattleRecordComponent.getUserBattleActivityRecordWithoutSave(userId, activityId);
        if (userBattleActivityRecord == null) {
            // 用户未参与副本活动，直接返回0
            log.debug("getActivityDungeonProgress userBattleActivityRecord is null, userId={}, activityId={}", userId, activityId);
            return 0;
        }

        // 如果不指定副本，并且活动当前闯关大于1，则返回单副本进度100
        if (StringUtils.isBlank(dungeonId) && userBattleActivityRecord.getCurProcess() > 1) {
            return 100;
        }

        // 查询符合条件的副本
        BattleActivityConfig.DungeonBasicConfig firstDungeon = activityConfig.getBattleDungeonConfig()
                .stream()
                .filter(e -> StringUtils.isBlank(dungeonId) || e.getBattleDungeonId().equals(dungeonId))
                .findFirst()
                .orElse(null);

        if (firstDungeon == null) {
            log.error("getActivityDungeonProgress firstDungeon is null, activityId={}, dungeonId={}", activityId, dungeonId);
            return 0;
        }

        // 查询用户副本记录
        BattleDungeonConfig dungeonConfig = battleDungeonConfigRepository.getNewBattleDungeonConfigById(dungeonId);
        UserCardBattleTaskRecord latestTaskRecord = userBattleTaskRepository.getLatestUserCardBattleTaskRecord(userId, dungeonId);
        if (latestTaskRecord == null || dungeonConfig == null) {
            log.debug("getActivityDungeonProgress latestTaskRecord or dungeonConfig is null, userId={}, dungeonId={}", userId, dungeonId);
            return 0;
        }

        int cnt = latestTaskRecord.unCompleted() ? latestTaskRecord.getCnt() - 1 : latestTaskRecord.getCnt();

        return Math.min(100, cnt * 100 / dungeonConfig.getTaskCnt());
    }

    public ResponseCodeMsg setAutoExploreType(long userId, String activityId, Integer status) {
        UserBattleActivityRecord activityRecord = userBattleRecordComponent.getUserBattleActivityRecord(userId, activityId);
        if (status.equals(activityRecord.getAutoExplore())) {
            log.warn("setAutoExploreStatus status is same, userId={}, activityId={}, status={}", userId, activityId, status);
            return ResponseCodeMsg.SUCCESS;
        }
        boolean ret = userBattleRecordComponent.updateActivityAutoExploreType(activityRecord.getId(), status);
        if (ret) {
            return ResponseCodeMsg.SUCCESS;
        } else {
            return ResponseCodeMsg.SYSTEM_ERROR;
        }
    }

}
