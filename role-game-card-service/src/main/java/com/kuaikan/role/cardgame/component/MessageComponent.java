package com.kuaikan.role.cardgame.component;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import com.kuaikan.common.enums.ActionType;
import com.kuaikan.message.bean.MessageActionTarget;
import com.kuaikan.message.bean.MessageTemplate;
import com.kuaikan.message.enums.MessagePlatform;
import com.kuaikan.message.enums.MessagePushFlag;
import com.kuaikan.message.enums.MessageSource;
import com.kuaikan.message.enums.MessageTemplateFlag;
import com.kuaikan.message.enums.MessageType;
import com.kuaikan.message.model.MessageParam;
import com.kuaikan.message.service.MessageService;
import com.kuaikan.message.utils.SingleMessageSender;
import com.kuaikan.role.cardgame.config.CardServiceApolloConfig;

/**
 * 用户私信组件
 *
 *<AUTHOR>
 *@date 2025/4/18
 */
@Slf4j
@Component
public class MessageComponent {

    @Resource
    private CardServiceApolloConfig cardServiceApolloConfig;
    @Resource
    private MessageService messageService;

    private void sendMessage(long userId, MessageParam param) {
        MessageTemplate messageTemplate = null;
        try {
            messageTemplate = messageService.addMessageV1(param);
        } catch (Exception e) {
            log.error("Exception in addMessageV1", e);
            return;
        }
        long messageTemplateId = messageTemplate.getId();
        boolean ret = false;
        try {
            ret = SingleMessageSender.sendSingleMsg((int) userId, messageTemplateId, System.currentTimeMillis(), param.getSendUserId());
        } catch (Exception e) {
            log.error("send review msg fail {}", param, e);
        }
        if (!ret) {
            log.error("send review msg fail {}", param);
        }
    }

    private MessageParam buildMessageParam(MessageParam param) {
        int sendUserId = cardServiceApolloConfig.getMessageSendUserId();
        // 发送者id
        param.setSendUserId(sendUserId);

        // 推送内容
        // param.setPurePushContent("TestPushContent");

        param.setMessagePlatform(MessagePlatform.ALL);
        // 指定用户
        param.setMessageType(MessageType.TO_TARGETUSER);
        // 消息源，normal
        param.setMessageSource(MessageSource.NORMAL.getCode());

        // 消息的过期时间
        // param.setExpireAt(5 * 60 * 1000);
        // 模版创建时间
        param.setNotifyAt(System.currentTimeMillis());
        // 是否推送
        param.setPushFlag(MessagePushFlag.NO_PUSH);
        // 消息内容是否包含昵称模版
        param.setTemplateFlag(MessageTemplateFlag.NORMAL);
        return param;
    }

    public void sendLotteryCoinsExpiredMessage(int userId, String activityName, String collectionName, long silverCoins, String targetUrl) {
        MessageActionTarget messageActionTarget = new MessageActionTarget();
        messageActionTarget.setActionType(ActionType.HYBRID.getCode());
        messageActionTarget.setTargetWebUrl(targetUrl);

        MessageParam messageParam = new MessageParam();
        String title = String.format("《%s》奖励补发", activityName);
        messageParam.setTitle(title);
        String desc = String.format("亲爱的KKer，这是您尚未兑换的【%s】，为将转换为银币%s，请查收～", collectionName, silverCoins);
        messageParam.setDescription(desc);
        messageParam.setMessageActionTarget(messageActionTarget);
        buildMessageParam(messageParam);
        log.info("sendLotteryCoinsExpiredMessage message. userId={}, messageParam={}", userId, messageParam);
        sendMessage(userId, messageParam);
    }

}
