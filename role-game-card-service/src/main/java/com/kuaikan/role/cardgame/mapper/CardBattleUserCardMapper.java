package com.kuaikan.role.cardgame.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.kuaikan.role.game.api.bean.cardbattle.CardBattleUserCard;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleUserCardDTO;

/**
 * 用户卡牌信息 mapper
 *
 * <AUTHOR>
 * @date 2024/6/17
 */
public interface CardBattleUserCardMapper {

    long getMaxId(@Param("tableName") String tableName);

    int batchUpdateUserCardModelSize(@Param("tableName") String tableName, @Param("cardId") String cardId, @Param("modelId") Integer modelId,
                                     @Param("cardTopicId") Integer cardTopicId, @Param("attr") Integer attr, @Param("beginId") Long beginId,
                                     @Param("endId") Long endId);

    int batchUpdateUserCardValidSize(@Param("tableName") String tableName, @Param("cardId") String cardId, @Param("modelId") Integer modelId,
                                     @Param("cardTopicId") Integer cardTopicId, @Param("attr") Integer attr, @Param("beginId") Long beginId,
                                     @Param("endId") Long endId);

    int batchUpdateUserCardInvalidSize(@Param("tableName") String tableName, @Param("cardId") String cardId, @Param("beginId") Long beginId,
                                       @Param("endId") Long endId);

    int updateUserCardModelByUid(@Param("tableName") String tableName, @Param("cardId") String cardId, @Param("modelId") Integer modelId,
                                 @Param("cardTopicId") Integer cardTopicId, @Param("attr") Integer attr, @Param("userId") Long userId);

    int insert(@Param("tableName") String tableName, @Param("userCard") CardBattleUserCard userCard);

    int batchInsert(@Param("tableName") String tableName, @Param("list") List<CardBattleUserCard> userCardList);

    CardBattleUserCard selectByPrimaryKey(@Param("tableName") String tableName, @Param("id") Long id);

    List<CardBattleUserCardDTO> listGtMonsterPower(@Param("tableName") String tableName, @Param("userId") long userId,
                                                   @Param("monsterBattlePower") Integer monsterBattlePower, @Param("roleLevel") Integer roleLevel,
                                                   @Param("roleLevelGrowth") double roleLevelGrowth, @Param("weakGrowth") double weakGrowth,
                                                   @Param("monsterWeakness") List<Integer> monsterWeakness, @Param("invalidIdList") List<Long> invalidIdList,
                                                   @Param("limitTopicIds") List<Integer> limitTopicIds,
                                                   @Param("selectCardGroupHpRatio") double selectCardGroupHpRatio,
                                                   @Param("costumeHpRatio") double costumeHpRatio, @Param("sourceList") List<Integer> sourceList);

    List<CardBattleUserCardDTO> getMaxBattlePowerCard(@Param("tableName") String tableName, @Param("userId") long userId, @Param("roleLevel") Integer roleLevel,
                                                      @Param("roleLevelGrowth") double roleLevelGrowth, @Param("weakGrowth") double weakGrowth,
                                                      @Param("monsterWeakness") List<Integer> monsterWeakness,
                                                      @Param("upLimitBattlePower") Integer upLimitBattlePower,
                                                      @Param("invalidIdList") List<Long> invalidCardIdList, @Param("limitTopicIds") List<Integer> limitTopicIds,
                                                      @Param("selectCardGroupHpRatio") double selectCardGroupHpRatio,
                                                      @Param("costumeHpRatio") double costumeHpRatio, @Param("sourceList") List<Integer> sourceList);

    List<CardBattleUserCardDTO> selectCardGroup(@Param("tableName") String tableName, @Param("userId") long userId,
                                                @Param("monsterBattlePower") Integer monsterBattlePower, @Param("roleLevel") Integer roleLevel,
                                                @Param("roleLevelGrowth") double roleLevelGrowth, @Param("weakGrowth") double weakGrowth,
                                                @Param("monsterWeakness") List<Integer> monsterWeakness, @Param("selectCardAtk") Integer selectCardAtk,
                                                @Param("selectCardHp") Integer selectCardHp, @Param("invalidIdList") List<Long> invalidGiveCardIdList,
                                                @Param("selectCount") Integer selectCount, @Param("limitTopicIds") List<Integer> limitTopicIds,
                                                @Param("selectCardGroupHpRatio") double selectCardGroupHpRatio, @Param("costumeHpRatio") double costumeHpRatio,
                                                @Param("sourceList") List<Integer> sourceList);

    List<CardBattleUserCardDTO> getCardList(@Param("tableName") String tableName, @Param("userId") long userId, @Param("rareList") List<Integer> rareList,
                                            @Param("attrList") List<Integer> attrList, @Param("topicIdList") List<Integer> topicIdList,
                                            @Param("sortParam") String sortParam, @Param("sortOrder") String sortOrder, @Param("cardIdList") List<String> cardIdList,
                                            @Param("offset") Integer offset, @Param("limit") Integer limit,
                                            @Param("giveUserCardIds") List<Long> giveUserCardIds, @Param("weakAttrs") List<Integer> weakAttrs,
                                            @Param("status") Integer status, @Param("raritySortType") String raritySortType,
                                            @Param("queryFrom") Integer queryFrom, @Param("sourceList") List<Integer> sourceList);

    // 暂时没有使用sql中的排序，先放着
    List<CardBattleUserCardDTO> getCardListForBattle(@Param("tableName") String tableName, @Param("userId") long userId,
                                                     @Param("rareList") List<Integer> rareList, @Param("attrList") List<Integer> attrList,
                                                     @Param("topicIdList") List<Integer> topicIdList, @Param("giveUserCardIds") List<Long> giveUserCardIds,
                                                     @Param("sourceList") List<Integer> sourceList, @Param("weakAttrs") List<Integer> weakAttrs,
                                                     @Param("weakAttrRatio") double weakAttrRatio, @Param("ratioTopicList") List<Integer> ratioTopicList,
                                                     @Param("topicRatio") double topicRatio, @Param("roleHpRatio") double roleHpRatio,
                                                     @Param("sortParam") String sortParam, @Param("sortOrder") String sortOrder,
                                                     @Param("offset") Integer offset, @Param("limit") Integer limit);

    List<CardBattleUserCardDTO> getCardListForSmartSelect(@Param("tableName") String tableName, @Param("userId") long userId,
                                                          @Param("topicIdList") List<Integer> topicIdList, @Param("giveUserCardIds") List<Long> giveUserCardIds,
                                                          @Param("sourceList") List<Integer> sourceList, @Param("offset") Integer offset,
                                                          @Param("limit") Integer limit);

    List<CardBattleUserCard> listUserCardByIds(@Param("tableName") String tableName, @Param("userId") long userId,
                                               @Param("userCardIds") List<Long> userCardIds);

    List<CardBattleUserCard> listUserCardByCardIds(@Param("tableName") String tableName, @Param("userId") long userId, @Param("cardIds") List<String> cardIds);

    List<CardBattleUserCard> listUserCardByCardSource(@Param("tableName") String tableName, @Param("userId") long userId, @Param("cardSource") int cardSource);

    int updateUserCardStatus(@Param("tableName") String tableName, @Param("userId") long userId, @Param("userCardIds") List<Long> userCardIds,
                             @Param("status") Integer status);

    int countSameCard(@Param("tableName") String tableName, @Param("userId") long userId, @Param("cardId") String cardId,
                      @Param("filterIds") List<Long> filterIds);

    Double getTotalCardExp(@Param("tableName") String tableName, @Param("userId") long userId, @Param("filterIds") List<Long> filterIds);

    List<CardBattleUserCardDTO> listUserCardDto(@Param("tableName") String tableName, @Param("userId") Long userId,
                                                @Param("selectedUserCardIdList") List<Long> selectedUserCardIdList, @Param("status") Integer status);

    int countUserCard(@Param("tableName") String tableName, @Param("userId") long userId);

    int countUserTopicListCardIgnoreStatus(@Param("tableName") String tableName, @Param("userId") long userId,
                                           @Param("limitTopicIds") List<Integer> limitTopicIds);

    CardBattleUserCardDTO selectSameCard(@Param("tableName") String tableName, @Param("userId") long userId, @Param("cardId") String cardId,
                                         @Param("filterIds") List<Long> filterIds);

    List<CardBattleUserCardDTO> listOneClickSelectUpCard(@Param("userId") long userId, @Param("canSelectCardRareIds") List<Integer> canSelectCardRareIds,
                                                         @Param("filterIds") List<Long> filterIds, @Param("cardLevel") Integer cardLevel,
                                                         @Param("tableName") String tableName);

    int updateByPrimaryKey(@Param("tableName") String tableName, @Param("userCard") CardBattleUserCard userCard);

    int countUserNormalOneLevelCard(@Param("tableName") String tableName, @Param("userId") long userId, @Param("filterIds") List<Long> filterIds);

    List<Integer> getCardTopicList(@Param("tableName") String tableName, @Param("userId") long userId);

    int countUserBattleCard(@Param("tableName") String tableName, @Param("userId") long userId);

    long refreshCardInfo(@Param("tableName") String tableName, @Param("cardId") String cardId, @Param("rareCode") Integer rareCode,
                         @Param("topicId") Integer topicId, @Param("cardName") String cardName, @Param("cardImg") String cardImg,
                         @Param("status") Integer status);

    List<CardBattleUserCardDTO> listByCardSource(@Param("tableName") String tableName, @Param("userId") long userId, @Param("cardSource") Integer cardSource,
                                                 @Param("status") Integer status);

    List<CardBattleUserCard> getUserCardByCardId(@Param("tableName") String tableName, @Param("userId") Integer userId, @Param("cardId") String cardId);

}
