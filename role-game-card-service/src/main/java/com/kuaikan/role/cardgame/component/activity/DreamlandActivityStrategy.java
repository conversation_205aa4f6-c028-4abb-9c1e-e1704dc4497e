package com.kuaikan.role.cardgame.component.activity;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.kuaikan.role.game.api.bean.cardbattle.BattleDungeonConfig;
import com.kuaikan.role.game.api.constant.BattleActivityConstants;
import com.kuaikan.role.game.api.enums.cardbattle.CardBattleDungeonTypeEnum;
import com.kuaikan.role.game.api.enums.cardbattle.CardBattleTaskTypeEnum;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleTaskDTO;
import com.kuaikan.role.game.api.rpc.result.cardbattle.NewActivityFlagDTO;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.kuaikan.common.bean.RequestInfo;
import com.kuaikan.role.cardgame.cache.CardBattleCacheManager;
import com.kuaikan.role.cardgame.repository.BattleActivityConfigRepository;
import com.kuaikan.role.game.api.bean.cardbattle.BattleActivityConfig;
import com.kuaikan.role.game.api.rpc.result.cardbattle.CardBattleActivityDTO;

/**
 *<AUTHOR>
 *@date 2024/10/28
 */
@Component
@Slf4j
public class DreamlandActivityStrategy extends AbstractBattleActivityStrategy {

    @Resource
    private BattleActivityConfigRepository battleActivityConfigRepository;
    @Resource
    private CardBattleCacheManager cardBattleCacheManager;

    @Override
    public List<CardBattleActivityDTO> getActivityInfo(RequestInfo requestInfo, Integer activityType) {
        long userId = requestInfo.getUserId();
        List<BattleActivityConfig> activityList = battleActivityConfigRepository.getValidActivityListByOrder(activityType);
        if (CollectionUtils.isEmpty(activityList)) {
            log.debug("【卡牌战斗】活动类型不存在，activityType:{}", activityType);
            return Collections.emptyList();
        }
        BattleActivityConfig activityConfig = activityList.stream().filter(e -> e.hitWhiteList((int) userId)).findFirst().orElse(null);
        if (activityConfig == null) {
            return Collections.emptyList();
        }
        CardBattleActivityDTO cardBattleActivity = buildCardBattleActivityDTO(userId, activityConfig);
        boolean newFlag = checkNewFlag((int) userId, activityType, activityConfig);
        cardBattleActivity.setHasNewActivity(newFlag);
        return Collections.singletonList(cardBattleActivity);
    }

    @Override
    public List<CardBattleActivityDTO> getActivityList(RequestInfo requestInfo, Integer activityType) {
        return null;
    }

    @Override
    protected void fillExploreTaskExtraInfo(CardBattleTaskDTO task, BattleDungeonConfig battleDungeonConfig) {
        if (task.getTaskMode().equals(BattleActivityConstants.TaskMode.BATTLE_TASK) && CardBattleDungeonTypeEnum.EXPLORATION.getCode()
                .equals(battleDungeonConfig.getType())) {
            task.setBattleTaskType(CardBattleTaskTypeEnum.EXPLORE.getCode());
        }
    }

    // 幻境探索只能有一个生效，红点逻辑特殊处理
    @Override
    public NewActivityFlagDTO checkNewActivityFlag(RequestInfo requestInfo, Integer activityType) {
        NewActivityFlagDTO activityFlag = new NewActivityFlagDTO();
        int userId = requestInfo.getUserId();
        List<BattleActivityConfig> activityList = battleActivityConfigRepository.getValidActivityListByOrder(activityType);
        if (CollectionUtils.isEmpty(activityList)) {
            log.debug("【卡牌战斗】幻境探索活动类型不存在，activityType:{}", activityType);
            activityFlag.setNewFlag(false);
            return activityFlag;
        }
        BattleActivityConfig activityConfig = activityList.stream().filter(e -> e.hitWhiteList(userId)).findFirst().orElse(null);
        if (activityList == null) {
            // 这里需要报警，说明部分用户已经看不到幻镜探索活动了
            log.error("【卡牌战斗】幻境探索活动白名单不匹配，activityType:{}", activityType);
            activityFlag.setNewFlag(false);
            return activityFlag;
        }
        List<BattleActivityConfig> cardBattleActivityRelation = cardBattleCacheManager.getCardBattleActivityRelation(userId, activityType);
        log.debug("DreamlandActivityStrategy checkNewActivityFlag userId={}, activityConfig={}, cardBattleActivityRelation={}", userId, activityConfig,
                cardBattleActivityRelation);
        if (CollectionUtils.isEmpty(cardBattleActivityRelation)) {
            activityFlag.setNewFlag(true);
            return activityFlag;
        }
        List<String> oldActivityIds = cardBattleActivityRelation.stream().map(BattleActivityConfig::getId).collect(Collectors.toList());
        boolean hasNewActivity = !oldActivityIds.contains(activityConfig.getId());
        if (hasNewActivity) {
            activityFlag.setNewFlag(true);
            return activityFlag;
        }
        List<String> oldBattleTaskIds = cardBattleActivityRelation.stream()
                .flatMap(e -> e.getBattleDungeonConfig().stream().map(BattleActivityConfig.DungeonBasicConfig::getBattleDungeonId))
                .collect(Collectors.toList());

        boolean hasNewDungeon = activityConfig.getBattleDungeonConfig().stream().anyMatch(e -> !oldBattleTaskIds.contains(e.getBattleDungeonId()));
        if (hasNewDungeon) {
            activityFlag.setNewFlag(true);
            return activityFlag;
        }
        activityFlag.setNewFlag(false);
        return activityFlag;
    }

    private Boolean checkNewFlag(int userId, Integer activityType, BattleActivityConfig activityConfig) {
        List<BattleActivityConfig> cardBattleActivityRelation = cardBattleCacheManager.getCardBattleActivityRelation(userId, activityType);
        if (CollectionUtils.isEmpty(cardBattleActivityRelation)) {
            return true;
        }
        log.debug("checkNewFlag, userId={}, cardBattleActivityRelation:{}", userId, cardBattleActivityRelation);
        List<String> oldActivityIds = cardBattleActivityRelation.stream().map(BattleActivityConfig::getId).collect(Collectors.toList());
        boolean hasNewActivity = !oldActivityIds.contains(activityConfig.getId());
        if (hasNewActivity) {
            log.debug("幻境探索副本活动变化，checkNewFlag true");
            return true;
        }
        List<String> oldDungeonIds = cardBattleActivityRelation.stream()
                .flatMap(e -> e.getBattleDungeonConfig().stream().map(BattleActivityConfig.DungeonBasicConfig::getBattleDungeonId))
                .collect(Collectors.toList());

        boolean hasNewDungeon = activityConfig.getBattleDungeonConfig()
                .stream()
                .map(BattleActivityConfig.DungeonBasicConfig::getBattleDungeonId)
                .anyMatch(e -> !oldDungeonIds.contains(e));
        if (hasNewDungeon) {
            log.debug("幻境探索副本变化，checkNewFlag true");
            return true;
        }
        return false;
    }

}
