package com.kuaikan.role.cardgame.component.activity;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import com.kuaikan.common.bean.RequestInfo;
import com.kuaikan.common.tools.serialize.JsonUtils;
import com.kuaikan.game.gamecard.activity.def.dto.UserCoinDto;
import com.kuaikan.role.cardgame.biz.CardBattleLotteryActivityBiz;
import com.kuaikan.role.cardgame.biz.CardBattleRolePlayerBiz;
import com.kuaikan.role.cardgame.cache.CardBattleCacheManager;
import com.kuaikan.role.cardgame.component.third.BattleRoleComponent;
import com.kuaikan.role.cardgame.component.third.GameCardComponent;
import com.kuaikan.role.cardgame.repository.BattleActivityConfigRepository;
import com.kuaikan.role.cardgame.repository.CardBattleActivitySelectRoleRepository;
import com.kuaikan.role.cardgame.util.RequestInfoUtil;
import com.kuaikan.role.game.api.bean.cardbattle.BattleActivityConfig;
import com.kuaikan.role.game.api.bean.cardbattle.BattleDungeonConfig;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleActivitySelectRoleRecord;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleLimitedTimeActivityConfig;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleLotteryActivityConfig;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleLotteryActivityConfig.LotteryPrizeItem;
import com.kuaikan.role.game.api.bean.cardbattle.UserBattleActivityRecord;
import com.kuaikan.role.game.api.constant.BattleActivityConstants;
import com.kuaikan.role.game.api.constant.CardBattleResponseCodeMsg;
import com.kuaikan.role.game.api.enums.cardbattle.CardBattleDungeonTypeEnum;
import com.kuaikan.role.game.api.enums.cardbattle.CardBattleLimitedTimeActivityTypeEnum;
import com.kuaikan.role.game.api.enums.cardbattle.CardBattleTaskTypeEnum;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleEntryInfoDTO;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleRoleDTO;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleRolePlayerDTO;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleTaskDTO;
import com.kuaikan.role.game.api.rpc.result.cardbattle.ActivityLotteryPrizeDTO;
import com.kuaikan.role.game.api.rpc.result.cardbattle.ActivityLotteryStagePrizeDTO;
import com.kuaikan.role.game.api.rpc.result.cardbattle.CardBattleActivityDTO;
import com.kuaikan.role.game.api.rpc.result.cardbattle.CardBattleActivityMainPageDTO;
import com.kuaikan.role.game.api.util.CardBattleUtils;

/**
 * 限时活动
 * <AUTHOR>
 * @date 2024/11/02
 */
@Component
@Slf4j
public class LotteryActivityStrategy extends AbstractBattleActivityStrategy {

    @Resource
    private GameCardComponent gameCardComponent;
    @Resource
    private BattleActivityConfigRepository battleActivityConfigRepository;
    @Resource
    private CardBattleActivitySelectRoleRepository cardBattleActivitySelectRoleRepository;
    @Resource
    private CardBattleRolePlayerBiz cardBattleRolePlayerBiz;
    @Resource
    private CardBattleCacheManager cardBattleCacheManager;
    @Resource
    private BattleRoleComponent battleRoleComponent;
    @Resource
    private CardBattleLotteryActivityBiz cardBattleLotteryActivityBiz;

    @Override
    public CardBattleRolePlayerDTO buildBattleRoleInfo(BattleExploreContext context) {
        CardBattleLotteryActivityConfig activityConfig = JsonUtils.fromJson(context.getActivityConfig().getExtraInfo(), CardBattleLotteryActivityConfig.class);
        if (activityConfig == null) {
            return cardBattleRolePlayerBiz.initUserRole();
        }
        //是否限定冒险者
        if (!activityConfig.isHasLimitRoles() || CollectionUtils.isEmpty(activityConfig.getLimitRoleIds())) {
            return super.buildBattleRoleInfo(context);
        }

        int userId = context.getRequestInfo().getUserId();
        String activityId = context.getActivityId();
        CardBattleActivitySelectRoleRecord selectRoleRecord = cardBattleActivitySelectRoleRepository.getSelectRoleRecord(userId, activityId);
        if (selectRoleRecord == null) {
            return cardBattleRolePlayerBiz.initUserLimitTimeRole(activityConfig);
        }
        int roleId = selectRoleRecord.getRoleId();
        if (!activityConfig.getLimitRoleIds().contains(roleId)) {
            log.debug("buildBattleRoleInfo roleId not in limitRoleIds, userId={}, roleId={}, limitRoleIds={}", userId, roleId,
                    activityConfig.getLimitRoleIds());
            return cardBattleRolePlayerBiz.initUserLimitTimeRole(activityConfig);
        }
        CardBattleRolePlayerDTO rolePlayer;
        if (roleId == -1) {//限时冒险者
            rolePlayer = cardBattleRolePlayerBiz.initUserLimitTimeRole(activityConfig);
        } else if (roleId == 0) {//团子
            rolePlayer = cardBattleRolePlayerBiz.initUserRole();
        } else {
            rolePlayer = cardBattleRolePlayerBiz.getRolePlayerByRoleId(userId, roleId);
        }
        return rolePlayer;
    }

    @Override
    public CardBattleEntryInfoDTO getEntry(BattleExploreContext context) {
        CardBattleEntryInfoDTO cardBattleEntryInfo = super.getEntry(context);
        int userId = context.getRequestInfo().getUserId();
        UserBattleActivityRecord userBattleActivityRecord = context.getUserActivityRecord();
        cardBattleEntryInfo.setCollectCount(userBattleActivityRecord.getCollectCount());

        BattleActivityConfig battleActivityConfig = context.getActivityConfig();
        CardBattleLotteryActivityConfig activityConfig = JsonUtils.fromJson(battleActivityConfig.getExtraInfo(), CardBattleLotteryActivityConfig.class);
        if (activityConfig == null || activityConfig.getCoinId() <= 0) {
            return cardBattleEntryInfo;
        }
        long coinId = activityConfig.getCoinId();
        UserCoinDto userCoin = gameCardComponent.getUserCoin(RequestInfoUtil.convert(context.getRequestInfo()), coinId);
        cardBattleEntryInfo.setCollectCount((int) userCoin.getOwnerCoins());
        cardBattleEntryInfo.setCollectPrizeImg(CardBattleUtils.getImgUrlJoinHost(activityConfig.getCollectPrizeImg()));
        ActivityLotteryPrizeDTO userActivityLotteryPrize = cardBattleLotteryActivityBiz.getUserActivityLotteryPrize(context.getRequestInfo(),
                context.getActivityId());
        cardBattleEntryInfo.setLotteryName(userActivityLotteryPrize.getLotteryName());
        cardBattleEntryInfo.setLotteryCount(getLotteryPrizeCount(userActivityLotteryPrize.getStagePrizeInfo(), userActivityLotteryPrize.getCollectCount()));

        CardBattleActivitySelectRoleRecord record = cardBattleActivitySelectRoleRepository.getSelectRoleRecord(userId, context.getActivityId());
        boolean firstParticipate = record == null;
        if (firstParticipate) {
            defaultSelectRole(userId, activityConfig.getLimitRoleIds(), activityConfig.getTimeLimitRole(), userBattleActivityRecord.getBattleExploreId());
        }
        CardBattleActivityDTO battleActivityInfo = new CardBattleActivityDTO();
        battleActivityInfo.setFirstParticipate(firstParticipate);
        cardBattleEntryInfo.setBattleActivityInfo(battleActivityInfo);

        log.debug("LotteryActivityStrategy buildEntryExtraInfo userId={}, cardBattleEntryInfo={}, userBattleActivityRecord={}", userId, cardBattleEntryInfo,
                userBattleActivityRecord);
        return cardBattleEntryInfo;
    }

    private void defaultSelectRole(int userId, List<Integer> limitRoleIds, CardBattleLimitedTimeActivityConfig.TimeLimitRole timeLimitRole, String activityId) {
        CardBattleRoleDTO initUserRole = CardBattleRoleDTO.builder()
                .id(0)
                .level(1)
                .adopted(true)
                .adoptTime(System.currentTimeMillis())
                .selected(false)
                .availability(false)
                .build();

        // 所有角色
        List<CardBattleRoleDTO> cardRoleList = battleRoleComponent.getCardRoleList(userId);
        cardRoleList.forEach(e -> e.setAvailability(limitRoleIds.contains(e.getId())));

        initUserRole.setAvailability(limitRoleIds.contains(0));
        cardRoleList.add(initUserRole);
        //限时冒险者
        if (timeLimitRole != null) {
            CardBattleRoleDTO role = CardBattleRoleDTO.builder()
                    .id(-1) // -1，限时冒险者
                    .name(timeLimitRole.getName())
                    .level(1)
                    .topicId(timeLimitRole.getTopicId())
                    .adopted(true)
                    .adoptTime(System.currentTimeMillis())
                    .selected(false)
                    .availability(false)
                    .build();

            role.setAvailability(limitRoleIds.contains(-1));
            cardRoleList.add(role);
        }

        cardRoleList.sort(Comparator.comparing(CardBattleRoleDTO::isAvailability, Comparator.reverseOrder())
                .thenComparing(CardBattleRoleDTO::getLevel, Comparator.reverseOrder())
                .thenComparing(CardBattleRoleDTO::isAdopted, Comparator.reverseOrder())
                .thenComparing(CardBattleRoleDTO::getAdoptTime));

        CardBattleRoleDTO cardBattleRoleDTO = cardRoleList.get(0);
        if (cardBattleRoleDTO.isAvailability() && cardBattleRoleDTO.isAdopted()) {
            CardBattleActivitySelectRoleRecord saveRecord = CardBattleActivitySelectRoleRecord.init(userId, activityId, cardBattleRoleDTO.getId());
            cardBattleActivitySelectRoleRepository.save(saveRecord);
        }
    }

    @Override
    public List<CardBattleActivityDTO> getActivityInfo(RequestInfo requestInfo, Integer activityType) {
        return fetchLimitedTimeActivityList(requestInfo, activityType);
    }

    @Override
    public List<CardBattleActivityDTO> getActivityList(RequestInfo requestInfo, Integer activityType) {
        return fetchLimitedTimeActivityList(requestInfo, activityType);
    }

    @Override
    protected void fillExploreTaskExtraInfo(CardBattleTaskDTO task, BattleDungeonConfig battleDungeonConfig) {
        if (task.getTaskMode().equals(BattleActivityConstants.TaskMode.BATTLE_TASK) && CardBattleDungeonTypeEnum.LIMITED_TIME_ACTIVITY.getCode()
                .equals(battleDungeonConfig.getType())) {
            task.setBattleTaskType(CardBattleTaskTypeEnum.LIMITED_TIME.getCode());
        }
    }

    private List<CardBattleActivityDTO> fetchLimitedTimeActivityList(RequestInfo requestInfo, Integer activityType) {
        int userId = requestInfo.getUserId();
        List<BattleActivityConfig> activityList = battleActivityConfigRepository.fetchActivityListByType(activityType);
        if (CollectionUtils.isEmpty(activityList)) {
            return new ArrayList<>();
        }
        activityList.sort(Comparator.comparing(BattleActivityConfig::getOrder));
        log.debug("fetchLimitedTimeActivityList userId={}, activityType={}, activityList={}", userId, activityType, activityList);
        long now = System.currentTimeMillis();
        List<CardBattleActivityDTO> activityDtoList = new ArrayList<>();
        List<BattleActivityConfig> filterActivityList = new ArrayList<>();
        activityList.forEach(e -> {
            if (!e.hitWhiteList(userId)) {
                return;
            }
            CardBattleLimitedTimeActivityConfig activityConfig = JsonUtils.fromJson(e.getExtraInfo(), CardBattleLimitedTimeActivityConfig.class);
            if (activityConfig == null) {
                log.warn("getActivityList activityConfig null, userId={}, limitedTimeActivity={}", userId, e);
                return;
            }
            if ((e.getStartTime() != null && now < e.getStartTime()) || (activityConfig.getOfflineTime() != null && now > activityConfig.getOfflineTime())) {
                return;
            }

            CardBattleActivityDTO activityDTO = buildCardBattleActivityDTO(userId, e);
            activityDTO.setLimitedTimeActivityType(activityConfig.getLimitedTimeActivityType());
            if (CardBattleLimitedTimeActivityTypeEnum.CUSTOM_ACTIVITY.getCode().equals(activityConfig.getLimitedTimeActivityType())) {
                activityDTO.setCustomActivityName(activityConfig.getCustomActivityName());
            }
            activityDTO.setIcon(CardBattleUtils.getImgUrlJoinHost(activityConfig.getPrizeIcon()));
            filterActivityList.add(e);
            activityDtoList.add(activityDTO);
        });
        checkNewFlag(userId, activityType, filterActivityList, activityDtoList);
        return activityDtoList;
    }

    private void checkNewFlag(int userId, Integer activityType, List<BattleActivityConfig> activityConfigList, List<CardBattleActivityDTO> activityDtoList) {
        List<BattleActivityConfig> cardBattleActivityRelation = cardBattleCacheManager.getCardBattleActivityRelation(userId, activityType);
        if (CollectionUtils.isEmpty(cardBattleActivityRelation)) {
            activityDtoList.stream().forEach(e -> e.setHasNewActivity(true));
            return;
        }
        Map<String, BattleActivityConfig> relationMap = cardBattleActivityRelation.stream().collect(Collectors.toMap(BattleActivityConfig::getId, e -> e));
        Map<String, BattleActivityConfig> configMap = activityConfigList.stream().collect(Collectors.toMap(BattleActivityConfig::getId, e -> e));
        for (CardBattleActivityDTO activityDTO : activityDtoList) {
            BattleActivityConfig activityConfig = relationMap.get(activityDTO.getActivityId());
            if (activityConfig == null) {
                activityDTO.setHasNewActivity(true);
                continue;
            }

            BattleActivityConfig newActivityConfig = configMap.get(activityDTO.getActivityId());
            List<String> oldBattleDungeonIds = activityConfig.getBattleDungeonConfig()
                    .stream()
                    .map(BattleActivityConfig.DungeonBasicConfig::getBattleDungeonId)
                    .collect(Collectors.toList());

            boolean hasNewDungeon = newActivityConfig.getBattleDungeonConfig()
                    .stream()
                    .map(BattleActivityConfig.DungeonBasicConfig::getBattleDungeonId)
                    .anyMatch(e -> !oldBattleDungeonIds.contains(e));
            if (hasNewDungeon) {
                activityDTO.setHasNewActivity(true);
                continue;
            }
            activityDTO.setHasNewActivity(false);
        }
    }

    @Override
    public CardBattleActivityMainPageDTO getActivityMainPage(RequestInfo requestInfo, BattleActivityContext battleExploreContext) {
        if (!checkContextValid(battleExploreContext)) {
            return CardBattleActivityMainPageDTO.init(battleExploreContext.getCodeMsg());
        }
        int userId = requestInfo.getUserId();
        CardBattleActivityMainPageDTO mainPageDTO = super.getActivityMainPage(requestInfo, battleExploreContext);
        BattleActivityConfig battleActivityConfig = battleExploreContext.getActivityConfig();
        CardBattleLotteryActivityConfig activityConfig = JsonUtils.fromJson(battleActivityConfig.getExtraInfo(), CardBattleLotteryActivityConfig.class);
        if (activityConfig == null) {
            log.warn("getActivityMainPage activityConfig null, userId={}, battleActivityConfig={}", userId, battleActivityConfig);
            return CardBattleActivityMainPageDTO.init(CardBattleResponseCodeMsg.CARD_BATTLE_NO_FOUND_EXPLORE_CONFIG);
        }
        long coinId = activityConfig.getCoinId();
        UserCoinDto userCoin = gameCardComponent.getUserCoin(RequestInfoUtil.convert(requestInfo), coinId);
        mainPageDTO.setLogo(CardBattleUtils.getImgUrlJoinHost(activityConfig.getLogo()));
        mainPageDTO.setCollectPrizeImg(CardBattleUtils.getImgUrlJoinHost(activityConfig.getCollectPrizeImg()));
        mainPageDTO.setCollectPrizeName(activityConfig.getCollectPrizeName());
        // 当前收集数量
        mainPageDTO.setCollectCount((int) userCoin.getOwnerCoins());
        mainPageDTO.setLimitedTimeActivityType(activityConfig.getLimitedTimeActivityType());
        mainPageDTO.setLotteryPrizeInfo(buildLotteryPrizeInfo(requestInfo, activityConfig, battleExploreContext));
        return mainPageDTO;
    }

    private CardBattleActivityMainPageDTO.LotteryPrizeInfoDTO buildLotteryPrizeInfo(RequestInfo requestInfo, CardBattleLotteryActivityConfig activityConfig,
                                                                                    BattleActivityContext battleExploreContext) {
        UserBattleActivityRecord userBattleActivityRecord = battleExploreContext.getUserActivityRecord();
        ActivityLotteryPrizeDTO userActivityLotteryPrize = cardBattleLotteryActivityBiz.getUserActivityLotteryPrize(requestInfo,
                battleExploreContext.getActivityId());
        if (userActivityLotteryPrize == null) {
            return null;
        }
        CardBattleActivityMainPageDTO.LotteryPrizeInfoDTO lotteryPrizeInfo = new CardBattleActivityMainPageDTO.LotteryPrizeInfoDTO();
        lotteryPrizeInfo.setLotteryRemainingTime(userActivityLotteryPrize.getLotteryRemainingTime());
        lotteryPrizeInfo.setLotteryName(userActivityLotteryPrize.getLotteryName());
        lotteryPrizeInfo.setBackgroundImg(CardBattleUtils.getImgUrlJoinHost(activityConfig.getLotteryBg()));
        lotteryPrizeInfo.setCurrentStage(userBattleActivityRecord.getLotteryStage() == null ? 1 : userBattleActivityRecord.getLotteryStage());
        List<LotteryPrizeItem> lotteryPrizeConfigList = activityConfig.getLotteryPrizeConf();
        if (CollectionUtils.isEmpty(lotteryPrizeConfigList)) {
            return lotteryPrizeInfo;
        }
        // 计算已解锁阶段单次消耗最少和限量大奖数量
        Long bigPrizeCount = 0L;
        Integer costNum = Integer.MAX_VALUE;
        Map<Integer, LotteryPrizeItem> stagePrizeMap = new HashMap<>();
        for (LotteryPrizeItem item : lotteryPrizeConfigList) {
            if (StringUtils.isNotEmpty(item.getTag())) {
                // 各个阶段大奖，每个阶段只取一个
                if (!stagePrizeMap.containsKey(item.getStage())) {
                    stagePrizeMap.put(item.getStage(), item);
                }
            }
            if (item.getPrizeStock() > 0) {
                bigPrizeCount = bigPrizeCount + item.getPrizeStock();
            }
        }
        for (ActivityLotteryPrizeDTO.StagePrizeInfoDTO stagePrizeInfo : userActivityLotteryPrize.getStagePrizeInfo()) {
            if (!stagePrizeInfo.getUnlock()) {
                continue;
            }
            boolean anyMatch = stagePrizeInfo.getPrizeList().stream().anyMatch(e -> {
                if (!e.getObtained() && (e.getPrizeStock() == -1 || e.getPrizeStock() > 0)) {
                    return true;
                }
                return false;
            });
            if (anyMatch && stagePrizeInfo.getCost() <= costNum) {
                costNum = stagePrizeInfo.getCost();
            }
        }
        int lotteryPrizeCount = getLotteryPrizeCount(userActivityLotteryPrize.getStagePrizeInfo(), userActivityLotteryPrize.getCollectCount());
        lotteryPrizeInfo.setLotteryCount(lotteryPrizeCount);
        lotteryPrizeInfo.setLimitPrizeCount(bigPrizeCount.intValue());
        if (stagePrizeMap.isEmpty()) {
            return lotteryPrizeInfo;
        }

        List<ActivityLotteryStagePrizeDTO> list = cardBattleLotteryActivityBiz.buildLotteryStagePrizeList(stagePrizeMap.values());
        lotteryPrizeInfo.setPrizeList(list);
        return lotteryPrizeInfo;
    }

    // 计算最多可抽奖次数
    private int getLotteryPrizeCount(List<ActivityLotteryPrizeDTO.StagePrizeInfoDTO> userStagePrizeInfo, long totalCoinCount) {
        if (totalCoinCount <= 0) {
            return 0;
        }
        Integer costNum = Integer.MAX_VALUE;
        for (ActivityLotteryPrizeDTO.StagePrizeInfoDTO stagePrizeInfo : userStagePrizeInfo) {
            if (!stagePrizeInfo.getUnlock()) {
                continue;
            }
            boolean anyMatch = stagePrizeInfo.getPrizeList().stream().anyMatch(e -> {
                if (!e.getObtained() && (e.getPrizeStock() == -1 || e.getPrizeStock() > 0)) {
                    return true;
                }
                return false;
            });
            if (anyMatch && stagePrizeInfo.getCost() <= costNum) {
                costNum = stagePrizeInfo.getCost();
            }
        }
        return (int) totalCoinCount / costNum;
    }

}
