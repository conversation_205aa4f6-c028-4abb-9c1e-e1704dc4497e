package com.kuaikan.role.cardgame.model;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @date 2025/4/8
 */
@Data
@Accessors(chain = true)
public class CardBattleRoleGroupInfo implements Serializable {

    private static final long serialVersionUID = 8023667683415621872L;

    private Integer roleGroupId;
    // 专属点数弹窗CP名
    private String pointIconName;
    private String pointIconUrl;
}
