package com.kuaikan.role.cardgame.repository;

import java.util.List;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import com.mongodb.client.result.UpdateResult;

import com.kuaikan.role.game.api.bean.cardbattle.PrizeInfoRecord;
import com.kuaikan.role.game.api.bean.cardbattle.UserBattleDungeonRecord;

/**
 *<AUTHOR>
 *@date 2024/11/11
 */
@Repository
@Slf4j
public class UserDungeonRecordRepository {

    @Resource
    private MongoOperations mongoTemplate;

    public void saveUserDungeonRecord(UserBattleDungeonRecord record) {
        mongoTemplate.save(record);
    }

    public UserBattleDungeonRecord getUserDungeonRecord(long userId, String dungeonId) {
        Query query = new Query(Criteria.where("userId").is(userId).and("battleActivityId").is(dungeonId));
        return mongoTemplate.findOne(query, UserBattleDungeonRecord.class);
    }

    public void updateUserDungeonRecord(UserBattleDungeonRecord record) {
        mongoTemplate.save(record);
    }

    public boolean updateUserDungeonRecordPrizeList(String id, List<PrizeInfoRecord> obtainedPrizeList) {
        Query query = new Query(Criteria.where("id").is(id));
        Update update = Update.update("prizeList", obtainedPrizeList).set("updatedTime", System.currentTimeMillis());
        UpdateResult updateResult = mongoTemplate.updateFirst(query, update, UserBattleDungeonRecord.class);
        return updateResult.isModifiedCountAvailable() && updateResult.getModifiedCount() > 0;
    }

    public boolean updateObtainPhotoStatus(String id, boolean status) {
        Query query = new Query(Criteria.where("id").is(id));
        Update update = new Update();
        // Set可以让字段不存在时写进去
        update.set("photoReceived", status);
        update.set("updatedTime", System.currentTimeMillis());
        UpdateResult updateResult = mongoTemplate.updateFirst(query, update, UserBattleDungeonRecord.class);
        return updateResult.isModifiedCountAvailable() && updateResult.getModifiedCount() > 0;
    }
}
