package com.kuaikan.role.cardgame.repository;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.stereotype.Repository;

import com.kuaikan.role.game.api.bean.cardbattle.CardBattleBondCostItem;

/**
 * 卡牌战斗用户羁绊点数明细
 *
 * <AUTHOR>
 * @date 2025/3/31
 */
@Slf4j
@Repository
public class CardBattleBondCostItemRepository {

    @Resource
    private MongoOperations mongoTemplate;

    public CardBattleBondCostItem save(CardBattleBondCostItem record) {
        return mongoTemplate.save(record);
    }
}
