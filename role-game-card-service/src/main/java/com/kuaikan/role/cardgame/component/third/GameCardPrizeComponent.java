package com.kuaikan.role.cardgame.component.third;

import java.util.List;
import java.util.Map;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.google.common.collect.Maps;

import com.kuaikan.game.gamecard.base.model.Prize;
import com.kuaikan.game.gamecard.prize.def.service.GameCardPrizeService;

/**
 *
 * <AUTHOR>
 * @date 2024/10/31
 */
@Slf4j
@Component
public class GameCardPrizeComponent {

    @Resource
    private GameCardPrizeService gameCardPrizeService;

    public Map<Long, Prize> fetchPrizeMapByIds(List<Long> prizeIdList) {
        try {
            if (CollectionUtils.isEmpty(prizeIdList)) {
                return Maps.newHashMap();
            }
            Map<Long, Prize> prizeMap = gameCardPrizeService.getOnlinePrizeMapByPrizeIdList(prizeIdList);
            log.debug("fetchPrizeByIds prizeIdList={}, prizeMap={}", prizeIdList, prizeMap);
            return prizeMap;
        } catch (Exception e) {
            log.error("fetchPrizeByIds error", e);
        }
        return Maps.newHashMap();
    }
}
