package com.kuaikan.role.cardgame.component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.kuaikan.common.model.RpcResult;
import com.kuaikan.role.game.api.model.AdoptCouponModel;
import com.kuaikan.role.game.api.model.BlindBoxCouponModel;
import com.kuaikan.role.game.api.model.UserCouponModel;
import com.kuaikan.role.game.api.service.CouponService;

/**
 * 获取用户的领养折扣券
 */
@Slf4j
@Component
public class UserCouponComponent {

    @Resource
    private CouponService adoptCouponService;

    /**
     *获取角色领养折扣券
     */
    public List<UserCouponModel> getUserAdoptCoupon(long userId) {
        try {
            if (userId == 0) {
                return Collections.emptyList();
            }
            RpcResult<AdoptCouponModel> result = adoptCouponService.queryAdoptDiscountCouponList((int) userId);
            log.info("getUserAdoptCoupon userId={}, result={}", userId, result);
            if (result == null || !result.isSuccess() || result.getData() == null) {
                return Collections.emptyList();
            }
            if (result.getData().getAvailableList() == null) {
                return Collections.emptyList();
            }
            Map<String, UserCouponModel> map = new HashMap<>();

            List<AdoptCouponModel.CouponModel> available = result.getData().getAvailableList();
            for (AdoptCouponModel.CouponModel coupon : available) {
                String key = coupon.getRoleGroupsModel().getId() + "-" + coupon.getRoleAdoptCoupon().getDiscountRate();
                if (map.containsKey(key)) {
                    UserCouponModel model = map.get(key);
                    model.setBalance(model.getBalance() + 1);
                } else {
                    UserCouponModel model = UserCouponModel.valueOf(coupon);
                    map.put(key, model);
                }
            }

            List<AdoptCouponModel.CouponModel> duplicate = result.getData().getDuplicateList();
            if (duplicate == null) {
                duplicate = new ArrayList<>();
            }
            for (AdoptCouponModel.CouponModel coupon : duplicate) {
                String key = coupon.getRoleGroupsModel().getId() + "-" + coupon.getRoleAdoptCoupon().getDiscountRate();
                if (map.containsKey(key)) {
                    UserCouponModel model = map.get(key);
                    model.setBalance(model.getBalance() + 1);
                } else {
                    UserCouponModel model = UserCouponModel.valueOf(coupon);
                    map.put(key, model);
                }
            }
            return new ArrayList<>(map.values());
        } catch (Exception e) {
            log.error("getUserAdoptCouponModel error, userId={}, ", userId, e);
        }
        return Collections.emptyList();
    }

    /**
     * 获取“装扮盲盒券”
     */
    public List<UserCouponModel> getUserCostumeBoxCoupon(long userId) {
        try {
            if (userId == 0) {
                return Collections.emptyList();
            }
            RpcResult<AdoptCouponModel> result = adoptCouponService.queryAdoptDiscountCouponList((int) userId);
            log.info("getUserCostumeBoxCoupon userId={}, result={}", userId, result);
            if (result == null || !result.isSuccess() || result.getData() == null) {
                return Collections.emptyList();
            }
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("getUserCostumeBoxCoupon error, userId={}, ", userId, e);
        }
        return Collections.emptyList();
    }

    public List<BlindBoxCouponModel.BlindBoxCoupon> getUserRoleCostumeCoupon(long userId) {
        try {
            RpcResult<BlindBoxCouponModel> rpcResult = adoptCouponService.queryBlindBoxCouponList((int) userId);
            log.info("getUserRoleCostumeCoupon userId={}, result={}", userId, rpcResult);
            if (rpcResult == null || !rpcResult.isSuccess() || rpcResult.getData() == null || CollectionUtils.isEmpty(rpcResult.getData().getBlindBoxCouponList())) {
                return Collections.emptyList();
            }
            return rpcResult.getData().getBlindBoxCouponList();
        } catch (Exception e) {
            log.error("getUserRoleCostumeCoupon error, userId={}, ", userId, e);
        }
        return Collections.emptyList();
    }
}
