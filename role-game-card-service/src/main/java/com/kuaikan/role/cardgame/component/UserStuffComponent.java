package com.kuaikan.role.cardgame.component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.kuaikan.common.model.RpcResult;
import com.kuaikan.role.game.api.model.EmotionBondBottleModel;
import com.kuaikan.role.game.api.model.UserEnergyItemModel;
import com.kuaikan.role.game.api.model.UserStuffModel;
import com.kuaikan.role.game.api.rpc.param.ListUserEnergyItemParam;
import com.kuaikan.role.game.api.rpc.param.ListUserStuffParam;
import com.kuaikan.role.game.api.rpc.param.SilverCoinChargeParam;
import com.kuaikan.role.game.api.rpc.param.SilverCoinConsumeParam;
import com.kuaikan.role.game.api.rpc.result.UserSilverCoinAccountModel;
import com.kuaikan.role.game.api.service.EnergyBottleService;
import com.kuaikan.role.game.api.service.RoleGroupService;
import com.kuaikan.role.game.api.service.SilverCoinService;
import com.kuaikan.role.game.api.service.UserStuffService;

/**
 * UserStuffComponent
 *
 * <AUTHOR>
 * @date 2024/6/24
 */
@Slf4j
@Component
public class UserStuffComponent {

    @Resource
    private UserStuffService userStuffService;
    @Resource
    private SilverCoinService silverCoinService;
    @Resource
    private EnergyBottleService energyBottleService;
    @Resource
    private RoleGroupService roleGroupService;


    /**
     * 获取角色化材料奖励
     *
     * @param userId 用户id
     * @return 材料奖励列表
     */
    public List<UserStuffModel> getUserStuff(long userId) {
        try {
            if (userId == 0) {
                return Collections.emptyList();
            }
            ListUserStuffParam param = new ListUserStuffParam().setUserId((int) userId);
            RpcResult<List<UserStuffModel>> result = userStuffService.listUserStuff(param);
            log.debug("getUserStuff userId={}, result={}", userId, result);
            if (result == null || !result.isSuccess() || result.getData() == null) {
                return Collections.emptyList();
            }
            return result.getData();
        } catch (Exception e) {
            log.error("getUserStuff error, userId={}, ", userId, e);
        }
        return Collections.emptyList();
    }

    /**
     * 获取角色化行动力药水
     *
     * @param userId 用户id
     * @return 行动力药水列表
     */
    public List<UserEnergyItemModel> getUserEnergyItem(long userId) {
        try {
            if (userId == 0) {
                return Collections.emptyList();
            }
            ListUserEnergyItemParam param = new ListUserEnergyItemParam().setUserId((int) userId);
            RpcResult<List<UserEnergyItemModel>> result = energyBottleService.listUserEnergyBottle(param);
            log.info("getUserEnergyItem userId={}, result={}", userId, result);
            if (result == null || !result.isSuccess() || result.getData() == null) {
                return Collections.emptyList();
            }
            return result.getData();
        } catch (Exception e) {
            log.error("getUserEnergyItem error, userId={}, ", userId, e);
        }
        return Collections.emptyList();
    }

    /**
     * 查询用户银币数量
     *
     * @param userId 用户id
     * @return
     */
    public Integer getSilverCoin(long userId) {
        try {
            RpcResult<UserSilverCoinAccountModel> result = silverCoinService.getUserAccount((int) userId);
            log.info("getSilverCoin userId={}, result={}", userId, result);
            if (result == null || !result.isSuccess()) {
                return 0;
            }
            return Optional.ofNullable(result.getData()).map(UserSilverCoinAccountModel::getBalance).orElse(0);
        } catch (Exception e) {
            log.error("getSilverCoin error, userId={}, ", userId, e);
        }
        return 0;
    }

    public List<EmotionBondBottleModel> getUserAllBondBottleList(long userId) {
        try {
            RpcResult<List<EmotionBondBottleModel>> result = roleGroupService.getEmotionBondBottleList((int) userId);
            if (result != null && result.isSuccess() && CollectionUtils.isNotEmpty(result.getData())) {
                return result.getData();
            }
        } catch (Exception e) {
            log.error("roleGroupService getEmotionBondBottleList error, userId={}, ", userId, e);
        }
        return Collections.emptyList();
    }

    /**
     * 充值银币
     *
     * @param userId          用户id
     * @param silverCoinValue 银币数量
     * @param orderId         订单id
     * @return 是否成功
     */
    public boolean chargeSilverCoin(long userId, int silverCoinValue, String orderId) {
        try {
            SilverCoinChargeParam chargeParam = new SilverCoinChargeParam().setUserId((int) userId).setCnt(silverCoinValue).setOrderId(orderId);
            RpcResult<Void> result = silverCoinService.cardCharge(chargeParam);
            log.info("chargeSilverCoin chargeParam={}, result={}", chargeParam, result);
            return result != null && result.isSuccess();
        } catch (Exception e) {
            log.error("chargeSilverCoin error, userId={}, orderId={}", userId, orderId, e);
        }
        return false;
    }

    /**
     * 消耗银币
     *
     * @param userId          用户id
     * @param silverCoinValue 银币数量
     * @param orderId         订单id
     * @return 是否成功
     */
    public boolean consumeSilverCoin(long userId, int silverCoinValue, String orderId) {
        try {
            SilverCoinConsumeParam consumeParam = new SilverCoinConsumeParam().setUserId((int) userId).setCnt(silverCoinValue).setOrderId(orderId);
            RpcResult<Void> result = silverCoinService.cardConsume(consumeParam);
            log.info("consumeSilverCoin consumeParam={}, result={}", consumeParam, result);
            return result != null && result.isSuccess();
        } catch (Exception e) {
            log.error("consumeSilverCoin error, userId={}, orderId={}", userId, orderId, e);
        }
        return false;
    }
}
