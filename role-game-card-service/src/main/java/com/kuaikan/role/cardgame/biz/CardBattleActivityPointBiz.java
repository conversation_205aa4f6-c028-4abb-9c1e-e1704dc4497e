package com.kuaikan.role.cardgame.biz;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import com.kuaikan.common.bean.RequestInfo;
import com.kuaikan.common.tools.serialize.JsonUtils;
import com.kuaikan.role.cardgame.cache.CardBattleLockManager;
import com.kuaikan.role.cardgame.config.CardServiceApolloConfig;
import com.kuaikan.role.cardgame.repository.BattleActivityConfigRepository;
import com.kuaikan.role.cardgame.repository.CardBattleActivityPointRecordRepository;
import com.kuaikan.role.game.api.bean.cardbattle.BattleActivityConfig;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleActivityPointRecord;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleLotteryActivityConfig;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleResourceAcquisitionDTO;
import com.kuaikan.role.game.api.rpc.result.cardbattle.CardBattleActivityPointDTO;
import com.kuaikan.role.game.api.util.CardBattleUtils;

/**
 * 卡牌战斗活动点数业务
 * <AUTHOR>
 * @date 2025/4/22
 */
@Slf4j
@Service
public class CardBattleActivityPointBiz {

    @Resource
    private CardBattleActivityPointRecordRepository cardBattleActivityPointRecordRepository;
    @Resource
    private BattleActivityConfigRepository battleActivityConfigRepository;
    @Resource
    private CardServiceApolloConfig apolloConfig;
    @Resource
    private CardBattleLockManager cardBattleLockManager;
    @Resource
    private CardBattleBiz cardBattleBiz;

    public CardBattleActivityPointDTO getActivityCostPointInfo(RequestInfo requestInfo, String activityId) {
        int userId = requestInfo.getUserId();
        BattleActivityConfig battleActivityConfig = battleActivityConfigRepository.getBattleExploreById(activityId);
        CardBattleLotteryActivityConfig activityConfig;
        if (battleActivityConfig == null
                || (activityConfig = JsonUtils.fromJson(battleActivityConfig.getExtraInfo(), CardBattleLotteryActivityConfig.class)) == null) {
            log.warn("getActivityCostPointInfo activityConfig invalid，activityId={}, battleActivityConfig={}", activityId, battleActivityConfig);
            return CardBattleActivityPointDTO.init(0, 0, 0, "");
        }

        CardBattleActivityPointRecord battlePointRecord = getAndRefreshPointRecord(userId, activityId, activityConfig);
        Integer point = Optional.ofNullable(battlePointRecord).map(CardBattleActivityPointRecord::getPoint).orElse(0);
        Integer limitPoint = Optional.ofNullable(activityConfig.getExclusiveTicket())
                .map(CardBattleLotteryActivityConfig.ExclusiveTicket::getTicketLimit)
                .orElse(0);
        int replyMin = apolloConfig.getActivityPointReplyTime();
        long bondNextPointRemainTime = Optional.ofNullable(battlePointRecord).map(CardBattleActivityPointRecord::getLastGetPointTime).orElse(0L)
                + TimeUnit.MINUTES.toMillis(replyMin) - System.currentTimeMillis();
        String icon = Optional.ofNullable(activityConfig.getExclusiveTicket()).map(e -> CardBattleUtils.getImgUrlJoinHost(e.getIcon())).orElse("");
        return CardBattleActivityPointDTO.init(point, limitPoint, bondNextPointRemainTime, icon);
    }

    public CardBattleActivityPointRecord getAndRefreshPointRecord(Integer userId, String activityId, CardBattleLotteryActivityConfig activityConfig) {
        CardBattleActivityPointRecord battleBondPointRecord = getActivityPointRecord(userId, activityId, activityConfig);
        updateUserActivityPoint(battleBondPointRecord, activityConfig);
        return battleBondPointRecord;
    }

    public CardBattleActivityPointRecord getActivityPointRecord(Integer userId, String activityId, CardBattleLotteryActivityConfig activityConfig) {
        CardBattleActivityPointRecord activityPointRecord = cardBattleActivityPointRecordRepository.getByUserIdAndActivityId(userId, activityId);
        Integer limitPoint = Optional.ofNullable(activityConfig.getExclusiveTicket())
                .map(CardBattleLotteryActivityConfig.ExclusiveTicket::getTicketLimit)
                .orElse(0);
        if (activityPointRecord == null) {
            activityPointRecord = initActivityPointRecord(userId, activityId, limitPoint);
        }
        return activityPointRecord;
    }

    public CardBattleActivityPointRecord initActivityPointRecord(Integer userId, String activityId, Integer point) {
        CardBattleActivityPointRecord activityPointRecord = new CardBattleActivityPointRecord();
        activityPointRecord.setUserId(userId);
        activityPointRecord.setActivityId(activityId);
        activityPointRecord.setPoint(point);
        long now = System.currentTimeMillis();
        activityPointRecord.setLastGetPointTime(now);
        activityPointRecord.setCreatedAt(now);
        activityPointRecord.setUpdatedAt(now);
        return cardBattleActivityPointRecordRepository.save(activityPointRecord);
    }

    public void updateUserActivityPoint(CardBattleActivityPointRecord activityPointRecord, CardBattleLotteryActivityConfig activityConfig) {
        long now = System.currentTimeMillis();
        Integer point = activityPointRecord.getPoint();
        Integer maxPoint = Optional.ofNullable(activityConfig.getExclusiveTicket())
                .map(CardBattleLotteryActivityConfig.ExclusiveTicket::getTicketLimit)
                .orElse(0);
        if (point >= maxPoint) {
            activityPointRecord.setLastGetPointTime(now);
            return;
        }
        int replyTime = apolloConfig.getActivityPointReplyTime();
        Long lastGetPointTime = activityPointRecord.getLastGetPointTime();
        int diff = (int) ((now - lastGetPointTime) / TimeUnit.MINUTES.toMillis(replyTime));
        int addPoint = Math.min(diff, maxPoint - point);
        if (addPoint > 0) {
            int curPoint = point + addPoint;
            activityPointRecord.setPoint(curPoint);
            activityPointRecord.setLastGetPointTime(now);
            updateActivityPointWithLock(activityPointRecord.getUserId(), activityPointRecord.getActivityId(), curPoint, now);
        }
    }

    private boolean updateActivityPointWithLock(Integer userId, String activityId, int curPoint, Long lastGetPointTime) {
        boolean isLock = cardBattleLockManager.lockForCardBattleUserActivityPoint(userId, activityId);
        if (!isLock) {
            log.warn("updateActivityPointWithLock lock fail, userId={}, activityId={}", userId, activityId);
            return false;
        }
        try {
            boolean changeRet = cardBattleActivityPointRecordRepository.updatePoint(userId, activityId, curPoint, lastGetPointTime);
            log.info("updateActivityPointWithLock changePoint, userId={}, activityId={}, curPoint={}, changeRet={}", userId, activityId, curPoint, changeRet);
            return changeRet;
        } catch (Exception e) {
            log.error("updateActivityPointWithLock error, userId={}, activityId={}, curPoint={}", userId, activityId, curPoint, e);
        } finally {
            cardBattleLockManager.unlockForCardBattleUserActivityPoint(userId, activityId);
        }
        return false;
    }

    public CardBattleResourceAcquisitionDTO getActivityPointPopUpWindow(RequestInfo requestInfo, String activityId) {
        int userId = requestInfo.getUserId();
        BattleActivityConfig battleActivityConfig = battleActivityConfigRepository.getBattleExploreById(activityId);
        CardBattleLotteryActivityConfig activityConfig;
        if (battleActivityConfig == null
                || (activityConfig = JsonUtils.fromJson(battleActivityConfig.getExtraInfo(), CardBattleLotteryActivityConfig.class)) == null) {
            log.error("getActivityPointPopUpWindow activityConfig invalid，activityId={}, battleActivityConfig={}", activityId, battleActivityConfig);
            return null;
        }
        CardBattleLotteryActivityConfig.ExclusiveTicket exclusiveTicket = activityConfig.getExclusiveTicket();
        if (exclusiveTicket == null) {
            return null;
        }
        CardBattleResourceAcquisitionDTO resourceAcquisitionDTO = new CardBattleResourceAcquisitionDTO();
        resourceAcquisitionDTO.setPopupTitle(exclusiveTicket.getName());
        resourceAcquisitionDTO.setPopupSubtitle(exclusiveTicket.getDesc());

        Long questContainerId = exclusiveTicket.getQuestContainerId();
        List<CardBattleResourceAcquisitionDTO.AcquisitionItem> acquisitionWays = cardBattleBiz.getAcquisitionWays(userId, questContainerId);
        resourceAcquisitionDTO.setAcquisitionWays(acquisitionWays);
        return resourceAcquisitionDTO;
    }

    public boolean consumeActivityPoint(int userId, BattleActivityConfig activityConfig, int point) {
        String activityId = activityConfig.getId();
        CardBattleLotteryActivityConfig limitedTimeActivityConfig = JsonUtils.fromJson(activityConfig.getExtraInfo(), CardBattleLotteryActivityConfig.class);
        if (limitedTimeActivityConfig == null) {
            log.error("consumeActivityPoint activityConfig invalid，activityId={}, activityConfig={}", activityId, activityConfig);
            return false;
        }
        Integer maxPoint = Optional.ofNullable(limitedTimeActivityConfig.getExclusiveTicket())
                .map(CardBattleLotteryActivityConfig.ExclusiveTicket::getTicketLimit)
                .orElse(0);
        CardBattleActivityPointRecord activityPointRecord = getActivityPointRecord(userId, activityId, limitedTimeActivityConfig);
        Integer userPoint = activityPointRecord.getPoint();
        if (userPoint < point) {
            return false;
        }

        Long lastGetPointTime = activityPointRecord.getLastGetPointTime();
        if (userPoint >= maxPoint && userPoint - point < maxPoint) {
            lastGetPointTime = System.currentTimeMillis();
        }
        int curPoint = userPoint - point;
        return updateActivityPointWithLock(userId, activityId, curPoint, lastGetPointTime);
    }
}
