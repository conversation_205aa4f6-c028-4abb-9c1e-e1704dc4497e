package com.kuaikan.role.cardgame.mapper;

import org.apache.ibatis.annotations.Param;

import com.kuaikan.role.game.api.bean.cardbattle.CardBattleMonsterLevelGrowthConfig;

/**
 * 战斗对象模板配置 mapper
 *
 * <AUTHOR>
 * @date 2024/6/18
 */
public interface CardBattleMonsterLevelGrowthConfigMapper {

    CardBattleMonsterLevelGrowthConfig selectByTypeAndLevel(@Param("type") Integer type, @Param("level") Integer level);

    CardBattleMonsterLevelGrowthConfig selectMaxLevelConfigByType(@Param("type") Integer type);

}
