package com.kuaikan.role.cardgame.biz;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.kuaikan.comic.enums.PageSourceType;
import com.kuaikan.common.bean.RequestInfo;
import com.kuaikan.common.enums.TargetType;
import com.kuaikan.idgenerator.sdk.BufferedIdGenerator;
import com.kuaikan.reward.enums.ActivityType;
import com.kuaikan.reward.enums.PageInfoBizType;
import com.kuaikan.role.cardgame.cache.CardBattleCacheManager;
import com.kuaikan.role.cardgame.component.BattleUserComponent;
import com.kuaikan.role.cardgame.config.CardServiceApolloConfig;
import com.kuaikan.role.cardgame.repository.CardBattleBondCostItemRepository;
import com.kuaikan.role.cardgame.repository.CardBattleCommonConfigRepository;
import com.kuaikan.role.cardgame.repository.CardBattleCostItemRepository;
import com.kuaikan.role.cardgame.repository.CardBattleRechargeOrderRepository;
import com.kuaikan.role.cardgame.util.DateUtil;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleBondCostItem;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleCommonConfig;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleCostItem;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleFeedProp;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleRechargeOrder;
import com.kuaikan.role.game.api.constant.BattleActivityConstants;
import com.kuaikan.role.game.api.constant.CardBattleResponseCodeMsg;
import com.kuaikan.role.game.api.enums.RewardOrderStatus;
import com.kuaikan.role.game.api.enums.cardbattle.CardBattleBondCostItemSceneEnum;
import com.kuaikan.role.game.api.enums.cardbattle.CardBattleCostItemSceneEnum;
import com.kuaikan.role.game.api.enums.cardbattle.CardBattleFeedPropEnum;
import com.kuaikan.role.game.api.model.RewardOrderModel;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleCreatRechargeOrderDTO;

/**
 * 卡牌战斗充值道具业务
 * <AUTHOR>
 * @date 2024/10/14
 */
@Slf4j
@Service
public class CardBattleRechargePropBiz {

    // 加料使用固定专题ID
    private static final Integer FEED_PROP_FIX_TOPIC_ID = 78;

    @Resource
    private CardServiceApolloConfig cardServiceApolloConfig;
    @Resource
    private CardBattleRechargeOrderRepository cardBattleRechargeOrderRepository;
    @Resource
    private CardBattleCommonConfigRepository cardBattleCommonConfigRepository;
    @Resource
    private BattleUserComponent battleUserComponent;
    @Resource
    private CardBattleCostItemRepository cardBattleCostItemRepository;
    @Resource
    private CardBattleCacheManager cardBattleCacheManager;
    @Resource
    private CardBattleBondPointBiz cardBattleBondPointBiz;
    @Resource
    private CardBattleBondCostItemRepository cardBattleBondCostItemRepository;

    public RewardOrderModel createRechargeOrder(RequestInfo requestInfo, String propId) {
        int userId = requestInfo.getUserId();
        List<CardBattleFeedProp> cardBattleFeedPropConfig = cardServiceApolloConfig.getCardBattleFeedProp();
        boolean isDiscount = false;
        CardBattleFeedProp cardBattleFeedProp = cardBattleFeedPropConfig.stream().filter(e -> Objects.equals(propId, e.getPropId())).findFirst().orElse(null);
        if (cardBattleFeedProp == null) {
            cardBattleFeedProp = cardBattleFeedPropConfig.stream().filter(e -> Objects.equals(propId, e.getDiscountPropId())).findFirst().orElse(null);
            isDiscount = true;
            if (cardBattleFeedProp == null) {
                log.error("createRechargeOrder feedProp not exist, userId={}, propId={}", userId, propId);
                return null;
            }
        }
        // 羁绊体力购买次数限制
        if (CardBattleFeedPropEnum.BOND_COMMON_POINT.getCode().equals(cardBattleFeedProp.getPropType())) {
            boolean notLimit = checkBondPointBuyTimesLimit(userId);
            if (!notLimit) {
                log.info("createRechargeOrder bond point buy times limit, userId={}, propId={}", userId, propId);
                return null;
            }
        }

        long orderId = BufferedIdGenerator.getRandomId();
        CardBattleRechargeOrder rechargeOrder = CardBattleRechargeOrder.init(userId, propId, String.valueOf(orderId), cardBattleFeedProp.getPropType(), null,
                RewardOrderStatus.CREATED.getCode());
        cardBattleRechargeOrderRepository.save(rechargeOrder);

        RewardOrderModel.PageInfo pageInfo = new RewardOrderModel.PageInfo();
        pageInfo.setPageSource(PageSourceType.HYBIRD.getCode());
        pageInfo.setBizType(PageInfoBizType.CARD_BATTLE.getCode());
        pageInfo.setUniqueIdentity(String.valueOf(orderId));
        RewardOrderModel model = new RewardOrderModel();
        model.setGiftId(String.valueOf(propId));
        model.setValue(isDiscount ? cardBattleFeedProp.getDiscountSpendKkb() : cardBattleFeedProp.getSpendKkb());
        model.setPageInfo(pageInfo);
        model.setGiftActivityType(ActivityType.COMMON.getCode());
        model.setTargetId(String.valueOf(FEED_PROP_FIX_TOPIC_ID));
        model.setTargetType(TargetType.TOPIC.getCode());
        return model;
    }

    private boolean checkBondPointBuyTimesLimit(int userId) {
        String windowId = DateUtil.getCurrentWindowId(BattleActivityConstants.RESET_HOUR);
        int count = cardBattleCacheManager.getBondPointBuyLimit(userId, windowId);
        int buyLimit = cardServiceApolloConfig.getBondPointBuyLimit();
        return count < buyLimit;
    }

    public CardBattleCreatRechargeOrderDTO queryRechargeOrder(RequestInfo requestInfo, String orderId) {
        int userId = requestInfo.getUserId();
        CardBattleRechargeOrder rechargeOrder = cardBattleRechargeOrderRepository.findByUk(userId, orderId);
        if (rechargeOrder == null) {
            log.error("queryRechargeOrder rechargeOrder not exist, userId={}, orderId={}", userId, orderId);
            return CardBattleCreatRechargeOrderDTO.init(CardBattleResponseCodeMsg.CARD_BATTLE_FEED_ORDER_QUERY_FAIL);
        }
        List<CardBattleFeedProp> cardBattleFeedPropConfig = cardServiceApolloConfig.getCardBattleFeedProp();
        CardBattleFeedProp cardBattleFeedProp = cardBattleFeedPropConfig.stream()
                .filter(e -> Objects.equals(rechargeOrder.getPropId(), e.getPropId()))
                .findFirst()
                .orElse(null);
        if (cardBattleFeedProp == null) {
            cardBattleFeedProp = cardBattleFeedPropConfig.stream()
                    .filter(e -> Objects.equals(rechargeOrder.getPropId(), e.getDiscountPropId()))
                    .findFirst()
                    .orElse(null);
            if (cardBattleFeedProp == null) {
                log.error("queryRechargeOrder feedProp not exist, userId={}, rechargeOrder={}", userId, rechargeOrder);
                return CardBattleCreatRechargeOrderDTO.init(CardBattleResponseCodeMsg.CARD_BATTLE_FEED_ORDER_QUERY_FAIL);
            } else {
                cardBattleFeedProp.setPropId(cardBattleFeedProp.getDiscountPropId());
                cardBattleFeedProp.setSpendKkb(cardBattleFeedProp.getDiscountSpendKkb());
            }
        }
        return CardBattleCreatRechargeOrderDTO.init(CardBattleResponseCodeMsg.SUCCESS, orderId, rechargeOrder.getStatus(), cardBattleFeedProp);
    }

    public void recharge(int userId, String orderId) {
        CardBattleRechargeOrder rechargeOrder = cardBattleRechargeOrderRepository.findByUk(userId, orderId);
        if (rechargeOrder == null) {
            log.error("recharge rechargeOrder not exist, userId={}, orderId={}", userId, orderId);
            throw new RuntimeException("recharge rechargeOrder not exist");
        }
        if (rechargeOrder.getStatus() == RewardOrderStatus.FINISHED.getCode()) {
            log.error("recharge rechargeOrder status is FINISHED, rechargeOrder={}", rechargeOrder);
            return;
        }
        List<CardBattleFeedProp> cardBattleFeedPropConfig = cardServiceApolloConfig.getCardBattleFeedProp();
        CardBattleFeedProp cardBattleFeedProp = cardBattleFeedPropConfig.stream()
                .filter(e -> Objects.equals(rechargeOrder.getPropId(), e.getPropId()) || Objects.equals(rechargeOrder.getPropId(), e.getDiscountPropId()))
                .findFirst()
                .orElse(null);
        if (cardBattleFeedProp == null) {
            log.error("recharge feedProp not exist, userId={}, rechargeOrder={}", userId, rechargeOrder);
            throw new RuntimeException("recharge feedProp not exist");
        }
        if (CardBattleFeedPropEnum.BATTLE_POINT.getCode().equals(cardBattleFeedProp.getPropType())) {
            boolean rechargeRet = doChargeBattlePoint(userId, cardBattleFeedProp);
            if (!rechargeRet) {
                log.error("recharge battle point failed, userId={}, rechargeOrder={}", userId, rechargeOrder);
                throw new RuntimeException("recharge battle point failed");
            }
            // 记录点数明细
            Integer chargeCost = cardBattleFeedProp.getFeedPropVal();
            recordCostItem(userId, chargeCost);
        } else if (CardBattleFeedPropEnum.BOND_COMMON_POINT.getCode().equals(cardBattleFeedProp.getPropType())) {
            boolean rechargeRet = doChargeBondCommonPoint(userId, cardBattleFeedProp);
            if (!rechargeRet) {
                log.error("recharge bond point failed, userId={}, rechargeOrder={}", userId, rechargeOrder);
                throw new RuntimeException("recharge bond point failed");
            }
            // 扣减次数
            String windowId = DateUtil.getCurrentWindowId(BattleActivityConstants.RESET_HOUR);
            Long count = cardBattleCacheManager.incrBondPointBuyLimit(userId, windowId);
            // 首次设置时添加过期时间
            if (count == 1) {
                int expireTime = (int) DateUtil.calculateTTL(BattleActivityConstants.RESET_HOUR);
                cardBattleCacheManager.expireBondPointBuyLimit(userId, windowId, expireTime);
            }
            // 记录点数明细
            Integer chargeCost = cardBattleFeedProp.getFeedPropVal();
            recordBondPointItem(userId, chargeCost);
        } else {
            log.error("recharge feed type not support, userId={}, rechargeOrder={}", userId, rechargeOrder);
            return;
        }
        boolean updateRet = cardBattleRechargeOrderRepository.updateStatus(rechargeOrder.getId(), RewardOrderStatus.FINISHED.getCode());
        if (!updateRet) {
            log.error("recharge finished updateStatus failed, userId={}, rechargeOrder={}", userId, rechargeOrder);
        }
    }

    private boolean doChargeBondCommonPoint(int userId, CardBattleFeedProp cardBattleFeedProp) {
        Integer feedPropVal = cardBattleFeedProp.getFeedPropVal();
        return cardBattleBondPointBiz.addUserBondCommonPoint(userId, feedPropVal);
    }

    private void recordBondPointItem(int userId, Integer chargeCost) {
        CardBattleBondCostItem costItem = CardBattleBondCostItem.init(userId, chargeCost, CardBattleBondCostItemSceneEnum.CHARGE_COST.getCode(),
                CardBattleBondPointBiz.COMMON_ROLE_GROUP_ID, null, null);
        cardBattleBondCostItemRepository.save(costItem);
    }

    private void recordCostItem(int userId, Integer chargeCost) {
        CardBattleCostItem costItem = CardBattleCostItem.init(userId, chargeCost, CardBattleCostItemSceneEnum.CHARGE_COST.getCode(), null);
        cardBattleCostItemRepository.save(costItem);
    }

    private boolean doChargeBattlePoint(int userId, CardBattleFeedProp cardBattleFeedProp) {
        Integer feedPropVal = cardBattleFeedProp.getFeedPropVal();
        CardBattleCommonConfig commonConfig = cardBattleCommonConfigRepository.getCommonConfig();
        return battleUserComponent.addUserPoint(userId, feedPropVal, commonConfig.getPointLimit());
    }

    public long countBuyPointRechargeOrder(long userId, long startTime, long endTime) {
        List<CardBattleRechargeOrder> rechargeOrders = cardBattleRechargeOrderRepository.findFinishedOrderByUser(userId, startTime, endTime);
        List<CardBattleFeedProp> propConfigs = cardServiceApolloConfig.getCardBattleFeedProp();
        if (CollectionUtils.isEmpty(propConfigs)) {
            log.error("战斗点数加料商品配置为空！！请检查配置！");
            return 0;
        }
        Map<String, Integer> propNumMap = propConfigs.stream().collect(Collectors.toMap(CardBattleFeedProp::getPropId, v -> 1, (v1, v2) -> v1));
        return rechargeOrders.stream().mapToLong(item -> propNumMap.getOrDefault(item.getPropId(), 0)).sum();
    }
}
