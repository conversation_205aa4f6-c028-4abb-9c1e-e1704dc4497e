package com.kuaikan.role.cardgame.config.datasource;

import java.util.Properties;
import javax.sql.DataSource;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import com.alibaba.druid.pool.DruidDataSource;
import com.github.pagehelper.PageInterceptor;

import com.kuaikan.common.db.DynamicDatasourceBeanFactory;

/**
 * <AUTHOR>
 * @date 2024/2/26
 */
@Configuration
@MapperScan(basePackages = { DynamicRoleGameDatasourceConfig.PACKAGE }, sqlSessionFactoryRef = DynamicRoleGameDatasourceConfig.SESSION_FACTORY)
public class DynamicRoleGameDatasourceConfig {

    private static final String TYPE_ALIASES_PACKAGE = "com.kuaikan.role.game.api.bean.cardbattle";
    static final String PACKAGE = "com.kuaikan.role.cardgame.mapper";
    static final String SESSION_FACTORY = "roleGameSqlSessionFactory";
    private static final String DATASOURCE = "roleGameWriteDataSource";

    @Bean
    public static DynamicDatasourceBeanFactory<DruidDataSource> roleGameDatasourceBeanFactory() {
        DynamicDatasourceBeanFactory<DruidDataSource> factory = new DynamicDatasourceBeanFactory<>();
        factory.setBeanName(DATASOURCE);
        factory.setDataSourceClass(DruidDataSource.class);
        factory.setConfigSources("jdbc");
        factory.setKeyPrefixes("common,gamecard.master");
        factory.setInitMethod("init");
        factory.setDestroyMethod("close");
        return factory;
    }

    @Primary
    @Bean(SESSION_FACTORY)
    public SqlSessionFactory roleGameSqlSessionFactory(@Qualifier(DATASOURCE) DataSource dataSource) throws Exception {
        SqlSessionFactoryBean factoryBean = new SqlSessionFactoryBean();
        factoryBean.setDataSource(dataSource);
        factoryBean.setTypeAliasesPackage(TYPE_ALIASES_PACKAGE);
        factoryBean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mappers/*.xml"));
        PageInterceptor pageInterceptor = new PageInterceptor();
        Properties properties = new Properties();
        properties.setProperty("helperDialect", "mysql");
        pageInterceptor.setProperties(properties);
        factoryBean.setPlugins(pageInterceptor);
        return factoryBean.getObject();
    }

    @Bean("roleGameTransaction")
    public DataSourceTransactionManager dataSourceTransactionManager(@Qualifier(DATASOURCE) DataSource dataSource) {
        DataSourceTransactionManager dataSourceTransactionManager = new DataSourceTransactionManager();
        dataSourceTransactionManager.setDataSource(dataSource);
        return dataSourceTransactionManager;
    }
}
