package com.kuaikan.role.cardgame.component.third;

import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.google.common.collect.Sets;

import com.kuaikan.common.config.Settings;
import com.kuaikan.data.kv.server.api.enums.IdType;
import com.kuaikan.data.kv.server.api.model.bean.common.FetchPropertiesReq;
import com.kuaikan.data.kv.server.api.model.bean.common.FetchPropertiesRes;
import com.kuaikan.data.kv.server.api.model.dto.base.ResultDTO;
import com.kuaikan.data.kv.server.api.service.CommonUserPropertiesService;
import com.kuaikan.game.common.util.DateUtil;
import com.kuaikan.game.common.util.gson.GsonUtils;
import com.kuaikan.role.cardgame.cache.AbstractCacheManager;
import com.kuaikan.role.game.api.constant.CacheKeyConstant;

/**
 * 用户画像属性组件
 *
 * <AUTHOR>
 * @date 2023/11/1
 */
@Slf4j
@Component
public class UserPropertyComponent extends AbstractCacheManager {

    @Resource
    private CommonUserPropertiesService commonUserPropertiesService;

    public static final String PAY_ABILITY_LEVEL_WEEK = "pay_ability_level_week";

    private static final String notExistValue = "-1";

    private static final Integer expiredTime = 60 * 60 * 12;

    private static Set<String> queryPropertiesByXDevice = Sets.newHashSet(PAY_ABILITY_LEVEL_WEEK);

    public Map<String, String> fetchUserPayAbilityWeek(String xDevice) {
        // 查询 xDevice 配置的数据
        Map<String, String> userPropertyMapWithXDevice = fetchUserPropertiesWithXDevice(xDevice, queryPropertiesByXDevice);
        log.debug("fetchUserActivePayAbilityWeek xDevice: {} ,userPropertiesWithXDevice:{}", xDevice, userPropertyMapWithXDevice);
        return userPropertyMapWithXDevice;
    }

    private Map<String, String> fetchUserPropertiesWithXDevice(String xDevice, Set<String> queryProperties) {
        if (StringUtils.isBlank(xDevice)) {
            return Collections.EMPTY_MAP;
        }
        IdType idType = IdType.XDEVICE;
        return fetchUserProperties(xDevice, idType, queryProperties);
    }

    private Map<String, String> fetchUserProperties(String uniqueKey, IdType idType, Set<String> queryProperties) {
        String key = String.format(CacheKeyConstant.USER_PROPERTIES_CACHE, uniqueKey, DateUtil.getCurrentDate());
        Map<String, String> cacheMap = currentRedis().hgetall(key);
        Set<String> missedQueryProperties = new HashSet<>();
        for (String queryProperty : queryProperties) {
            if (cacheMap.get(queryProperty) == null) {
                missedQueryProperties.add(queryProperty);
            }
        }
        if (CollectionUtils.isEmpty(missedQueryProperties)) {
            return cacheMap;
        }
        // 查询结果
        FetchPropertiesReq req = FetchPropertiesReq.builder().idType(idType).uniqueKey(uniqueKey).properties(queryProperties).build();
        Map<String, String> rpcResult = fetchUserProperties(uniqueKey, missedQueryProperties, req);
        cacheMap.putAll(rpcResult);
        return cacheMap;
    }

    private Map<String, String> fetchUserProperties(String uniqueKey, Set<String> queryProperties, FetchPropertiesReq req) {
        Map<String, String> result = fetchUserPropertiesWithoutCache(req);
        // 空数据缓存
        if (MapUtils.isEmpty(result)) {
            return Collections.EMPTY_MAP;
        }
        cacheUserProperties(uniqueKey, queryProperties, result);
        return result;
    }

    private Map<String, String> fetchUserPropertiesWithoutCache(FetchPropertiesReq req) {
        Map<String, Object> userProperties = doFetchUserProperties(req);
        if (userProperties == null) {
            return Collections.EMPTY_MAP;
        }
        Map<String, String> result = new HashMap<>();
        // 这里要根据req的参数来解析
        for (String propertyKey : req.getProperties()) {
            String value = null;
            Object obj = userProperties.get(propertyKey);
            if (obj != null) {
                value = String.valueOf(obj);
            }
            if (StringUtils.isNotBlank(value)) {
                result.put(propertyKey, value);
            }
        }
        return result;
    }

    public Map<String, Object> doFetchUserProperties(FetchPropertiesReq req) {
        try {
            ResultDTO<FetchPropertiesRes> result = commonUserPropertiesService.fetchProperties(req, Settings.getServiceName());
            log.info("fetchUserProperties req {} result {}", req, result);
            if (result.isSuccess()) {
                Map<String, Object> userProperties = result.getData().getProperties();
                userProperties = MapUtils.emptyIfNull(userProperties);
                return userProperties;
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("error in fetchUserProperties   req:{}", GsonUtils.toJson(req), e);
        }
        return null;
    }

    private void cacheUserProperties(String uniqueKey, Set<String> queryProperties, Map<String, String> userProperties) {
        String key = String.format(CacheKeyConstant.USER_PROPERTIES_CACHE, uniqueKey, DateUtil.getCurrentDate());
        for (String queryProperty : queryProperties) {
            if (MapUtils.isNotEmpty(userProperties) && userProperties.get(queryProperty) != null) {
                currentRedis().hset(key, queryProperty, userProperties.get(queryProperty));
            } else {
                currentRedis().hset(key, queryProperty, notExistValue);
            }
        }
        currentRedis().expire(key, expiredTime);
    }

}
