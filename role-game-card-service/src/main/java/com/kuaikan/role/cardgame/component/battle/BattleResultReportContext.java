package com.kuaikan.role.cardgame.component.battle;

import java.util.ArrayList;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleMonsterDTO;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleUserCardDTO;

/**
 *<AUTHOR>
 *@date 2024/9/13
 */
@Data
@Accessors(chain = true)
public class BattleResultReportContext extends BattleTaskBaseContext {

    // 选择的用户卡牌
    private List<CardBattleUserCardDTO> selectedUserCardList = new ArrayList<>();

    // 前端上报的战斗结果（消耗的boss血量）
    private Integer reportRoleDamage;

    private CardBattleMonsterDTO battleMonster;
}
