package com.kuaikan.role.cardgame.repository;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import com.kuaikan.role.game.api.bean.cardbattle.UserBattleLoveGuideRecord;

/**
 *<AUTHOR>
 *@date 2025/1/6
 */
@Repository
@Slf4j
public class UserBattleLoveGuideRepository {
    @Resource
    private MongoOperations mongoTemplate;

    public void saveUserBattleLoveGuideRecord(UserBattleLoveGuideRecord record) {
        record.setUpdatedTime(System.currentTimeMillis());
        mongoTemplate.save(record);
    }

    public UserBattleLoveGuideRecord getUserBattleLoveGuideRecord(Long userId) {
        Query query = new Query(Criteria.where("userId").is(userId));
        return mongoTemplate.findOne(query, UserBattleLoveGuideRecord.class);
    }

}
