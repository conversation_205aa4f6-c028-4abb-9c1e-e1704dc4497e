package com.kuaikan.role.cardgame.util;

import java.time.DayOfWeek;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.Calendar;
import java.util.Date;
import java.util.concurrent.TimeUnit;

public class DateUtil {

    private static final String TIME_ZONE_GMT8 = "GMT+8";

    public static int getRemainingTime(Long expireTime) {
        ZoneId gmt8Zone = ZoneId.of(TIME_ZONE_GMT8);
        Instant nowInstant = Instant.now();
        Instant expireInstant = Instant.ofEpochMilli(expireTime);  // 将 Long 类型转换为 Instant

        LocalDate today = nowInstant.atZone(gmt8Zone).toLocalDate();
        LocalDate expireDate = expireInstant.atZone(gmt8Zone).toLocalDate();

        if (expireDate.equals(today)) {
            // 同一天，计算剩余小时数
            long diffInMillis = expireInstant.toEpochMilli() - nowInstant.toEpochMilli();
            return (int) TimeUnit.MILLISECONDS.toHours(diffInMillis);
        }
        // 不在同一天，计算剩余天数
        return (int) ChronoUnit.DAYS.between(today, expireDate);
    }

    public static Date getExpireTime(Date now, Integer validDays) {
        Date date = new Date(now.getTime() + validDays * 24 * 60 * 60 * 1000L);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 指定时间戳所在周的结束时间
     * @param timestamp 时间戳
     */
    public static long getEndOfWeek(long timestamp) {
        // 指定中国时区
        ZoneId chinaZone = ZoneId.of("Asia/Shanghai");

        // 转换时间戳为中国时区的 LocalDateTime
        LocalDateTime dateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), chinaZone);

        // 获取本周日的日期，并设置为23:59:59.999
        LocalDateTime endOfWeek = dateTime.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY))
                .withHour(23)
                .withMinute(59)
                .withSecond(59)
                .withNano(999_999_999);

        // 转换回时间戳
        return endOfWeek.atZone(chinaZone).toInstant().toEpochMilli();
    }

    /**
     * 获取当前时间窗口标识（格式：yyyyMMdd）
     * 示例：当前时间 2023-10-10 03:00 → 窗口 20231009
     *       当前时间 2023-10-10 05:00 → 窗口 20231010
     */
    public static String getCurrentWindowId(int resetHour) {
        ZoneId zone = ZoneId.of("Asia/Shanghai");
        ZonedDateTime now = ZonedDateTime.now(zone);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        // 如果当前时间早于4点，属于前一天的时间窗口
        if (now.getHour() < resetHour) {
            return now.minusDays(1).format(formatter);
        }
        return now.format(formatter);
    }

    /**
     * 计算当前时间到下个重置时间的秒数（用于非零点刷新设置Redis过期时间）
     */
    public static long calculateTTL(int resetHour) {
        ZoneId zone = ZoneId.of("Asia/Shanghai");
        ZonedDateTime now = ZonedDateTime.now(zone);
        ZonedDateTime nextReset;

        if (now.getHour() < resetHour) {
            // 当天重置点
            nextReset = now.withHour(resetHour).withMinute(0).withSecond(0);
        } else {
            // 次日重置点
            nextReset = now.plusDays(1).withHour(resetHour).withMinute(0).withSecond(0);
        }

        return Duration.between(now, nextReset).getSeconds();
    }

    public static void main(String[] args) {
        // System.out.println(getEndOfWeek(System.currentTimeMillis()));
        System.out.println(getCurrentWindowId(4));
        System.out.println(calculateTTL(4));
    }
}
