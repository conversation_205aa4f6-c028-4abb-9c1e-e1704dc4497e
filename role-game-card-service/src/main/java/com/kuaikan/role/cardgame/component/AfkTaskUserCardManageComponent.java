package com.kuaikan.role.cardgame.component;

import java.util.Collections;
import java.util.List;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.kuaikan.role.cardgame.cache.CardBattleCacheManager;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleUserCardDTO;

/**
 * 挂机卡牌管理组件
 * <AUTHOR>
 * @date 2025/3/7
 */
@Slf4j
@Service
public class AfkTaskUserCardManageComponent {

    @Resource
    private CardBattleCacheManager cardBattleCacheManager;

    /**
     * 保存用户卡牌使用记录，使用score保存挂机到期时间，方便后续根据score批量删除
     */
    public boolean saveAfkTaskUserCardsUseRecord(long userId, List<Long> userCardIds, long endTime) {
        long count = 0;
        try {
            for (Long userCardId : userCardIds) {
                long saveRet = cardBattleCacheManager.saveAfkTaskUserCardUseRecord(userId, userCardId, endTime);
                count += saveRet;
            }
        } catch (Exception e) {
            log.error("saveAfkTaskUserCardsUseRecord error, userId:{}, userCardIds:{}, endTime:{}", userId, userCardIds, endTime, e);
        }
        return count == userCardIds.size();
    }

    public boolean releaseAfkTaskUserCardsUseRecord(int userId, List<Long> selectUserCardIds) {
        long count = 0;
        try {
            for (Long userCardId : selectUserCardIds) {
                long remRet = cardBattleCacheManager.singleDelAfkTaskUserCardUseRecord(userId, userCardId);
                count += remRet;
            }
        } catch (Exception e) {
            log.error("releaseAfkTaskUserCardsUseRecord error, userId:{}, userCardIds:{}", userId, selectUserCardIds, e);
        }
        return count == selectUserCardIds.size();
    }

    public List<Long> fetchAndRefreshAfkInProgressCardIds(long userId) {
        try {
            long now = System.currentTimeMillis();
            // 获取还没过期的卡排列表
            List<Long> cardIds = cardBattleCacheManager.getAfkInProgressCardIds(userId, now);
            // 删除小于当前时间的卡牌(防止大key)
            cardBattleCacheManager.remOverdueAfkTaskCard(userId, now);
            return cardIds;
        } catch (Exception e) {
            log.error("fetchAndRefreshAfkInProgressCardIds error, userId:{}", userId, e);
        }
        return Collections.emptyList();
    }

    public void setAfkStatusForCardList(long userId, List<CardBattleUserCardDTO> cardList) {
        List<Long> afkInProgressCardIds = fetchAndRefreshAfkInProgressCardIds(userId);
        if (CollectionUtils.isEmpty(afkInProgressCardIds)) {
            return;
        }
        cardList.forEach(card -> {
            card.setAfkInProcess(afkInProgressCardIds.contains(card.getId()));
        });
        log.debug("setAfkStatusForCardList, userId={}, afkInProgressCardIds={}, cardList={}", userId, afkInProgressCardIds, cardList);
    }
}
