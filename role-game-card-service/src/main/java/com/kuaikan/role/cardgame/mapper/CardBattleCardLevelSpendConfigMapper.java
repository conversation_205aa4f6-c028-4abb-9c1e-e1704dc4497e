package com.kuaikan.role.cardgame.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.kuaikan.role.game.api.bean.cardbattle.CardBattleCardLevelSpendConfig;

/**
 * 卡牌等级消耗配置 mapper
 *
 * <AUTHOR>
 * @date 2024/6/18
 */
public interface CardBattleCardLevelSpendConfigMapper {

    CardBattleCardLevelSpendConfig selectByLevel(@Param("level") int level);

    List<CardBattleCardLevelSpendConfig> selectAll();

    CardBattleCardLevelSpendConfig selectByLevelAndRare(@Param("level") int level, @Param("rareCode") int rareCode);

    List<CardBattleCardLevelSpendConfig> selectByRare(@Param("rareCode") int rareCode);
}
