package com.kuaikan.role.cardgame.biz;

import static com.kuaikan.role.cardgame.util.DingDingMessageUtil.CARD_BATTLE_ROBOT_SECRET;
import static com.kuaikan.role.cardgame.util.DingDingMessageUtil.CARD_BATTLE_ROBOT_TOKEN;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.kuaikan.common.config.Environment;
import com.kuaikan.common.config.Settings;
import com.kuaikan.common.tools.lang.convert.SafeConverter;
import com.kuaikan.game.common.enums.TablePartitionEnum;
import com.kuaikan.game.common.util.TablePartitionUtil;
import com.kuaikan.game.gamecard.dubbo.constants.CardRarity;
import com.kuaikan.game.gamecard.dubbo.dto.SubjectDTO;
import com.kuaikan.game.gamecard.dubbo.model.Card;
import com.kuaikan.game.gamecard.dubbo.service.GameCardBasicService;
import com.kuaikan.game.gamecard.dubbo.service.GameCardItemService;
import com.kuaikan.role.cardgame.cache.CardBattleCacheManager;
import com.kuaikan.role.cardgame.component.CardBasicComponent;
import com.kuaikan.role.cardgame.component.third.MarketingComponent;
import com.kuaikan.role.cardgame.config.TablePartition;
import com.kuaikan.role.cardgame.config.ThreadPools;
import com.kuaikan.role.cardgame.mapper.CardBattleCardIdModelConfigMapper;
import com.kuaikan.role.cardgame.mapper.CardBattleUserCardMapper;
import com.kuaikan.role.cardgame.repository.CardBattleCardModelRepository;
import com.kuaikan.role.cardgame.util.DingDingMessageUtil;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleCardBasicInfo;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleCardIdModelConfig;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleCardModelConfig;
import com.kuaikan.role.game.api.bean.cardbattle.PlotCard;
import com.kuaikan.role.game.api.bo.CardBattleCardCheckInfo;
import com.kuaikan.role.game.api.enums.cardbattle.CardBattleCardRarityEnum;
import com.kuaikan.role.game.api.enums.cardbattle.CardBattleCardSourceEnum;

/**
 * 数据修复
 *
 * <AUTHOR>
 * @date 2024/8/23
 */
@Slf4j
@Component
public class CardBattleDataFixBiz {

    private static final int BATCH_SIZE = 10000;

    @Resource
    private CardBattleUserCardMapper cardBattleUserCardMapper;
    @Resource
    private CardBattleCardIdModelConfigMapper cardBattleCardIdModelConfigMapper;
    @Resource
    private MarketingComponent marketingComponent;
    @Resource
    private CardBattleCardModelRepository cardBattleCardModelRepository;
    @Resource
    private GameCardBasicService gameCardBasicService;
    @Resource
    private GameCardItemService gameCardItemService;
    @Resource
    private CardBasicComponent cardBasicComponent;
    @Resource
    private CardBattleCacheManager cardBattleCacheManager;
    @Resource
    private UpdateUserCardModelBiz updateUserCardModelBiz;

    public String updateUserCardInfoByUid(long userId) {
        List<CardBattleCardIdModelConfig> changeCardConfigs = cardBattleCardIdModelConfigMapper.selectAll();
        String tableName = TablePartitionUtil.getTableName(TablePartitionEnum.CARD_BATTLE_USER_CARD, userId);
        for (CardBattleCardIdModelConfig cardConfig : changeCardConfigs) {
            int ret = cardBattleUserCardMapper.updateUserCardModelByUid(tableName, cardConfig.getCardId(), cardConfig.getModelId(), cardConfig.getTopicId(),
                    cardConfig.getAttr(), userId);
            log.info("updateUserCardInfoByUid处理table: {}, cardId:{}, 修改行数:{}", tableName, cardConfig.getCardId(), ret);
        }
        return "success";
    }

    public String updateUserCardInfo(int beginNum, int endNum) {
        List<CardBattleCardIdModelConfig> changeCardConfigs = cardBattleCardIdModelConfigMapper.selectAll();
        for (int i = beginNum; i < endNum; i++) {
            String tableName = TablePartitionUtil.getTableName(TablePartitionEnum.CARD_BATTLE_USER_CARD, i);
            long maxId = cardBattleUserCardMapper.getMaxId(tableName);
            CompletableFuture.runAsync(() -> changeCardModelInfoSize(tableName, changeCardConfigs, maxId), ThreadPools.USER_CARD_SYNC_POOL);
        }
        return "success";
    }

    public void changeCardModelInfoSize(String tableName, List<CardBattleCardIdModelConfig> changeCardConfigs, Long maxId) {
        log.info("开始处理 {}, maxId:{}", tableName, maxId);
        long startTime = System.currentTimeMillis();

        if (CollectionUtils.isEmpty(changeCardConfigs)) {
            return;
        }
        for (CardBattleCardIdModelConfig cardConfig : changeCardConfigs) {
            long beginId = 1;
            long total = 0;
            while (beginId < maxId) {
                int ret = cardBattleUserCardMapper.batchUpdateUserCardModelSize(tableName, cardConfig.getCardId(), cardConfig.getModelId(),
                        cardConfig.getTopicId(), cardConfig.getAttr(), beginId, beginId + BATCH_SIZE);
                beginId += BATCH_SIZE;
                total += ret;
            }
            log.info("changeCardModelInfo处理table: {}, cardId:{}, 修改行数:{}", tableName, cardConfig.getCardId(), total);
        }
        long cost = System.currentTimeMillis() - startTime;
        log.info("{}处理结束, 耗时:{}分钟", tableName, TimeUnit.MILLISECONDS.toMinutes(cost));
    }

    public void refreshPlotCardInfo(String activityName, Boolean isRefresh) {
        List<PlotCard> plotCardList = marketingComponent.getPlotCardList(activityName);
        if (CollectionUtils.isEmpty(plotCardList)) {
            log.info("refreshPlotCardInfo plotCardList empty, activityName={}", activityName);
            return;
        }
        log.info("refreshPlotCardInfo activityName={}, plotCardList={}", activityName, plotCardList);
        if (!isRefresh) {
            return;
        }
        int tableCount = TablePartition.CARD_BATTLE_USER_CARD.getTableCount();
        for (int i = 0; i < tableCount; i++) {
            String tableName = TablePartitionUtil.getTableName(TablePartitionEnum.CARD_BATTLE_USER_CARD, i);
            long startTime = System.currentTimeMillis();
            log.info("{}开始处理, startTime={}", tableName, startTime);
            for (PlotCard plotCard : plotCardList) {
                if (plotCard == null || plotCard.getRareCode() == null) {
                    log.info("plotCard is null or rareCode is null, plotCard={}", plotCard);
                    continue;
                }
                String plotCardId = plotCard.getPlotCardId();
                cardBattleUserCardMapper.refreshCardInfo(tableName, plotCardId, plotCard.getRareCode(), null, null, null, null);
            }
            long cost = System.currentTimeMillis() - startTime;
            log.info("{}处理结束, 耗时:{}分钟", tableName, TimeUnit.MILLISECONDS.toMinutes(cost));
        }
    }

    public void checkCardInfo(List<CardBattleCardIdModelConfig> params) {
        if (CollectionUtils.isEmpty(params)) {
            return;
        }
        CompletableFuture.runAsync(() -> doCheck(params));
    }

    private void doCheck(List<CardBattleCardIdModelConfig> params) {
        List<CardBattleCardCheckInfo> rareCheckInfos = new ArrayList<>();
        List<CardBattleCardCheckInfo> topicCheckInfos = new ArrayList<>();

        log.debug("checkCardInfo doCheck start, params size={}", params.size());
        try {
            for (CardBattleCardIdModelConfig param : params) {
                // TODO：非爱心卡未进行校验，存在风险（因为现在接口不支持返回稀有度这些信息）
                if (param.getCardType() != CardBattleCardSourceEnum.LOVE_CARD.getCode()) {
                    continue;
                }
                int uploadTopicId = param.getTopicId();
                int uploadModelId = param.getModelId();
                CardBattleCardModelConfig cardModelConfig = cardBattleCardModelRepository.getByModelId(uploadModelId);
                if (cardModelConfig == null) {
                    log.info("checkCardInfo cardModelConfig null, param={}", param);
                    continue;
                }
                int uploadRareCode = cardModelConfig.getRareCode();

                // 爱心卡
                Card cardInfo = gameCardBasicService.getCardByCardId(Long.parseLong(param.getCardId()));
                if (cardInfo == null) {
                    cardInfo = gameCardBasicService.getCardByCardId(Long.parseLong(param.getCardId()));
                    if (cardInfo == null) {
                        log.error("checkCardInfo cardInfo null, CardIdModelConfig={}", param);
                        continue;
                    }
                }
                String rarityLabel = CardRarity.getRarityLabel(cardInfo.getRarity());
                Integer rareCode = CardBattleCardRarityEnum.parseByName(rarityLabel);
                if (rareCode == null || uploadRareCode != rareCode) {
                    log.info("checkCardInfo 稀有度不一致, CardIdModelConfig={}, cardModelConfig={}, cardInfo={}", param, cardModelConfig, cardInfo);
                    CardBattleCardCheckInfo checkInfo = new CardBattleCardCheckInfo().setCardId(param.getCardId())
                            .setModelId(uploadModelId)
                            .setLoveCardRare(rareCode)
                            .setModelRare(uploadRareCode);
                    rareCheckInfos.add(checkInfo);
                }
                SubjectDTO subject = gameCardItemService.getSubjectDtoBySubjectId(cardInfo.getSubjectId());
                if (subject == null || uploadTopicId != subject.getComicTopicId()) {
                    log.info("checkCardInfo 专题不一致, CardIdModelConfig={}, cardModelConfig={}, cardInfo={}, subject={}", param, cardModelConfig, cardInfo,
                            subject);
                    CardBattleCardCheckInfo checkInfo = new CardBattleCardCheckInfo().setCardId(param.getCardId())
                            .setModelId(uploadModelId)
                            .setLoveCardTopicId(subject == null ? -1 : SafeConverter.toInt(subject.getComicTopicId()))
                            .setModelTopicId(uploadTopicId);
                    topicCheckInfos.add(checkInfo);
                }
            }

            log.debug("checkCardInfo doCheck end, params rareCheckInfos={}, topicCheckInfos={}", rareCheckInfos, topicCheckInfos);
            if (CollectionUtils.isNotEmpty(rareCheckInfos)) {
                DingDingMessageUtil.DingDingContent content = DingDingMessageUtil.builderContent().title("文件上传，检测到稀有度不一致\n");
                rareCheckInfos.forEach(e -> {
                    content.lineKV("内容",
                            "卡ID：" + e.getCardId() + "，卡真实稀有度：" + e.getLoveCardRare() + "，模版稀有度：" + e.getModelRare() + "，模版ID：" + e.getModelId());
                });
                content.sendMsg(CARD_BATTLE_ROBOT_SECRET, CARD_BATTLE_ROBOT_TOKEN);
            }
            if (CollectionUtils.isNotEmpty(topicCheckInfos)) {
                DingDingMessageUtil.DingDingContent content = DingDingMessageUtil.builderContent().title("文件上传，检测到专题不一致\n");
                topicCheckInfos.forEach(e -> {
                    content.lineKV("内容", "卡ID："
                            + e.getCardId()
                            + "，卡真实专题："
                            + e.getLoveCardTopicId()
                            + "，上传的专题："
                            + e.getModelTopicId()
                            + "，模版ID："
                            + e.getModelId());
                });
                content.sendMsg(CARD_BATTLE_ROBOT_SECRET, CARD_BATTLE_ROBOT_TOKEN);
            }
        } catch (Exception e) {
            log.error("checkCardInfo doCheck error", e);
        }
    }

    public void refreshCardInfo(String cardIds, Integer status) {
        int tableCount = TablePartition.CARD_BATTLE_USER_CARD.getTableCount();
        for (int i = 0; i < tableCount; i++) {
            String tableName = TablePartitionUtil.getTableName(TablePartitionEnum.CARD_BATTLE_USER_CARD, i);
            long startTime = System.currentTimeMillis();
            log.info("{}开始处理, startTime={}", tableName, startTime);
            String[] split = cardIds.split(",");
            for (String cardId : split) {
                long count = cardBattleUserCardMapper.refreshCardInfo(tableName, cardId, null, null, null, null, status);
                log.info("refreshCardInfo处理table: {}, cardId:{}, 修改行数:{}", tableName, cardId, count);
            }

            long cost = System.currentTimeMillis() - startTime;
            log.info("{}处理结束, 耗时:{}分钟", tableName, TimeUnit.MILLISECONDS.toMinutes(cost));
        }

    }

    public void initAllBattleCardRare(String tableName) {
        CompletableFuture.runAsync(() -> doInit(tableName));
    }

    private void doInit(String oTableName) {
        try {
            List<CardBattleCardIdModelConfig> cardIdModelConfigs = cardBattleCardIdModelConfigMapper.selectAll();
            int tableCount = Settings.getEnvironment().ge(Environment.PREVIEW) ? TablePartition.CARD_BATTLE_USER_CARD.getTableCount() : 2;
            for (CardBattleCardIdModelConfig cardIdModelConfig : cardIdModelConfigs) {
                String cardId = cardIdModelConfig.getCardId();
                CardBattleCardBasicInfo cardBasicInfo = cardBasicComponent.getCardBasicInfo(cardId);
                if (cardBasicInfo == null) {
                    log.info("initAllBattleCardRare cardBasicInfo is null, cardId:{}", cardId);
                    continue;
                }
                int rareCode = cardBasicInfo.getCardRarity();
                long startTime = System.currentTimeMillis();
                log.info("{}开始处理, rareCode={}, startTime={}", cardId, rareCode, startTime);
                if (StringUtils.isNotBlank(oTableName)) {
                    cardBattleUserCardMapper.refreshCardInfo(oTableName, cardId, rareCode, null, null, null, null);
                } else {
                    for (int i = 0; i < tableCount; i++) {
                        String tableName = TablePartitionUtil.getTableName(TablePartitionEnum.CARD_BATTLE_USER_CARD, i);
                        cardBattleUserCardMapper.refreshCardInfo(tableName, cardId, rareCode, null, null, null, null);
                    }
                }

                cardBattleCacheManager.setCardInfoCache(cardId, rareCode);
                long cost = System.currentTimeMillis() - startTime;
                log.info("{}处理结束, 耗时:{}分钟", cardId, TimeUnit.MILLISECONDS.toMinutes(cost));
            }
            log.info("initAllBattleCardRare end");
        } catch (Exception e) {
            log.error("initAllBattleCardRare error", e);
        }

    }

    public void checkCardRelation() {
        String cardRelation = marketingComponent.getCardRelation();
        if (StringUtils.isBlank(cardRelation)) {
            log.info("checkCardRelation cardRelation is null");
            return;
        }
        cardBattleCacheManager.setCardRelation(cardRelation);
        // 待补充的卡ID模版对应关系配置信息
        List<CardBattleCardIdModelConfig> insertCardModelConfigList = new ArrayList<>();
        String[] cardGroupArr = cardRelation.split(",");
        for (String cardGroup : cardGroupArr) {
            List<String> sameCardIdList = Arrays.stream(cardGroup.split(":")).map(String::trim).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(sameCardIdList)) {
                continue;
            }
            List<CardBattleCardBasicInfo> existCardInfoList = cardBattleCardIdModelConfigMapper.fetchCardInfoByCardIds(sameCardIdList);
            if (CollectionUtils.isEmpty(existCardInfoList)) {
                log.info("checkCardRelation existCardInfoList empty, sameCardIdList={},cardRelation={}", sameCardIdList, cardRelation);
                continue;
            }
            if (existCardInfoList.size() == sameCardIdList.size()) {
                log.info("checkCardRelation already exist, sameCardIdList={},cardRelation={}", sameCardIdList, cardRelation);
                continue;
            }
            List<String> existCardIdList = existCardInfoList.stream().map(CardBattleCardBasicInfo::getCardId).collect(Collectors.toList());
            List<String> notExistCardIdList = sameCardIdList.stream().filter(e -> !existCardIdList.contains(e)).collect(Collectors.toList());
            CardBattleCardBasicInfo basicInfo = existCardInfoList.get(0);
            List<CardBattleCardIdModelConfig> notExistCardList = notExistCardIdList.stream().map(e -> {
                CardBattleCardIdModelConfig modelConfig = new CardBattleCardIdModelConfig();
                modelConfig.setCardId(e);
                modelConfig.setCardType(2);
                modelConfig.setModelId(basicInfo.getModelId());
                modelConfig.setTopicId(basicInfo.getTopicId());
                modelConfig.setAttr(basicInfo.getAttr());
                modelConfig.setStatus(0);
                return modelConfig;
            }).collect(Collectors.toList());
            insertCardModelConfigList.addAll(notExistCardList);
        }

        log.info("checkCardRelation cardRelation={}, insertCardModelConfigList={}", cardRelation, insertCardModelConfigList);

        if (CollectionUtils.isNotEmpty(insertCardModelConfigList)) {
            int insertCount = cardBattleCardIdModelConfigMapper.batchInsert(insertCardModelConfigList);
            if (insertCount != insertCardModelConfigList.size()) {
                log.error("checkCardRelation insert exception, insertCount={}, insertCardModelConfigList={}", insertCount, insertCardModelConfigList);
            }
            // 刷新用户卡表
            updateUserCardModelBiz.updateUserBattleCards(null, insertCardModelConfigList, null);
        }
    }

    public void fixCardModel(String cardIds) {
        String[] split = cardIds.split(",");
        List<String> list = Arrays.asList(split);
        log.info("fixCardModel start, size={}, cardIds={}", list.size(), list);
        List<CardBattleCardBasicInfo> basicInfoList = cardBattleCardIdModelConfigMapper.fetchCardInfoByCardIds(list);
        List<CardBattleCardIdModelConfig> changeCardConfigs = new ArrayList<>();
        for (CardBattleCardBasicInfo basicInfo : basicInfoList) {
            CardBattleCardIdModelConfig modelConfig = new CardBattleCardIdModelConfig();
            modelConfig.setCardId(basicInfo.getCardId());
            modelConfig.setModelId(basicInfo.getModelId());
            modelConfig.setTopicId(basicInfo.getTopicId());
            modelConfig.setAttr(basicInfo.getAttr());
            modelConfig.setStatus(0);
            changeCardConfigs.add(modelConfig);
        }
        log.info("fixCardModel mid, size={}, cardIds={}", changeCardConfigs.size(), changeCardConfigs);
        updateUserCardModelBiz.updateUserBattleCards(null, null, changeCardConfigs);
    }
}
