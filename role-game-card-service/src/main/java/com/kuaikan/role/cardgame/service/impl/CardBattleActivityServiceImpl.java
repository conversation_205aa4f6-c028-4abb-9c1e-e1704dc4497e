package com.kuaikan.role.cardgame.service.impl;

import java.util.Collections;
import java.util.List;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.bean.RequestInfo;
import com.kuaikan.common.model.RpcResult;
import com.kuaikan.role.cardgame.biz.BattleActivityBiz;
import com.kuaikan.role.cardgame.biz.CardBattleActivityPointBiz;
import com.kuaikan.role.cardgame.biz.CardBattleBiz;
import com.kuaikan.role.cardgame.biz.CardBattleBondActivityBiz;
import com.kuaikan.role.cardgame.biz.CardBattleLimitedTimeActivityBiz;
import com.kuaikan.role.cardgame.biz.CardBattleLotteryActivityBiz;
import com.kuaikan.role.cardgame.biz.GameLevelBiz;
import com.kuaikan.role.cardgame.component.third.MarketingComponent;
import com.kuaikan.role.game.api.bean.cardbattle.PlotCard;
import com.kuaikan.role.game.api.enums.cardbattle.CardBattleAutoExploreTypeEnum;
import com.kuaikan.role.game.api.enums.cardbattle.CardBattleExploreTypeEnum;
import com.kuaikan.role.game.api.enums.cardbattle.CardBattleNewFlagType;
import com.kuaikan.role.game.api.enums.cardbattle.CardBattleTaskTypeEnum;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleActivityLotteryParam;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleExploreParam;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleLimitedTimeActivityPlayRuleDTO;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleLimitedTimeActivityPrizeDTO;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleLimitedTimeActivityReceivePrizeDTO;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleResourceAcquisitionDTO;
import com.kuaikan.role.game.api.rpc.result.cardbattle.ActivityLotteryPrizeDTO;
import com.kuaikan.role.game.api.rpc.result.cardbattle.ActivityLotteryStagePrizeDTO;
import com.kuaikan.role.game.api.rpc.result.cardbattle.CardBattleActivityDTO;
import com.kuaikan.role.game.api.rpc.result.cardbattle.CardBattleActivityListDTO;
import com.kuaikan.role.game.api.rpc.result.cardbattle.CardBattleActivityMainPageDTO;
import com.kuaikan.role.game.api.rpc.result.cardbattle.CardBattleActivityNewFlagDTO;
import com.kuaikan.role.game.api.rpc.result.cardbattle.CardBattleActivityPointDTO;
import com.kuaikan.role.game.api.rpc.result.cardbattle.CardBattlePrizeBuffRuleDTO;
import com.kuaikan.role.game.api.rpc.result.cardbattle.UserActivityLotteryPrizeInfo;
import com.kuaikan.role.game.api.service.cardbattle.CardBattleActivityService;

/**
 *
 * <AUTHOR>
 * @date 2024/10/29
 */
@Slf4j
@DubboService(version = "1.0", group = "role-game")
public class CardBattleActivityServiceImpl implements CardBattleActivityService {

    @Resource
    private CardBattleLimitedTimeActivityBiz limitedTimeActivityBiz;

    @Resource
    private BattleActivityBiz battleActivityBiz;

    @Resource
    private GameLevelBiz gameLevelBiz;

    @Resource
    private CardBattleBiz cardBattleBiz;

    @Resource
    private MarketingComponent marketingComponent;

    @Resource
    private CardBattleBondActivityBiz cardBattleBondActivityBiz;

    @Resource
    private CardBattleLotteryActivityBiz cardBattleLotteryActivityBiz;

    @Resource
    private CardBattleActivityPointBiz cardBattleActivityPointBiz;

    @Override
    public CardBattleActivityListDTO getActivityList(RequestInfo requestInfo, Integer activityType) {
        List<CardBattleActivityDTO> activityList;
        if (CardBattleExploreTypeEnum.LIMITED_TIME_EXPLORE.getCode() == activityType
                || CardBattleExploreTypeEnum.LIMITED_TIME_LOTTERY.getCode() == activityType) {
            activityList = battleActivityBiz.getActivityList(requestInfo, CardBattleExploreTypeEnum.LIMITED_TIME_LOTTERY.getCode());
            List<CardBattleActivityDTO> limitedActivityList = battleActivityBiz.getActivityList(requestInfo,
                    CardBattleExploreTypeEnum.LIMITED_TIME_EXPLORE.getCode());
            activityList.addAll(limitedActivityList);
        } else {
            activityList = battleActivityBiz.getActivityList(requestInfo, activityType);
        }
        return CardBattleActivityListDTO.init(ResponseCodeMsg.SUCCESS, activityList);
    }

    @Override
    public CardBattleLimitedTimeActivityPlayRuleDTO getActivityPlayRule(RequestInfo requestInfo, String activityId) {
        return limitedTimeActivityBiz.getActivityPlayRule(requestInfo, activityId);
    }

    @Override
    public CardBattleActivityMainPageDTO getActivityMainPage(RequestInfo requestInfo, CardBattleExploreParam param) {
        return battleActivityBiz.getActivityMainPage(requestInfo, param);
    }

    @Override
    public CardBattleLimitedTimeActivityPrizeDTO getActivityPrizeList(RequestInfo requestInfo, String activityId) {
        return limitedTimeActivityBiz.getActivityPrizeList(requestInfo, activityId);
    }

    @Override
    public CardBattleLimitedTimeActivityReceivePrizeDTO receivePrize(RequestInfo requestInfo, String activityId, Long prizeId) {
        return limitedTimeActivityBiz.receivePrize(requestInfo, activityId, prizeId);
    }

    @Override
    public CardBattleActivityNewFlagDTO getUserActivityNewFlag(RequestInfo requestInfo) {
        // new文案 优先级： 限时活动>羁绊之旅>幻境探索>心辉奇遇>战斗点数回满
        CardBattleActivityNewFlagDTO activityNewFlag = battleActivityBiz.getActivityNewFlag(requestInfo);
        if (activityNewFlag != null) {
            return activityNewFlag;
        }
        boolean battleVisitFlag = gameLevelBiz.getBattleVisitFlag(requestInfo, CardBattleTaskTypeEnum.PRACTICE.getCode());
        if (battleVisitFlag) {
            return new CardBattleActivityNewFlagDTO().setType(CardBattleNewFlagType.PRACTICE.getCode()).setContext("心辉奇遇已刷新！");
        }
        // 战斗点数
        boolean battlePointUpLimit = cardBattleBiz.ifUserBattlePointUpLimit(requestInfo.getUserId());
        if (battlePointUpLimit) {
            return new CardBattleActivityNewFlagDTO().setType(CardBattleNewFlagType.FULL_POINT.getCode()).setContext("战斗点数已回满！");
        }
        return new CardBattleActivityNewFlagDTO().setType(CardBattleNewFlagType.NONE.getCode()).setContext("");
    }

    @Override
    public CardBattleActivityNewFlagDTO getBondDungeonNewFlag(RequestInfo requestInfo) {
        return battleActivityBiz.getActivityNewFlagByType(requestInfo, CardBattleExploreTypeEnum.BOND.getCode());
    }

    @Override
    public CardBattleActivityNewFlagDTO getLimitActivityNewFlag(RequestInfo requestInfo) {
        return battleActivityBiz.getActivityNewFlagByType(requestInfo, CardBattleExploreTypeEnum.LIMITED_TIME_EXPLORE.getCode());
    }

    @Override
    public List<PlotCard> fetchLimitActivityCardPoolCardList(String activitySign) {
        if (StringUtils.isBlank(activitySign)) {
            return Collections.emptyList();
        }
        return marketingComponent.getMixedPlotCardList(activitySign);
    }

    @Override
    public CardBattlePrizeBuffRuleDTO getActivityPrizeBuffRule(RequestInfo requestInfo, String activityId) {
        return cardBattleBondActivityBiz.getActivityPrizeBuffRule(requestInfo, activityId);
    }

    @Override
    public ResponseCodeMsg setAutoExploreStatus(RequestInfo requestInfo, String activityId, Integer status) {
        // 参数校验
        if (StringUtils.isBlank(activityId) || CardBattleAutoExploreTypeEnum.getByCode(status) == null) {
            return ResponseCodeMsg.INVALID_PARAM;
        }
        return battleActivityBiz.setAutoExploreType(requestInfo.getUserId(), activityId, status);
    }

    @Override
    public ActivityLotteryPrizeDTO getActivityLotteryPrize(RequestInfo requestInfo, String activityId) {
        if (StringUtils.isBlank(activityId) || requestInfo.getUserId() <= 0) {
            return new ActivityLotteryPrizeDTO().setCodeMsg(ResponseCodeMsg.INVALID_PARAM);
        }
        return cardBattleLotteryActivityBiz.getUserActivityLotteryPrize(requestInfo, activityId);
    }

    @Override
    public RpcResult<UserActivityLotteryPrizeInfo> getLotteryActivityRemainingPrize(RequestInfo requestInfo, String activityId) {
        return cardBattleLotteryActivityBiz.getLotteryActivityRemainingPrize(requestInfo, activityId);
    }

    @Override
    public ActivityLotteryPrizeDTO getActivityLotteryPrizeRule(String activityId) {
        if (StringUtils.isBlank(activityId)) {
            return new ActivityLotteryPrizeDTO().setCodeMsg(ResponseCodeMsg.INVALID_PARAM);
        }
        return cardBattleLotteryActivityBiz.getActivityLotteryPrizeRule(activityId);
    }

    @Override
    public CardBattleResourceAcquisitionDTO getLotteryCoinAcquisition(RequestInfo requestInfo, String activityId) {
        return cardBattleLotteryActivityBiz.getCoinAcquisitionInfo(requestInfo, activityId);
    }

    @Override
    public CardBattleLimitedTimeActivityReceivePrizeDTO doActivityLottery(RequestInfo requestInfo, CardBattleActivityLotteryParam param) {
        return cardBattleLotteryActivityBiz.activityLottery(requestInfo, param);
    }

    @Override
    public CardBattleActivityPointDTO getActivityCostPointInfo(RequestInfo requestInfo, String activityId) {
        return cardBattleActivityPointBiz.getActivityCostPointInfo(requestInfo, activityId);
    }

    @Override
    public CardBattleResourceAcquisitionDTO getActivityPointPopUpWindow(RequestInfo requestInfo, String activityId) {
        return cardBattleActivityPointBiz.getActivityPointPopUpWindow(requestInfo, activityId);
    }
}
