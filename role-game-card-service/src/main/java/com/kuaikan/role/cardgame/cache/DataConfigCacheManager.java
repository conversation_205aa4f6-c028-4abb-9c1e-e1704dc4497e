package com.kuaikan.role.cardgame.cache;

import static com.kuaikan.role.game.api.constant.CardBattleCacheKeyConfig.BATTLE_CARD_ACTIVITY_POSSIBLE_PRIZE_LIST;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.kuaikan.common.tools.serialize.JsonUtils;
import com.kuaikan.role.cardgame.mapper.CardBattleCardModelConfigMapper;
import com.kuaikan.role.cardgame.mapper.CardBattleCardRareConfigMapper;
import com.kuaikan.role.cardgame.mapper.CardBattleCommonConfigMapper;
import com.kuaikan.role.cardgame.mapper.CardBattleLevelGrowthConfigMapper;
import com.kuaikan.role.cardgame.mapper.CardBattleModelLevelAttrMapper;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleCardLevelSpendConfig;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleCardModelConfig;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleCardRareConfig;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleCommonConfig;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleLevelGrowthConfig;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleModelLevelAttr;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleMonsterLevelGrowthConfig;
import com.kuaikan.role.game.api.constant.CacheKeyConstant;
import com.kuaikan.role.game.api.constant.CardBattleCacheKeyConfig;
import com.kuaikan.role.game.api.enums.cardbattle.CardBattleCardRarityEnum;
import com.kuaikan.role.game.api.rpc.result.cardbattle.CardBattleActivityMainPageDTO;

/**
 *配置缓存管理
 *
 *<AUTHOR>
 *@date 2024/8/30
 */
@Slf4j
@Component
public class DataConfigCacheManager extends AbstractCacheManager {

    @Resource
    private CardBattleCardModelConfigMapper cardBattleCardModelConfigMapper;

    @Resource
    private CardBattleLevelGrowthConfigMapper cardBattleLevelGrowthConfigMapper;

    @Resource
    private CardBattleCommonConfigMapper cardBattleCommonConfigMapper;

    @Resource
    private CardBattleCardRareConfigMapper cardBattleCardRareConfigMapper;

    @Resource
    private CardBattleModelLevelAttrMapper cardBattleModelLevelAttrMapper;

    public void deleteCommonConfigCache() {
        currentRedis().del(CacheKeyConstant.BATTLE_CARD_COMMON_CONFIG);
    }

    public void deleteCardIdConfigCache(Collection<String> cardIds) {
        List<String> keys = cardIds.stream()
                .map(cardId -> String.format(CardBattleCacheKeyConfig.BATTLE_CARD_ID_MODEL_CONFIG.getKeyPattern(), cardId))
                .collect(Collectors.toList());
        currentRedis().del(keys.toArray(new String[0]));
    }

    public void deleteCardModelConfigCache(Collection<Integer> modelIds) {
        List<String> keys = modelIds.stream().map(cardId -> String.format(CacheKeyConstant.BATTLE_CARD_MODEL_CONFIG, cardId)).collect(Collectors.toList());
        keys.add(CacheKeyConstant.BATTLE_CARD_ALL_MODEL_CONFIG);
        currentRedis().del(keys.toArray(new String[0]));
    }

    public void deleteCardLevelGrowthCache(Collection<Integer> levels) {
        List<String> keys = levels.stream().map(level -> String.format(CacheKeyConstant.BATTLE_CARD_LEVEL_GROWTH_CONFIG, level)).collect(Collectors.toList());
        keys.add(CacheKeyConstant.BATTLE_CARD_ALL_LEVEL_GROWTH_CONFIG);
        currentRedis().del(keys.toArray(new String[0]));
    }

    public void deleteMonsterLevelGrowthCache(Collection<CardBattleMonsterLevelGrowthConfig> configs) {
        List<String> keys = new ArrayList<>();
        for (CardBattleMonsterLevelGrowthConfig config : configs) {
            keys.add(String.format(CacheKeyConstant.BATTLE_CARD_MONSTER_LEVEL_GROWTH_CONFIG, config.getType(), config.getLevel()));
        }
        currentRedis().del(keys.toArray(new String[0]));
    }

    public void deleteCardPoolCache(Collection<Integer> poolIds) {
        List<String> keys = poolIds.stream().map(poolId -> String.format(CacheKeyConstant.BATTLE_CARD_CARD_POOL_CONFIG, poolId)).collect(Collectors.toList());
        currentRedis().del(keys.toArray(new String[0]));
    }

    public void deleteRareConfigCache() {
        List<String> keys = new ArrayList<>();
        for (CardBattleCardRarityEnum rarityEnum : CardBattleCardRarityEnum.values()) {
            keys.add(String.format(CacheKeyConstant.BATTLE_CARD_RARE_CONFIG_BY_RARE, rarityEnum.getCode()));
        }
        currentRedis().del(keys.toArray(new String[0]));
    }

    public void deleteCardLevelSpendCache(Collection<CardBattleCardLevelSpendConfig> configs) {
        List<String> keys = new ArrayList<>();
        for (CardBattleCardLevelSpendConfig config : configs) {
            keys.add(String.format(CacheKeyConstant.BATTLE_CARD_LEVEL_SPEND_CONFIG, config.getRareCode(), config.getLevel()));
        }
        currentRedis().del(keys.toArray(new String[0]));

        List<String> rareListKeys = new ArrayList<>();
        for (CardBattleCardRarityEnum cardRarityEnum : CardBattleCardRarityEnum.values()) {
            rareListKeys.add(String.format(CacheKeyConstant.BATTLE_CARD_LEVEL_SPEND_RARE_LIST, cardRarityEnum.getCode()));
        }
        currentRedis().del(rareListKeys.toArray(new String[0]));
    }

    public void deleteActivityProbPrizeCache(String activityId) {
        String key = String.format(BATTLE_CARD_ACTIVITY_POSSIBLE_PRIZE_LIST.getKeyPattern(), activityId);
        currentRedis().del(key);
    }

    public void uploadModelLevelAttr() {
        List<CardBattleCardModelConfig> cardModelConfigs = cardBattleCardModelConfigMapper.selectAll();
        List<CardBattleLevelGrowthConfig> levelGrowthConfigs = cardBattleLevelGrowthConfigMapper.selectAll();
        if (CollectionUtils.isEmpty(cardModelConfigs) || CollectionUtils.isEmpty(levelGrowthConfigs)) {
            log.error("uploadModelLevelAttr error, model config not found");
        }
        CardBattleCommonConfig commonConfig = cardBattleCommonConfigMapper.findFirst();
        BigDecimal attrRatio = Optional.ofNullable(commonConfig).map(CardBattleCommonConfig::getAttrRatio).orElse(BigDecimal.valueOf(1.3));
        List<CardBattleCardRareConfig> rareConfigs = cardBattleCardRareConfigMapper.selectAll();
        Map<Integer, CardBattleCardRareConfig> rareConfigMap = rareConfigs.stream()
                .collect(Collectors.toMap(CardBattleCardRareConfig::getRareCode, Function.identity()));
        Map<Integer, CardBattleLevelGrowthConfig> levelConfigMap = levelGrowthConfigs.stream()
                .collect(Collectors.toMap(CardBattleLevelGrowthConfig::getLevel, Function.identity()));
        cardModelConfigs.forEach(cardModelConfig -> {
            List<CardBattleModelLevelAttr> record = new ArrayList<>();
            levelGrowthConfigs.stream().forEach(levelGrowthConfig -> {
                int rareCode = cardModelConfig.getRareCode();
                CardBattleCardRareConfig rareConfig = rareConfigMap.get(rareCode);
                int maxLevel = rareConfig.getInitLevelLimit() + rareConfig.getUpgradeCount() * 10;
                CardBattleLevelGrowthConfig growthConfig = levelConfigMap.get(maxLevel);
                BigDecimal maxLevelRatio = growthConfig.getGrowthRate(rareCode);
                int minBreakNum = getMinBreakNum(levelGrowthConfig.getLevel(), rareConfig);
                for (int breakNum = minBreakNum; breakNum <= rareConfig.getUpgradeCount(); breakNum++) {
                    // 如果该等级的最小突破次数大于breakNum，跳过
                    CardBattleModelLevelAttr modelLevelAttr = calcLevelAttr(cardModelConfig, levelGrowthConfig, rareCode, breakNum, maxLevelRatio, rareConfig,
                            attrRatio);
                    log.debug("uploadModelLevelAttr cardModelConfig={}, levelGrowthConfig={}, rareConfig={}", cardModelConfig, levelGrowthConfig, rareConfig);
                    if (modelLevelAttr != null) {
                        record.add(modelLevelAttr);
                        // 删除单条记录缓存
                        String concatKey = cardModelConfig.getModelId() + "_" + levelGrowthConfig.getLevel() + "_" + breakNum;
                        String key = String.format(CardBattleCacheKeyConfig.BATTLE_CARD_MODEL_LEVEL_ATTR_CONFIG.getKeyPattern(), concatKey);
                        currentRedis().del(key);
                    }
                }
            });
            cardBattleModelLevelAttrMapper.batchInsert(record);
            // 删除旧model list缓存
            String modelListKey = String.format(CardBattleCacheKeyConfig.BATTLE_CARD_MODEL_LEVEL_ATTR_MODEL_LIST.getKeyPattern(), cardModelConfig.getModelId());
            currentRedis().del(modelListKey);
        });
    }

    private int getMinBreakNum(int level, CardBattleCardRareConfig rareConfig) {
        for (int i = 0; i <= rareConfig.getUpgradeCount(); i++) {
            if (level <= rareConfig.getInitLevelLimit() + i * 10) {
                return i;
            }
        }
        return rareConfig.getUpgradeCount();
    }

    private CardBattleModelLevelAttr calcLevelAttr(CardBattleCardModelConfig cardModelConfig, CardBattleLevelGrowthConfig levelGrowthConfig, int rareCode,
                                                   int breakNum, BigDecimal maxLevelRatio, CardBattleCardRareConfig rareConfig, BigDecimal attrRatio) {
        BigDecimal growthRate = levelGrowthConfig.getGrowthRate(rareCode);

        if (growthRate.compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }
        List<BigDecimal> upgradeRatioList = rareConfig.getUpgradeRatio();
        BigDecimal upgradeRatio = BigDecimal.ZERO;
        if (breakNum >= 1) {
            upgradeRatio = upgradeRatioList.get(breakNum >= upgradeRatioList.size() ? upgradeRatioList.size() - 1 : breakNum - 1).multiply(maxLevelRatio);
        }

        CardBattleModelLevelAttr modelLevelAttr = new CardBattleModelLevelAttr();
        modelLevelAttr.setModelId(cardModelConfig.getModelId());
        modelLevelAttr.setLevel(levelGrowthConfig.getLevel());
        modelLevelAttr.setBreakNum(breakNum);
        modelLevelAttr.setAtk(growthRate.add(upgradeRatio).multiply(BigDecimal.valueOf(cardModelConfig.getAtk())));
        modelLevelAttr.setHp(growthRate.add(upgradeRatio).multiply(BigDecimal.valueOf(cardModelConfig.getHp())));
        modelLevelAttr.setCost(cardModelConfig.getCost());
        modelLevelAttr.setBaseBattlePower(BigDecimal.valueOf(Math.pow(modelLevelAttr.getAtk().multiply(modelLevelAttr.getHp()).doubleValue(), 0.55))
                .divide(BigDecimal.valueOf(0.6), 2, RoundingMode.HALF_UP));
        BigDecimal atkRatio = BigDecimal.valueOf(1).add(attrRatio);

        modelLevelAttr.setWeakBattlePower(
                BigDecimal.valueOf(Math.pow(modelLevelAttr.getAtk().multiply(atkRatio).multiply(modelLevelAttr.getHp()).doubleValue(), 0.55))
                        .divide(BigDecimal.valueOf(0.6), 2, RoundingMode.HALF_UP));
        return modelLevelAttr;
    }

    public boolean addAntiBrushFlag(int componentType, long activityId) {
        try {
            String key = String.format(com.kuaikan.game.gamecard.activity.def.constants.CacheKeyConstant.ACTIVITY_ANTI_BRUSH, componentType, activityId);
            currentRedis().set(key, "1");
        } catch (Exception e) {
            log.error("addAntiBrushFlag fail,componentType:{},activityId:{}", componentType, activityId, e);
            return false;
        }
        return true;
    }

    public void refreshTopicWaitFreeBattleTask(Integer topicId) {
        String key = String.format(CardBattleCacheKeyConfig.CARD_BATTLE_TOPIC_WAIT_FREE_BATTLE.getKeyPattern(), topicId);
        currentRedis().del(key);
    }

    public void saveLimitedTimeActivityPrizeList(String battleExploreId, List<CardBattleActivityMainPageDTO.CollectProgressDTO> prizeList) {
        String key = String.format(CardBattleCacheKeyConfig.CARD_BATTLE_LIMITED_TIME_ACTIVITY_PRIZE.getKeyPattern(), battleExploreId);
        currentTendis().set(key, JsonUtils.toJson(prizeList));
    }

    public List<CardBattleActivityMainPageDTO.CollectProgressDTO> getLimitedTimeActivityPrizeList(String battleExploreId) {
        String key = String.format(CardBattleCacheKeyConfig.CARD_BATTLE_LIMITED_TIME_ACTIVITY_PRIZE.getKeyPattern(), battleExploreId);
        String val = currentTendis().get(key);
        return Optional.ofNullable(JsonUtils.findList(val, CardBattleActivityMainPageDTO.CollectProgressDTO.class)).orElse(new ArrayList<>());
    }

    public void delLimitedTimeActivityPrizeList(String battleExploreId) {
        String key = String.format(CardBattleCacheKeyConfig.CARD_BATTLE_LIMITED_TIME_ACTIVITY_PRIZE.getKeyPattern(), battleExploreId);
        currentTendis().del(key);
    }

    public void saveLimitedTimeActivityUserPrizeList(int userId, String battleExploreId, List<CardBattleActivityMainPageDTO.CollectProgressDTO> prizeList) {
        String key = String.format(CardBattleCacheKeyConfig.CARD_BATTLE_LIMITED_TIME_ACTIVITY_USER_PRIZE_CACHE.getKeyPattern(), battleExploreId, userId);
        currentTendis().set(key, JsonUtils.toJson(prizeList));
    }

    public List<CardBattleActivityMainPageDTO.CollectProgressDTO> getLimitedTimeActivityUserPrizeList(int userId, String battleExploreId) {
        String key = String.format(CardBattleCacheKeyConfig.CARD_BATTLE_LIMITED_TIME_ACTIVITY_USER_PRIZE_CACHE.getKeyPattern(), battleExploreId, userId);
        String val = currentTendis().get(key);
        return Optional.ofNullable(JsonUtils.findList(val, CardBattleActivityMainPageDTO.CollectProgressDTO.class)).orElse(Collections.emptyList());
    }

    public void delLimitedTimeActivityUserPrizeList(String battleExploreId) {
        String key = String.format(CardBattleCacheKeyConfig.CARD_BATTLE_LIMITED_TIME_ACTIVITY_USER_PRIZE_CACHE.getKeyPattern(), battleExploreId);
        currentTendis().del(key);
    }

}
