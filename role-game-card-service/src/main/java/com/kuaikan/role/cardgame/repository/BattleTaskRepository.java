package com.kuaikan.role.cardgame.repository;

import java.util.List;
import java.util.Set;
import javax.annotation.Resource;

import org.bson.types.ObjectId;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import com.kuaikan.comic.common.utils.CommonLettuceClusterUtil;
import com.kuaikan.role.cardgame.config.ThreadPools;
import com.kuaikan.role.game.api.bean.cardbattle.BattleTask;
import com.kuaikan.role.game.api.constant.CardBattleCacheKeyConfig;
import com.kuaikan.role.game.api.enums.cardbattle.CardBattleTaskTypeEnum;

/**
 * 单独的战斗关卡配置 Repository
 *
 *<AUTHOR>
 *@date 2024/8/29
 */
@Repository
public class BattleTaskRepository {

    @Resource
    private MongoOperations mongoTemplate;

    public BattleTask getBattleTaskById(String id) {
        if (!ObjectId.isValid(id)) {
            return null;
        }
        Query query = new Query(Criteria.where("_id").is(new ObjectId(id)).and("status").is(1));
        return mongoTemplate.findOne(query, BattleTask.class);
    }

    public List<BattleTask> getWaitFreeBattleTaskByTopicId(Integer topicId) {
        return CommonLettuceClusterUtil.getList(topicId, CardBattleCacheKeyConfig.CARD_BATTLE_TOPIC_WAIT_FREE_BATTLE, BattleTask.class,
                this::getWaitFreeBattleTaskByDb, null, ThreadPools.REDIS_POOL);
    }

    public BattleTask getFirstPracticeBattleTask() {
        long now = System.currentTimeMillis();
        Query query = new Query();
        Criteria criteria = Criteria.where("battleType")
                .is(CardBattleTaskTypeEnum.PRACTICE.getCode())
                .and("startTime")
                .lt(now)
                .and("endTime")
                .gte(now)
                .and("status")
                .is(1);
        query.addCriteria(criteria);
        query.with(new Sort(Sort.Direction.DESC, "createAt"));
        return mongoTemplate.findOne(query, BattleTask.class);
    }

    public List<BattleTask> getAllValidPracticeBattleTask() {
        long now = System.currentTimeMillis();
        Query query = new Query();
        Criteria criteria = Criteria.where("battleType")
                .is(CardBattleTaskTypeEnum.PRACTICE.getCode())
                .and("startTime")
                .lt(now)
                .and("endTime")
                .gte(now)
                .and("status")
                .is(1);
        query.addCriteria(criteria);
        query.with(new Sort(Sort.Direction.DESC, "createAt"));
        return mongoTemplate.find(query, BattleTask.class);
    }

    public List<BattleTask> getWaitFreeBattleTaskByDb(Integer topicId) {
        Query query = new Query();
        Criteria criteria = Criteria.where("battleType")
                .is(CardBattleTaskTypeEnum.WAIT_FREE.getCode())
                .and("waitFreeTopicIds")
                .elemMatch(Criteria.where("$eq").is(topicId))
                .and("status")
                .is(1);
        query.addCriteria(criteria);
        query.with(new Sort(Sort.Direction.DESC, "classifyId"));
        return mongoTemplate.find(query, BattleTask.class);
    }

    public List<BattleTask> getAllValidBattleTask() {
        return CommonLettuceClusterUtil.getList(CardBattleCacheKeyConfig.CARD_BATTLE_ALL_VALID_TASKS, BattleTask.class, this::getAllValidBattleTaskByDb,
                ThreadPools.REDIS_POOL);
    }

    public List<BattleTask> getAllValidBattleTaskByDb() {
        Query query = new Query(Criteria.where("status").is(1));
        return mongoTemplate.find(query, BattleTask.class);
    }

    public List<BattleTask> listByIds(Set<String> battleTaskIdSet) {
        Query query = new Query(Criteria.where("status").is(1).and("_id").in(battleTaskIdSet));
        return mongoTemplate.find(query, BattleTask.class);
    }
}
