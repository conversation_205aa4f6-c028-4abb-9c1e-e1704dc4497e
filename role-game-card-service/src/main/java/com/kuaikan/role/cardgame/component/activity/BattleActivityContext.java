package com.kuaikan.role.cardgame.component.activity;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.bean.RequestInfo;
import com.kuaikan.role.game.api.bean.cardbattle.BattleActivityConfig;
import com.kuaikan.role.game.api.bean.cardbattle.UserBattleActivityRecord;
import com.kuaikan.role.game.api.rpc.result.cardbattle.CardBattleRoleGroupInfoDTO;

/**
 *<AUTHOR>
 *@date 2024/10/25
 */
@Data
@Accessors(chain = true)
public class BattleActivityContext implements Serializable {

    private static final long serialVersionUID = -8701878293842460579L;
    private ResponseCodeMsg codeMsg;

    // 用户信息
    private RequestInfo requestInfo;

    // 活动id
    private String activityId;

    // 活动id配置
    private BattleActivityConfig activityConfig;

    // 用户活动参与记录
    private UserBattleActivityRecord userActivityRecord;

    // 活动avg列表（聚合所有副本的）
    private List<DungeonAvgItemConfig> dungeonAvgItemList;
    // 活动照片列表（聚合所有副本的）
    private List<DungeonPhotoItemConfig> dungeonPhotoItemList;

    // 用户当前角色组信息（仅羁绊之旅活动）
    private CardBattleRoleGroupInfoDTO roleGroupInfo;

    @Data
    public static class DungeonAvgItemConfig implements Serializable {

        private static final long serialVersionUID = 3418491759508299196L;
        // 副本序号
        private int index;
        // 副本id
        private String dungeonId;

        private String dungeonTitle;

        private Integer unlockLevel;

        List<AvgItemConfig> avgItemList;

    }

    @Data
    public static class AvgItemConfig implements Serializable {

        private static final long serialVersionUID = -7918189733251099146L;
        // 剧情id
        private Integer storyId;
        // avg id
        private Integer avgId;
        private String avgIcon;
        // 类型，0：关卡前，1：关卡后
        private int type;
        // 关卡数
        private int level;

        // 解锁状态
        private boolean unlock;
    }

    @Data
    public static class DungeonPhotoItemConfig implements Serializable {

        private static final long serialVersionUID = 2423774676046757135L;
        // 副本id
        private String dungeonId;
        // 照片id
        private Integer storyId;
        // 照片名称
        private String name;
        // 解锁状态
        private boolean unlock;
        // 解锁时会有图片信息
        private String image;
        private Long acquireTime;
    }
}
