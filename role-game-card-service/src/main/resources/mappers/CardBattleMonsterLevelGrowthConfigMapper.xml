<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
  namespace="com.kuaikan.role.cardgame.mapper.CardBattleMonsterLevelGrowthConfigMapper">

  <resultMap id="BaseResultMap"
    type="com.kuaikan.role.game.api.bean.cardbattle.CardBattleMonsterLevelGrowthConfig">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="type" jdbcType="INTEGER" property="type"/>
    <result column="level" jdbcType="INTEGER" property="level"/>
    <result column="hp_ratio" jdbcType="DECIMAL" property="hpRatio"/>
    <result column="atk_ratio" jdbcType="DECIMAL" property="atkRatio"/>
  </resultMap>

  <sql id="Base_Column_List">
    id
    ,
    `type`, level, hp_ratio, atk_ratio
  </sql>

  <select id="selectByTypeAndLevel" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM card_battle_monster_level_growth_config
    WHERE type = #{type,jdbcType=INTEGER}
    AND level = #{level,jdbcType=INTEGER}
  </select>

  <select id="selectMaxLevelConfigByType" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM card_battle_monster_level_growth_config
    WHERE type = #{type,jdbcType=INTEGER}
    ORDER BY level DESC
    LIMIT 1
  </select>

</mapper>