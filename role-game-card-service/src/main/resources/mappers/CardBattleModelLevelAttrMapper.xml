<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.kuaikan.role.cardgame.mapper.CardBattleModelLevelAttrMapper">
    <resultMap id="BaseResultMap"
               type="com.kuaikan.role.game.api.bean.cardbattle.CardBattleModelLevelAttr">
        <id column="id" property="id"/>
        <result column="model_id" property="modelId"/>
        <result column="level" property="level"/>
        <result column="break_num" property="breakNum"/>
        <result column="atk" property="atk"/>
        <result column="hp" property="hp"/>
        <result column="cost" property="cost"/>
    </resultMap>


    <select id="findAll"
            resultType="com.kuaikan.role.game.api.bean.cardbattle.CardBattleModelLevelAttr">
        SELECT *
        FROM card_battle_model_level_attr
    </select>

    <!-- Add more SQL statements as needed -->

    <select id="findByUnionId"
            resultType="com.kuaikan.role.game.api.bean.cardbattle.CardBattleModelLevelAttr">
        SELECT *
        FROM card_battle_model_level_attr
        WHERE model_id = #{modelId}
          AND level = #{level}
          AND break_num = #{breakNum}
    </select>


    <select id="findByModelId" resultMap="BaseResultMap">
        SELECT *
        FROM card_battle_model_level_attr
        WHERE model_id = #{modelId}
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO card_battle_model_level_attr (model_id, level, break_num, cost, atk, hp,
        base_battle_power, weak_battle_power)
        VALUES
        <foreach collection="list" item="record" separator=",">
            (#{record.modelId,jdbcType=INTEGER},
            #{record.level,jdbcType=INTEGER},#{record.breakNum,jdbcType=INTEGER},
            #{record.cost,jdbcType=INTEGER}, #{record.atk,jdbcType=DECIMAL},
            #{record.hp,jdbcType=DECIMAL}, #{record.baseBattlePower,jdbcType=DECIMAL},
            #{record.weakBattlePower,jdbcType=DECIMAL})
        </foreach>
        ON DUPLICATE KEY UPDATE cost = VALUES(cost), atk = VALUES(atk), hp = VALUES(hp),
        base_battle_power = VALUES(base_battle_power), weak_battle_power = VALUES(weak_battle_power)
    </insert>

</mapper>