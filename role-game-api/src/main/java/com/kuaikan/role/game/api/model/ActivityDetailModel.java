package com.kuaikan.role.game.api.model;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.bean.Costume;
import com.kuaikan.role.game.api.bean.CostumeBlindBoxActivity;
import com.kuaikan.role.game.api.bo.ImageInfo;

@Data
@Accessors(chain = true)
public class ActivityDetailModel implements Serializable {

    private static final long serialVersionUID = -4076566139122053890L;

    private CostumeBlindBoxActivity activity;

    private List<SimpleRoleCostume> costumePreview;

    private PrizeInfo nextReward;

    private int usedFreeCount;

    private boolean showRedDot;

    private List<BlindBoxModel.Button> buttons;

    private Map<Integer, Costume> costumeMap;

    @Data
    @Accessors(chain = true)
    public static class SimpleRoleCostume implements Serializable {

        private static final long serialVersionUID = -6528293745477387630L;

        private int roleId;

        private List<SimpleCostume> costumes;
    }

    @Data
    @Accessors(chain = true)
    public static class SimpleCostume implements Serializable {

        private static final long serialVersionUID = -265786540613610349L;

        private int costumeId;

        private ImageInfo imageInfo;
    }

    @Data
    @Accessors(chain = true)
    public static class PrizeInfo implements Serializable {

        private static final long serialVersionUID = -2997825132779741660L;

        private String desc;

        private ImageInfo image;

    }

}
