package com.kuaikan.role.game.api.service.cardbattle;

import java.util.List;

import org.apache.commons.lang3.tuple.Pair;

import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.bean.RequestInfo;
import com.kuaikan.common.model.RpcResult;
import com.kuaikan.role.game.api.model.CompleteScheduleModel;
import com.kuaikan.role.game.api.rpc.param.cardbattle.BattleSelectRoleParam;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleBreakCardParam;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleCardTopicListDTO;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleEntryInfoDTO;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleMainPageDto;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattlePrepareFightDTO;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattlePrizeContextParam;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattlePrizeDTO;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattlePropSynthesisParam;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleResourceAcquisitionDTO;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleResourceDTO;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleRoleListDTO;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleRoleMaterialPrizeListDTO;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleSelectedCardResultDTO;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleTaskDTO;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleUpCardParam;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleUserAwardsDTO;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleUserCardDetailDTO;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleUserCardListDTO;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleUserCardQueryParam;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleUserInfoDTO;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleUserPropDTO;
import com.kuaikan.role.game.api.rpc.param.cardbattle.ReportBattleResultDTO;
import com.kuaikan.role.game.api.rpc.param.cardbattle.SmallTrumpetData;
import com.kuaikan.role.game.api.rpc.result.cardbattle.CardBattleBondPointDTO;
import com.kuaikan.role.game.api.rpc.result.cardbattle.CardBattleHeartDTO;

/**
 * 卡牌战斗service
 *
 * <AUTHOR>
 * @date 2024/4/28
 */
public interface CardBattleService {

    /**
     * 获取卡牌战斗静态资源
     *
     * @param battleActivityId 探索活动id
     * @return
     */
    CardBattleResourceDTO getResource(String battleActivityId);

    /**
     * 获取卡牌战斗主页信息
     *
     * @param requestInfo 用户信息
     * @return
     */
    CardBattleMainPageDto getMainPageNew(RequestInfo requestInfo);

    CardBattleMainPageDto getMainPageNew(RequestInfo requestInfo, boolean guide);

    /**
     * 获取卡牌战斗用户信息
     *
     * @param requestInfo 用户信息
     * @return
     */
    CardBattleUserInfoDTO getCardBattleUserInfo(RequestInfo requestInfo);

    Integer getUserCardBattlePoint(long userId);

    /**
     * 获取卡牌战斗点数获取弹窗
     *
     * @param userId 用户信息
     * @return
     */
    CardBattleResourceAcquisitionDTO getPointAcquisition(int userId);

    /**
     * 获取卡牌战斗银币获取弹窗
     *
     * @param userId 用户信息
     * @return
     */
    CardBattleResourceAcquisitionDTO getSilverCoinAcquisition(int userId);

    /**
     * 获取卡牌战斗入口信息
     *
     * @param userId       用户信息
     * @param battleActivityId 探索活动id
     * @return
     */
    CardBattleEntryInfoDTO entryInfo(int userId, String battleActivityId);

    /**
     * 获取角色化养成列表
     *
     * @param userId 用户信息
     * @return
     */
    CardBattleRoleListDTO getRoleList(int userId);

    /**
     * 获取角色化养成列表(新)
     *
     * @param param 请求信息
     * @return
     */
    CardBattleRoleListDTO getRoleListNew(BattleSelectRoleParam param);

    /**
     * 选择角色化id
     *
     * @param userId 用户信息
     * @param roleId 角色id
     * @return
     */
    ResponseCodeMsg updateSelectedRole(int userId, Integer roleId);

    /**
     * 选择角色化id（新）
     *
     * @param param 请求参数
     * @return
     */
    ResponseCodeMsg updateSelectedRoleNew(BattleSelectRoleParam param);

    /**
     * 获取卡牌-战斗信息展示状态
     *
     * @param userId 用户信息
     * @return
     */
    Integer getBattleInfoDisplayStatus(int userId);

    /**
     * 更新卡牌-战斗信息展示状态
     *
     * @param userId 用户信息
     * @param status     状态: 0，开启，1，关闭
     * @return
     */
    ResponseCodeMsg updateDisplayStatus(int userId, Integer status);

    /**
     * 卡牌战斗-探索
     *
     * @param requestInfo       用户信息
     * @param battleActivityId 探索活动id
     * @return
     */
    CardBattleTaskDTO explore(RequestInfo requestInfo, String battleActivityId);

    /**
     * 卡牌战斗-探索奖励
     *
     * @param userId       用户信息
     * @param battleActivityId 探索活动id
     * @return
     */
    CardBattleUserAwardsDTO exploreAwards(int userId, String battleActivityId);

    /**
     * 卡牌战斗-领取探索奖励 （奖励掉落模式）
     *
     * @param requestInfo       用户信息
     * @param param 领奖入参
     * @return
     */
    List<CardBattlePrizeDTO> obtainExploreAwards(RequestInfo requestInfo, CardBattlePrizeContextParam param);

    /**
     * 卡牌战斗-领取奖励
     *
     * @param requestInfo 用户信息
     * @param param      领奖入参
     * @return
     */
    CardBattlePrizeDTO obtainAward(RequestInfo requestInfo, CardBattlePrizeContextParam param);

    /**
     * 卡牌战斗-领取探索通关奖励
     *
     * @param requestInfo       用户信息
     * @param battleActivityId 探索活动id
     * @return
     */
    List<CardBattlePrizeDTO> obtainCompletedAwards(RequestInfo requestInfo, String battleActivityId);

    /**
     * 判断是否有未领取奖励
     *
     * @param userId       用户信息
     * @param battleActivityId 探索活动id
     * @return
     */
    Boolean hasUnclaimedAwards(int userId, String battleActivityId);

    /**
     * 快速上阵卡牌
     *
     * @param userId       用户信息
     * @param userBattleTaskId 战斗任务id
     * @return CardBattleUserCardListDTO
     */
    CardBattleUserCardListDTO smartSelectCards(int userId, String userBattleTaskId);

    /**
     * 获取卡牌列表
     *
     * @param userId 用户信息
     * @param queryParam 查询参数
     * @return CardBattleUserCardListDTO
     */
    CardBattleUserCardListDTO getCardListV2(int userId, CardBattleUserCardQueryParam queryParam);

    /**
     * 获取背包材料
     *
     * @param userId 用户信息
     * @return CardBattleRoleMaterialPrizeListDTO
     */
    CardBattleRoleMaterialPrizeListDTO getRoleMaterialPrize(int userId);

    /**
     * 卡牌出售
     *
     * @param userId      用户信息
     * @param userCardIds     用户卡牌id列表
     * @param silverCoinValue 银币价值
     * @return ResponseCodeMsg
     */
    ResponseCodeMsg sellCards(int userId, List<Long> userCardIds, Integer silverCoinValue);

    /**
     * 获取卡牌详情
     *
     * @param userId 用户信息
     * @param userCardId 用户卡牌id
     * @return CardBattleUserCardDetailDTO
     */
    CardBattleUserCardDetailDTO getCardDetail(int userId, Long userCardId);

    /**
     * 卡牌战斗准备信息
     *
     * @param userId       用户信息
     * @param userBattleTaskId 战斗任务id
     * @return CardBattlePrepareFightDTO
     */
    CardBattlePrepareFightDTO prepareBattle(int userId, String userBattleTaskId);

    /**
     * 选择卡牌
     *
     * @param userId       用户信息
     * @param userBattleTaskId 战斗任务id
     * @param userCardIdList   用户选择卡牌id列表
     * @return CardBattleSelectedCardResultDTO
     */
    CardBattleSelectedCardResultDTO reportSelectedCardV2(int userId, String userBattleTaskId, List<Long> userCardIdList);

    /**
     * 战斗结果上报
     *
     * @param requestInfo       用户信息
     * @param userBattleTaskId 用户战斗任务id
     * @param userCardIdList   用户卡牌id列表
     * @return ReportBattleResultDTO
     */
    ReportBattleResultDTO reportBattleResultV2(RequestInfo requestInfo, String userBattleTaskId, List<Long> userCardIdList);

    /**
     * 一键选择
     *
     * @param userId 用户信息
     * @param userCardId 用户卡牌id
     * @param type       类型
     * @return CardBattleUserCardListDTO
     */
    CardBattleUserCardListDTO oneClickSelect(int userId, Long userCardId, Integer type);

    /**
     * 卡牌升级
     *
     * @param upCardParam 升级参数
     * @return ResponseCodeMsg
     */
    ResponseCodeMsg upCard(CardBattleUpCardParam upCardParam);

    /**
     * 卡牌突破
     *
     * @param userId         用户信息
     * @param userCardId         用户卡牌id
     * @param selectUserCardIds  选择的卡牌id
     * @param breakAfterMaxLevel 突破后最大等级
     * @param spendSilverCoins   消耗银币
     * @return ResponseCodeMsg
     */
    @Deprecated
    ResponseCodeMsg breakCard(int userId, Long userCardId, List<Long> selectUserCardIds, Integer breakAfterMaxLevel, Integer spendSilverCoins);

    /**
     * 卡牌突破
     *
     * @param breakCardParam 突破参数
     * @return ResponseCodeMsg
     */
    ResponseCodeMsg breakCard(CardBattleBreakCardParam breakCardParam);

    /**
     * 删除材料红点标记
     *
     * @param userId 用户信息
     * @param materialId 材料id
     */
    void delMaterialRedPointFlag(int userId, Integer materialId, Integer propType, String uniqueId);

    /**
     * 删除卡牌红点标记
     *
     * @param userId 用户信息
     * @param userCardId 用户卡牌id
     */
    void delCardRedPointFlag(int userId, Long userCardId);

    /**
     * 获取卡牌专题信息
     *
     * @param userId 用户信息
     * @return CardBattleMainPageDto
     */
    CardBattleCardTopicListDTO getTopicListV2(int userId);

    /**
     * 查询背包的材料tab和卡牌战斗tab是否有红点
     */
    Pair<Boolean, Boolean> checkPackRedPoint(long userId);

    CardBattleHeartDTO getBattleActivityHeartInfo(RequestInfo requestInfo);

    CardBattlePrizeDTO receiveLoveHeartPrize(RequestInfo requestInfo);

    /**
     * 获取小喇叭广播数据
     * @return SmallTrumpetData
     */
    SmallTrumpetData getSmallTrumpetData();

    /**
     * 获取用户点数信息
     *
     * @param userId 用户信息
     * @return CardBattleBondPointDTO
     */
    CardBattleBondPointDTO getCostPointInfo(int userId);

    /**
     * 获取羁绊点数获取弹窗
     *
     * @param userId 用户信息
     * @return CardBattleResourceAcquisitionDTO
     */
    CardBattleResourceAcquisitionDTO getBondCommonPointAcquisition(int userId);

    /**
     * 获取羁绊专属点数获取弹窗
     *
     * @param userId 用户信息
     * @return CardBattleResourceAcquisitionDTO
     */
    CardBattleResourceAcquisitionDTO getBondExclusivePointAcquisition(int userId, Integer roleGroupId);

    RpcResult<CardBattleUserPropDTO> propSynthesis(RequestInfo requestInfo, CardBattlePropSynthesisParam param);
}
