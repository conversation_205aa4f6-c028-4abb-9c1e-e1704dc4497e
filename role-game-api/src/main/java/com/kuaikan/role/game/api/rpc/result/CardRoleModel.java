package com.kuaikan.role.game.api.rpc.result;

import com.kuaikan.role.game.api.bean.Role;
import com.kuaikan.role.game.api.bo.ImageInfo;

import java.io.Serializable;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CardRoleModel implements Serializable {

    private static final long serialVersionUID = -7389849500250090237L;
    private int id;

    /**
     * 名称
     */
    private String name;

    /**
     * 图片
     */
    private ImageInfo image;

    /**
     * 头像
     */
    private ImageInfo avatar;

    /**
     * 专题id
     */
    private int topicId;

    /**
     * 专题id
     */
    private String topicTitle;

    /**
     * 等级
     */
    private int level;

    /**
     * 默认装扮id
     */
    private int defaultCostumeId;

    /**
     * 是否已领养
     */
    private boolean adopted;

    private Long adoptTime;

    /**
     * 生效的装扮信息
     */
    private CostumeModel costume;

    /**
     * 角色化的介绍图
     * @param role
     * @return
     */
    private ImageInfo introductionImage;

    public static CardRoleModel valueOf(Role role) {
        Role.Config config = role.getConfig();
        CardRoleModel cardRoleModel = new CardRoleModel().setId(role.getId())
                .setName(role.getName())
                .setImage(role.getImage())
                .setAvatar(role.getAvatar())
                .setTopicId(role.getTopicId())
                .setTopicTitle(role.getTopicName());
        if (config != null) {
            cardRoleModel.setIntroductionImage(config.getIntroductionImage());
        }
        return cardRoleModel;
    }

}