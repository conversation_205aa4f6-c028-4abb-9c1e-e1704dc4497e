package com.kuaikan.role.game.api.model;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * CompleteScheduleModel
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
@Data
@Accessors(chain = true)
public class CompleteScheduleModel implements Serializable {

    private static final long serialVersionUID = 8913653990983987371L;
    private int consumeMinute;
    private int energyChange;
    private int moodChange;
    private List<Integer> moodCutPoints;
    private int tirednessChange;
    private List<Integer> tirednessCutPoints;
    private int silverCoinChange;
    private int pickUpCoins;
    private int pickUpExps;

    private LevelUpModel levelUp;
    private DimensionLevelUpModel dimensionLevelUp;
    private List<UnlockStoryModel> unlockStories;
    private UnlockLetterStoryModel unlockLetterStory;
    private EmotionBondLevelUpModel emotionBondLevelUp;
    private List<ScheduleExtraAwardModel> extraAwardModels;
}
