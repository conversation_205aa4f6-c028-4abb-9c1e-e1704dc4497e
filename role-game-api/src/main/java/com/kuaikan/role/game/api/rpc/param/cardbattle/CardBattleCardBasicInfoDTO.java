package com.kuaikan.role.game.api.rpc.param.cardbattle;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 卡牌基础信息信息
 *
 * <AUTHOR>
 * @date 2024/7/24
 */
@Data
@Accessors(chain = true)
public class CardBattleCardBasicInfoDTO implements Serializable {

    private static final long serialVersionUID = 4927685642393129515L;
    // 卡牌名称
    private String cardName;
    // 卡片id
    private String cardId;
    // 模板id
    private int modelId;
    // 专题名称
    private int topicId;
    // 属性
    private int attr;
    // 卡片稀有度 {@link CardBattleCardRarityEnum}
    private int cardRarity;
    // 卡牌图片
    private String cardImg;
    // 行动消耗点数
    private int cost;
    // 攻击力
    private int atk;
    // 血量
    private int hp;
    // 是否已收集
    private boolean gathered;
}
