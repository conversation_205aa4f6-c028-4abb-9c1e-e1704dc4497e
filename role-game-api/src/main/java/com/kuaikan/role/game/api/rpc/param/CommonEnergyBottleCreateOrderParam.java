package com.kuaikan.role.game.api.rpc.param;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.common.bean.UserAgent;

@Data
@Accessors(chain = true)
public class CommonEnergyBottleCreateOrderParam implements Serializable {

    private static final long serialVersionUID = 1L;

    private int userId;
    private int mapId;
    private String propId;
    private UserAgent userAgent;

    public static CommonEnergyBottleCreateOrderParam of(int userId, int mapId, String propId, UserAgent userAgent) {
        CommonEnergyBottleCreateOrderParam param = new CommonEnergyBottleCreateOrderParam();
        param.setUserId(userId);
        param.setMapId(mapId);
        param.setPropId(propId);
        param.setUserAgent(userAgent);
        return param;
    }
}
