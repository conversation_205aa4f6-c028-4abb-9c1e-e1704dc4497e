package com.kuaikan.role.game.api.rpc.param.cardbattle;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 用户 战斗点数变更
 *
 * <AUTHOR>
 * @date 2024/7/11
 */
@Data
@Accessors(chain = true)
public class UserPointChangeParam implements Serializable {

    private static final long serialVersionUID = 6721875620340192867L;

    private long userId;

    private int point;

    private String source;

    private String orderId;
}
