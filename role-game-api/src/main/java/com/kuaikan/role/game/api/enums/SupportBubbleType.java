package com.kuaikan.role.game.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SupportBubbleType {

    TALK_Bubble(1, "说话气泡"),
    THINK_Bubble(2, "思考气泡");
    
    private Integer code;
    private String desc;

    public static boolean isValidType(int inputValue) {
        for (SupportBubbleType type : SupportBubbleType.values()) {
            if (type.getCode() == inputValue) {
                return true;
            }
        }
        return false;
    }
}