package com.kuaikan.role.game.api.service;

import java.util.List;

import com.kuaikan.common.model.RpcResult;
import com.kuaikan.role.game.api.model.RoleFullInfoModel;
import com.kuaikan.role.game.api.model.RoleFullInfoModelV2;
import com.kuaikan.role.game.api.model.RoleGroupModel;
import com.kuaikan.role.game.api.model.RolePropertyModel;
import com.kuaikan.role.game.api.model.StoryUnlockInfoModel;
import com.kuaikan.role.game.api.rpc.param.AdoptionRolesParam;
import com.kuaikan.role.game.api.rpc.param.QueryCostumeRoleListParam;
import com.kuaikan.role.game.api.rpc.param.SwitchRolesParam;
import com.kuaikan.role.game.api.rpc.param.TalkLaterParam;

/**
 * <AUTHOR>
 * @date 2024/2/28
 */
public interface UserRoleService {

    /**
     * 领养角色
     *
     * @param adoptionRolesParam 领养角色参数
     * @return 领养结果
     */
    RpcResult<RoleFullInfoModelV2> adoptionRoleV2(AdoptionRolesParam adoptionRolesParam);

    /**
     * 切换角色
     *
     * @param switchRolesParam 切换角色参数
     * @return 切换结果
     */
    RpcResult<RoleFullInfoModelV2> switchRoleGroup(SwitchRolesParam switchRolesParam);

    /**
     * 获取用户完整基本信息
     */
    RpcResult<RoleFullInfoModel> queryUserRoleFullInfo(int userId, int roleId);

    RpcResult<List<StoryUnlockInfoModel>> getRoleLockStoryTips(int userId, int roleId);

    /**
     * 获取角色等级等属性信息
     */
    RpcResult<RolePropertyModel> queryUserRoleProperty(int userId, int roleId);

    RpcResult<Void> handleUserRolePropertyOldData();

    RpcResult<List<RoleGroupModel>> queryUserRoleGroupInfo(QueryCostumeRoleListParam param);

    /**
     * 查询用户角色组详情(包含角色组所有角色的领养信息)
     */
    RpcResult<List<RoleGroupModel>> queryUserRoleGroupDetails(QueryCostumeRoleListParam param);

    List<Integer> queryRoleIdsCanTrap(int userId);

    RpcResult<Void> talkLater(TalkLaterParam talkLaterParam);

    RpcResult<Boolean> clearRoleSpiritRedDot(int userId, int roleId);
}
