package com.kuaikan.role.game.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/4/19
 */
@AllArgsConstructor
@Getter
public enum SilverCoinDropSourceEnum {
    UNKNOWN(0, "未知"),
    EAT_CANDY(1, "吃糖果"),
    USE_ITEM(2, "使用道具");

    private int code;

    private String desc;

    public static SilverCoinDropSourceEnum getByCode(int code) {
        for (SilverCoinDropSourceEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return UNKNOWN;
    }

}
