package com.kuaikan.role.game.api.bo;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * SystemConfig, 系统配置
 *
 * <AUTHOR>
 * @since 2024-05-09
 */
@Data
@Accessors(chain = true)
public class SystemConfig implements Serializable {

    private static final long serialVersionUID = 2743890029872909645L;
    // 统一签到提醒
    private boolean globalSignInRemind;
    // 给白名单发送，如果开启，只给对应白名单用户发送
    private boolean whiteListPush;
    private List<Integer> userIdWhiteList;
    // 签到的push内容
    private SignInRemindContent signInRemindContent;

}
