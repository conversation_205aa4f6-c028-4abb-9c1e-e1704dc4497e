package com.kuaikan.role.game.api.model;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 2024-05-20
 */
@Data
@Accessors(chain = true)
public class RewardOrderQueryModel implements Serializable {

    private static final long serialVersionUID = 1865249100405571449L;

    /**
     * 订单类型
     * @see com.kuaikan.role.game.api.enums.RewardOrderType
     */
    private int type;

    private BlindBoxOrderQueryModel blindBoxOrderQueryModel;

    private CompleteScheduleModel completeScheduleModel;
    private CommonScheduleResultModel commonScheduleResultModel;

    private RoleGroupModel roleGroupModel;
    private ItemOrderModel itemOrderModel;
}
