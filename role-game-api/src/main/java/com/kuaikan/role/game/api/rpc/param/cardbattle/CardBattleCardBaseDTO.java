package com.kuaikan.role.game.api.rpc.param.cardbattle;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 卡牌战斗-卡牌基础信息DTO
 *
 * <AUTHOR>
 * @date 2024/7/12
 */
@Data
@Accessors(chain = true)
public class CardBattleCardBaseDTO implements Serializable {

    private static final long serialVersionUID = -1091911303845354994L;

    private String cardId;

    // 战斗点数
    private Integer cost;

    // 卡牌属性 @see CardBattleCardAttrEnum
    private Integer attr;

    // 卡片稀有度（卡面） {@link CardBattleCardRarityEnum}
    private Integer cardRarity;

    // 用户拥有的此类卡牌数量, 可能为0
    private Integer count;

    // 当前用户战力最大的卡牌id， 可能不存在
    private Long userBattleCardId;

    // 当前用户战力最大的卡牌对应的等级， 可能不存在
    private Integer userBattleCardLevel;

}
