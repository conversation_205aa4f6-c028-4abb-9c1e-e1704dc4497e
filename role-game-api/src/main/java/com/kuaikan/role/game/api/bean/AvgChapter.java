package com.kuaikan.role.game.api.bean;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Maps;

import com.kuaikan.role.game.api.bo.AvgChapterTextNode;
import com.kuaikan.role.game.api.enums.AvgChapterType;
import com.kuaikan.role.game.api.enums.AvgTextLoopType;
import com.kuaikan.role.game.api.enums.AvgTextRatioType;
import com.kuaikan.role.game.api.enums.AvgTextUnlockCondition;
import com.kuaikan.role.game.api.enums.AvgTextValueCondition;
import com.kuaikan.role.game.api.enums.CharType;
import com.kuaikan.role.game.api.enums.EffectType;
import com.kuaikan.role.game.api.util.AvgOrderUtils;

/**
 * <AUTHOR>
 * @date 2024/5/16
 */

@Data
@Accessors(chain = true)
@Document("avg_chapter")
@Slf4j
public class AvgChapter implements Serializable {

    public static final String MP3 = ".mp3";
    public static final String JPG = ".jpg";
    public static final String GIF = ".gif";
    public static final String WEBP = ".webp";
    public static final String MP4 = ".mp4";
    public static final String PNG = ".png";
    public static final String HIDE_OPTIONS = "#select#";
    public static final String INSERT_BACK = "#BACK#";
    public static final String HIDE_OPTIONS_CN = "隐藏选项";
    public static final String STOP_OPTIONS = "#STOP#";
    public static final int CHAPTER_MIN = 100001;

    private static final long serialVersionUID = 6715321522625768344L;

    @Id
    private String objectId;

    private String projectId;

    /**
     * 章节类型 {@link AvgChapterType}
     */
    private Integer type;

    private String chapterFileName;

    private String chapterFileKey;

    private Integer chapterId;

    private String chapterName;

    private Integer styleId;

    private List<Text> textList;

    private boolean hadNextText;

    private String textContentMd5;

    private Date createdAt;

    private Date updatedAt;

    /**
     * 章节状态
     * {@link com.kuaikan.role.game.api.enums.CommonStatus}
     */
    private Integer status;

    @Data
    @Accessors(chain = true)
    @ToString
    public static class Text implements Serializable {

        private static final long serialVersionUID = -5250664690618217355L;
        /**
         * 文本ID
         */
        private String textId;
        /**
         * 对白文案
         */
        private String dialogue;

        /**
         * 名字框位置
         */
        private Integer nameCard;

        /**
         * 修改为 【名字框位置 ｜ 选项位置 ｜ 前置文案展示】，例如【13 | 1 | 2】
         */
        private String nameCardStr;

        /**
         * 后接文本ID
         */
        private List<String> nextIds;

        /**
         * 选项解锁条件
         */
        private String unlock;

        /**
         * 数值变化
         */
        private String value;

        /**
         * 一次性选项
         */
        private String onetime;

        /**
         * 背景
         */
        private String bg;
        /**
         * 背景动效
         */
        private Effect bgEffectV2;
        /**
         * 背景动效播放配置
         */
        private PlayConfig bgEffectPlayConfig;
        /**
         * 表情
         */
        private Integer face;
        /**
         * 立绘
         */
        private String character;
        /**
         * 立绘位置
         */
        private Integer charPosition;
        /**
         * 立绘动效
         */
        private Effect charEffectV2;
        /**
         * q版本立绘左
         */
        private String spineLeft;
        /**
         * q版本立绘中
         */
        private String spineMiddle;
        /**
         * q版本立绘右
         */
        private String spineRight;
        /**
         * 头像
         */
        private String head;
        /**
         * 头像位
         */
        private Integer position;
        /**
         * 音乐
         */
        private String bgm;
        /**
         * 音效
         */
        private String sound;
        /**
         * 音效循环
         */
        private Integer loop;
        /**
         * CV语音
         */
        private String cv;
        /**
         * 高光视频
         */
        private String video;

        @Deprecated
        private Integer playMode;
        /**
         * 高光视频播放配置
         */
        private PlayConfig videoPlayConfig;
        /**
         * 点触热区
         */
        private String hotZone;
        /**
         * 高光视频音频
         */
        private String videoVoice;
        /**
         * 后接段落
         */
        private Integer nextChapter;

        /**
         * 循环配置
         */
        private String textLoopConfig;

        /**
         * 是否循环
         */
        private boolean textLoop;

        /**
         * 循环类型 {@link AvgTextLoopType}
         */
        private Integer textLoopType;

        /**
         * 循环次数
         */
        private Integer textLoopCount;

        /**
         * 循环退出文本ID
         */
        private String loopNextTextId;

        /**
         * 是否退出循环
         */
        private boolean loopBreak;

        /**
         * 中插片段
         */
        private Integer insertChapter;

        /**
         * 中插片段返回标识
         */
        private String insertChapterBack;

        /**
         * 结束tips
         */
        private String endTips;

        /**
         * 物品
         */
        private String stuff;

        /**
         * 物品移动
         */
        private String stuffMove;

        /**
         * 立绘移动
         */
        private String charMove;

        /**
         * q版立绘移动
         */
        private String spineMove;

        /**
         * 中插片段返回标识
         */
        private boolean insertChapterBackFlag;

        /**
         * 中插片段返回id
         */
        private String insertChapterBackId;

        @Data
        @Accessors(chain = true)
        public static class Effect implements Serializable {

            private static final long serialVersionUID = -7477760512697039963L;
            /**
             * 类型
             */
            private Integer type;
            /**
             * 速率
             */
            private Integer speed;
            /**
             * 震动幅度
             */
            private Integer vibration;

            public static Effect valueOf(String effectStr) {
                if (StringUtils.isBlank(effectStr)) {
                    return null;
                }
                String[] s = effectStr.split("#");
                Effect effect = new Effect();
                effect.setType(Integer.parseInt(s[0]));
                if (s.length == 2) {
                    effect.setSpeed(Integer.parseInt(s[1]));
                } else if (s.length == 3) {
                    effect.setSpeed(Integer.parseInt(s[1]));
                    effect.setVibration(Integer.parseInt(s[2]));
                }
                return effect;
            }
        }

        @Data
        @Accessors(chain = true)
        public static class PlayConfig implements Serializable {

            private static final long serialVersionUID = -1835860203263126734L;
            /**
             * 播放模式
             */
            private Integer playMode;
            /**
             * 播放比例
             * @see com.kuaikan.role.game.api.enums.AvgTextRatioType
             */
            private Integer ratio;

            public static PlayConfig valueOf(String playConfigStr) {
                if (StringUtils.isBlank(playConfigStr)) {
                    return null;
                }
                int index = playConfigStr.indexOf("|");
                PlayConfig playConfig = new PlayConfig();
                if (index == -1) {
                    playConfig.setPlayMode(Integer.parseInt(playConfigStr));
                } else {
                    String playMode = playConfigStr.substring(0, index);
                    String ratio = playConfigStr.substring(index + 1);
                    playConfig.setPlayMode(Integer.valueOf(playMode));
                    playConfig.setRatio(Integer.valueOf(ratio));
                }
                return playConfig;
            }
        }
    }

    @Data
    @Accessors(chain = true)
    public static class DirFileContents {

        private Set<String> dynamicRoleDirNames;

        private Set<String> spineNames;

        private Set<String> qSpineNames;

        private Set<String> fileNames;

        private Set<String> hotZoneNames;

        public DirFileContents() {
            this.dynamicRoleDirNames = new HashSet<>();
            this.fileNames = new HashSet<>();
            this.spineNames = new HashSet<>();
            this.qSpineNames = new HashSet<>();
            this.hotZoneNames = new HashSet<>();
        }

    }

    @JSONField(serialize = false)
    public String getTextContentMd5() {
        List<Text> textList = this.textList;
        if (CollectionUtils.isEmpty(textList)) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (Text text : textList) {
            sb.append(text.toString());
        }
        return DigestUtils.md5Hex(sb.toString());
    }

    public String getOriginTextContentMd5() {
        return this.textContentMd5;
    }

    @JSONField(serialize = false)
    public DirFileContents getDirFileContents() {
        DirFileContents dirFileContents = new DirFileContents();
        if (CollectionUtils.isEmpty(textList)) {
            return dirFileContents;
        }
        for (Text text : textList) {
            String bg = text.getBg();
            if (StringUtils.isNotBlank(bg)) {
                dirFileContents.getFileNames().add(bg);
            }
            processChar(text.getCharacter(), false, dirFileContents);
            processChar(text.getSpineLeft(), true, dirFileContents);
            processChar(text.getSpineMiddle(), true, dirFileContents);
            processChar(text.getSpineRight(), true, dirFileContents);
            String head = text.getHead();
            if (StringUtils.isNotBlank(head)) {
                dirFileContents.getFileNames().add(head);
            }
            String bgm = text.getBgm();
            if (StringUtils.isNotBlank(bgm)) {
                dirFileContents.getFileNames().add(bgm);
            }
            String sound = text.getSound();
            if (StringUtils.isNotBlank(sound)) {
                dirFileContents.getFileNames().add(sound);
            }
            String cv = text.getCv();
            if (StringUtils.isNotBlank(cv)) {
                dirFileContents.getFileNames().add(cv);
            }
            String video = text.getVideo();
            if (StringUtils.isNotBlank(video)) {
                dirFileContents.getFileNames().add(video);
            }
            String videoVoice = text.getVideoVoice();
            if (StringUtils.isNotBlank(videoVoice)) {
                dirFileContents.getFileNames().add(videoVoice);
            }

            String hotZone = text.getHotZone();
            if (StringUtils.isNotBlank(hotZone)) {
                dirFileContents.getHotZoneNames().add(hotZone);
            }

            String stuff = text.getStuff();
            if (StringUtils.isNotBlank(stuff)) {
                dirFileContents.getFileNames().add(stuff);
            }
        }
        return dirFileContents;
    }

    public static List<String> verify(int type, List<AvgChapter.Text> textList, List<AvgChapter> nextChapters, List<AvgChapter> insertChapters) {
        List<String> errors = new ArrayList<>();
        Map<String, Integer> lastIndexMap = Maps.newHashMap();
        List<String> textIds = textList.stream().map(Text::getTextId).collect(Collectors.toList());
        List<Integer> nextChaptersIds = nextChapters.stream().map(AvgChapter::getChapterId).collect(Collectors.toList());
        List<Integer> insertChapterIds = insertChapters.stream().map(AvgChapter::getChapterId).collect(Collectors.toList());
        for (int i = 0; i < textList.size(); i++) {
            Text text = textList.get(i);
            String textId = text.getTextId();
            int row = i + 3;
            if (StringUtils.isBlank(textId)) {
                errors.add("第" + (row) + "行文本ID不能为空\n");
            }
            if (textIds.subList(i + 1, textIds.size()).contains(textId)) {
                errors.add("第" + (row) + "行文本ID不能重复\n");
            }
            if (text.getNextIds().contains(textId)) {
                errors.add("第" + (row) + "行文本ID" + textId + "的后接ID不能与文本ID重复\n");
            }
            String bg = text.getBg();
            if (StringUtils.isNotBlank(bg) && !(bg.endsWith(PNG) || bg.endsWith(JPG) || bg.endsWith(MP4))) {
                errors.add("第" + (row) + "行的背景格式不正确\n");
            }
            Text.Effect bgEffect = text.getBgEffectV2();
            Text.PlayConfig bgEffectPlayConfig = text.getBgEffectPlayConfig();
            if (bgEffect != null && bgEffectPlayConfig != null) {
                if (bgEffect.getType() >= EffectType.PUSH_IN.getCode()
                        && bgEffect.getType() <= EffectType.BACKGROUND_SHAKE.getCode()
                        && bgEffectPlayConfig.getRatio() == AvgTextRatioType.FULL_SCREEN.getCode()) {
                    errors.add("第" + (row) + "行的背景动效比例配置不正确\n");
                }
            }
            String character = text.getCharacter();
            if (StringUtils.isNotBlank(character) && CharType.getCharType(character) == CharType.UNKNOWN) {
                errors.add("第" + (row) + "行的立绘格式不正确\n");
            }
            if (StringUtils.isNotBlank(character) && text.getFace() == null) {
                text.setFace(1);
            }
            String head = text.getHead();
            if (StringUtils.isNotBlank(head) && !head.endsWith(PNG)) {
                errors.add("第" + (row) + "行的头像图片格式不正确\n");
            }
            if (StringUtils.isNotBlank(head) && text.getPosition() == null) {
                text.setPosition(1);
            }
            String bgm = text.getBgm();
            if (StringUtils.isNotBlank(bgm)) {
                if (!bgm.endsWith(MP3) && !bgm.equals(STOP_OPTIONS)) {
                    errors.add("第" + (row) + "行的背景音乐格式不正确\n");
                }
            }
            String sound = text.getSound();
            if (StringUtils.isNotBlank(sound)) {
                if (!sound.endsWith(MP3) && !sound.equals(STOP_OPTIONS)) {
                    errors.add("第" + (row) + "行的音效格式不正确\n");
                }
            }
            String cv = text.getCv();
            if (StringUtils.isNotBlank(cv) && !cv.endsWith(MP3)) {
                errors.add("第" + (row) + "行的CV语音格式不正确\n");
            }
            String video = text.getVideo();
            if (StringUtils.isNotBlank(video) && !video.endsWith(MP4)) {
                errors.add("第" + (row) + "行的视频格式不正确\n");
            }
            String videoVoice = text.getVideoVoice();
            if (StringUtils.isNotBlank(videoVoice) && !videoVoice.endsWith(MP3)) {
                errors.add("第" + (row) + "行的视频语音格式不正确\n");
            }
            String stuff = text.getStuff();
            String stuffMove = text.getStuffMove();
            if (StringUtils.isNotBlank(stuff)) {
                if (!(stuff.endsWith(PNG) || stuff.endsWith(JPG) || stuff.endsWith(WEBP) || stuff.endsWith(GIF))) {
                    errors.add("第" + (row) + "行的物品格式不正确\n");
                } else if (StringUtils.isNotBlank(stuffMove)) {
                    lastIndexMap.put(stuff, i);
                } else if (lastIndexMap.containsKey(stuff) && lastIndexMap.get(stuff) == i - 1) {
                    lastIndexMap.put(stuff, i);
                } else {
                    errors.add("第" + (row) + "行的物品移动格式不正确\n");
                }
            } else if (StringUtils.isNotBlank(stuffMove)) {
                errors.add("第" + (row) + "行的物品移动格式不正确\n");
            }
            int dialogueLength = 0;
            boolean hideOptions = HIDE_OPTIONS.equals(text.getDialogue());
            boolean[] isOrderOption = new boolean[text.getNextIds().size()];
            if (StringUtils.isNotBlank(text.getDialogue())) {
                if (text.getDialogue().contains("|")) {
                    String[] dialogues = text.getDialogue().split("\\|");
                    dialogueLength = dialogues.length;
                    if (dialogues.length > 15) {
                        errors.add("第" + (row) + "行对话选项不能超过15个\n");
                    }
                    if (dialogues.length != text.getNextIds().size()) {
                        errors.add("第" + (row) + "行文本ID" + text.getTextId() + "里对话选项与后接ID选项数量不一致\n");
                    }
                    for (int index = 0; index < dialogues.length; index++) {
                        String order = AvgOrderUtils.findTextOrder(dialogues[index]);
                        if (StringUtils.isNotBlank(order)) {
                            if (!AvgOrderUtils.EXIST_TEXT_ORDER_REGEX.contains(order)) {
                                errors.add("第" + (row) + "行文本ID" + text.getTextId() + "填写的指令" + dialogues[index] + "不支持\n");
                            }
                            if (index < isOrderOption.length) {
                                isOrderOption[index] = true;
                            }
                        }
                    }
                } else if (hideOptions) {
                    dialogueLength = CollectionUtils.size(text.getNextIds());
                } else {
                    dialogueLength = 1;
                }
            }
            int nextIdsLength = 0;
            List<String> nextIds = text.getNextIds();
            if (CollectionUtils.isNotEmpty(nextIds)) {
                nextIdsLength = nextIds.size();
                for (int index = 0; index < nextIds.size(); index++) {
                    String nextId = nextIds.get(index);
                    String order = AvgOrderUtils.findTextOrder(nextId);
                    if (StringUtils.isNotBlank(order)) {
                        if (!AvgOrderUtils.EXIST_TEXT_ORDER_REGEX.contains(order)) {
                            errors.add("第" + (row) + "行文本ID" + text.getTextId() + "填写的指令" + nextId + "不支持\n");
                        }
                        if (hideOptions) {
                            errors.add("第" + (row) + "行文本ID" + text.getTextId() + "文本ID" + text.getTextId() + "为隐藏选项，不支持指令\n");
                        }
                        isOrderOption[index] = true;
                        continue;
                    }
                    if (!textIds.contains(nextId)) {
                        errors.add("第" + (row) + "行文本ID" + text.getTextId() + "填写的后接ID" + nextId + "不存在，无法继续播放\n");
                    }
                }
            }
            int unlockLength = 0;
            String unlockStr = text.getUnlock();
            if (StringUtils.isNotBlank(unlockStr)) {
                if (unlockStr.contains("|")) {
                    String[] unlocks = unlockStr.split("\\|");
                    unlockLength = unlocks.length;
                    for (int j = 0; j < unlocks.length; j++) {
                        String unlock = unlocks[j];
                        AvgTextUnlockCondition avgTextUnlockCondition = AvgTextUnlockCondition.getByCode(NumberUtils.toInt(unlock));
                        if (j < isOrderOption.length && isOrderOption[j] && avgTextUnlockCondition != AvgTextUnlockCondition.NOT_ONLINE) {
                            errors.add("第" + (row) + "行文本ID" + text.getTextId() + "有指令，不支持解锁条件" + unlock + "\n");
                        }
                        if (avgTextUnlockCondition == null) {
                            errors.add("第" + (row) + "行文本ID" + text.getTextId() + "填写的解锁条件" + unlock + "不支持\n");
                        }
                        if (hideOptions) {
                            if (avgTextUnlockCondition == AvgTextUnlockCondition.PAY) {
                                errors.add("第" + (row) + "行文本ID" + text.getTextId() + "文本ID" + text.getTextId() + "为隐藏选项，请检查条件配置");
                            }
                            if (j == unlocks.length - 1 && avgTextUnlockCondition != AvgTextUnlockCondition.NOT_ONLINE) {
                                errors.add("第" + (row) + "行文本ID" + text.getTextId() + "文本ID" + text.getTextId() + "为隐藏选项，请检查条件配置");
                            } else if (j != unlocks.length - 1 && avgTextUnlockCondition != AvgTextUnlockCondition.ATTRIBUTE) {
                                errors.add("第" + (row) + "行文本ID" + text.getTextId() + "文本ID" + text.getTextId() + "为隐藏选项，请检查条件配置");
                            }
                        }
                    }
                } else {
                    unlockLength = 1;
                }
            }
            int valueLength = 0;
            String valueStr = text.getValue();
            if (StringUtils.isNotBlank(valueStr)) {
                if (valueStr.contains("|")) {
                    String[] values = valueStr.split("\\|");
                    valueLength = values.length;
                    for (int j = 0; j < values.length; j++) {
                        String value = values[j];
                        AvgTextValueCondition avgTextValueCondition = AvgTextValueCondition.getByCode(NumberUtils.toInt(value));
                        if (j < isOrderOption.length && isOrderOption[j] && avgTextValueCondition != AvgTextValueCondition.NO_CHANGE) {
                            errors.add("第" + (row) + "行文本ID" + text.getTextId() + "有指令，不支持数值变化" + value + "\n");
                        }
                        if (avgTextValueCondition == null) {
                            errors.add("第" + (row) + "行文本ID" + text.getTextId() + "填写的数值变化" + value + "不支持\n");
                        }
                    }
                } else {
                    valueLength = 1;
                }
            }
            int onetimeLength = 0;
            String loopConfig = text.getTextLoopConfig();
            if (StringUtils.isNotBlank(loopConfig)) {
                String[] split = loopConfig.split(",");
                AvgTextLoopType avgTextLoopType = AvgTextLoopType.valueOf(split[0]);
                if (avgTextLoopType == AvgTextLoopType.LOOPSTART) {
                    text.setTextLoop(true);
                    text.setTextLoopType(avgTextLoopType.getCode());
                    if (NumberUtils.toInt(split[1]) < 1) {
                        errors.add("第" + (row) + "行文本ID" + text.getTextId() + "填写的循环次数不正确\n");
                    }
                    int textLoopCount = NumberUtils.toInt(split[1]);
                    text.setTextLoopCount(textLoopCount);
                    String oneTimeStr = split[2];
                    if (!oneTimeStr.contains("|")) {
                        errors.add("第" + (row) + "行文本ID" + text.getTextId() + "填写的一次性选项不正确\n");
                        onetimeLength = -1;
                    } else {
                        String[] onetimeArr = oneTimeStr.split("\\|");
                        //onetimeArr 如果都是0，则size必须大于等于textLoopCount
                        if (Arrays.stream(onetimeArr).allMatch("1"::equals)) {
                            if (onetimeArr.length < textLoopCount) {
                                errors.add("第" + (row) + "行文本ID" + text.getTextId() + "都是一次性选项数量必须大于等于循环次数\n");
                            }
                        }
                        onetimeLength = onetimeArr.length;
                        text.setOnetime(oneTimeStr);
                    }
                } else if (avgTextLoopType == AvgTextLoopType.LOOPEND) {
                    text.setTextLoop(true);
                    text.setTextLoopType(avgTextLoopType.getCode());
                    text.setLoopNextTextId(split[1]);
                    if (split.length == 3) {
                        AvgTextLoopType isBreak = AvgTextLoopType.valueOf(split[2]);
                        text.setLoopBreak(isBreak == AvgTextLoopType.BREAK);
                    }
                } else {
                    errors.add("第" + (row) + "行文本ID" + text.getTextId() + "填写的循环类型不正确\n");
                }
                String nameCardStr = text.getNameCardStr();
                if (StringUtils.isNotBlank(nameCardStr)) {
                    if (nameCardStr.contains("0")) {
                        errors.add("第" + (row) + "行文本ID" + text.getTextId() + "填写的名字框位置不正确\n");
                    }
                }
            }

            if (dialogueLength > 1 && StringUtils.isNotBlank(text.getOnetime())) {
                if (dialogueLength != nextIdsLength || dialogueLength != unlockLength || dialogueLength != valueLength || dialogueLength != onetimeLength) {
                    errors.add("第" + (row) + "行文本ID" + text.getTextId() + "里对话选项与后接ID选项数量、解锁条件数量、数值变化数量不一致\n");
                }
            }
            Integer nextChapter = text.getNextChapter();
            if (type == AvgChapterType.INSERT_CHAPTER.getCode() && nextChapter != null) {
                errors.add("第" + (row) + "行填写的后接段落ID" + nextChapter + "不应该填写\n");
            }
            if (type == AvgChapterType.TEXT.getCode() && StringUtils.isNotBlank(text.getInsertChapterBack())) {
                errors.add("第" + (row) + "行段落中不应该填写中插片段返回标识\n");
            }
            if (nextChapter != null && !nextChaptersIds.contains(nextChapter)) {
                errors.add("第" + (row) + "行填写的后接段落ID" + nextChapter + "不存在\n");
            }
            Integer insertChapter = text.getInsertChapter();
            if (insertChapter != null && !insertChapterIds.contains(insertChapter)) {
                errors.add("第" + (row) + "行填写的中插段落ID" + insertChapter + "不存在\n");
            }
        }

        AvgChapterTextNode avgChapterTextNode = AvgChapterTextNode.buildTreeFromAvgChapter(textList);
        avgChapterTextNode.validateLoopNodes();

        //剧情段落是否能结束播放，段落里至少有一条文本的后接ID为空，如果不符合提示“此段落无法结束播放，不存在后接ID为空的文本”。
        Optional<Text> hadEmptyNextId = textList.stream().filter(text -> CollectionUtils.isEmpty(text.getNextIds())).findAny();
        if (!hadEmptyNextId.isPresent()) {
            errors.add("此段落无法结束播放，不存在后接ID为空的文本");
        }
        return errors;
    }

    private void processChar(String text, boolean isQSpine, DirFileContents dirFileContents) {
        if (StringUtils.isNotBlank(text)) {
            if (text.contains("|")) {
                text = text.split("\\|")[0];
            }
            CharType charType = CharType.getCharType(text);
            switch (charType) {
                case DYNAMIC:
                    dirFileContents.getDynamicRoleDirNames().add(text);
                    break;
                case SPINE:
                    if (isQSpine) {
                        dirFileContents.getQSpineNames().add(text);
                    } else {
                        dirFileContents.getSpineNames().add(text);
                    }
                    break;
                default:
                    dirFileContents.getFileNames().add(text);
                    break;
            }
        }
    }
}
