package com.kuaikan.role.game.api.model;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.bo.ImageInfo;

/**
 * <AUTHOR>
 * @version 2024-04-02
 */
@Data
@Accessors(chain = true)
public class CollectionModel implements Serializable {

    private static final long serialVersionUID = 8925636877414569401L;
    private List<CollectionItemModel> collectionItemModels;

    @Data
    @Accessors(chain = true)
    public static class CollectionItemModel implements Serializable {

        private static final long serialVersionUID = 5994147640822417585L;
        private int storyId;

        private String name;
        /**
         * @see com.kuaikan.role.game.api.enums.StoryType
         */
        private String type;
        /**
         * 星级
         * @see com.kuaikan.role.game.api.enums.StoryLevel
         */
        private int level;
        /**
         * @see com.kuaikan.role.game.api.enums.CollectionItemStatus
         */
        private String status;

        private String obtainCopywriting;
        /**
         * 解锁等级
         */
        private Integer unlockLevel;

        private ImageInfo sceneThumbnail;

        private ImageInfo largeImage;

        private ImageInfo storyThumbnail;

        private ImageInfo collectionImage;
        /**
         * 获得剧情的时间
         */
        private Date acquireTime;
        /**
         * 排序等级
         */
        private int orderNum;

        private Date storyRoleRelationCreateTime;
        private boolean showRedDot;

    }

}
