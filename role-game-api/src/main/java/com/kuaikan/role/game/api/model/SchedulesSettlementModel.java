package com.kuaikan.role.game.api.model;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * ScheduleSettlementModel
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
@Data
@Accessors(chain = true)
public class SchedulesSettlementModel implements Serializable {

    private static final long serialVersionUID = 8824505010568185292L;

    private List<Integer> moodCutPoints;
    private List<Integer> tirednessCutPoints;
    private int silverCoinChange;
    private int totalSilverCoinChange;
    private List<RoleSettlementModel> roleSettlementModels;
    private List<RoleGroupSettlementModel> roleGroupSettlementModels;

    @Data
    @Accessors(chain = true)
    public static class RoleSettlementModel implements Serializable {

        private static final long serialVersionUID = 4612209339182467755L;
        private int roleId;
        private int consumeMinute;
        private int energyChange;
        private int moodChange;
        private int tirednessChange;
        private LevelUpModel levelUpModel;
        private DimensionLevelUpModel dimensionLevelUpModel;
        private int pickUpCoins;
        private int pickUpExps;
        private List<UnlockStoryModel> unlockStoryModels;
        private UnlockLetterStoryModel unlockLetterStoryModel;

    }

    @Data
    @Accessors(chain = true)
    public static class RoleGroupSettlementModel implements Serializable {

        private static final long serialVersionUID = 290343003627502936L;

        private int roleGroupId;
        private EmotionBondLevelUpModel emotionBondLevelUpModel;
        private List<ScheduleExtraAwardModel> extraAwardModels;
    }
}
