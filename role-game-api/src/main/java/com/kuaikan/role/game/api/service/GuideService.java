package com.kuaikan.role.game.api.service;

import com.kuaikan.common.model.RpcResult;
import com.kuaikan.role.game.api.model.CompleteScheduleModel;
import com.kuaikan.role.game.api.model.GuideRecordModel;
import com.kuaikan.role.game.api.rpc.param.GuideFinishScheduleParam;
import com.kuaikan.role.game.api.rpc.param.UpdateGuideRecordParam;

/**
 * GuideService
 *
 * <AUTHOR>
 * @since 2024/7/16
 */
public interface GuideService {

    RpcResult<GuideRecordModel> queryGuideRecord(int userId);

    RpcResult<Void> updateGuideRecord(UpdateGuideRecordParam updateGuideRecordParam);

    RpcResult<Void> updateIgnoreGuideRecord(UpdateGuideRecordParam updateGuideRecordParam);

    RpcResult<CompleteScheduleModel> finishSchedule(GuideFinishScheduleParam param);

    RpcResult<Void> skip(int userId);
}
