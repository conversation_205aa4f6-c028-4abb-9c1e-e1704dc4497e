package com.kuaikan.role.game.api.rpc.result.cardbattle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

import com.fasterxml.jackson.annotation.JsonIgnore;

import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleUserCardDTO;

/**
 * 卡牌战斗奖励加成弹窗规则DTO
 *
 * <AUTHOR>
 * @date 2025/2/12
 */
@Data
@Accessors(chain = true)
public class CardBattlePrizeBuffRuleDTO implements Serializable {

    private static final long serialVersionUID = -4087751288486343814L;

    @JsonIgnore
    private ResponseCodeMsg codeMsg;

    private String roleGroupName;

    // 专题名
    private String topicName;
    // 专题卡上阵加成系数
    private BigDecimal limitTopicCardBuff;

    // 活动名称（祈愿池名称）
    private List<String> newActivityNames;
    // 活动卡(新添加战斗卡)上阵加成系数
    private BigDecimal activityCardBuff;

    // 不同稀有度卡牌加成，key：稀有度
    private List<BigDecimal> cardRareBuffList;

    // 推荐战斗卡列表
    private List<CardBattleUserCardDTO> recBattleCardList;

    // 卡池专辑id
    private String subjectId;
    // 卡池专辑类型
    private Integer subjectType;

}
