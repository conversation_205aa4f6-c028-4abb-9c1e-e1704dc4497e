package com.kuaikan.role.game.api.model;

import java.io.Serializable;
import java.util.List;

import com.kuaikan.role.game.api.bo.ImageInfo;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CommonScheduleListModel implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private List<CommonScheduleModel> schedules;
    
    @Data
    @Accessors(chain = true)
    public static class CommonScheduleModel implements Serializable{
        
        private int scheduleId;
        
        private String scheduleName;
        
        private String buildingName;
        
        private String areaName;
        private List<CommonPrizeModel> commonPrizes;
        private List<StoryPrizeModel> storyPrizes;
        private float energyConsume;
        private OngoingScheduleModel ongoingSchedule;
        private int areaId;
        private int status;
        
    }

    @Data
    @Accessors(chain = true)
    public static class OngoingScheduleModel implements Serializable {
        private int userScheduleId;
        private long startTime;
        private long endTime;
    }

    @Data
    @Accessors(chain = true)
    public static class CommonPrizeModel implements Serializable {
        private int id;
        private String name;
        private String icon;
        private long unit;
    }

    @Data
    @Accessors(chain = true)
    public static class StoryPrizeModel implements Serializable {
        
        private int id;
        
        private String name;
    }
}
