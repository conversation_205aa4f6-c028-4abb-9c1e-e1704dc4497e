package com.kuaikan.role.game.api.rpc.param.cardbattle;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.bean.cardbattle.BattleMonster;

/**
 * 卡牌战斗-怪物信息
 *
 * <AUTHOR>
 * @date 2024/4/28
 */
@Data
@Accessors(chain = true)
public class CardBattleMonsterDTO implements Serializable {

    private static final long serialVersionUID = -7630231221739463159L;
    private String id;
    private String name;
    private Integer level;
    private boolean maxLevel;
    private Integer maxLevelNum;
    private String icon;

    /**
     * 注：使用战斗对象模板对应的type（历史数据使用的怪物类型）
     */
    private Integer type;

    /**
     * 攻击力
     */
    private Integer atk;

    /**
     * 血量
     */
    private Integer hp;

    /**
     * 当前血量, 单独战斗关卡时使用
     */
    private Integer curHp;

    /**
     * 战斗力
     */
    private Integer battlePower;

    /**
     * 弱点属性
     */
    private List<Integer> weakAttrs;

    /**
     * 模版id
     */
    private Integer modelId;

    /**
     * 所属专题漫画id
     */
    private long topicId;
    /**
     * 所属专题漫画名称
     */
    private String topicName;
    /**
     * 描述
     */
    private String desc;

    /**
     * 被hit特效
     */
    private List<String> hitEffect;

    /**
     * 通过怪物配置初始化DTO
     * @param battleMonster
     * @return
     */
    public CardBattleMonsterDTO initByConfig(BattleMonster battleMonster) {
        this.id = battleMonster.getId();
        this.name = battleMonster.getName();
        this.icon = battleMonster.getIcon();
        this.type = battleMonster.getType();
        this.weakAttrs = battleMonster.getWeakAttrs();
        this.topicId = battleMonster.getTopicId();
        this.desc = battleMonster.getDesc();
        this.hitEffect = battleMonster.getHitEffect();
        this.modelId = battleMonster.getBattleModelId();
        return this;
    }

}
