package com.kuaikan.role.game.api.rpc.result.cardbattle;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

import org.codehaus.jackson.annotate.JsonIgnore;

/**
 *<AUTHOR>
 *@date 2024/12/26
 */
@Data
@Accessors(chain = true)
public class CardBattleStoryDTO implements Serializable {

    private static final long serialVersionUID = -8728296736423427045L;

    // 副本id
    @JsonIgnore
    private String dungeonId;

    private Integer storyId;
    private String name;
    private Boolean unlock;
    private Long acquireTime;

    private String displayUrl;

    private String storyText;

}
