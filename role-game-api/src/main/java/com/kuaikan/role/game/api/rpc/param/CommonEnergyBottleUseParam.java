package com.kuaikan.role.game.api.rpc.param;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CommonEnergyBottleUseParam implements Serializable {

    private static final long serialVersionUID = 1L;

    private int userId;
    private int mapId;
    private int count;

    public static CommonEnergyBottleUseParam of(int userId, int mapId, int count) {
        CommonEnergyBottleUseParam param = new CommonEnergyBottleUseParam();
        param.setUserId(userId);
        param.setMapId(mapId);
        param.setCount(count);
        return param;
    }
}
