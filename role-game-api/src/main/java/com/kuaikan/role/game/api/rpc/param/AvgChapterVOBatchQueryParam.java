package com.kuaikan.role.game.api.rpc.param;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.common.bean.UserAgent;

/**
 * <AUTHOR>
 * @version 2024-03-19
 */
@Data
@Accessors(chain = true)
public class AvgChapterVOBatchQueryParam implements Serializable {

    private static final long serialVersionUID = 5562441139608436897L;

    private int userId;

    /**
     * avg段落id
     */
    private Set<Integer> chapterIds;

    private Map<Integer, String> chapterCurrentTextIdMap;

    private int textLimit;

    /**
     * 是否需要阅读记录
     */
    private boolean needRecord;
    /**
     * false 默认不带版本查询  true带版本查询
     */
    private boolean recordWithVersion;

    /**
     * recordWithVersion为true必填 章节id和版本号
     */
    private Map<Integer, Integer> chapter2VersionMap;

    private int source;

    /**
     * avg内容域名
     */
    private String domain;

    /**
     * user agent
     */
    private UserAgent userAgent;

    /**
     * 玩家起名
     */
    private String leadingRoleName;

}
