package com.kuaikan.role.game.api.bean;

import java.io.Serializable;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.experimental.Accessors;

import org.apache.commons.collections4.CollectionUtils;

import com.kuaikan.role.game.api.bo.ImageInfo;

/**
 * role_spirit_stone_config
 * <AUTHOR>
 * @date 2025/3/5 17:29
 */
@Data
@Accessors(chain = true)
public class RoleSpiritStoneConfig implements Serializable {

    private static final long serialVersionUID = 2968146075828134687L;
    private Integer id;
    private Integer roleId;
    private Config config;
    private Date createdAt;
    private Date updatedAt;

    @Data
    @Accessors(chain = true)
    public static class Config implements Serializable {

        private static final long serialVersionUID = -6025642651428525291L;
        private ImageInfo image;
        private Integer amount;
        private Integer worth;
        /** 卡片后台活动id */
        private Integer activityId;
    }

    public static List<RoleSpiritStoneConfig> init(Collection<Integer> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return Collections.emptyList();
        }
        return roleIds.stream()
                .map(roleId -> new RoleSpiritStoneConfig().setRoleId(roleId).setConfig(new RoleSpiritStoneConfig.Config().setAmount(20).setWorth(40)))
                .sorted(Comparator.comparing(RoleSpiritStoneConfig::getRoleId))
                .collect(Collectors.toList());
    }
}
