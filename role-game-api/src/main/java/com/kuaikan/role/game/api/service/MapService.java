package com.kuaikan.role.game.api.service;

import com.kuaikan.common.model.RpcResult;
import com.kuaikan.role.game.api.model.BuildingMapModel;
import com.kuaikan.role.game.api.model.CityMapModel;
import com.kuaikan.role.game.api.model.MapArrayModel;
import com.kuaikan.role.game.api.model.UserCityModel;
import com.kuaikan.role.game.api.model.UserMapInfoModel;

/**
 *
 * <AUTHOR>
 * @date 2024/6/25
 */
public interface MapService {

    RpcResult<CityMapModel> cityMap(int userId);

    RpcResult<CityMapModel> cityMap(int userId, Integer cityId);

    RpcResult<BuildingMapModel> buildingMap(int userId, int buildingMapId);

    /**
     * 获取用户快看城市信息
     * @param userId
     * @return
     */
    RpcResult<UserCityModel> getUserCity(int userId);

    RpcResult<MapArrayModel> getCityMapArray();

    RpcResult<UserMapInfoModel> getUserEnergy(int userId, int mapId);
}
