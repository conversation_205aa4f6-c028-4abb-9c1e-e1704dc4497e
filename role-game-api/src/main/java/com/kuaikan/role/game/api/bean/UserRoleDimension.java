package com.kuaikan.role.game.api.bean;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

import org.springframework.data.annotation.Id;

import com.kuaikan.role.game.api.enums.DimensionType;

/**
 * UserRoleDimension
 *
 * <AUTHOR>
 * @since 2024-06-06
 */
@Data
@Accessors(chain = true)
public class UserRoleDimension implements Serializable {

    private static final long serialVersionUID = -5643468924815365810L;
    @Id
    private String id;
    private int userId;
    private int roleId;
    /**
     * 维度类型, {@link DimensionType}
     */
    private int dimensionType;
    private int exp;
    private int level;
    private long createdAt;
    private long updatedAt;
}
