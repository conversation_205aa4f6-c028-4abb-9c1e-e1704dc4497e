package com.kuaikan.role.game.api.rpc.param;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.common.bean.UserAgent;
import com.kuaikan.role.game.api.enums.ItemType;

/**
 * RewardItemOrderCreateParam
 *
 * <AUTHOR>
 * @since 2024-08-12
 */
@Data
@Accessors(chain = true)
public class RewardItemOrderCreateParam implements Serializable {

    private static final long serialVersionUID = -520483083334208302L;
    private int userId;
    private int roleId;
    private String giftId;
    /** 道具类型， {@link ItemType}*/
    private int itemType;
    private UserAgent userAgent;
}
