package com.kuaikan.role.game.api.rpc.result.cardbattle;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

import org.codehaus.jackson.annotate.JsonIgnore;

import com.kuaikan.role.game.api.bean.cardbattle.BattleActivityConfig;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattlePrizeDTO;

/**
 * 卡牌战斗副本活动DTO
 * <AUTHOR>
 * @date 2024/10/29
 */
@Data
@Accessors(chain = true)
public class CardBattleActivityDTO implements Serializable {

    private static final long serialVersionUID = -228637679254258552L;

    private String activityId;
    private Integer type;
    private String name;
    private String subTitle;
    // @see CardBattleExploreTypeEnum
    private Integer activityType;
    // @see CardBattleLimitedTimeActivityTypeEnum
    private Integer limitedTimeActivityType;
    // 自定义活动名称
    private String customActivityName;
    private String banner;
    private String icon;
    private Long startTime;
    private Long endTime;
    @JsonIgnore
    private Long createTime;
    /**
     * 完成奖励（所有副本通关奖励聚合）
     */
    private List<CardBattlePrizeDTO> completedPrizes;
    // 幻镜探索副本使用，计算爱心值奖励
    @JsonIgnore
    private List<List<CardBattlePrizeDTO>> completedPrizesList;

    /**
     * 角色组信息（羁绊副本）
     */
    private CardBattleRoleGroupInfoDTO roleGroupInfo;
    // 背景颜色(羁绊副本列表页)
    private String bgColor;
    // 总副本数
    private Integer totalDungeonCount;
    // 已完成副本数
    private Integer completeDungeonCount;
    /**
     * 有效期，剩余时间
     */
    private long remainingTime;
    // 是否有奖励可领取
    private Boolean hasPrizeObtain;
    // 上线新活动或关联了新副本
    private Boolean hasNewActivity;
    // 是否首次参与
    private Boolean firstParticipate;

    public static CardBattleActivityDTO convertByBattleExplore(BattleActivityConfig battleActivityConfig) {
        CardBattleActivityDTO cardBattleActivityDTO = new CardBattleActivityDTO().setActivityId(battleActivityConfig.getId())
                .setType(battleActivityConfig.getType())
                .setName(battleActivityConfig.getName())
                .setSubTitle(battleActivityConfig.getSubTitle())
                .setBanner(battleActivityConfig.getBanner())
                .setStartTime(battleActivityConfig.getStartTime())
                .setEndTime(battleActivityConfig.getEndTime())
                .setCreateTime(battleActivityConfig.getCreateTime());
        if (battleActivityConfig.getEndTime() != null && battleActivityConfig.getEndTime() > System.currentTimeMillis()) {
            cardBattleActivityDTO.setRemainingTime(battleActivityConfig.getEndTime() - System.currentTimeMillis());
        }
        return cardBattleActivityDTO;
    }

}
