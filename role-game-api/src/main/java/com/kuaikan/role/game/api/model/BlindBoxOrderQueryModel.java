package com.kuaikan.role.game.api.model;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.bean.RewardOrderBlindBoxExtraInfo;
import com.kuaikan.role.game.api.bo.ImageInfo;

/**
 * <AUTHOR>
 * @version 2024-05-11
 */
@Data
@Accessors(chain = true)
public class BlindBoxOrderQueryModel implements Serializable {

    private static final long serialVersionUID = -2810967516448637750L;

    private List<CostumePartInfo> costumePartInfos;

    private List<CollectCostumeInfo> collectCostumeInfos;

    private List<CollectingCostumeInfo> collectingCostumeInfos;

    private DecomposeInfo decomposeInfo;

    /**
     * 消耗的kkb数量
     */
    private int spendKKB;

    /**
     * 消费充值币数量
     */
    private int spendRechargeKKB;

    /**
     * 是否盲盒新用户
     */
    private boolean newBlindBoxUser;

    private boolean dressUpVoucher;

    @Data
    @Accessors(chain = true)
    public static class CostumePartInfo implements Serializable {

        private static final long serialVersionUID = 2271827574747725035L;
        private int id;
        private String name;
        private int level;
        private ImageInfo image;

        public static CostumePartInfo valueOf(RewardOrderBlindBoxExtraInfo.CostumePartInfo costumePartInfo) {
            return new CostumePartInfo().setId(costumePartInfo.getCostumePartId())
                    .setImage(costumePartInfo.getImage())
                    .setLevel(costumePartInfo.getLevel())
                    .setName(costumePartInfo.getName());
        }
    }

    @Data
    @Accessors(chain = true)
    public static class CollectCostumeInfo implements Serializable {

        private int id;
        private String name;
        private int level;
        private ImageInfo image;
        private Double hpBonusBuff;

        public static CollectCostumeInfo valueOf(RewardOrderBlindBoxExtraInfo.ComposeCostumeInfo composeCostumeInfo) {
            return new CollectCostumeInfo().setName(composeCostumeInfo.getName())
                    .setId(composeCostumeInfo.getId())
                    .setImage(composeCostumeInfo.getImage())
                    .setLevel(composeCostumeInfo.getLevel())
                    .setHpBonusBuff(composeCostumeInfo.getHpBonusBuff());
        }
    }

    @Data
    @Accessors(chain = true)
    public static class CollectingCostumeInfo implements Serializable {

        private int id;
        private String name;
        private int level;
        private ImageInfo image;
        private int ownCount;
        private int needCount;

        public static CollectingCostumeInfo valueOf(RewardOrderBlindBoxExtraInfo.CollectingCostumeInfo collectingCostumeInfo) {
            return new CollectingCostumeInfo().setId(collectingCostumeInfo.getId())
                    .setImage(collectingCostumeInfo.getImage())
                    .setLevel(collectingCostumeInfo.getLevel())
                    .setName(collectingCostumeInfo.getName())
                    .setNeedCount(collectingCostumeInfo.getNeedCount())
                    .setOwnCount(collectingCostumeInfo.getOwnCount());
        }
    }

    @Data
    @Accessors(chain = true)
    public static class DecomposeInfo implements Serializable {

        private int count;
        private List<StuffInfo> stuffInfos;

        public static DecomposeInfo valueOf(RewardOrderBlindBoxExtraInfo.DecomposeInfo decomposeInfo) {
            if (decomposeInfo == null) {
                return null;
            }
            return new DecomposeInfo().setCount(decomposeInfo.getCostumePartCount())
                    .setStuffInfos(decomposeInfo.getStuffInfos().stream().map(StuffInfo::valueOf).collect(Collectors.toList()));
        }
    }

    @Data
    @Accessors(chain = true)
    public static class StuffInfo implements Serializable {

        private int id;
        private String name;
        private ImageInfo image;
        private int count;

        public static StuffInfo valueOf(RewardOrderBlindBoxExtraInfo.StuffInfo stuffInfo) {
            return new StuffInfo().setId(stuffInfo.getId()).setImage(stuffInfo.getImage()).setName(stuffInfo.getName()).setCount(stuffInfo.getCount());
        }
    }

}
