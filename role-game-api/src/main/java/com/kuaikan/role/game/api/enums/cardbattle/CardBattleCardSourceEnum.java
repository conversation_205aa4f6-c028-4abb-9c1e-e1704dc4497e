package com.kuaikan.role.game.api.enums.cardbattle;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 卡牌来源
 *
 * <AUTHOR>
 * @date 2024/6/17
 */
@Getter
@AllArgsConstructor
public enum CardBattleCardSourceEnum {

    LOVE_CARD(1, "爱心卡"),
    PLOT_CARD(2, "剧情卡"),
    LIMITED_DAY_CARD(3, "赠送日卡（24小时有效）"),
    LIMITED_TIMES_CARD(4, "限次卡"),
    LIMITED_NATURE_WEEK_CARD(5, "增卡（自然周有效）")
    ;

    private Integer code;
    private String desc;

}
