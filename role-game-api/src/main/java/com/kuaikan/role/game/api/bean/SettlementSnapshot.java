package com.kuaikan.role.game.api.bean;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * SettlementSnapshot
 *
 * <AUTHOR>
 * @since 2024-06-06
 */
@Data
@Accessors(chain = true)
public class SettlementSnapshot implements Serializable {

    private static final long serialVersionUID = -5961966797641085555L;
    /** 银币。*/
    private int silverCoinCharge;
    /** 心情。*/
    private int moodChange;
    /** 疲劳。*/
    private int tirednessChange;
    /** 五维的维度。{@link com.kuaikan.role.game.api.enums.DimensionType}*/
    private int dimensionType;
    /** 五维经验值。*/
    private int dimensionExp;
    /** 心情状态 */
    private int moodStatus;
    /** 疲劳状态 */
    private int tirednessStatus;

    // 保存用户已捡起了多少金币和经验值，然后可以根据当前进行的时间和已拾取的
    /** 已生成的金币 */
    private int pickUpCoins;
    /** 已拾取的exp */
    private int pickUpExps;
    // 下面是每个周期相关数值的变化
    private float coinChangePerPeriod;
    private float energyChangePerPeriod;
    private float expChangePerPeriod;
    private float moodChangePerPeriod;
    private float tirednessChangePerPeriod;
    /** 实际的银币周期变化，包含心情的buff*/
    private float actualCoinChangePerPeriod;
    /** 实际的经验周期变化，包含心情的buff*/
    private float actualExpChangePerPeriod;
}
