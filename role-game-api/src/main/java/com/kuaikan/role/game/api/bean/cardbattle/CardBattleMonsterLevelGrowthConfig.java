package com.kuaikan.role.game.api.bean.cardbattle;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

@Data
public class CardBattleMonsterLevelGrowthConfig implements Serializable {

    private static final long serialVersionUID = -7667221225938101974L;
    //id
    private int id;
    //类型
    private int type;
    //等级
    private int level;
    //战斗系数
    private BigDecimal atkRatio;
    //生命系数
    private BigDecimal hpRatio;
}
