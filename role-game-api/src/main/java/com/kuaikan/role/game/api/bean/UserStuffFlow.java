package com.kuaikan.role.game.api.bean;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.enums.UserStuffFlowSource;
import com.kuaikan.role.game.api.enums.UserStuffFlowType;

/**
 * <AUTHOR>
 * @version 2024-04-23
 */
@Data
@Accessors(chain = true)
public class UserStuffFlow implements Serializable {

    private static final long serialVersionUID = -7341813032571793309L;

    private int id;
    private int userId;
    private int stuffId;
    private long bid;
    private int beforeBalance;
    private int afterBalance;
    private int stuffNum;
    /**
     * 来源
     * @see UserStuffFlowSource
     */
    private String source;
    private String thirdId;
    /**
     * 类型，增加/扣减
     * @see UserStuffFlowType
     */
    private int type;
    private ExtraInfo extraInfo;
    private Date createdAt;
    private Date updatedAt;

    @Data
    @Accessors(chain = true)
    public static class ExtraInfo implements Serializable {

        private static final long serialVersionUID = 8622363182974875694L;

    }
}
