package com.kuaikan.role.game.api.bo;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * EggStoryConfig
 *
 * <AUTHOR>
 * @since 2024-06-26
 */
@Data
@Accessors(chain = true)
public class InteractiveEggStoryConfig implements Serializable {

    private static final long serialVersionUID = 6119633677998215844L;
    //定时触发互动彩蛋时间范围
    private long startSecond;
    private long endSecond;

    //互动彩蛋触发最小时间间隔
    private long minTriggerInterval;

    //互动彩蛋触发概率
    private int triggerProbability;
}
