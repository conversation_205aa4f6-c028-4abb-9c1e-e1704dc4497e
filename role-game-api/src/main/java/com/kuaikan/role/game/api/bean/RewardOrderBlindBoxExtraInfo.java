package com.kuaikan.role.game.api.bean;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.bo.ImageInfo;

/**
 * <AUTHOR>
 * @version 2024-07-11
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class RewardOrderBlindBoxExtraInfo extends RewardOrder.ExtraInfo implements Serializable {

    private static final long serialVersionUID = -8463426303280087420L;

    private List<Long> lotteryRecordBids;

    private List<ComposeCostumeInfo> composeCostumeInfos;

    private List<CostumePartInfo> costumePartInfos;

    private List<CollectingCostumeInfo> collectingCostumeInfos;

    private DecomposeInfo decomposeInfo;

    private int roleId;

    @Deprecated
    private CostumeBlindBoxConfig costumeBlindBoxConfig;

    private CostumeBlindBoxConfigV2 costumeBlindBoxConfigV2;

    /**
     * 是否盲盒新用户
     */
    private boolean newBlindBoxUser;

    /** 是否使用装扮盲盒券 */
    private boolean dressUpVoucher;

    /**
     *用户盲盒券bid
     * */
    private Long couponBid;

    @Data
    @Accessors(chain = true)
    public static class CostumePartInfo implements Serializable {

        private int costumePartId;
        private ImageInfo image;
        private String name;
        private int level;
    }

    @Data
    @Accessors(chain = true)
    public static class ComposeCostumeInfo implements Serializable {

        private int id;
        private ImageInfo image;
        private String name;
        private int level;
        private Double hpBonusBuff;
    }

    @Data
    @Accessors(chain = true)
    public static class CostumeBlindBoxConfig implements Serializable {

        private static final long serialVersionUID = -1662205889216102215L;
        /**
         * 1星单品抽取概率
         */
        private double oneStarPartProbability;
        /**
         * 2星单品抽取概率
         */
        private double twoStarPartProbability;

        /**
         * 3星单品抽取概率
         */
        private double threeStarPartProbability;
        /**
         * 4星单品抽取概率
         */
        private double fourStarPartProbability;
        /**
         * 5星单品抽取概率
         */
        private double fiveStarPartProbability;
        /**
         * 未获得单品抽取概率
         */
        private double notAcquiredCostumePartProbability;

        private GearConfig gearConfig;

        private List<LotteryConfig> lotteryConfigs;

        public static CostumeBlindBoxConfig valueOf(com.kuaikan.role.game.api.bean.CostumeBlindBoxConfig config,
                                                    com.kuaikan.role.game.api.bean.CostumeBlindBoxConfig.GearConfig gearConfig,
                                                    List<LotteryConfig> lotteryConfigs) {
            CostumeBlindBoxConfig costumeBlindBoxConfig = new CostumeBlindBoxConfig();
            costumeBlindBoxConfig.setOneStarPartProbability(config.getOneStarPartProbability());
            costumeBlindBoxConfig.setTwoStarPartProbability(config.getTwoStarPartProbability());
            costumeBlindBoxConfig.setThreeStarPartProbability(config.getThreeStarPartProbability());
            costumeBlindBoxConfig.setFourStarPartProbability(config.getFourStarPartProbability());
            costumeBlindBoxConfig.setFiveStarPartProbability(config.getFiveStarPartProbability());
            costumeBlindBoxConfig.setGearConfig(null == gearConfig ? null : GearConfig.valueOf(gearConfig));
            costumeBlindBoxConfig.setNotAcquiredCostumePartProbability(config.getNotAcquiredCostumePartProbability());
            costumeBlindBoxConfig.setLotteryConfigs(lotteryConfigs);
            return costumeBlindBoxConfig;
        }
    }

    @Data
    @Accessors(chain = true)
    public static class CostumeBlindBoxConfigV2 implements Serializable {

        private static final long serialVersionUID = 8338725383145584423L;
        private double oneStarPartProbability;
        private double twoStarPartProbability;
        private double threeStarPartProbability;
        private double fourStarPartProbability;
        private double fiveStarPartProbability;

        /** @see com.kuaikan.role.game.api.enums.CostumeStarLevelType */
        private Integer tenDrawMinimumStar;
        private List<UpCostumeRate> upCostumeRateList;
        private TargetedCostume targetedCostume;
        private double notAcquiredCostumePartProbability;
        private GearConfig gearConfig;
        private String cornerMark;
        private String ruleDescription;

        private List<LotteryConfig> lotteryConfigs;

        public static CostumeBlindBoxConfigV2 valueOf(BlindBoxProbRuleConfig.Config config, BlindBoxProbRuleConfig.GearConfig gearConfig,
                                                      List<LotteryConfig> lotteryConfigs) {
            CostumeBlindBoxConfigV2 costumeBlindBoxConfig = new CostumeBlindBoxConfigV2();
            costumeBlindBoxConfig.setOneStarPartProbability(config.getOneStarPartProbability());
            costumeBlindBoxConfig.setTwoStarPartProbability(config.getTwoStarPartProbability());
            costumeBlindBoxConfig.setThreeStarPartProbability(config.getThreeStarPartProbability());
            costumeBlindBoxConfig.setFourStarPartProbability(config.getFourStarPartProbability());
            costumeBlindBoxConfig.setFiveStarPartProbability(config.getFiveStarPartProbability());
            costumeBlindBoxConfig.setTenDrawMinimumStar(config.getTenDrawMinimumStar());
            List<UpCostumeRate> upCostumeRates = Optional.ofNullable(config.getUpCostumeRateList())
                    .orElse(new ArrayList<>())
                    .stream()
                    .map(UpCostumeRate::valueOf)
                    .collect(Collectors.toList());
            costumeBlindBoxConfig.setUpCostumeRateList(upCostumeRates);
            costumeBlindBoxConfig.setTargetedCostume(TargetedCostume.valueOf(config.getTargetedCostumeOdds()));
            costumeBlindBoxConfig.setNotAcquiredCostumePartProbability(config.getNotAcquiredCostumePartProbability());
            costumeBlindBoxConfig.setGearConfig(null == gearConfig ? null : GearConfig.valueOf(gearConfig));
            costumeBlindBoxConfig.setCornerMark(config.getCornerMark());
            costumeBlindBoxConfig.setRuleDescription(config.getRuleDescription());
            costumeBlindBoxConfig.setLotteryConfigs(lotteryConfigs);
            return costumeBlindBoxConfig;
        }
    }

    @Data
    @Accessors(chain = true)
    public static class UpCostumeRate implements Serializable {

        private static final long serialVersionUID = -1552152501319996750L;
        private int costumeId;
        private int costumeWeight;

        public static UpCostumeRate valueOf(BlindBoxProbRuleConfig.UpCostumeRate config) {
            UpCostumeRate upCostumeRate = new UpCostumeRate();
            upCostumeRate.setCostumeId(config.getCostumeId());
            upCostumeRate.setCostumeWeight(config.getCostumeWeight());
            return upCostumeRate;
        }
    }

    @Data
    @Accessors(chain = true)
    public static class TargetedCostume implements Serializable {

        private static final long serialVersionUID = -6755365407748777883L;
        /** @see com.kuaikan.role.game.api.enums.CostumeStarLevelType */
        private int targetedStar;
        private int targetedWeight;

        public static TargetedCostume valueOf(BlindBoxProbRuleConfig.TargetedCostume config) {
            if (config == null) {
                return null;
            }
            TargetedCostume targetedCostume = new TargetedCostume();
            targetedCostume.setTargetedStar(config.getTargetedStar());
            targetedCostume.setTargetedWeight(config.getTargetedWeight());
            return targetedCostume;
        }
    }

    @Data
    @Accessors(chain = true)
    @Deprecated
    public static class GearConfig implements Serializable {

        private static final long serialVersionUID = 5872680544438578418L;
        private String giftId;
        private int lotteryNum;
        private int amount;

        public static GearConfig valueOf(com.kuaikan.role.game.api.bean.CostumeBlindBoxConfig.GearConfig config) {
            if (config == null) {
                return null;
            }
            GearConfig gearConfig = new GearConfig();
            gearConfig.setAmount(config.getAmount());
            gearConfig.setLotteryNum(config.getLotteryNum());
            gearConfig.setGiftId(config.getGiftId());
            return gearConfig;
        }

        public static GearConfig valueOf(BlindBoxProbRuleConfig.GearConfig config) {
            if (config == null) {
                return null;
            }
            GearConfig gearConfig = new GearConfig();
            gearConfig.setAmount(config.getAmount());
            gearConfig.setLotteryNum(config.getLotteryNum());
            gearConfig.setGiftId(config.getGiftId());
            return gearConfig;
        }
    }

    @Data
    @Accessors
    public static class LotteryConfig implements Serializable {

        private static final long serialVersionUID = -4860245116879955085L;
        @Deprecated
        /**
         * todo 改版后删除这个字段
         */
        private double probability;
        private int level;
        private int costumeId;
        private boolean canLottery;

        public static LotteryConfig valueOf(Costume costume, RoleCostumeRelation.Config config) {
            LotteryConfig lotteryConfig = new LotteryConfig();
            lotteryConfig.setCostumeId(costume.getId());
            lotteryConfig.setLevel(costume.getLevel());
            if (config != null) {
                lotteryConfig.setCanLottery(config.isCanLottery());
                lotteryConfig.setProbability(config.getLotteryProbability());
            }
            return lotteryConfig;
        }

        public static LotteryConfig valueOf(Costume costume, RoleGroupCostumeBlindBoxConfig.Config config) {
            LotteryConfig lotteryConfig = new LotteryConfig();
            lotteryConfig.setCostumeId(costume.getId());
            lotteryConfig.setLevel(costume.getLevel());
            if (config != null) {
                lotteryConfig.setCanLottery(config.isCanLottery());
                lotteryConfig.setProbability(config.getProbability());
            }
            return lotteryConfig;
        }
    }

    @Data
    @Accessors(chain = true)
    public static class CollectingCostumeInfo implements Serializable {

        private int id;
        private String name;
        private int level;
        private ImageInfo image;
        private int ownCount;
        private int needCount;
    }

    @Data
    @Accessors(chain = true)
    public static class DecomposeInfo implements Serializable {

        private int costumePartCount;
        private List<StuffInfo> stuffInfos;
    }

    @Data
    @Accessors(chain = true)
    public static class StuffInfo implements Serializable {

        private int id;
        private String name;
        private ImageInfo image;
        private int count;
    }

}
