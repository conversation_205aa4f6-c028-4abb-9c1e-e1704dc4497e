package com.kuaikan.role.game.api.bean.cardbattle;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import com.kuaikan.common.tools.time.DateUtils;
import com.kuaikan.role.game.api.enums.cardbattle.CardBattleAfkStatusEnum;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleMonsterDTO;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattlePrizeDTO;
import com.kuaikan.role.game.api.rpc.result.cardbattle.CardBattleStoryDTO;

/**
 * 卡牌战斗-挂机任务
 * 索引：userId + activityId + dungeonId
 * <AUTHOR>
 * @date 2025/2/28
 */
@Data
@Accessors(chain = true)
@Document(collection = "card_battle_afk_task")
public class CardBattleAfkTask implements Serializable {

    private static final long serialVersionUID = -4023411845251022358L;

    @Id
    private String id;
    // 用户uid
    private Integer userId;
    // 活动id
    private String activityId;
    // 副本id
    private String dungeonId;
    // 任务状态 {@link com.kuaikan.role.game.api.enums.cardbattle.CardBattleAfkStatusEnum}
    private Integer status;
    // 挂机时间(分钟)
    private Integer afkTime;
    // 挂机点数消耗-通用
    private Integer cost;
    // 挂机点数消耗-羁绊专属
    private Integer bondExclusiveCost;
    // 挂机奖励
    private List<CardBattlePrizeDTO> prizeList;
    // 记忆照片
    private CardBattleStoryDTO photo;
    // 挂机任务选择卡牌列表
    private List<Long> selectUserCardIds;
    // 挂机遇到的战力最大怪物信息
    private List<CardBattleMonsterDTO> monsterList;
    // 挂机开始时间
    private Long startTime;
    // 创建时间
    private Long createdTime;
    // 更新时间
    private Long updatedTime;

    public boolean completedAfkTask() {
        if (!CardBattleAfkStatusEnum.IN_PROGRESS.getCode().equals(status)) {
            return false;
        }
        long remainTime = System.currentTimeMillis() - (startTime + afkTime * DateUtils.MIN_TIME_LENGTH_IN_MILLIS);
        return remainTime >= 0;
    }

    public boolean unCompletedAfkTask() {
        return !completedAfkTask();
    }

    public static CardBattleAfkTask init(Integer userId, String activityId, String dungeonId, Integer status, Integer afkTime, Integer cost,
                                         List<Long> selectUserCardIds, List<CardBattleMonsterDTO> monsterList, List<CardBattlePrizeDTO> prizeList,
                                         CardBattleStoryDTO photo, Integer bondExclusiveCost) {

        CardBattleAfkTask afkTask = new CardBattleAfkTask();
        afkTask.setUserId(userId);
        afkTask.setActivityId(activityId);
        afkTask.setDungeonId(dungeonId);
        afkTask.setStatus(status);
        afkTask.setAfkTime(afkTime);
        afkTask.setCost(cost);
        afkTask.setBondExclusiveCost(bondExclusiveCost);
        afkTask.setSelectUserCardIds(selectUserCardIds);
        afkTask.setMonsterList(monsterList);
        afkTask.setPrizeList(prizeList);
        afkTask.setPhoto(photo);
        long now = System.currentTimeMillis();
        afkTask.setStartTime(now);
        afkTask.setCreatedTime(now);
        afkTask.setUpdatedTime(now);
        return afkTask;
    }
}
