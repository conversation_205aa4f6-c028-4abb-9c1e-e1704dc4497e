package com.kuaikan.role.game.api.model.mq;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.bean.UserAvgChapterRecord;

/**
 * <AUTHOR>
 * @date 2024/8/12
 */
@Data
@Accessors(chain = true)
public class AvgRecordChangeMessage implements Serializable {

    private static final long serialVersionUID = -9073518399742363093L;

    /**
     * 用户id
     */
    private int userId;

    /**
     * avg段落id
     */
    private int chapterId;

    /**
     * 阅读textId数量
     */
    private int readCount;

    /**
     * 最大阅读textId数量
     */
    private int maxReadCount;

    /**
     * 总textId数量
     */
    private int totalTextCount;

    /**
     * 最后阅读的textId
     */
    private String lastTextId;

    /**
     * 最后阅读时间
     */
    private Date lastReadTime;

    /**
     * 已经选择的textId
     */
    private List<String> selectedTextIds;

    /**
     * json字符串{@link CustomParam}
     *
     */
    private String customParam;

    private int nextChapterId;

    @Data
    @Accessors(chain = true)
    public static class CustomParam implements Serializable {

        private static final long serialVersionUID = 1779410918167591684L;

        private String bizId;
    }

    public static AvgRecordChangeMessage valueOf(UserAvgChapterRecord avgChapterRecord) {
        return new AvgRecordChangeMessage().setUserId(avgChapterRecord.getUserId())
                .setChapterId(avgChapterRecord.getChapterId())
                .setReadCount(avgChapterRecord.getReadCount())
                .setMaxReadCount(avgChapterRecord.getMaxReadCount())
                .setTotalTextCount(avgChapterRecord.getTotalTextCount())
                .setLastTextId(avgChapterRecord.getLastTextId())
                .setLastReadTime(avgChapterRecord.getLastReadTime())
                .setSelectedTextIds(avgChapterRecord.getSelectedTextIds());
    }

}
