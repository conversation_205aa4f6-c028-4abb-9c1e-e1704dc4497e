package com.kuaikan.role.game.api.model.map;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 *<AUTHOR>
 *@date 2025/6/5
 */
@Data
@Accessors(chain = true)
public class UnlockMapStoryModel implements Serializable {

    private static final long serialVersionUID = -1249763905796357954L;

    // 通用地图id
    private int mapId;

    // 剧情库id
    private int libraryId;

    // 剧情tag
    private String tag;

    // avg段落id
    private int avgChapterId;

}
