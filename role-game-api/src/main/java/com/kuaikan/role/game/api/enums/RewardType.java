package com.kuaikan.role.game.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 2024-03-19
 */
@AllArgsConstructor
@Getter
public enum RewardType {
    /**
     * 糖果
     */
    CANDY(1, "糖果"),
    /**
     * 道具
     */
    ITEM(2, "道具"),
    /**
     * 装扮
     */
    COSTUME(3, "装扮"),
    /**
     * 场景
     */
    SCENE(4, "场景"),
    /**
     * 补签卡
     */
    SUPPLEMENT(5, "补签卡"),
    /**
     * 银币
     */
    SILVER_COIN(6, "银币"),
    /**
     * 装扮单品
     */
    COSTUME_PART(7, "装扮单品"),
    /**
     * 材料
     */
    STUFF(8, "材料"),
    /**
     * 材料
     */
    ENERGY_BOTTLE(9, "行动力药水"),
    /**
     * 互动道具
     */
    ACTION_ITEM(10, "互动道具"),
    /**
     * 领养券
     */
    ADOPT_COUPON(11, "领养券"),
    /**
     * 羁绊魔药
     */
    EMOTION_BOND_BOTTLE(12, "羁绊魔药"),
    /**
     * 羁绊值
     */
    EMOTION_BOND(13, "羁绊值"),
    /**
     * 食物
     */
    FOOD(14, "食物"),
    /**
     * 互动动作
     */
    INTERACTIVE_ACTION(15, "互动动作"),
    /**
     * 盲盒券
     */
    BLIND_BOX_COUPON(16, "盲盒券"),

    /** 祈愿券 */
    WISH_COUPON(17, "祈愿券"),

    /** KK币 */
    KK_COIN(18, "KK币"),

    /** 漫崽小馒头 */
    SPIRIT_STONE(19, "漫崽小馒头"),

    /** 自选礼包 */
    PRIZE_BAG(20, "自选礼包"),

    /** 通用地图行动力药水 */
    COMMON_MAP_ENERGY_BOTTLE(21, "通用地图行动力药水"),
    ;

    private int code;

    private String desc;

    public static RewardType getByCode(int code) {
        for (RewardType value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
