package com.kuaikan.role.game.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/2/27
 */
@AllArgsConstructor
@Getter
public enum MaterialBizType {

    UNKNOWN(0, "未知"),
    COSTUME(1, "装扮"),
    STORY(2, "剧情"),
    ROLE_UNHEALTHY_STATE(3, "角色不健康状态");

    private final int code;
    private final String desc;

    public static MaterialBizType getByCode(int code) {
        for (MaterialBizType status : MaterialBizType.values()) {
            if (code == status.getCode()) {
                return status;
            }
        }
        return UNKNOWN;
    }

}
