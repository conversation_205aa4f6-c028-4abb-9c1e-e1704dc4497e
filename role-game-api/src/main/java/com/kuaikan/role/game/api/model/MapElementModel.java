package com.kuaikan.role.game.api.model;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.bean.SpineMaterial;
import com.kuaikan.role.game.api.bo.ImageInfo;

/**
 *
 * <AUTHOR>
 * @date 2024/6/25
 */
@Data
@Accessors(chain = true)
public class MapElementModel implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 地图元素id
     */
    private Integer id;

    /**
     * 名称
     */
    private String name;

    /**
     * 位置
     */
    private MapPointModel position;

    /**
     * 元素类型
     */
    private Integer elementTypeId;

    /**
     * 关联建筑实例ID
     */
    private Integer relatedBuildingInstanceId;

    private Integer buildingId;

    /**
     * 特殊建筑类型
     */
    private Integer specialBuildingType;

    /**
     * 图片素材
     */
    private ImageInfo imageInfo;

    /**
     * 骨骼动画文件
     */
    private SpineMaterial spineMaterial;

    /**
     * 缩放比例
     */
    private Double scaleRatio;

    /**
     * 遮挡范围
     */
    private List<List<MapPointModel>> blockAreaList;
    private List<List<MapPointModel>> computedBlockAreaList;

    /**
     * 遮挡关系坐标
     */
    private MapPointModel shelterCalculatePoint;

    /**
     * 日程头像位置
     */
    private List<MapPointModel> scheduleAvatarPosition;

}
