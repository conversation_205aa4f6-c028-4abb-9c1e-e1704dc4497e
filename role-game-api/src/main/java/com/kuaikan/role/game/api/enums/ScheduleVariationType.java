package com.kuaikan.role.game.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 2024-07-08
 */
@Getter
@AllArgsConstructor
public enum ScheduleVariationType {
    ENERGY(1, "行动力", DimensionType.UNKNOWN.getType()),
    MOOD(2, "心情", DimensionType.UNKNOWN.getType()),
    TIREDNESS(3, "疲劳", DimensionType.UNKNOWN.getType()),
    SILVER_COIN(4, "银币", DimensionType.UNKNOWN.getType()),
    CULTURE(5, DimensionType.CULTURE.getDesc(), DimensionType.CULTURE.getType()),
    SPORT(6, DimensionType.SPORT.getDesc(), DimensionType.SPORT.getType()),
    ART(7, DimensionType.ART.getDesc(), DimensionType.ART.getType()),
    SOCIAL(8, DimensionType.SOCIAL.getDesc(), DimensionType.SOCIAL.getType()),
    HOUSEWORK(9, DimensionType.HOUSEWORK.getDesc(), DimensionType.HOUSEWORK.getType())
    ;

    private int type;

    private String desc;

    private int dimensionType;

    public static ScheduleVariationType getByDimensionType(int dimensionType) {
        for (ScheduleVariationType scheduleVariationType : ScheduleVariationType.values()) {
            if (dimensionType == scheduleVariationType.getDimensionType()) {
                return scheduleVariationType;
            }
        }
        return null;
    }
}
