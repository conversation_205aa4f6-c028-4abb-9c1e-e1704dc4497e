package com.kuaikan.role.game.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 2024-04-23
 */

@Getter
@AllArgsConstructor
public enum UserCostumeStatus {
    /**
     * 已获得使用中
     */
    ACQUIRED_USING(1, "已获得使用中", "使用中", ""),
    /**
     * 已获得未使用
     */
    ACQUIRED_NOT_USE(2, "已获得未使用", "已获得", ""),
    /**
     * 未获得未收集
     */
    NOT_ACQUIRED_NOT_COLLECT(3, "未获得未收集", "未收集", "有%d个单品未集齐"),
    /**
     * 未获得收集中
     */
    NOT_ACQUIRED_COLLECTING(4, "未获得收集中", "%d/%d", "有%d个单品未集齐");

    private int code;

    private String desc;

    private String text;
    private String previewText;

    public static UserCostumeStatus getByOwnedAndEffective(boolean hasOwned, boolean effective, int costumePartNum) {
        if (hasOwned && effective) {
            return ACQUIRED_USING;
        } else if (hasOwned) {
            return ACQUIRED_NOT_USE;
        } else if (costumePartNum == 0) {
            return NOT_ACQUIRED_NOT_COLLECT;
        } else {
            return NOT_ACQUIRED_COLLECTING;
        }
    }
}
