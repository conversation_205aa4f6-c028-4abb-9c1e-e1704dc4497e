package com.kuaikan.role.game.api.model;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/12/30 19:06
 */

@Data
@Accessors(chain = true)
public class BlindBoxCouponImageUrlModel implements Serializable {

    private static final long serialVersionUID = 7349889889837225799L;

    // 通用盲盒券
    private String commonUrl;

    // 限定盲盒券
    private List<RoleGroupImage> specificUrls;

    @Data
    @Accessors(chain = true)
    public static class RoleGroupImage implements Serializable {

        private static final long serialVersionUID = -6993392789806302697L;
        private Integer groupId;
        private String imageUrl;
    }
}
