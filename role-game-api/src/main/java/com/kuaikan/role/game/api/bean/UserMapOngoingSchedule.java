package com.kuaikan.role.game.api.bean;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class UserMapOngoingSchedule implements Serializable {
    private static final long serialVersionUID = 1L;

    private int id;
    private int userId;
    private int mapId;
    private int buildingId;
    private int scheduleId;
    private long startTime;
    private long endTime;
    private CommonScheduleConsumptionSnapshot consumptionSnapshot;
    private CommonScheduleSettlementSnapshot settlementSnapshot;
    private ExtraInfo extraInfo;   
    private Date createdAt;
    private Date updatedAt;

    @Data
    @Accessors(chain = true)
    public static class ExtraInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        
    }
}
