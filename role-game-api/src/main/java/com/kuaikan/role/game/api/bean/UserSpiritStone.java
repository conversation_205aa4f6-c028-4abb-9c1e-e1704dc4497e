package com.kuaikan.role.game.api.bean;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 *<AUTHOR>
 *@date 2025/3/11
 */
@Data
@Accessors(chain = true)
public class UserSpiritStone implements Serializable {

    private static final long serialVersionUID = 1671533398154993506L;

    private long id;

    /**
     * 用户id
     */
    private long userId;

    /**
     * 角色id
     */
    private int roleId;

    /**
     * 之前账户余额
     */
    private int balance;

    /**
     * 是否有小红点(漫灵石足够领养角色时展示，默认设置为true)
     */
    private boolean showRedDot;

    private Date createdAt;

    private Date updatedAt;
}
