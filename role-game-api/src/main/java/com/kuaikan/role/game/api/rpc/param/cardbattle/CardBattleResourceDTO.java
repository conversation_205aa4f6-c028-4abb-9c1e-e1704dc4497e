package com.kuaikan.role.game.api.rpc.param.cardbattle;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 卡牌战斗-入口资源信息
 *
 * <AUTHOR>
 * @date 2024/4/28
 */
@Data
@Accessors(chain = true)
public class CardBattleResourceDTO implements Serializable {

    private static final long serialVersionUID = -2834223804480294379L;

    /**
     * 探索背景图片
     */
    private List<String> exploreBgImgList;
    private String battleBgImg;

}

