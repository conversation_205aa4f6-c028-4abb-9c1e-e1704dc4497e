package com.kuaikan.role.game.api.model;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.bo.ImageInfo;

@Data
@Accessors
public class UserCouponModel implements Serializable {
    private static final long serialVersionUID = 4869605612267329047L;
    //id
    private int id;
    //名称
    private String name;
    //角色组id
    private int roleGroupId;
    //折扣力度
    private String discountRate;
    //数量
    private int balance;
    //角色
    private List<RoleModel> roleList;

    private Long fetchTime;

    public static UserCouponModel valueOf(AdoptCouponModel.CouponModel couponModel) {
        if(couponModel == null){
            return null;
        }
        UserCouponModel userCouponModel = new UserCouponModel();
        int roleGroupId = couponModel.getRoleGroupsModel().getId();
        userCouponModel.setRoleGroupId(roleGroupId);
        userCouponModel.setFetchTime(new Date().getTime());
        String discountRate = couponModel.getRoleAdoptCoupon().getDiscountRate();
        userCouponModel.setDiscountRate(discountRate);
        int id = (roleGroupId << 3) | (int)(Double.parseDouble(discountRate) * 10);
        userCouponModel.setId(id);
        userCouponModel.setBalance(1);
        List<AdoptCouponModel.RoleModel> roleModels = couponModel.getRoleGroupsModel().getRoleList();
        userCouponModel.setRoleList(roleModels.stream().map(RoleModel::valueOf).collect(Collectors.toList()));
        return userCouponModel;
    }

    @Data
    @Accessors(chain = true)
    public static class RoleModel implements Serializable {
        private static final long serialVersionUID = -1101966858331275792L;
        /**
         * 角色id
         */
        private int id;
        /**
         * 角色名字
         */
        private String name;

        private ImageInfo image;

        public static RoleModel valueOf(AdoptCouponModel.RoleModel roleModel){
            if(roleModel == null){
                return null;
            }
            RoleModel model = new RoleModel();
            model.setId(roleModel.getId());
            model.setName(roleModel.getName());
            model.setImage(roleModel.getImage());
            return model;
        }
    }
}
