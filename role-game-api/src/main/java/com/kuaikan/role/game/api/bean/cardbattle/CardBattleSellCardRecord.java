package com.kuaikan.role.game.api.bean.cardbattle;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 卡牌战斗用户售卡记录
 *
 * <AUTHOR>
 * @date 2024/6/24
 */
@Data
@Accessors(chain = true)
@Document(collection = "card_battle_sell_card_record")
public class CardBattleSellCardRecord implements Serializable {

    private static final long serialVersionUID = 7503140766342498256L;

    @Id
    private String id;
    //uid
    private Long userId;
    // 订单ID
    private String orderId;
    // 用户卡ID
    private List<Long> userCardIds;
    // 售卖价值
    private Integer sellValue;
    // 售卖时间
    private Long sellTime;
    // 创建时间
    private Long createdTime;
    // 更新时间
    private Long updatedTime;

    public static CardBattleSellCardRecord init(Long userId, String orderId, List<Long> userCardIds, Integer sellValue) {
        long now = System.currentTimeMillis();
        return new CardBattleSellCardRecord().setUserId(userId)
                .setOrderId(orderId)
                .setUserCardIds(userCardIds)
                .setSellValue(sellValue)
                .setSellTime(now)
                .setCreatedTime(now)
                .setUpdatedTime(now);
    }
}
