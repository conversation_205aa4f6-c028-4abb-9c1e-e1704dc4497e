package com.kuaikan.role.game.api.rpc.result;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.bean.Role;

/**
 * <AUTHOR>
 * @date 2025/3/24 14:34
 */

@Data
@Accessors(chain = true)
public class AdoptAnimationModel implements Serializable {

    private static final long serialVersionUID = 7715222526155870096L;

    private String greetingText;
    private String expectedCpText;
    private List<String> entranceAnimation;
    private List<String> greetingAnimation;
    private List<String> expectedCpAnimation;

    public static AdoptAnimationModel valueOf(Role.Config.AdoptAnimation adoptAnimation) {
        if (adoptAnimation == null) {
            return null;
        }
        return new AdoptAnimationModel().setGreetingText(adoptAnimation.getGreetingText())
                .setExpectedCpText(adoptAnimation.getExpectedCpText())
                .setEntranceAnimation(adoptAnimation.getEntranceAnimation())
                .setGreetingAnimation(adoptAnimation.getGreetingAnimation())
                .setExpectedCpAnimation(adoptAnimation.getExpectedCpAnimation());
    }
}
