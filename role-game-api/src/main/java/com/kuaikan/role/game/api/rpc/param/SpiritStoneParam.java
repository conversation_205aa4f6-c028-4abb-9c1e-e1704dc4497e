package com.kuaikan.role.game.api.rpc.param;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.enums.SpiritStoneEnum;

/**
 * <AUTHOR>
 * @date 2025/3/17 19:52
 */

@Data
@Accessors(chain = true)
public class SpiritStoneParam implements Serializable {

    private static final long serialVersionUID = -8893067898996850310L;

    private int userId;
    private int amount;
    private int roleId;
    /** @see SpiritStoneEnum */
    private int source;
    private String orderId;
    private int spiritStoneId;
}
