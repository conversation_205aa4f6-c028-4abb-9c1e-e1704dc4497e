package com.kuaikan.role.game.api.bean;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

import org.springframework.data.mongodb.core.mapping.Document;

import com.kuaikan.role.game.api.model.StoryLetterContent;

/**
 * <AUTHOR>
 * @version 2024-03-25
 */
@Data
@Accessors(chain = true)
@Document(collection = "story_letter")
public class StoryLetter implements Serializable {

    private static final long serialVersionUID = -5306151167308615808L;
    private StoryLetterContent letterContent;

    private int storyId;

    private String materialMd5;

    private long createdAt;

    private long updatedAt;

}
