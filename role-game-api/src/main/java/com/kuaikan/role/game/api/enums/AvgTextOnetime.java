package com.kuaikan.role.game.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 * <AUTHOR>
 * @since 2024-03-05
 */
@Getter
@AllArgsConstructor
public enum AvgTextOnetime {

    NOT_ONETIME(0, "非一次性"),
    ONETIME(1, "一次性");

    private final int code;
    private final String desc;

    public static AvgTextOnetime getByCode(int code) {
        for (AvgTextOnetime status : AvgTextOnetime.values()) {
            if (code == status.getCode()) {
                return status;
            }
        }
        return null;
    }
}
