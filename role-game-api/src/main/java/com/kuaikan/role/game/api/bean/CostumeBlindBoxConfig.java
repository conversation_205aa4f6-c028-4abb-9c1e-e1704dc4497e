package com.kuaikan.role.game.api.bean;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.rpc.param.CostumeBlindBoxConfigParam;

/**
 * <AUTHOR>
 * @version 2024-04-22
 */
@Data
@Accessors(chain = true)
public class CostumeBlindBoxConfig implements Serializable {

    private static final long serialVersionUID = 2265063245740935292L;

    public static final double MAX_PROBABILITY = 100d;
    /**
     * 1星单品抽取概率
     */
    private double oneStarPartProbability;
    /**
     * 2星单品抽取概率
     */
    private double twoStarPartProbability;
    /**
     * 3星单品抽取概率
     */
    private double threeStarPartProbability;
    /**
     * 4星单品抽取概率
     */
    private double fourStarPartProbability;
    /**
     * 5星单品抽取概率
     */
    private double fiveStarPartProbability;
    /**
     * 未获得单品抽取概率
     */
    private double notAcquiredCostumePartProbability;

    private List<GearConfig> gearConfigs;
    /**
     * 角标文字
     */
    private String cornerMark;
    /**
     * 规则文案
     */
    private String ruleDescription;

    @Data
    @Accessors(chain = true)
    public static class GearConfig implements Serializable {

        private String giftId;
        private int lotteryNum;
        private int amount;

        public static GearConfig valueOf(CostumeBlindBoxConfigParam.GearConfig gearConfig) {
            if (gearConfig == null) {
                return null;
            }
            return new GearConfig().setGiftId(gearConfig.getGiftId()).setLotteryNum(gearConfig.getLotteryNum()).setAmount(gearConfig.getAmount());
        }
    }
}
