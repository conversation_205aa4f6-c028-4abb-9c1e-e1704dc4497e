package com.kuaikan.role.game.api.rpc.param.cardbattle;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.kuaikan.role.game.api.bean.cardbattle.BattleDungeonConfig;
import com.kuaikan.role.game.api.rpc.result.cardbattle.CardBattleAfkTaskDTO;
import com.kuaikan.role.game.api.rpc.result.cardbattle.CardBattleStoryDTO;

/**
 * 副本展示信息
 *
 * <AUTHOR>
 * @date 2024/6/14
 */
@Data
@NoArgsConstructor
public class CardBattleDungeonInfoDTO implements Serializable {

    private static final long serialVersionUID = 3913251077469938021L;

    /**
     * 探索活动id, 兼容旧数据，暂时保留
     */
    @Deprecated
    private String battleActivityId;

    private String dungeonId;

    /**
     * 探索标题
     */
    private String title;

    /**
     * 探索描述(副标题)
     */
    private String desc;

    /**
     * 探索图标
     */
    private String icon;

    /**
     * 探索banner
     */
    private String banner;

    /**
     * 总探索次数
     */
    private Integer totalTaskCnt;

    /**
     * 探索进度，
     * 当completedTaskCnt == totalTaskCnt时，如果最后一关是探索关卡且未完成
     * 则completedTaskCnt = totalTaskCnt - 1
     */
    private Integer completedTaskCnt;

    /**
     * 是否已领取奖励
     */
    private boolean prizeReceived;

    /**
     * 探索完成奖励
     */
    private List<CardBattlePrizeDTO> completedPrizes;

    /**
     * 可能获取的奖励（仅道具）
     */
    private List<CardBattlePrizeDTO> possiblePrizes;

    /**
     * 可能获取的奖励（卡牌）
     */
    private List<CardBattleCardBasicInfoDTO> possibleCardPrizes;

    /**
     * 有效期，剩余时间
     */
    private long remainingTime;

    // 是否解锁
    private Boolean unlock;
    // 羁绊副本 - 解锁要求羁绊等级
    private Integer unlockBondLevel;
    // 羁绊副本 - 记忆照片
    private CardBattleStoryDTO photo;
    // 挂机任务
    private CardBattleAfkTaskDTO afkTaskDTO;

    public static CardBattleDungeonInfoDTO initFromActivity(BattleDungeonConfig activity) {
        CardBattleDungeonInfoDTO activityInfo = new CardBattleDungeonInfoDTO();
        activityInfo.setBattleActivityId(activity.getId());
        activityInfo.setDungeonId(activity.getId());
        activityInfo.setTitle(activity.getName());
        activityInfo.setDesc(activity.getDesc());
        activityInfo.setTotalTaskCnt(activity.getTaskCnt());
        activityInfo.setCompletedPrizes(new ArrayList<>());
        activityInfo.setPossiblePrizes(new ArrayList<>());
        activityInfo.setPossibleCardPrizes(new ArrayList<>());
        if (activity.getEndTime() != null && activity.getEndTime() > System.currentTimeMillis()) {
            activityInfo.setRemainingTime(activity.getEndTime() - System.currentTimeMillis());
        }
        return activityInfo;
    }

}
