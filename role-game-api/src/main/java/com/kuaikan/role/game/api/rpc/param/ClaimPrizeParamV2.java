package com.kuaikan.role.game.api.rpc.param;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.common.bean.UserAgent;
import com.kuaikan.role.game.api.enums.PrizeSourceType;

@Data
@Accessors(chain = true)
public class ClaimPrizeParamV2 implements Serializable {
    private static final long serialVersionUID = 1745172389942127710L;
    private int userId;

    private String xDevice;

    private UserAgent userAgent;
    /**
     * 奖品id和数量
     */
    private Map<Long,Integer> prizeCountMap;

    private PrizeSourceType prizeSourceType;

}
