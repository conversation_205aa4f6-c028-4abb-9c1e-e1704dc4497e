package com.kuaikan.role.game.api.rpc.param;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/12/5 10:20
 */
@Data
@Accessors(chain = true)
public class UserCostumePartAcquiredInfo implements Serializable {

    private static final long serialVersionUID = 1032203476836213545L;

    /**
     * 装扮单品是否来自抽盲盒(0,4,5)
     */
    private boolean blindBox;

    /**
     * 是否盲盒新用户
     */
    private boolean newBlindBoxUser;
    /**
     * 活动ID,
     * 非活动为空
     */
    private String activityId;

    /**
     * 是否使用盲盒券
     */
    private boolean dressUpVoucher;
}
