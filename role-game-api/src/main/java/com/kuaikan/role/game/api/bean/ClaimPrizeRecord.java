package com.kuaikan.role.game.api.bean;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.common.tools.serialize.JsonUtils;
import com.kuaikan.role.game.api.model.ClaimPrizeRecordModel;

/**
 * <AUTHOR>
 * @version 2024-03-27
 */
@Data
@Accessors(chain = true)
public class ClaimPrizeRecord implements Serializable {

    private static final long serialVersionUID = 9047329079819263431L;

    private Integer id;
    /**
     * 唯一 id
     */
    private Long bid;
    /**
     * 来源枚举
     * @see com.kuaikan.role.game.api.enums.PrizeSourceType
     */
    private String source;

    private Long prizeId;

    private Integer status;

    private Integer userId;

    private String extraInfo;

    private Date createdAt;

    private Date updatedAt;

    @Data
    @Accessors(chain = true)
    public static class ExtraInfo implements Serializable {

        private static final long serialVersionUID = -4026783715572453138L;
        private ObtainPrizeInParam inParam;

        private Object outParam;

    }

    public static ClaimPrizeRecord valueOf(ClaimPrizeRecordModel claimPrizeRecordModel) {
        if (claimPrizeRecordModel == null) {
            return null;
        }
        ClaimPrizeRecord claimPrizeRecord = new ClaimPrizeRecord().setId(claimPrizeRecordModel.getId())
                .setBid(claimPrizeRecordModel.getBid())
                .setSource(claimPrizeRecordModel.getSource())
                .setPrizeId(claimPrizeRecordModel.getPrizeId())
                .setUserId(claimPrizeRecordModel.getUserId());
        claimPrizeRecord.setExtraInfo(JsonUtils.toJson(claimPrizeRecordModel.getExtraInfo()));
        return claimPrizeRecord;
    }

}
