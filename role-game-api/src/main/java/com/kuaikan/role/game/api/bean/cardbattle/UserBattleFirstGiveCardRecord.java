package com.kuaikan.role.game.api.bean.cardbattle;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import com.kuaikan.role.game.api.enums.cardbattle.CardBattleCardStatusEnum;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleCardInfoDTO;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleUserCardDTO;

/**
 * 卡牌战斗用户首次赠卡记录 唯一索引：userId + cardId
 *
 * <AUTHOR>
 * @date 2024/5/14
 */
@Data
@Accessors(chain = true)
@Document(collection = "user_battle_first_give_card_record")
public class UserBattleFirstGiveCardRecord implements Serializable {

    private static final long serialVersionUID = -2038242672874453996L;

    @Id
    private String id;
    // 新版用户卡牌ID
    private Long userCardId;
    // 卡牌id
    private String cardId;
    // 用户id
    private Long userId;
    // 活动id
    private String battleActivityId;
    // 首次送卡时间
    private Long giveCardTime;
    // 赠送状态 {@link CardBattleCardStatusEnum}
    private Integer status;
    // 创建时间
    private Long createdTime;
    // 更新时间
    private Long updatedTime;

    public static UserBattleFirstGiveCardRecord parseByCardBattleCardInfoDto(CardBattleCardInfoDTO cardInfoDto, Long userId, String battleActivityId,
                                                                             long giveCardTime, Long userCardId) {
        return new UserBattleFirstGiveCardRecord().setUserCardId(userCardId)
                .setCardId(cardInfoDto.getId())
                .setUserId(userId)
                .setBattleActivityId(battleActivityId)
                .setStatus(CardBattleCardStatusEnum.NORMAL.getCode())
                .setGiveCardTime(giveCardTime)
                .setCreatedTime(giveCardTime)
                .setUpdatedTime(giveCardTime);
    }

    public static UserBattleFirstGiveCardRecord parseByUserCardDto(CardBattleUserCardDTO userCardDto, Long userId, String battleActivityId, long giveCardTime,
                                                                   Long userCardId) {
        return new UserBattleFirstGiveCardRecord().setUserCardId(userCardId)
                .setCardId(userCardDto.getCardId())
                .setUserId(userId)
                .setBattleActivityId(battleActivityId)
                .setStatus(CardBattleCardStatusEnum.NORMAL.getCode())
                .setGiveCardTime(giveCardTime)
                .setCreatedTime(giveCardTime)
                .setUpdatedTime(giveCardTime);
    }
}
