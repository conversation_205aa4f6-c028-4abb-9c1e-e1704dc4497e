package com.kuaikan.role.game.api.bean;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 2024-04-22
 */
@Data
@Accessors(chain = true)
public class UserStuff implements Serializable {

    private long id;

    private int userId;

    private int stuffId;

    private int balance;

    private Date createdAt;

    private Date updatedAt;

}
