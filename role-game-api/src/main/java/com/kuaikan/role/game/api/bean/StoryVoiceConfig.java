package com.kuaikan.role.game.api.bean;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.bo.ImageInfo;

/**
 * <AUTHOR>
 * @version 2024-10-30
 */
@Data
@Accessors(chain = true)
public class StoryVoiceConfig implements Serializable, StoryConfig {

    private static final long serialVersionUID = -5742254009645156264L;

    private String configFileKey;

    private String configFileName;

    private Integer unlockingConditions;

    /**
     * 封面资源
     */
    private ImageInfo coverImageKey;

    /**
     * 背景资源
     */
    private String backgroundImageKey;

    /**
     * 试听段落音频文件key
     */
    private String auditionAudioFileKey;

    /**
     * 试听时长（单位：秒，5~30s）
     */
    private Double auditionDuration;

    /**
     * 剧情简介（20字以内）
     */
    private String brief;

    /**
     * 展示类型
     */
    private Integer displayType;

}
