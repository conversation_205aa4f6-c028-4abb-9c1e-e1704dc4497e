package com.kuaikan.role.game.api.service;

import com.kuaikan.common.model.RpcResult;
import com.kuaikan.role.game.api.model.AdoptCouponModel;
import com.kuaikan.role.game.api.model.BlindBoxCouponModel;

/**
 * 券类服务
 *
 * <AUTHOR>
 * @since 2024/10/28
 */
public interface CouponService {

    /**
     * 角色组领养折扣券列表查询
     *
     * @param userId 用户id
     * @return
     */
    RpcResult<AdoptCouponModel> queryAdoptDiscountCouponList(int userId);

    /** 用户装扮盲盒券列表查询 */
    RpcResult<BlindBoxCouponModel> queryBlindBoxCouponList(int userId);
}
