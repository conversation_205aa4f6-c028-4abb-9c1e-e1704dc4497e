package com.kuaikan.role.game.api.rpc.result.cardbattle;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

import com.fasterxml.jackson.annotation.JsonIgnore;

import com.kuaikan.common.ResponseCodeMsg;

/**
 *<AUTHOR>
 *@date 2025/3/25
 */
@Data
@Accessors(chain = true)
public class ActivityLotteryPrizeDTO implements Serializable {

    private static final long serialVersionUID = 6100447586804731267L;

    @JsonIgnore
    private ResponseCodeMsg codeMsg;

    // 抽奖名称
    private String lotteryName;

    // 收集物名称
    private String collectPrizeName;

    private String collectPrizeImg;

    // 当前收集物数量（抽奖活动玩法，为金币数量）
    private Long collectCount;
    // 当前消耗收集物数量（仅限抽奖活动玩法）
    private Integer consumeCount;

    private Long lotteryRemainingTime;

    // 背景图
    private String backgroundImg;

    // 获奖播报列表
    private List<BroadcastDTO> broadcastList;

    private List<StagePrizeInfoDTO> stagePrizeInfo;

    @Data
    public static class BroadcastDTO implements Serializable {

        private static final long serialVersionUID = 7845601724168843877L;
        // 获奖玩家
        private String playerName;
        // 玩家头像
        private String playerAvatar;
        // 获奖物品
        private String prizeName;
        // 获奖数量
        private Integer prizeCount;
    }

    @Data
    public static class StagePrizeInfoDTO implements Serializable {

        private static final long serialVersionUID = 2971064516202077721L;
        // 阶段
        private Integer stage;

        // 每次抽奖消耗金币数量
        private Integer cost;

        // 是否解锁
        private Boolean unlock;

        // 本阶段抽奖解锁条件1(收集物消耗数量)
        private Integer unlockConditionNum;
        // 本阶段抽奖解锁条件2(获取对应奖励id)
        private ActivityLotteryStagePrizeDTO unlockConditionPrize;

        // 奖励列表
        private List<ActivityLotteryStagePrizeDTO> prizeList;

    }

}
