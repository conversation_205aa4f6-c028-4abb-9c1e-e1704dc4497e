package com.kuaikan.role.game.api.rpc.param.cardbattle;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

import com.fasterxml.jackson.annotation.JsonIgnore;

import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleFeedProp;

/**
 * 卡牌战斗充值订单信息
 * <AUTHOR>
 * @date 2024/10/14
 */
@Data
@Accessors(chain = true)
public class CardBattleCreatRechargeOrderDTO implements Serializable {

    private static final long serialVersionUID = -2792684086451389979L;

    @JsonIgnore
    private ResponseCodeMsg codeMsg;
    // 订单号
    private String orderId;
    // 订单状态 {@link com.kuaikan.role.game.api.enums.RewardOrderStatus}
    private Integer status;
    // 道具信息
    private CardBattleFeedProp cardBattleFeedProp;

    public static CardBattleCreatRechargeOrderDTO init(ResponseCodeMsg codeMsg) {
        return init(codeMsg, null, null, null);
    }

    public static CardBattleCreatRechargeOrderDTO init(ResponseCodeMsg codeMsg, String orderId, Integer status, CardBattleFeedProp cardBattleFeedProp) {
        return new CardBattleCreatRechargeOrderDTO().setCodeMsg(codeMsg).setOrderId(orderId).setStatus(status).setCardBattleFeedProp(cardBattleFeedProp);
    }
}
