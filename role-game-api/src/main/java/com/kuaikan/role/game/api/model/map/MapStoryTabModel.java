package com.kuaikan.role.game.api.model.map;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 *<AUTHOR>
 *@date 2025/6/5
 */
@Data
@Accessors(chain = true)
public class MapStoryTabModel implements Serializable {

    private static final long serialVersionUID = -7431315413926624291L;
    private String tabName;

    private int totalStoryCnt;

    private int userStoryCnt;

    private boolean showRedDot;

    private boolean newRedDot;

    List<MapStoryModel> storyList;
}
