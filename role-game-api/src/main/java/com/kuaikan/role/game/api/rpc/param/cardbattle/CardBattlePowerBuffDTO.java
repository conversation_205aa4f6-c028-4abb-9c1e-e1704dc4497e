package com.kuaikan.role.game.api.rpc.param.cardbattle;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 卡牌战斗-战力加成信息
 * <AUTHOR>
 * @date 2024/9/26
 */
@Data
@Accessors(chain = true)
public class CardBattlePowerBuffDTO implements Serializable {

    private static final long serialVersionUID = 4622126724213707794L;
    // 卡牌属性加成
    private Double cardAttrBuff;
    // 角色等级加成
    private Double roleLevelBuff;
    private Double roleLevelBuffRight;
    // 装扮等级
    private Integer roleCostumeLevel;
    private Integer roleCostumeLevelRight;
    // 装扮等级加成
    private Double roleCostumeLevelBuff;
    private Double roleCostumeLevelBuffRight;
    // 卡组生命力加成
    private Double cardGroupHpBuff;
    // 限定卡池攻击加成
    private Double limitCardPoolAtkBuff;
    // 限定专题攻击加成
    private Double limitTopicAtkBuff;
    private List<String> limitTopicNames;
}
