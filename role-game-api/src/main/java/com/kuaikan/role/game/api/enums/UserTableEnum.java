package com.kuaikan.role.game.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import com.kuaikan.common.config.Environment;
import com.kuaikan.common.config.Settings;

/**
 * UserTableEnum
 *
 * <AUTHOR>
 * @since 2024-03-06
 */
@Getter
@AllArgsConstructor
public enum UserTableEnum {

    USER_STORY(4, "user_story_", 256),
    CLAIM_PRIZE_RECORD(5, "claim_prize_record_", 256),
    USER_ROLE(6, "user_role_", 256),
    USER_SCENE(7, "user_scene_", 256),
    USER_COSTUME(8, "user_costume_", 256),
    USER_STUFF(9, "user_stuff_", 256),
    USER_STUFF_FLOW(10, "user_stuff_flow_", 256),
    USER_COSTUME_PART_COMPOSE_ORDER(11, "user_costume_part_compose_order_", 256),
    USER_COSTUME_PART_DECOMPOSE_ORDER(12, "user_costume_part_decompose_order_", 256),
    USER_COSTUME_PART(13, "user_costume_part_", 256),
    SILVER_COIN_DROP_RECORD(9, "silver_coin_drop_record_", 256),
    SILVER_COIN_ASSIGN_RECORD(10, "silver_coin_assign_record_", 256),
    SILVER_COIN_ACCOUNT_FLOW(11, "silver_coin_account_flow_", 256),
    USER_COSTUME_PART_FLOW(14, "user_costume_part_flow_", 256),
    COSTUME_BLIND_BOX_LOTTERY_RECORD(15, "costume_blind_box_lottery_record_", 256),
    REWARD_ORDER(16, "reward_order_", 256),
    USER_ROLE_FINISH_SCHEDULE(17, "user_role_finish_schedule_", 256),
    RED_DOT_USER_RECORD(18, "red_dot_user_record_", 256),
    USER_CITY_UPGRADE_RECORD(19, "user_city_upgrade_record_", 256),
    USER_ROLE_TIREDNESS_FLOW(20, "user_role_tiredness_flow_", 256),
    USER_ROLE_MOOD_FLOW(21, "user_role_mood_flow_", 256),
    USER_ROLE_ENERGY_FLOW(22, "user_role_energy_flow_", 256),
    USER_ITEM_FLOW(23, "user_item_flow_", 256),
    USER_ADOPT_COUPON_RECORD(24, "user_adopt_coupon_record_", 256),
    USER_INTERACTIVE_ITEM_FLOW(25, "user_interactive_item_flow_", 256),
    USER_ROLE_GROUP_EMOTION_BOND(26, "user_role_group_emotion_bond_", 256),
    USER_FOOD_FLOW(27, "user_food_flow_", 256),
    COSTUME_BLIND_BOX_ACTIVITY_REWARD_RECORD(28, "costume_blind_box_activity_reward_record_", 256),
    USER_ROLE_GROUP(29, "user_role_group_", 256),
    USER_EMOTION_BOND_RECORD(30, "user_emotion_bond_record_", 256),
    USER_BLIND_BOX_COUPON_RECORD(31, "user_blind_box_coupon_record_", 256),
    USER_BLIND_BOX_TARGET_AWARD(32, "user_blind_box_target_award_", 256),
    USER_BLIND_BOX_TARGET_AWARD_RECORD(33, "user_blind_box_target_award_record_", 256),
    USER_SPIRIT_STONE(34, "user_spirit_stone_", 256),
    USER_SPIRIT_STONE_FLOW(35, "user_spirit_stone_flow_", 256),
    USER_MAP(36, "user_map_", 256),
    USER_MAP_FINISH_SCHEDULE(37, "user_map_finish_schedule_", 256),
    USER_MAP_ENERGY_FLOW(38, "user_map_energy_flow_", 256),
    USER_MAP_STORY_FLOW(39, "user_map_story_flow_", 256),
    USER_MAP_STORY(40, "user_map_story_", 256),
    ;

    int code;
    String prefix;
    int count;

    public String getTableName(int userId) {
        if (userId < 0) {
            throw new IllegalArgumentException(String.format("illegal uid %s", userId));
        }
        // 非线上环境分表为2个
        if (Settings.getEnvironment().le(Environment.PREVIEW)) {
            return String.format("%s%s", prefix, (userId % 2));
        }
        return String.format("%s%s", prefix, (userId % count));
    }
}
