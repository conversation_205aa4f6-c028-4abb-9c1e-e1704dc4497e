package com.kuaikan.role.game.api.rpc.param.cardbattle;

import lombok.Data;

/**
 * 卡牌专题 查询
 *
 * <AUTHOR>
 * @date 2024/7/12
 */
@Data
public class CardIdQueryParam implements java.io.Serializable {

    private static final long serialVersionUID = -2571856111499828753L;
    // 爱心卡id or 特典卡id
    private String cardId;

    // 卡片类型1：爱心卡，2：特典卡 , @see CardBattleCardSourceEnum
    private int cardType;

}
