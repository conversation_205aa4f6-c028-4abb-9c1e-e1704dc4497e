package com.kuaikan.role.game.api.model;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.bo.ImageInfo;

/**
 * <AUTHOR>
 * @version 2024-06-21
 */
@Data
@Accessors(chain = true)
public class ScheduleListRoleModel implements Serializable {

    private static final long serialVersionUID = -991038793932349575L;

    private List<RoleModel> roles;

    @Data
    @Accessors(chain = true)
    public static class RoleModel implements Serializable {

        private static final long serialVersionUID = -4199968500361170424L;

        private int id;

        private String name;

        private ImageInfo avatar;

        private int level;

        private int mood;

        private String moodDesc;

        private int moodType;

        private int tiredness;

        private String tirednessDesc;

        private int tirednessType;

        private int energy;

        private int artLevel;

        private int houseworkLevel;

        private int sportLevel;

        private int cultureLevel;

        private int socialLevel;

        private long adoptionTime;
        private int orderNum;


    }
}
