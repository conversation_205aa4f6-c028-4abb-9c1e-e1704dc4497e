package com.kuaikan.role.game.api.rpc.result.cardbattle;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.experimental.Accessors;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import com.kuaikan.role.game.api.bean.InteractiveAction;
import com.kuaikan.role.game.api.bo.ImageInfo;
import com.kuaikan.role.game.api.enums.StoryType;
import com.kuaikan.role.game.api.model.AvgConfigModel;
import com.kuaikan.role.game.api.model.CostumePartModel;
import com.kuaikan.role.game.api.model.EmotionBondLevelUpModel;
import com.kuaikan.role.game.api.model.InteractiveActionDetailListModel;
import com.kuaikan.role.game.api.model.PrizeSimpleModel;
import com.kuaikan.role.game.api.model.UserStoryModel;
import com.kuaikan.role.game.api.model.avg.StoryPhotoConfigModel;

/**
 *<AUTHOR>
 *@date 2024/12/11
 */
@Data
@Accessors(chain = true)
public class CardBattleEmotionBondLevelUpModel implements Serializable {

    private static final long serialVersionUID = -2827778435576176450L;

    /** 本次获得的exp*/
    private int obtainExp;
    /** 是否升级*/
    private boolean levelUp;
    /** 当前等级*/
    private int currentLevel;
    /** 升级后的exp **/
    private int currentExp;
    /** 升级前等级*/
    private int oldLevel;
    /** 升级前经验值*/
    private int oldExp;
    /** 升级前对应的下个等级经验值*/
    private int oldNextLevelExp;
    /** 升到下个等级总共的exp*/
    private int nextLevelExp;
    private List<InteractActionDetailVO> unlockAction;
    private int actionAddCount;
    private Integer levelActionCount;
    private List<CostumeSimpleItem> costumePart;

    /**
     * key是对应等级，value是对应等级解锁的剧情
     */
    private Map<Integer, StorySimpleVO> unlockStoryModels;
    /** 升级奖励，key是对应等级，value该等级对应的奖励*/
    private Map<Integer, List<PrizeSimpleModel>> levelUpPrizes;

    public static CardBattleEmotionBondLevelUpModel from(EmotionBondLevelUpModel resource, String domain) {
        if (resource == null) {
            return null;
        }
        CardBattleEmotionBondLevelUpModel target = new CardBattleEmotionBondLevelUpModel();
        BeanUtils.copyProperties(resource, target);
        if (MapUtils.isNotEmpty(resource.getUnlockActionMap()) && resource.getUnlockActionMap().containsKey(resource.getCurrentLevel())) {
            final Map<Integer, List<InteractiveActionDetailListModel.InteractActionDetailModel>> unlockModelMap = resource.getUnlockActionMap();
            List<InteractiveActionDetailListModel.InteractActionDetailModel> interactActionDetailModels = unlockModelMap.get(resource.getCurrentLevel());
            target.setUnlockAction(interactActionDetailModels.stream().map(item -> InteractActionDetailVO.valueOf(item, domain)).collect(Collectors.toList()));
        }

        if (resource.getCostumePartMap() != null && resource.getCostumePartMap().containsKey(resource.getCurrentLevel())) {
            target.setCostumePart(resource.getCostumePartMap()
                    .get(resource.getCurrentLevel())
                    .stream()
                    .map(item -> CardBattleEmotionBondLevelUpModel.CostumeSimpleItem.fromModel(item, domain))
                    .collect(Collectors.toList()));
        }

        target.setLevelActionCount(resource.getActionAddCountMap().get(resource.getCurrentLevel()));
        Map<Integer, UserStoryModel> unlockStoryModels = resource.getUnlockStoryModels();
        if (MapUtils.isNotEmpty(unlockStoryModels)) {
            Map<Integer, StorySimpleVO> unlockStoryMap = unlockStoryModels.entrySet()
                    .stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, entry -> StorySimpleVO.valueOf(entry.getValue(), domain)));
            target.setUnlockStoryModels(unlockStoryMap);
        }
        Map<Integer, List<PrizeSimpleModel>> levelUpPrizes = resource.getLevelUpPrizes();
        Map<Integer, List<PrizeSimpleModel>> targetLevelUpPrizes = new HashMap<>();
        for (Map.Entry<Integer, List<PrizeSimpleModel>> levelUpPrizeEntry : levelUpPrizes.entrySet()) {
            List<PrizeSimpleModel> prizeSimpleModels = new ArrayList<>();
            for (PrizeSimpleModel prizeSimpleModel : levelUpPrizeEntry.getValue()) {
                String image = prizeSimpleModel.getImage();
                prizeSimpleModel.setImage(domain + image);
                prizeSimpleModels.add(prizeSimpleModel);
            }
            targetLevelUpPrizes.put(levelUpPrizeEntry.getKey(), prizeSimpleModels);
        }
        target.setLevelUpPrizes(targetLevelUpPrizes);
        return target;
    }

    @Data
    @Accessors(chain = true)
    public static class CostumeSimpleItem implements Serializable {

        private static final long serialVersionUID = 4197771559359752160L;
        private int id;
        private String name;
        private String imgUrl;

        public static CostumeSimpleItem fromModel(CostumePartModel resource, String domain) {
            if (resource == null) {
                return null;
            }
            CostumeSimpleItem target = new CostumeSimpleItem();
            target.setId(resource.getId()).setName(resource.getName());
            ImageInfo imageInfo = resource.getConsumePartImage();
            if (imageInfo != null && StringUtils.isNotBlank(imageInfo.getUrl())) {
                target.setImgUrl(domain + imageInfo.getUrl());
            }
            return target;
        }
    }

    @Data
    @Accessors(chain = true)
    public static class StorySimpleVO implements Serializable {

        private static final long serialVersionUID = -8303537560766434269L;
        private int id;

        private String name;

        private String type;

        private ImageInfoVO image;

        private Integer avgChapterId;

        private int lock;

        public static StorySimpleVO valueOf(UserStoryModel userStoryModel, String domain) {
            if (userStoryModel == null) {
                return null;
            }
            StorySimpleVO storySimpleVO = new StorySimpleVO().setId(userStoryModel.getStoryId())
                    .setName(userStoryModel.getName())
                    .setType(userStoryModel.getType())
                    .setLock(userStoryModel.isHadOwned() ? 0 : 1);
            String type = userStoryModel.getType();
            if (StoryType.PHOTO.getCode().equals(type)) {
                StoryPhotoConfigModel storyConfigModel = (StoryPhotoConfigModel) userStoryModel.getStoryConfigModel();
                storySimpleVO.setImage(ImageInfoVO.valueOf(storyConfigModel.getImageInfo(), domain));
            } else if (StoryType.AVG.getCode().equals(type)) {
                AvgConfigModel storyConfigModel = (AvgConfigModel) userStoryModel.getStoryConfigModel();
                storySimpleVO.setImage(ImageInfoVO.valueOf(storyConfigModel.getCollectionImage(), domain));
                storySimpleVO.setAvgChapterId(storyConfigModel.getAvgChapterId());
            }
            return storySimpleVO;
        }

    }

    @Data
    @Accessors(chain = true)
    public static class ImageInfoVO implements Serializable {

        private static final long serialVersionUID = -6149135951552375301L;

        private int size;

        private String firstImage;

        private String displayFirstImage;

        private boolean isDynamic;

        /**
         * 图片类型
         */
        private Integer type;

        /**
         * 图片地址
         */
        private String url;

        private String displayUrl;

        public static ImageInfoVO valueOf(ImageInfo imageInfo, String domain) {
            if (imageInfo == null) {
                return null;
            }
            ImageInfoVO imageInfoVO = new ImageInfoVO().setFirstImage(imageInfo.getFirstImage())
                    .setUrl(imageInfo.getUrl())
                    .setSize(imageInfo.getSize())
                    .setDynamic(imageInfo.isDynamic())
                    .setType(imageInfo.getType());
            if (StringUtils.isNotBlank(imageInfo.getFirstImage())) {
                imageInfoVO.setDisplayFirstImage(domain + imageInfo.getFirstImage());
            }
            if (StringUtils.isNotBlank(imageInfo.getUrl())) {
                imageInfoVO.setDisplayUrl(domain + imageInfo.getUrl());
            }
            return imageInfoVO;
        }

    }

    @Data
    @Accessors(chain = true)
    public static class InteractActionDetailVO implements Serializable {

        private static final long serialVersionUID = -1080320603083042064L;
        private int id;

        private String name;

        private ImageInfoVO image;

        private AnimationConfigVO animationConfig;

        private Integer unlockCondition;

        private Integer unlockType;

        private Integer emotionBondValue;

        private String actionTarget;

        public static InteractActionDetailVO valueOf(InteractiveActionDetailListModel.InteractActionDetailModel model, String domain) {
            if (model == null) {
                return null;
            }
            InteractActionDetailVO vo = new InteractActionDetailVO();
            vo.setId(model.getId());
            vo.setImage(ImageInfoVO.valueOf(model.getImage(), domain));
            vo.setUnlockCondition(model.getUnlockCondition());
            vo.setUnlockType(model.getUnlockType());
            vo.setName(model.getName());
            vo.setAnimationConfig(AnimationConfigVO.valueOf(model.getAnimationConfig()));
            return vo;

        }
    }

    @Data
    @Accessors(chain = true)
    public static class AnimationConfigVO implements Serializable {

        private static final long serialVersionUID = -59971160691140323L;
        /**
         * 剧情类型固定是动作剧情
         */

        private String configFileUrl;

        private String configFileKey;

        private String configFileName;

        private List<RoleAnimationConfigVO> roleAnimationConfigs;

        private Integer spacingX;

        private Integer spacingY;

        private Integer upperLayerRoleId;

        public static AnimationConfigVO valueOf(InteractiveAction.Config.AnimationConfig animationConfig) {
            if (animationConfig == null) {
                return null;
            }
            AnimationConfigVO vo = new AnimationConfigVO();
            vo.setConfigFileKey(animationConfig.getConfigFileKey());
            vo.setConfigFileName(animationConfig.getConfigFileName());
            vo.setRoleAnimationConfigs(animationConfig.getRoleAnimationConfigs().stream().map(RoleAnimationConfigVO::valueOf).collect(Collectors.toList()));
            vo.setSpacingX(animationConfig.getSpacingX());
            vo.setSpacingY(animationConfig.getSpacingY());
            vo.setUpperLayerRoleId(animationConfig.getUpperLayerRoleId());
            return vo;
        }
    }

    @Data
    @Accessors(chain = true)
    public static class RoleAnimationConfigVO implements Serializable {

        private static final long serialVersionUID = -1007282599998617550L;
        private Integer roleId;

        private String roleName;

        private List<DialogueConfigVO> dialogueConfigs;

        public static RoleAnimationConfigVO valueOf(InteractiveAction.Config.RoleAnimationConfig roleAnimationConfig) {
            if (roleAnimationConfig == null) {
                return null;
            }
            RoleAnimationConfigVO vo = new RoleAnimationConfigVO();
            vo.setRoleId(roleAnimationConfig.getRoleId());
            vo.setRoleName(roleAnimationConfig.getRoleName());
            vo.setDialogueConfigs(roleAnimationConfig.getDialogueConfigs().stream().map(DialogueConfigVO::valueOf).collect(Collectors.toList()));
            return vo;
        }
    }

    @Data
    @Accessors(chain = true)
    public static class DialogueConfigVO implements Serializable {

        private static final long serialVersionUID = -8247418165736684181L;
        private String animation;
        private Integer bubbleType;
        private String word;
        private Double animationStartSecond;
        private Double animationEndSecond;
        private Double wordStartSecond;

        public static DialogueConfigVO valueOf(InteractiveAction.Config.DialogueConfig dialogueConfig) {
            if (dialogueConfig == null) {
                return null;
            }
            DialogueConfigVO vo = new DialogueConfigVO();
            vo.setAnimation(dialogueConfig.getAnimation());
            vo.setBubbleType(dialogueConfig.getBubbleType());
            vo.setWord(dialogueConfig.getWord());
            vo.setAnimationStartSecond(dialogueConfig.getAnimationStartSecond());
            vo.setAnimationEndSecond(dialogueConfig.getAnimationEndSecond());
            vo.setWordStartSecond(dialogueConfig.getWordStartSecond());
            return vo;
        }
    }

}
