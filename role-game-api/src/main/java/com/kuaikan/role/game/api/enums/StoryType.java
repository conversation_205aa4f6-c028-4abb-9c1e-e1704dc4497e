package com.kuaikan.role.game.api.enums;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Getter;

import com.google.common.collect.ImmutableList;

/**
 * <AUTHOR>
 * @version 2024-03-12
 */
@Getter
@AllArgsConstructor
public enum StoryType {
    /**
     * 动作剧情
     */
    ACTION("action", "动作剧情"),
    /**
     * 信件剧情
     */
    LETTER("letter", "心动短信"),
    /**
     * avg
     */
    AVG("avg", "avg剧情"),

    /**
     * 互动彩蛋
     */
    EGG("egg", "互动彩蛋"),
    /**
     * 铭刻留声
     */
    VOICE("voice", "铭刻留声"),
    /**
     * 记忆照片
     */
    PHOTO("photo", "记忆照片"),
    /**
     * 点滴日常
     */
    DAILY("daily", "点滴日常"),
    ;
    private final String code;

    private final String desc;

    public static StoryType of(String code) {
        for (StoryType storyType : values()) {
            if (storyType.code.equals(code)) {
                return storyType;
            }
        }
        return null;
    }

    @Getter
    @AllArgsConstructor
    public enum SceneType {
        HOME_PAGE(1, "养成首页"),
        ROLE_PAGE(2, "角色详情页");

        private final Integer code;
        private final String desc;

        /** 动作剧情使用场景 */
        public static SceneType of(Integer code) {
            if (code == null) {
                return null;
            }
            for (SceneType sceneType : SceneType.values()) {
                if (sceneType.code.equals(code)) {
                    return sceneType;
                }
            }
            return null;
        }
    }

    public static List<Integer> availableScene() {
        return ImmutableList.of(SceneType.HOME_PAGE.code, SceneType.ROLE_PAGE.code);
    }

}
