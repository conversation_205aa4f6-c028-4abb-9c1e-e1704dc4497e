package com.kuaikan.role.game.api.bean;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 2024-04-23
 */
@Data
@Accessors(chain = true)
public class UserCostumePartComposeOrder implements Serializable {

    private int id;

    private long bid;

    private int userId;

    private int costumePartId;

    private ComposeConfig composeConfig;

    private Date createdAt;

    private Date updatedAt;

    public void setComposeConfig(CostumePart.ComposeConfig composeConfig) {
        this.composeConfig = new ComposeConfig().setSilverCoinCount(composeConfig.getSilverCoinCount())
                .setStuffConfigs(composeConfig.getStuffConfigs()
                        .stream()
                        .map(stuffConfig -> new StuffConfig().setStuffId(stuffConfig.getStuffId()).setStuffCount(stuffConfig.getStuffCount()))
                        .collect(Collectors.toList()));
    }

    @Data
    @Accessors(chain = true)
    public static class ComposeConfig implements Serializable {

        private static final long serialVersionUID = -8333336693192046393L;
        private int silverCoinCount;

        private List<StuffConfig> stuffConfigs;
    }

    @Data
    @Accessors(chain = true)
    public static class StuffConfig implements Serializable {

        private static final long serialVersionUID = -4404241033039573544L;

        private int stuffId;

        private int stuffCount;
    }
}
