package com.kuaikan.role.game.api.rpc.param;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.common.bean.UserAgent;
import com.kuaikan.role.game.api.enums.PrizeSourceType;

/**
 * <AUTHOR>
 * @version 2024-03-29
 */
@Data
@Accessors(chain = true)
public class ClaimPrizeParam implements Serializable {

    private static final long serialVersionUID = 1745172389942127710L;
    private int userId;

    private String xDevice;

    private UserAgent userAgent;

    List<Long> prizeIds;

    PrizeSourceType prizeSourceType;

}
