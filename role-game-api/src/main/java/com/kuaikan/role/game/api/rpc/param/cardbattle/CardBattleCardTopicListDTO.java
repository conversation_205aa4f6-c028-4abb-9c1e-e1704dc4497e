package com.kuaikan.role.game.api.rpc.param.cardbattle;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import lombok.Data;
import lombok.experimental.Accessors;

import com.fasterxml.jackson.annotation.JsonIgnore;

import com.kuaikan.common.ResponseCodeMsg;

/**
 * <AUTHOR>
 * @date 2024/7/27
 */
@Data
@Accessors(chain = true)
public class CardBattleCardTopicListDTO implements Serializable {

    private static final long serialVersionUID = 315152707466118554L;

    @JsonIgnore
    private ResponseCodeMsg codeMsg;
    private Map<String, List<CardBattleTopicInfoDTO>> cardTopicMap;

    public static CardBattleCardTopicListDTO init(Map<String, List<CardBattleTopicInfoDTO>> cardTopicMap, ResponseCodeMsg codeMsg) {
        return new CardBattleCardTopicListDTO().setCardTopicMap(cardTopicMap).setCodeMsg(codeMsg);
    }
}
