package com.kuaikan.role.game.api.model;

import com.kuaikan.role.game.api.bo.ImageInfo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/25
 */
@Data
@Accessors(chain = true)
public class AdoptCouponModel implements Serializable {
    private static final long serialVersionUID = -6711402858737531196L;

    private List<CouponModel> availableList;

    private List<CouponModel> duplicateList;

    @Data
    @Accessors(chain = true)
    public static class CouponModel implements Serializable {

        private static final long serialVersionUID = -3101516945198583760L;

        private RoleGroupsModel roleGroupsModel;

        private RoleAdoptCouponModel roleAdoptCoupon;
    }


    @Data
    @Accessors(chain = true)
    public static class RoleGroupsModel implements Serializable {

        private static final long serialVersionUID = -3550433869541445314L;
        /**
         * 角色组id
         */
        private int id;

        /**
         * 角色组的角色列表
         */
        private List<RoleModel> roleList;
    }

    @Data
    @Accessors(chain = true)
    public static class RoleModel implements Serializable {

        private static final long serialVersionUID = -5247315426143418425L;

        /**
         * 角色id
         */
        private int id;

        /**
         * 角色名字
         */
        private String name;

        private ImageInfo image;
    }

    @Data
    @Accessors(chain = true)
    public static class RoleAdoptCouponModel implements Serializable {

        private static final long serialVersionUID = -7591424074551960079L;
        /**
         * 折扣券ID
         */
        private int couponId;

        /**
         * 唯一 id
         */
        private Long bid;

        /**
         * 提示内容
         */
        private String tipContent;

        /**
         * 折扣券到期时间 yyyy-MM-dd
         */
        private String expiryTime;

        /**
         * 折扣率
         */
        private String discountRate;

        /**
         * 能量药水数
         */
        private int energyBottleCount;
    }
}
