package com.kuaikan.role.game.api.rpc.param;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 2024-07-18
 */
@Data
@Accessors(chain = true)
public class ActionStoryTriggerParam implements Serializable {

    private static final long serialVersionUID = -8124340067190556559L;

    private List<Integer> roleIds;

    private int userId;

    private boolean cpCostume;
}
