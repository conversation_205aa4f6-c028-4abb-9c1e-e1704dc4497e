package com.kuaikan.role.game.api.rpc.param;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CommonEnergyBottleOrderPaidNotifyParam implements Serializable {

    private static final long serialVersionUID = 1L;

    private int userId;
    private int mapId;
    private long orderId;

    public static CommonEnergyBottleOrderPaidNotifyParam of(int userId, int mapId, long orderId) {
        CommonEnergyBottleOrderPaidNotifyParam param = new CommonEnergyBottleOrderPaidNotifyParam();
        param.setUserId(userId);
        param.setMapId(mapId);
        param.setOrderId(orderId);
        return param;
    }
}
