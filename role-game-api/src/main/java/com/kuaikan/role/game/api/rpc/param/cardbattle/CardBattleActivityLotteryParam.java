package com.kuaikan.role.game.api.rpc.param.cardbattle;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 *<AUTHOR>
 *@date 2025/3/26
 */
@Data
@Accessors(chain = true)
public class CardBattleActivityLotteryParam implements Serializable {

    private static final long serialVersionUID = 3010443016350432444L;

    // 副本活动id（限时抽奖活动）
    private String activityId;

    // 阶段
    private Integer stage;

    // 抽奖次数
    private int lotteryTimes;

}
