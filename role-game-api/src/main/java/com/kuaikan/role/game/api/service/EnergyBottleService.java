package com.kuaikan.role.game.api.service;

import java.util.List;

import com.kuaikan.common.model.RpcResult;
import com.kuaikan.role.game.api.model.EnergyBottleInfoModel;
import com.kuaikan.role.game.api.model.UserEnergyItemModel;
import com.kuaikan.role.game.api.rpc.param.ListUserEnergyItemParam;

/**
 * EnergyBottleService
 *
 * <AUTHOR>
 * @since 2024-08-09
 */
public interface EnergyBottleService {

    RpcResult<EnergyBottleInfoModel> getEnergyBottleInfo(int userId);

    RpcResult<Void> useEnergyBottle(int userId, int roleId, int count);

    RpcResult<List<UserEnergyItemModel>> listUserEnergyBottle(ListUserEnergyItemParam param);

    RpcResult<Void> convBottleByAdoptCoupon(int userId, List<Integer> couponIds, List<Long> bids);

    RpcResult<Void> convBottleByAdoptCoupon(int userId, List<Integer> couponIds);
}
