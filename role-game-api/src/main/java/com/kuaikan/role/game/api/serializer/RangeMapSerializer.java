package com.kuaikan.role.game.api.serializer;

import java.io.IOException;
import java.util.Map;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.google.common.collect.Range;
import com.google.common.collect.RangeMap;

/**
 * <AUTHOR>
 * @version 2025-03-03
 */

public class RangeMapSerializer extends JsonSerializer<RangeMap<?, ?>> {

    @Override
    public void serialize(RangeMap<?, ?> treeRangeMap, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        jsonGenerator.writeStartArray();
        for (Map.Entry<? extends Range<?>, ?> entry : treeRangeMap.asMapOfRanges().entrySet()) {
            jsonGenerator.writeStartObject();
            jsonGenerator.writeStringField("range", entry.getKey().toString());
            jsonGenerator.writeObjectField("value", entry.getValue());
            jsonGenerator.writeEndObject();
        }
        jsonGenerator.writeEndArray();
    }
}
