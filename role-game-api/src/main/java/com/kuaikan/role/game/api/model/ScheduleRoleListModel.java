package com.kuaikan.role.game.api.model;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.bo.ImageInfo;

/**
 * <AUTHOR>
 * @version 2024-07-09
 */
@Data
@Accessors(chain = true)
public class ScheduleRoleListModel implements Serializable {

    private static final long serialVersionUID = -8828951185634327139L;

    private List<RoleModel> roleModels;

    @Data
    @Accessors(chain = true)
    public static class RoleModel implements Serializable {

        private static final long serialVersionUID = -1929490348448977329L;

        private int id;

        private String name;

        private ImageInfo avatar;

        private int level;

        private int mood;

        private String moodDesc;

        private int tiredness;

        private String tirednessDesc;

        private int energy;

        private int artLevel;

        private int houseworkLevel;

        private int sportLevel;

        private int cultureLevel;

        private int socialLevel;

        private long adoptionTime;
    }

}
