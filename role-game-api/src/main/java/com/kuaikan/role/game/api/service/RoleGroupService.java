package com.kuaikan.role.game.api.service;

import java.util.List;

import com.kuaikan.common.model.RpcResult;
import com.kuaikan.role.game.api.bean.ClientInfo;
import com.kuaikan.role.game.api.model.EmotionCostumePartModel;
import com.kuaikan.role.game.api.model.GroupEmotionPopupWindowModel;
import com.kuaikan.role.game.api.model.EmotionBondBottleModel;
import com.kuaikan.role.game.api.model.EmotionBondLevelUpModel;
import com.kuaikan.role.game.api.model.RoleGroupModel;
import com.kuaikan.role.game.api.model.RoleGroupHealthModel;
import com.kuaikan.role.game.api.model.RoleGroupModel;
import com.kuaikan.role.game.api.model.collection.RoleGroupDetailModel;
import com.kuaikan.role.game.api.rpc.param.RoleGroupEmotionCostumePartParam;
import com.kuaikan.role.game.api.rpc.param.RoleGroupHealthParam;

/**
 *
 * <AUTHOR>
 * @date 2024/7/10
 */
public interface RoleGroupService {

    /**
     * 获取领养角色组信息
     * @param userId 用户id
     * @return 角色组信息
     */
    RpcResult<List<RoleGroupDetailModel>> queryRelatedRoleGroupList(int userId);

    RpcResult<RoleGroupModel> getRoleGroupInfo(int userId, int roleGroupId);

    RpcResult<List<RoleGroupModel>> getRoleGroupInfoList(int userId);

    /**
     * 获取角色组健康状态
     * @param param
     * @return
     */
    RpcResult<RoleGroupHealthModel> getRoleGroupHealth(RoleGroupHealthParam param);

    /**
     * 恢复健康
     * @param groupId
     * @param userId
     * @return
     */
    RpcResult<Void> recoverHealth(int groupId, int userId);

    RpcResult<EmotionCostumePartModel> groupEmotionCostumePart(RoleGroupEmotionCostumePartParam param);

    RpcResult<GroupEmotionPopupWindowModel> emotionPopupWindow(int userId, int roleGroupId);

    /**
     * 获取角色组羁绊魔药信息
     *
     * @param groupId
     * @param userId
     * @return
     */
    RpcResult<EmotionBondBottleModel> getEmotionBondBottle(int userId, int groupId);

    /**
     * 获取用户所有角色组羁绊魔药列表, 用于展示在背包，只包含基本信息
     *
     * @param userId
     * @return
     */
    RpcResult<List<EmotionBondBottleModel>> getEmotionBondBottleList(int userId);

    /**
     * 角色组使用羁绊魔药一键升级
     *
     * @param userId
     * @param bottleId
     * @return
     */
    RpcResult<EmotionBondLevelUpModel> oneClickUpgrade(int userId, int bottleId, ClientInfo clientInfo);

    /**
     * 获取1级羁绊奖励
     *
     * @param userId
     * @param groupId
     * @return
     */
    RpcResult<EmotionBondLevelUpModel> getLevelOneBondPrize(int userId, int groupId);

    /**
     * 关闭羁绊开启1级奖励弹窗
     * @param userId
     * @param roleGroupId
     * @return
     */
    RpcResult<Void> updateBondPopup(int userId, int roleGroupId);
    /**
     * 初始化羁绊组
     * @param userId
     * @return
     */
    RpcResult<Void> initEmotionBond(int userId);

}
