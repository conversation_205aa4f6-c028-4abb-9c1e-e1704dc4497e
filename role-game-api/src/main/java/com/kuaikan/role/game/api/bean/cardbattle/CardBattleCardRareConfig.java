package com.kuaikan.role.game.api.bean.cardbattle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import lombok.Data;

/**
 * 卡牌稀有度相关配置表
 *
 * <AUTHOR>
 * @date 2024/6/13
 */
@Data
public class CardBattleCardRareConfig implements Serializable {

    private static final long serialVersionUID = -5269202952794296365L;
    // id
    private long id;
    // 稀有度code
    private int rareCode;
    // 可突破次数
    private int upgradeCount;
    // 初始卡牌等级上限
    private int initLevelLimit;
    // 突破消耗银币数列表
    private List<Integer> upgradeSpend;
    // 突破加成系数列表(等级上限系数与突破系数相乘 = 最终要加的突破系数)
    private List<BigDecimal> upgradeRatio;

}