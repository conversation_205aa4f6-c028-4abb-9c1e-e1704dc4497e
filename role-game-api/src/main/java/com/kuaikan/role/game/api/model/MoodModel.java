package com.kuaikan.role.game.api.model;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.enums.MoodStatus;

/**
 * MoodModel
 *
 * <AUTHOR>
 * @since 2024-05-22
 */
@Data
@Accessors(chain = true)
public class MoodModel implements Serializable {

    private static final long serialVersionUID = 4941311205907280522L;
    // 心情值
    private Integer mood;
    /**
     * 心情状态
     * @see MoodStatus
     */
    private int moodStatus;
    // 心情值上限
    private int moodLimit;
    // 分割点，用于判断状态的切换
    private List<Integer> cutPoints;
}
