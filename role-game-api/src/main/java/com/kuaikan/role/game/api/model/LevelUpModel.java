package com.kuaikan.role.game.api.model;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.experimental.Accessors;

import org.apache.commons.collections.MapUtils;

import com.kuaikan.role.game.api.bean.RewardOrderScheduleExtraInfo;

/**
 * LevelUpModel
 *
 * <AUTHOR>
 * @since 2024-03-29
 */
@Data
@Accessors(chain = true)
public class LevelUpModel implements Serializable {

    private static final long serialVersionUID = 9207114056241747672L;
    /** 本次获得的exp*/
    private int obtainExp;
    /** 是否升级*/
    private boolean levelUp;
    /** 当前等级*/
    private int currentLevel;
    /** 升级前等级*/
    private int oldLevel;
    /** 升级前经验值*/
    private int oldExp;
    /** 升级前对应的下个等级经验值*/
    private int oldNextLevelExp;
    /** 升到下个等级总共的exp*/
    private int nextLevelExp;
    /** 升级奖励，key是对应等级，value该等级对应的奖励*/
    private Map<Integer, List<PrizeSimpleModel>> levelUpPrizes;
    private double attackBonusBuffPerLevel;

    public static LevelUpModel valueOf(RewardOrderScheduleExtraInfo.LevelUp levelUp) {
        if (levelUp == null) {
            return null;
        }
        LevelUpModel levelUpModel = new LevelUpModel();
        levelUpModel.setObtainExp(levelUp.getObtainExp());
        levelUpModel.setLevelUp(levelUp.isLevelUp());
        levelUpModel.setCurrentLevel(levelUp.getCurrentLevel());
        levelUpModel.setOldLevel(levelUp.getOldLevel());
        levelUpModel.setOldExp(levelUp.getOldExp());
        levelUpModel.setOldNextLevelExp(levelUp.getOldNextLevelExp());
        levelUpModel.setNextLevelExp(levelUp.getNextLevelExp());
        levelUpModel.setAttackBonusBuffPerLevel(levelUp.getAttackBonusBuffPerLevel());
        if (MapUtils.isNotEmpty(levelUp.getLevelUpPrizes())) {
            levelUpModel.setLevelUpPrizes(levelUp.getLevelUpPrizes()
                    .entrySet()
                    .stream()
                    .collect(Collectors.toMap(Map.Entry::getKey,
                            entry -> entry.getValue().stream().map(PrizeSimpleModel::valueOf).collect(Collectors.toList()))));
        }
        return levelUpModel;
    }
}
