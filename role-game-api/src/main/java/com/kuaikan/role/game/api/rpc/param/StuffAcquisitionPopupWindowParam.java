package com.kuaikan.role.game.api.rpc.param;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 2024-05-07
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class StuffAcquisitionPopupWindowParam extends AcquisitionPopupWindowParam implements Serializable {

    private static final long serialVersionUID = -7548358657686621084L;

    private int stuffId;

}
