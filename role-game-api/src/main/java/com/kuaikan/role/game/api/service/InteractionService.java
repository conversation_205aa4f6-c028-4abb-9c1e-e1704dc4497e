package com.kuaikan.role.game.api.service;

import com.kuaikan.common.model.RpcResult;
import com.kuaikan.role.game.api.model.InteractiveActionUseModel;
import com.kuaikan.role.game.api.model.InteractiveItemUseModel;
import com.kuaikan.role.game.api.model.InteractiveListModel;
import com.kuaikan.role.game.api.rpc.param.InteractiveActionUseParam;
import com.kuaikan.role.game.api.rpc.param.InteractiveItemUseParam;
import com.kuaikan.role.game.api.rpc.param.InteractiveListParam;

/**
 * <AUTHOR>
 * @version 2024-09-11
 */
public interface InteractionService {

    RpcResult<InteractiveListModel> list(InteractiveListParam param);

    RpcResult<InteractiveActionUseModel> useAction(InteractiveActionUseParam param);

    RpcResult<InteractiveItemUseModel> useItem(InteractiveItemUseParam param);

    RpcResult<Void> clearInteractionRedDot(int userId, int roleGroupId);
}
