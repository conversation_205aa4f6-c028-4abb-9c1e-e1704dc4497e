package com.kuaikan.role.game.api.bean;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * material
 * <AUTHOR>
@Data
@Accessors(chain = true)
public class Material implements Serializable {

    private static final long serialVersionUID = -8416945536038428168L;
    private Integer id;

    /**
     * 文件类型
     */
    private Integer type;

    /**
     * 文件key
     */
    private String key;

    /**
     * 文件md5
     */
    private String md5;

    /**
     * 文件名称
     */
    private String name;

    /**
     * 物料版本
     */
    private Integer version;

    /**
     * 发布状态
     */
    private Integer status;

    /**
     * 物料业务信息
     */
    private BizInfo bizInfo;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

    @Data
    @Accessors(chain = true)
    public static class BizInfo implements Serializable {

        private static final long serialVersionUID = 4087833681555813245L;

        private Integer type;

        private Integer costumeId;

        private Integer storyId;

        private Set<Integer> roleIds;

        private Integer roleId;

    }

}