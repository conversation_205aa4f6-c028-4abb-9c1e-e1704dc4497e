package com.kuaikan.role.game.api.model;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * EnergyBottleInfoModel
 *
 * <AUTHOR>
 * @since 2024-08-09
 */
@Data
@Accessors(chain = true)
public class EnergyBottleInfoModel implements Serializable {

    private static final long serialVersionUID = 7193485769630075207L;
    /** 药水余额 */
    private int balance;
    /** 药水兑换行动力比例 */
    private int bottleToEnergyRatio;
    /** 剩余优惠次数，剩余优惠次数大于0，即可使用优惠的礼物id下单 */
    private int usedCount;
    /** 总优惠次数 */
    private int discountCount;
    /** 礼物id */
    private List<GiftInfo> giftList;

    @Data
    @Accessors(chain = true)
    public static class GiftInfo implements Serializable {

        private static final long serialVersionUID = -8950684062793188814L;
        private int order;
        private String giftId;
        private int originPrice;
        private int discountPrice;
        private int count;
    }

}
