package com.kuaikan.role.game.api.enums.cardbattle;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 独立战斗关卡类型
 *
 *<AUTHOR>
 *@date 2024/9/13
 */
@Getter
@AllArgsConstructor
public enum CardBattleTaskTypeEnum {
    EXPLORE(0, "探索战斗"),
    NORMAL(1, "普通战斗关卡"),
    WAIT_FREE(2, "等免战斗关卡"),
    PRACTICE(3, "历练关卡"),
    LIMITED_TIME(4, "限时活动战斗"),
    ROLE_BOND(5, "羁绊活动战斗"),
    MATERIAL(6, "材料历练战斗"),
    LIMITED_TIME_V2(7, "限时活动V2战斗"),
    ;

    private Integer code;
    private String desc;
}
