package com.kuaikan.role.game.api.bean;

import java.util.Date;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class UserMapEnergyFlow {
    private int id;
    private int userId;
    private int mapId;
    private long bid;
    private int beforeBalance;
    private int afterBalance;
    private int num;
    /** {@link EnergyAssignSource}*/
    private int source;
    private String thirdId;
    /**
     * 类型，增加/扣减
     * @see FlowType
     */
    private int type;
    private String extraInfo;
    private Date createdAt;
    private Date updatedAt;
}
