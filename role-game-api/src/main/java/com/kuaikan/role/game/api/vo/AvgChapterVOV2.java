package com.kuaikan.role.game.api.vo;

import static com.kuaikan.role.game.api.rpc.result.AvgChapterModel.STOP_FLAG;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import com.kuaikan.cdn.core.CdnHandler;
import com.kuaikan.common.bean.UserAgent;
import com.kuaikan.common.enums.Platform;
import com.kuaikan.role.game.api.enums.AvgBgType;
import com.kuaikan.role.game.api.enums.AvgChapterSource;
import com.kuaikan.role.game.api.enums.AvgHotZoneType;
import com.kuaikan.role.game.api.enums.AvgTextLoopType;
import com.kuaikan.role.game.api.enums.AvgTextUnlockCondition;
import com.kuaikan.role.game.api.enums.AvgTextValueCondition;
import com.kuaikan.role.game.api.enums.CdnPayType;
import com.kuaikan.role.game.api.model.AvgChapterDetailModel;
import com.kuaikan.role.game.api.model.AvgChapterDetailModelV2;
import com.kuaikan.role.game.api.rpc.result.AvgChapterModel;
import com.kuaikan.role.game.api.rpc.result.AvgChapterModelV2;
import com.kuaikan.role.game.api.rpc.result.AvgChapterRecordModel;
import com.kuaikan.role.game.api.rpc.result.avg.BgModel;
import com.kuaikan.role.game.api.rpc.result.avg.CharModel;
import com.kuaikan.role.game.api.rpc.result.avg.FileModel;
import com.kuaikan.role.game.api.rpc.result.avg.SpineFileModel;
import com.kuaikan.role.game.api.rpc.result.avg.VideoDetailModel;
import com.kuaikan.role.game.api.rpc.result.avg.VideoModel;

/**
 * <AUTHOR>
 * @date 2024/8/12
 */
@Slf4j
@Data
@Accessors(chain = true)
public class AvgChapterVOV2 {

    public static final List<String> IMAGE_EXTENSIONS = new ArrayList<>();

    public static final String PNG_SUFFIX = ".png";
    public static final String WEBP_SUFFIX = ".webp";

    static {
        IMAGE_EXTENSIONS.add(PNG_SUFFIX);
        IMAGE_EXTENSIONS.add(WEBP_SUFFIX);
        IMAGE_EXTENSIONS.add(".jpg");
    }

    private String projectId;

    private int chapterId;

    private String playerName;

    private List<TextVO> textList;

    private String lastPlayTextId;

    private List<FileVO> styleFiles;

    private List<String> hadSelectTextIds;

    /**
     * 骨骼动画文件
     */
    private List<SpineFileVO> spineFiles;

    @Data
    @Accessors(chain = true)
    public static class TextVO implements Serializable {

        private static final long serialVersionUID = -7664575234884320275L;
        /**
         * 文本ID
         */
        private String textId;
        /**
         * 对白文案
         */
        private String dialogue;

        /**
         * 对白名字
         */
        private String dialogueName;

        private List<DialogueOptionVO> dialogueOptionList;

        /**
         * 名字框位置
         */
        private Integer nameCard;
        /**
         * 选项位置
         */
        private Integer optionLocation;
        /**
         * 前置文案位置
         */
        private Integer preCopywritingLocation;
        /**
         * 是否忽略选择记录
         */
        private Boolean ignoreSelectRecord;
        /**
         * 结尾提示语
         */
        private String endTips;
        /**
         * 后接文本ID
         */
        private List<String> nextIds;
        /**
         * 背景
         */
        private String bg;
        /**
         * 动态背景 & 静态背景
         */
        private BgVO bgInfo;

        private AvgEffectVO bgEffectV2;

        private AvgPlayConfigVO bgEffectPlayConfig;
        /**
         * 立绘
         */
        private String character;
        /**
         * 动态立绘
         */
        private List<String> characters;
        /**
         * 立绘信息
         */
        private CharVO charInfo;
        /**
         * 表情
         */
        private Integer face;
        private AvgEffectVO charEffectV2;
        /**
         * 立绘位置
         */
        private Integer charPosition;
        /**
         * q版立绘左
         */
        private CharVO spineLeft;
        /**
         * q版立绘中
         */
        private CharVO spineMiddle;
        /**
         * q版立绘右
         */
        private CharVO spineRight;

        /**
         * 头像
         */
        private String head;
        /**
         * 头像位
         */
        private Integer position;
        /**
         * 音乐
         */
        private String bgm;
        /**
         * 音效
         */
        private String sound;
        /**
         * 音效循环
         */
        private Integer loop;
        /**
         * CV语音
         */
        private String cv;
        /**
         * 高光视频
         */
        private String video;
        /**
         * 高光视频音频
         */
        private String videoVoice;
        /**
         * 高光视频
         */
        private VideoVO highlightVideo;
        /**
         * 播放模式
         */
        @Deprecated
        private Integer playMode;

        private AvgPlayConfigVO videoPlayConfig;
        /**
         * 点触热区
         */
        @Deprecated
        private List<List<List<Integer>>> hotZone;
        /**
         * 点触热区v2
         */
        private List<HotZoneVO> hotZoneV2;
        /**
         * 提示文案
         */
        private ContextVO context;
        /**
         * 后接段落
         */
        private Integer nextStoryId;

        /**
         * 是否循环
         */
        private boolean textLoop;

        /**
         * 循环类型 {@link AvgTextLoopType}
         */
        private Integer loopType;

        /**
         * 循环次数
         */
        private Integer loopCount;

        /**
         * 循环退出文本id
         */
        private String loopNextTextId;

        /**
         * 是否退出循环
         */
        private boolean loopBreak;

        /**
         * 中插片段返回标识
         */
        private boolean insertChapterBack;

        /**
         * 中插段落文本
         */
        private List<TextVO> insertChapterTextList;

        /**
         * 物品信息
         */
        private StuffVO stuff;

        /**
         * 物品移动
         */
        private String stuffMove;

        /**
         * 立绘移动
         */
        private String charMove;

        /**
         * q版立绘移动
         */
        private String spineMove;
    }

    @Data
    @Accessors(chain = true)
    public static class StuffVO {

        private String name;
        private String key;
        private AnchorVO anchor;

        public static StuffVO valueOf(AvgChapterModelV2.StuffModel stuff, Map<String, String> urlMap) {
            if (stuff == null) {
                return null;
            }
            return new StuffVO().setName(stuff.getName()).setKey(urlMap.get(stuff.getKey())).setAnchor(AnchorVO.valueOf(stuff.getAnchor()));
        }
    }

    @Data
    @Accessors(chain = true)
    public static class AnchorVO {

        private Integer type;
        private PointVO point;

        public static AnchorVO valueOf(AvgChapterModelV2.AnchorModel anchor) {
            if (anchor == null) {
                return null;
            }
            return new AnchorVO().setType(anchor.getType()).setPoint(PointVO.valueOf(anchor.getPoint()));
        }
    }

    @Data
    @Accessors(chain = true)
    public static class PointVO {

        private Double x;
        private Double y;

        public static PointVO valueOf(AvgChapterModelV2.PointModel point) {
            if (point == null) {
                return null;
            }
            return new PointVO().setX(point.getX()).setY(point.getY());
        }
    }

    @Data
    @Accessors(chain = true)
    public static class AvgEffectVO {

        private Integer type;

        private Integer speed;

        private Integer vibration;

        public static AvgEffectVO valueOf(AvgChapterModelV2.AvgEffectModel model) {
            if (model == null) {
                return null;
            }
            return new AvgEffectVO().setType(model.getType()).setSpeed(model.getSpeed()).setVibration(model.getVibration());
        }
    }

    @Data
    @Accessors(chain = true)
    public static class AvgPlayConfigVO {

        private Integer playMode;

        private Integer ratio;

        public static AvgPlayConfigVO valueOf(AvgChapterModelV2.AvgPlayConfigModel model) {
            if (model == null) {
                return null;
            }
            return new AvgPlayConfigVO().setPlayMode(model.getPlayMode()).setRatio(model.getRatio());
        }
    }

    @Data
    @Accessors(chain = true)
    public static class SpineFileVO {

        private String spineName;

        private List<FileVO> files;
    }

    @Data
    @Accessors(chain = true)
    public static class DialogueOptionVO {

        /**
         * 选项id,目前不是全局唯一，是OptionList的索引下标，后期可能变为自增id
         */
        private Integer optionId;

        private String dialogue;

        private String nextId;
        /**
         * 解锁条件{@link AvgTextUnlockCondition}
         */
        private Integer unlockCondition;
        /**
         * 数值变化 {@link AvgTextValueCondition}
         */
        private Integer valueCondition;

        /**
         * 是否隐藏选项
         */
        private Boolean isHideOptions;

        /**
         * 是否一次性选项
         */
        private Boolean isOnetime;
    }

    @Data
    @Accessors(chain = true)
    public static class CharVO {

        /**
         * {@link com.kuaikan.role.game.api.enums.CharType}
         */
        private int type;

        private String name;

        private List<FileVO> files;
    }

    @Data
    @Accessors(chain = true)
    public static class FileVO {

        private String name;
        private String key;
    }

    @Data
    @Accessors(chain = true)
    public static class BgVO {

        /**
         * 图片 & 视频 ,1-图片 2-视频
         */
        private int type;
        /**
         * 静态bg图片key
         */
        private String imgKey;
        /**
         * 视频bg
         */
        private VideoVO bgVideo;

        public static BgVO valueOf(BgModel model, Map<String, String> urlMap) {
            if (model == null) {
                return null;
            }
            return new BgVO().setType(model.getType()).setImgKey(urlMap.get(model.getImgKey())).setBgVideo(VideoVO.valueOf(model.getBgVideo(), urlMap));
        }
    }

    @Data
    @Accessors(chain = true)
    public static class VideoVO {

        private VideoDetailVO mp4Video;
        private VideoDetailVO webmVideo;

        public static VideoVO valueOf(VideoModel videoModel, Map<String, String> urlMap) {
            if (videoModel == null) {
                return null;
            }
            VideoVO videoVO = new VideoVO();
            videoVO.setMp4Video(VideoDetailVO.valueOf(videoModel.getMp4Video(), urlMap));
            videoVO.setWebmVideo(VideoDetailVO.valueOf(videoModel.getWebmVideo(), urlMap));
            return videoVO;
        }
    }

    @Data
    @Accessors(chain = true)
    public static class VideoDetailVO {

        /**
         * 高光视频
         */
        private String video;
        /**
         * 高光视频封面首帧
         */
        private String videoCover;
        /**
         * 高光视频的音频
         */
        private String videoVoice;

        public static VideoDetailVO valueOf(VideoDetailModel model, Map<String, String> urlMap) {
            if (model == null) {
                return null;
            }
            return new VideoDetailVO().setVideo(urlMap.get(model.getVideo()))
                    .setVideoCover(urlMap.get(model.getVideoCover()))
                    .setVideoVoice(urlMap.get(model.getVideoVoice()));
        }
    }

    @Data
    @Accessors(chain = true)
    public static class HotZoneVO {

        /**
         * 热区类型
         * @see AvgHotZoneType
         */
        private int styleType;
        /**
         * 热区坐标,左上点
         */
        private PointAxisVO leftUpAxis;
        /**
         * 热区坐标,右下点
         */
        private PointAxisVO rightDownAxis;
        /**
         * 校验点，styleType为滑动时才会有值
         */
        private List<PointAxisVO> checkPoints;
        /**
         * 曲线点，多个离散点构成一条平滑曲线，styleType为滑动时才会有值
         */
        private List<PointAxisVO> linePoints;

        public static HotZoneVO valueOf(AvgChapterModelV2.HotZoneModel model) {
            if (model == null) {
                return null;
            }
            List<PointAxisVO> checkPoints = null;
            if (CollectionUtils.isNotEmpty(model.getCheckPoints())) {
                checkPoints = model.getCheckPoints().stream().map(AvgChapterVOV2.PointAxisVO::valueOf).collect(Collectors.toList());
            }
            List<PointAxisVO> linePoints = null;
            if (CollectionUtils.isNotEmpty(model.getLinePoints())) {
                linePoints = model.getLinePoints().stream().map(AvgChapterVOV2.PointAxisVO::valueOf).collect(Collectors.toList());
            }
            return new HotZoneVO().setStyleType(model.getStyleType())
                    .setLeftUpAxis(PointAxisVO.valueOf(model.getLeftUpAxis()))
                    .setRightDownAxis(PointAxisVO.valueOf(model.getRightDownAxis()))
                    .setCheckPoints(checkPoints)
                    .setLinePoints(linePoints);
        }
    }

    @Data
    @Accessors(chain = true)
    public static class PointAxisVO {

        private int x;
        private int y;

        public static PointAxisVO valueOf(AvgChapterModelV2.PointAxisModel model) {
            if (model == null) {
                return null;
            }
            return new PointAxisVO().setX(model.getX()).setY(model.getY());
        }
    }

    @Data
    @Accessors(chain = true)
    public static class ContextVO {

        private String hint;
        /**
         * 提示文本框的左上角坐标
         */
        private PointAxisVO leftUpAxis;
        /**
         * 提示文本框的右下角坐标
         */
        private PointAxisVO rightDownAxis;

        public static ContextVO valueOf(AvgChapterModelV2.ContextModel model) {
            if (model == null) {
                return null;
            }
            return new ContextVO().setHint(model.getHint())
                    .setLeftUpAxis(PointAxisVO.valueOf(model.getLeftUpAxis()))
                    .setRightDownAxis(PointAxisVO.valueOf(model.getRightDownAxis()));
        }
    }

    /**
     * 组装vo
     * @param param  组装业务来源 {@link AvgChapterDetailModel}
     * @return vo
     */
    public static AvgChapterVOV2 valueOf(AvgChapterDetailModelV2 param) {
        int source = param.getSource();
        AvgChapterModelV2 avgChapterModel = param.getAvgChapterModel();
        AvgChapterRecordModel avgChapterRecordModel = param.getAvgChapterRecordModel();
        String payDomain = param.getPayDomain();
        UserAgent userAgent = param.getUserAgent();
        if (avgChapterModel == null) {
            return null;
        }
        AvgChapterVOV2 avgChapterVO = new AvgChapterVOV2();
        avgChapterVO.setProjectId(avgChapterModel.getProjectId());
        avgChapterVO.setChapterId(avgChapterModel.getChapterId());
        if (CollectionUtils.isNotEmpty(avgChapterModel.getStyleFiles())) {
            Set<String> keys = avgChapterModel.getStyleFiles().stream().map(FileModel::getKey).filter(Objects::nonNull).collect(Collectors.toSet());
            // key=styleFile.getKey(), value=url
            Map<String, String> styleFileUrlMap = keys.stream()
                    .collect(Collectors.toMap(key -> key,
                            key -> Optional.ofNullable(getCdnUrl(payDomain + key, CdnPayType.PAY.getCode(), userAgent)).orElse("")));

            List<FileVO> fileVOS = avgChapterModel.getStyleFiles().stream().map(styleFile -> {
                FileVO fileVO = new FileVO();
                fileVO.setName(styleFile.getName());
                fileVO.setKey(styleFileUrlMap.get(styleFile.getKey()));
                return fileVO;
            }).collect(Collectors.toList());
            avgChapterVO.setStyleFiles(fileVOS);
        }
        List<SpineFileModel> spineFiles = avgChapterModel.getSpineFiles();
        if (CollectionUtils.isNotEmpty(spineFiles)) {
            Set<String> keys = spineFiles.stream().flatMap(files -> files.getFiles().stream()).map(FileModel::getKey).collect(Collectors.toSet());
            // key=spineFile.getKey(), value=url
            Map<String, String> spineFileUrlMap = keys.stream()
                    .collect(Collectors.toMap(key -> key,
                            key -> Optional.ofNullable(CdnHandler.getEncryptionUrl(payDomain + key, CdnPayType.PAY.getCode())).orElse("")));

            List<SpineFileVO> spineFileVOS = spineFiles.stream().map(s -> {
                SpineFileVO spineFileVO = new SpineFileVO();
                spineFileVO.setSpineName(s.getSpineName());
                List<FileVO> fileVOS = s.getFiles().stream().map(spineFile -> {
                    FileVO fileVO = new FileVO();
                    fileVO.setName(spineFile.getName());
                    fileVO.setKey(spineFileUrlMap.get(spineFile.getKey()));
                    return fileVO;
                }).collect(Collectors.toList());
                spineFileVO.setFiles(fileVOS);
                return spineFileVO;
            }).collect(Collectors.toList());
            avgChapterVO.setSpineFiles(spineFileVOS);
        }
        if (avgChapterRecordModel != null && StringUtils.isNotBlank(avgChapterRecordModel.getTextContentMd5()) && avgChapterRecordModel.getTextContentMd5()
                .equals(avgChapterModel.getTextContentMd5())) {
            avgChapterVO.setLastPlayTextId(avgChapterRecordModel.getLastTextId());
            avgChapterVO.setHadSelectTextIds(avgChapterRecordModel.getSelectedTextIds());
        }
        List<AvgChapterModelV2.TextModel> textList = avgChapterModel.getTextList();
        List<AvgChapterModelV2.TextModel> insertTextList = avgChapterModel.getTextList()
                .stream()
                .map(AvgChapterModelV2.TextModel::getInsertChapterTextList)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<AvgChapterModelV2.TextModel> allTextList = new ArrayList<>();
        allTextList.addAll(textList);
        allTextList.addAll(insertTextList);
        Set<String> keys = allTextList.stream().flatMap(textModel -> {
            Optional<BgModel> bgInfo = Optional.ofNullable(textModel.getBgInfo());
            Optional<VideoModel> highlightVideo = Optional.ofNullable(textModel.getHighlightVideo());
            Optional<AvgChapterModelV2.StuffModel> stuffInfo = Optional.ofNullable(textModel.getStuff());
            Stream<String> keyStream = Stream.of(bgInfo.map(BgModel::getImgKey).orElse(null),
                    bgInfo.map(BgModel::getBgVideo).map(VideoModel::getMp4Video).map(VideoDetailModel::getVideo).orElse(null),
                    bgInfo.map(BgModel::getBgVideo).map(VideoModel::getMp4Video).map(VideoDetailModel::getVideoCover).orElse(null),
                    bgInfo.map(BgModel::getBgVideo).map(VideoModel::getWebmVideo).map(VideoDetailModel::getVideo).orElse(null),
                    bgInfo.map(BgModel::getBgVideo).map(VideoModel::getWebmVideo).map(VideoDetailModel::getVideoVoice).orElse(null),
                    stuffInfo.map(AvgChapterModelV2.StuffModel::getKey).orElse(null), textModel.getCharacterKey(), textModel.getHeadKey(), textModel.getBgmKey(),
                    textModel.getSoundKey(), textModel.getCvKey(), highlightVideo.map(VideoModel::getMp4Video).map(VideoDetailModel::getVideo).orElse(null),
                    highlightVideo.map(VideoModel::getMp4Video).map(VideoDetailModel::getVideoVoice).orElse(null),
                    highlightVideo.map(VideoModel::getMp4Video).map(VideoDetailModel::getVideoCover).orElse(null),
                    highlightVideo.map(VideoModel::getWebmVideo).map(VideoDetailModel::getVideo).orElse(null),
                    highlightVideo.map(VideoModel::getWebmVideo).map(VideoDetailModel::getVideoVoice).orElse(null),
                    highlightVideo.map(VideoModel::getWebmVideo).map(VideoDetailModel::getVideoCover).orElse(null));
            Stream<String> charactersStream = Stream.empty();
            Stream<String> charFileStream = Stream.empty();
            Stream<String> spineLeftStream = Stream.empty();
            Stream<String> spineMiddleStream = Stream.empty();
            Stream<String> spineRightStream = Stream.empty();
            if (textModel.getCharacters() != null) {
                charactersStream = textModel.getCharacters().stream();
            }
            if (textModel.getCharModel() != null) {
                charactersStream = textModel.getCharModel().getFiles().stream().map(FileModel::getKey).collect(Collectors.toList()).stream();
            }
            if (textModel.getSpineLeft() != null) {
                spineLeftStream = textModel.getSpineLeft().getFiles().stream().map(FileModel::getKey).collect(Collectors.toList()).stream();
            }
            if (textModel.getSpineMiddle() != null) {
                spineMiddleStream = textModel.getSpineMiddle().getFiles().stream().map(FileModel::getKey).collect(Collectors.toList()).stream();
            }
            if (textModel.getSpineRight() != null) {
                spineRightStream = textModel.getSpineRight().getFiles().stream().map(FileModel::getKey).collect(Collectors.toList()).stream();
            }
            // 多个流合并
            return Stream.concat(
                    Stream.concat(Stream.concat(Stream.concat(Stream.concat(charactersStream, charFileStream), spineLeftStream), spineMiddleStream),
                            spineRightStream), keyStream);
        }).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<String, String> textUrlMap = keys.stream()
                .collect(Collectors.toMap(key -> key, key -> Optional.ofNullable(getCdnUrl(payDomain + key, CdnPayType.PAY.getCode(), userAgent)).orElse("")));

        if (CollectionUtils.isNotEmpty(textList)) {
            List<TextVO> textVOS = textList.stream().map(textModel -> valueOf(source, textModel, textUrlMap)).collect(Collectors.toList());
            avgChapterVO.setTextList(textVOS);
        }
        if (source == AvgChapterSource.COMIC_WIDGET.getCode() || source == AvgChapterSource.ROLE_GAME.getCode()) {
            List<TextVO> resultTextList = avgChapterVO.getTextList();
            if (CollectionUtils.isNotEmpty(resultTextList)) {
                for (TextVO textVO : resultTextList) {
                    List<DialogueOptionVO> dialogueOptions = textVO.getDialogueOptionList();
                    if (CollectionUtils.isNotEmpty(dialogueOptions)) {
                        for (DialogueOptionVO dialogueOption : dialogueOptions) {
                            dialogueOption.setUnlockCondition(AvgTextUnlockCondition.NOT_ONLINE.getCode());
                            dialogueOption.setValueCondition(AvgTextValueCondition.NO_CHANGE.getCode());
                            dialogueOption.setIsOnetime(false);
                            dialogueOption.setIsHideOptions(false);
                        }
                    }
                    textVO.setTextLoop(false);
                    textVO.setLoopType(null);
                    textVO.setLoopCount(null);
                    textVO.setLoopNextTextId(null);
                    textVO.setLoopBreak(false);
                    textVO.setInsertChapterBack(false);
                }
            }
        }
        return avgChapterVO;
    }

    public void replacePlayerName(String playerName) {
        this.playerName = playerName;
    }

    private static TextVO valueOf(int source, AvgChapterModelV2.TextModel textModel, Map<String, String> textUrlMap) {
        TextVO textVO = new TextVO();
        textVO.setTextId(textModel.getTextId());
        textVO.setDialogue(textModel.getDialogue());
        textVO.setDialogueName(textModel.getDialogueName());
        List<AvgChapterModelV2.DialogueOption> dialogueOptions = textModel.getDialogueOptions();
        if (CollectionUtils.isNotEmpty(dialogueOptions)) {
            List<DialogueOptionVO> dialogueOptionVOS = dialogueOptions.stream()
                    .map(AvgChapterVOV2::valueOf)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            textVO.setDialogueOptionList(dialogueOptionVOS);
        }
        textVO.setNameCard(textModel.getNameCard());
        textVO.setOptionLocation(textModel.getOptionLocation());
        textVO.setPreCopywritingLocation(textModel.getPreCopywritingLocation());
        textVO.setIgnoreSelectRecord(textModel.getIgnoreSelectRecord());
        textVO.setEndTips(textModel.getEndTips());
        textVO.setNextIds(textModel.getNextIds());
        textVO.setCharEffectV2(AvgEffectVO.valueOf(textModel.getCharEffectV2()));
        textVO.setCharPosition(textModel.getCharPosition());
        textVO.setCharInfo(valueOf(textModel.getCharModel(), textUrlMap));
        textVO.setSpineLeft(valueOf(textModel.getSpineLeft(), textUrlMap));
        textVO.setSpineMiddle(valueOf(textModel.getSpineMiddle(), textUrlMap));
        textVO.setSpineRight(valueOf(textModel.getSpineRight(), textUrlMap));
        if (StringUtils.isNotBlank(textModel.getBgKey())) {
            textVO.setBg(textUrlMap.get(textModel.getBgKey()));
        }
        BgModel bgInfo = textModel.getBgInfo();
        if (bgInfo != null) {
            if (textModel.getBgInfo().getType() == AvgBgType.PICTURE.getCode()) {
                textVO.setBg(textUrlMap.get(textModel.getBgInfo().getImgKey()));
            } else if (textModel.getBgInfo().getType() == AvgBgType.VIDEO.getCode()) {
                textVO.setBg(textUrlMap.get(textModel.getBgInfo().getBgVideo().getMp4Video().getVideo()));
            }
        }
        textVO.setBgInfo(BgVO.valueOf(textModel.getBgInfo(), textUrlMap));
        textVO.setBgEffectV2(AvgEffectVO.valueOf(textModel.getBgEffectV2()));
        textVO.setBgEffectPlayConfig(AvgPlayConfigVO.valueOf(textModel.getBgEffectPlayConfig()));
        if (StringUtils.isNotBlank(textModel.getCharacterKey())) {
            textVO.setCharacter(textUrlMap.get(textModel.getCharacterKey()));
        }
        if (CollectionUtils.isNotEmpty(textModel.getCharacters())) {
            List<String> characters = textModel.getCharacters().stream().map(characterKey -> textUrlMap.get(characterKey)).collect(Collectors.toList());
            textVO.setCharacters(characters);
        }
        textVO.setFace(textModel.getFace());
        if (StringUtils.isNotBlank(textModel.getHeadKey())) {
            textVO.setHead(textUrlMap.get(textModel.getHeadKey()));
        }
        textVO.setPosition(textModel.getPosition());
        if (StringUtils.isNotBlank(textModel.getBgmKey())) {
            textVO.setBgm(!textModel.getBgmKey().equals(STOP_FLAG) ? textUrlMap.get(textModel.getBgmKey()) : STOP_FLAG);
        }
        if (StringUtils.isNotBlank(textModel.getSoundKey())) {
            textVO.setSound(textUrlMap.get(textModel.getSoundKey()));
            textVO.setSound(!textModel.getSoundKey().equals(STOP_FLAG) ? textUrlMap.get(textModel.getSoundKey()) : STOP_FLAG);
        }
        textVO.setLoop(textModel.getLoop());
        if (StringUtils.isNotBlank(textModel.getCvKey())) {
            textVO.setCv(textUrlMap.get(textModel.getCvKey()));
        }
        if (textModel.getHighlightVideo() != null && textModel.getHighlightVideo().getMp4Video() != null) {
            textVO.setVideo(textUrlMap.get(textModel.getHighlightVideo().getMp4Video().getVideo()));
            textVO.setVideoVoice(textUrlMap.get(textModel.getHighlightVideo().getMp4Video().getVideoVoice()));
        }
        textVO.setHighlightVideo(VideoVO.valueOf(textModel.getHighlightVideo(), textUrlMap));
        textVO.setPlayMode(textModel.getPlayMode());
        textVO.setVideoPlayConfig(AvgPlayConfigVO.valueOf(textModel.getVideoPlayConfig()));
        textVO.setHotZone(textModel.getHotZone());
        if (CollectionUtils.isNotEmpty(textModel.getHotZoneV2())) {
            List<HotZoneVO> hotZoneV2 = textModel.getHotZoneV2().stream().map(HotZoneVO::valueOf).collect(Collectors.toList());
            textVO.setHotZoneV2(hotZoneV2);
        }
        textVO.setContext(ContextVO.valueOf(textModel.getContext()));
        if (source == AvgChapterSource.COMIC_AVG_CHAPTER.getCode()) {
            textVO.setNextStoryId(textModel.getNextChapter());
        }
        textVO.setTextLoop(textModel.isTextLoop());
        textVO.setLoopType(textModel.getTextLoopType());
        textVO.setLoopCount(textModel.getTextLoopCount());
        textVO.setLoopNextTextId(textModel.getLoopNextTextId());
        textVO.setLoopBreak(textModel.isLoopBreak());
        textVO.setInsertChapterBack(textModel.isInsertChapterBack());
        textVO.setStuff(StuffVO.valueOf(textModel.getStuff(), textUrlMap));
        textVO.setStuffMove(textModel.getStuffMove());
        textVO.setCharMove(textModel.getCharMove());
        textVO.setSpineMove(textModel.getSpineMove());
        return textVO;
    }

    private static CharVO valueOf(CharModel charModel, Map<String, String> urlMap) {
        if (charModel == null) {
            return null;
        }
        CharVO charVO = new CharVO();
        charVO.setType(charModel.getType());
        charVO.setName(charModel.getName());
        if (CollectionUtils.isNotEmpty(charModel.getFiles())) {
            List<FileVO> fileVOS = charModel.getFiles().stream().map(charFile -> {
                FileVO fileVO = new FileVO();
                fileVO.setName(charFile.getName());
                fileVO.setKey(urlMap.get(charFile.getKey()));
                return fileVO;
            }).collect(Collectors.toList());
            charVO.setFiles(fileVOS);
        }
        return charVO;
    }

    private static DialogueOptionVO valueOf(AvgChapterModelV2.DialogueOption dialogueOption) {
        if (dialogueOption == null) {
            return null;
        }
        DialogueOptionVO dialogueOptionVO = new DialogueOptionVO();
        dialogueOptionVO.setOptionId(dialogueOption.getOptionId());
        dialogueOptionVO.setDialogue(dialogueOption.getDialogue());
        dialogueOptionVO.setNextId(dialogueOption.getNextId());
        dialogueOptionVO.setUnlockCondition(dialogueOption.getUnlockCondition());
        dialogueOptionVO.setValueCondition(dialogueOption.getValueCondition());
        dialogueOptionVO.setIsHideOptions(dialogueOption.isHideOptions());
        dialogueOptionVO.setIsOnetime(dialogueOption.isOnetime());
        return dialogueOptionVO;
    }

    public static AvgChapterVOV2 valueOf(int source, AvgChapterModelV2 avgChapterModel, AvgChapterRecordModel avgChapterRecordModel, String payDomain) {
        AvgChapterDetailModelV2 param = new AvgChapterDetailModelV2().setSource(source)
                .setAvgChapterModel(avgChapterModel)
                .setAvgChapterRecordModel(avgChapterRecordModel)
                .setPayDomain(payDomain);
        return valueOf(param);
    }

    private static String getCdnUrl(String url, int payType, UserAgent userAgent) {
        if (StringUtils.isBlank(url)) {
            return null;
        }
        if (userAgent == null) {
            log.warn("userAgent does not exist");
            return CdnHandler.getEncryptionUrl(url, payType);
        }
        String extension = url.substring(url.lastIndexOf("."));
        if (IMAGE_EXTENSIONS.contains(extension)) {
            Platform platform = userAgent.getPlatform();
            String systemVersion = userAgent.getSystemVersion().toLowerCase().replaceAll("ios", "").trim();
            if (platform == Platform.IPHONE && systemVersion.compareTo("14.0") < 0) {
                if (!extension.equals(PNG_SUFFIX)) {
                    return CdnHandler.getEncryptionUrl(url + "-t.w1125.png.h", payType);
                }
            } else {
                if (!extension.equals(WEBP_SUFFIX)) {
                    return CdnHandler.getEncryptionUrl(url + "-t.w1125.webp.h", payType);
                }
            }
        }
        return CdnHandler.getEncryptionUrl(url, payType);
    }

}

