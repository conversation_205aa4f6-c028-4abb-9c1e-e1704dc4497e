package com.kuaikan.role.game.api.rpc.result.avg;

import java.io.Serializable;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.experimental.Accessors;

import com.google.common.collect.Lists;

import com.kuaikan.role.game.api.bean.AvgDir;
import com.kuaikan.role.game.api.bean.AvgOriginFile;
import com.kuaikan.role.game.api.enums.CharType;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CharModel extends AvgFileModel implements Serializable {

    private static final long serialVersionUID = 5829002372002071308L;
    /**
     * {@link CharType}
     */
    private int type;

    private String name;

    private List<FileModel> files;

    public static AvgFileModel valueOfByFile(AvgOriginFile avgOriginFile) {
        if (avgOriginFile == null) {
            return null;
        }
        return new CharModel().setName(avgOriginFile.getName())
                .setType(CharType.STATIC.getCode())
                .setFiles(Lists.newArrayList(new FileModel().setKey(avgOriginFile.getKey()).setName(avgOriginFile.getName())))
                .setFileType(avgOriginFile.getType())
                .setFileName(avgOriginFile.getName());
    }

    public static AvgFileModel valueOfByDir(AvgDir avgDir, List<AvgOriginFile> dirFiles) {
        if (avgDir == null) {
            return null;
        }
        CharModel charModel = new CharModel();
        charModel.setType(CharType.getCharType(avgDir.getName()).getCode());
        charModel.setName(avgDir.getName());
        charModel.setFileType(avgDir.getType());
        charModel.setFileName(avgDir.getName());
        List<FileModel> charFiles = dirFiles.stream().map(item -> {
            FileModel fileModel = new FileModel();
            fileModel.setKey(item.getKey());
            fileModel.setName(item.getName());
            fileModel.setFileType(avgDir.getType());
            return fileModel;
        }).sorted(Comparator.comparing(FileModel::getName)).collect(Collectors.toList());
        charModel.setFiles(charFiles);
        return charModel;
    }
}
