package com.kuaikan.role.game.api.bean;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 盲盒活动奖励记录
 */
@Data
@Accessors(chain = true)
public class CostumeBlindBoxActivityRewardRecord implements Serializable {

    private static final long serialVersionUID = 3994766266509878756L;

    private int id;

    /**
     * 唯一 id
     */
    private long bid;

    /**
     * 活动 id
     */
    private int activityId;

    /**
     * 奖励档位
     */
    private int levelIndex;

    /**
     * 用户 id
     */
    private int userId;

    /**
     * 奖励 id
     */
    private int prizeId;

    /**
     * 奖励数量
     */
    private int num;

    /**
     * 状态
     */
    private int status;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

}
