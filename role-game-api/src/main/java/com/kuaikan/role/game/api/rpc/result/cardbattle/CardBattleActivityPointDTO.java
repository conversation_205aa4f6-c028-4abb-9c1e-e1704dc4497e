package com.kuaikan.role.game.api.rpc.result.cardbattle;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 卡牌战斗活动2.0体力DTO
 * <AUTHOR>
 * @date 2025/4/22
 */
@Data
@Accessors(chain = true)
public class CardBattleActivityPointDTO implements Serializable {

    private static final long serialVersionUID = 5261996721338047877L;
    private Integer point;
    private Integer pointLimit;
    // 下次获取战斗点数剩余时间
    private Long nextPointRemainTime;
    // 羁绊通用战斗点数图标
    private String pointIcon;

    public static CardBattleActivityPointDTO init(int point, int pointLimit, long nextPointRemainTime, String pointIcon) {
        return new CardBattleActivityPointDTO().setPoint(point).setPointLimit(pointLimit).setNextPointRemainTime(nextPointRemainTime).setPointIcon(pointIcon);
    }
}
