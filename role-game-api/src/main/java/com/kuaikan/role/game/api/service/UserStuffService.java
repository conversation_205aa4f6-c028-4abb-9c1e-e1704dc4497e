package com.kuaikan.role.game.api.service;

import java.util.List;

import com.kuaikan.common.model.RpcResult;
import com.kuaikan.role.game.api.model.AcquisitionPopupWindowModel;
import com.kuaikan.role.game.api.model.UserStuffModel;
import com.kuaikan.role.game.api.rpc.param.ListUserStuffByCostumeIdParam;
import com.kuaikan.role.game.api.rpc.param.ListUserStuffByRoleIdParam;
import com.kuaikan.role.game.api.rpc.param.ListUserStuffParam;
import com.kuaikan.role.game.api.rpc.param.StuffAcquisitionPopupWindowParam;

/**
 * <AUTHOR>
 * @version 2024-04-24
 */
public interface UserStuffService {

    RpcResult<List<UserStuffModel>> listUserStuffByRoleId(ListUserStuffByRoleIdParam param);

    RpcResult<List<UserStuffModel>> listUserStuffByCostumeId(ListUserStuffByCostumeIdParam param);

    RpcResult<List<UserStuffModel>> listUserStuff(ListUserStuffParam param);

    RpcResult<AcquisitionPopupWindowModel> getStuffAcquisitionPopupWindow(StuffAcquisitionPopupWindowParam param);

    /**
     * 漫灵石获取图鉴弹窗 任务信息， 用户信息uid没有作用，仅使用活动任务的跳转能力
     *
     * @return
     */
    RpcResult<AcquisitionPopupWindowModel> getSpiritStoneAcquisitionPopup(int userId, int roleId);

}
