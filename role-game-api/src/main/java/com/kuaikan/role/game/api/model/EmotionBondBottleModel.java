package com.kuaikan.role.game.api.model;

import com.kuaikan.role.game.api.bean.Item;
import com.kuaikan.role.game.api.bo.ImageInfo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/11/14
 */
@Data
@Accessors(chain = true)
public class EmotionBondBottleModel implements Serializable {

    private static final long serialVersionUID = -2647152616804902499L;

    private int id;

    private String name;

    private int balance;

    private int emotionBondValue;

    private Integer roleGroupId;

    private String activityId;

    private ImageInfo image;

    private int fullLevelCount;

    private ActionTargetModel actionTarget;

    private int maxLevel;

    private int currentLevel;

    // 首次获取时间
    private Date firstGetTime;

    public static EmotionBondBottleModel valueOf(Item item, int balance, int fullLevelCount, int maxLevel, int currentLevel, ActionTargetModel actionTarget) {
        if (item == null) {
            return null;
        }
        return new EmotionBondBottleModel().setId(item.getId())
                .setName(item.getName())
                .setEmotionBondValue(item.getConfig().getEmotionBondValue())
                .setActivityId(item.getConfig().getActivityId())
                .setRoleGroupId(item.getConfig().getRoleGroupId())
                .setImage(item.getConfig().getImage())
                .setFirstGetTime(item.getCreatedAt())
                .setBalance(balance)
                .setFullLevelCount(fullLevelCount)
                .setMaxLevel(maxLevel)
                .setCurrentLevel(currentLevel)
                .setActionTarget(actionTarget);
    }

}
