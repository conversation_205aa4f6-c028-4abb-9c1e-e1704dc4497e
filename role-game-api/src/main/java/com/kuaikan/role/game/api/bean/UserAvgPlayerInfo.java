package com.kuaikan.role.game.api.bean;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Accessors(chain = true)
@Document("user_avg_player_info")
@Slf4j
public class UserAvgPlayerInfo implements Serializable {

    private static final long serialVersionUID = -3666487567466432731L;
    @Id
    private String id;

    private int userId;

    private int topicId;

    private String playerName;

    private Date createdAt;

    private Date updatedAt;

}
