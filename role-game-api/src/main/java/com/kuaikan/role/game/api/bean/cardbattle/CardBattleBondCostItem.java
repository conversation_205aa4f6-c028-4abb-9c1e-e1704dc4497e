package com.kuaikan.role.game.api.bean.cardbattle;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 卡牌战斗-羁绊战斗点数记录明细
 * <AUTHOR>
 * @date 2025/3/27
 */
@Data
@Accessors(chain = true)
@Document(collection = "card_battle_bond_cost_item")
public class CardBattleBondCostItem implements Serializable {

    private static final long serialVersionUID = -5731472683028439771L;
    @Id
    private String id;
    private Integer userId;
    // 点数值
    private Integer cost;
    // 使用场景 CardBattleBondCostItemSceneEnum
    private Integer scene;
    // 角色组ID
    private Integer roleGroupId;
    // 活动ID
    private String activityId;
    // 副本ID
    private String dungeonId;
    private Long createTime;
    private Long updateTime;

    public static CardBattleBondCostItem init(Integer userId, Integer cost, Integer scene, Integer roleGroupId, String activityId, String dungeonId) {
        long now = System.currentTimeMillis();
        return new CardBattleBondCostItem().setUserId(userId)
                .setCost(cost)
                .setScene(scene)
                .setRoleGroupId(roleGroupId)
                .setActivityId(activityId)
                .setDungeonId(dungeonId)
                .setCreateTime(now)
                .setUpdateTime(now);
    }

}
