package com.kuaikan.role.game.api.service;

import java.util.List;

import com.kuaikan.common.model.RpcResult;
import com.kuaikan.role.game.api.model.AcquisitionPopupWindowModel;
import com.kuaikan.role.game.api.rpc.param.SilverCoinAcquisitionPopupWindowParam;
import com.kuaikan.role.game.api.rpc.param.SilverCoinChargeParam;
import com.kuaikan.role.game.api.rpc.param.SilverCoinConsumeParam;
import com.kuaikan.role.game.api.rpc.param.SilverCoinPickUpParam;
import com.kuaikan.role.game.api.rpc.result.SilverCoinDropRecordModel;
import com.kuaikan.role.game.api.rpc.result.UserSilverCoinAccountModel;

/**
 * <AUTHOR>
 * @date 2024/4/19
 */
public interface SilverCoinService {

    RpcResult<List<SilverCoinDropRecordModel>> getUserUnPickedList(int userId);

    RpcResult<Void> pickUp(SilverCoinPickUpParam silverCoinPickUpParam);

    /**
     * 卡牌充值
     * @param silverCoinChargeParam 充值参数
     * @return 结果
     */
    RpcResult<Void> cardCharge(SilverCoinChargeParam silverCoinChargeParam);

    /**
     * 卡牌消耗
     * @param silverCoinConsumeParam 消耗参数
     * @return 结果
     */
    RpcResult<Void> cardConsume(SilverCoinConsumeParam silverCoinConsumeParam);

    RpcResult<UserSilverCoinAccountModel> getUserAccount(int userId);

    RpcResult<AcquisitionPopupWindowModel> getAcquisitionPopupWindow(SilverCoinAcquisitionPopupWindowParam param);

    RpcResult<Void> addInitSilverCoin(int userId);
}
