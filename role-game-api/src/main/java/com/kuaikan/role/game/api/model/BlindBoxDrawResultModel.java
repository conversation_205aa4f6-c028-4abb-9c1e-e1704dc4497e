package com.kuaikan.role.game.api.model;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/2/27 14:30
 */

@Data
@Accessors(chain = true)
public class BlindBoxDrawResultModel implements Serializable {

    private static final long serialVersionUID = 8891706977893904363L;

    private List<DrawInfoModel> drawInfoModels;

    @Data
    @Accessors(chain = true)
    public static class DrawInfoModel implements Serializable {

        private static final long serialVersionUID = 5070534021263820845L;

        private int costumeId;

        private int costumePartId;

        private Integer targetAwardCostumeId;

        private int level;

        private String costumeProbabilities;

        private String costumeRanges;

        private double costumeRandom;

        private boolean fallback;

        private boolean guaranteed;

        private int nonGuaranteedCount;

        private String costumeLevelRanges;
    }
}
