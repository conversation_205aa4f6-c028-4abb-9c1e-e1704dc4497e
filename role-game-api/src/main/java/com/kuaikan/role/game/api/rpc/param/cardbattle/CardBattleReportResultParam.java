package com.kuaikan.role.game.api.rpc.param.cardbattle;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 *<AUTHOR>
 *@date 2024/9/14
 */
@Data
@Accessors(chain = true)
public class CardBattleReportResultParam implements Serializable {

    private static final long serialVersionUID = 3577131193111671645L;

    // 活动ID
    private String activityId;

    // 战斗关卡id
    private String battleTaskId;

    // 关卡类型
    private Integer battleTaskType;

    // 等免专题id
    private Integer topicId;

    // 用户出战卡牌id列表
    private List<Long> userCardIdList;

    // 战斗结果, 角色造成的伤害血量
    private Integer reportRoleDamage;
}
