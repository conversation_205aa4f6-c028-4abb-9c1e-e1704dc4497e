package com.kuaikan.role.game.api.enums;

public enum AvgHighlightVideoPlayMode {
    STOP_AFTER_PLAYING(1, "播放完静止"),
    AUTO_END_AFTER_PLAYING(2, "播放完自动结束"),
    LOOP_PLAYING(3, "循环播放"),
    ;

    private final int code;

    private final String desc;

    AvgHighlightVideoPlayMode(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AvgHighlightVideoPlayMode getByCode(Integer code) {
        if (code != null) {
            for (AvgHighlightVideoPlayMode value : values()) {
                if (value.code == code) {
                    return value;
                }
            }
        }
        // 默认返回第一个
        return STOP_AFTER_PLAYING;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
