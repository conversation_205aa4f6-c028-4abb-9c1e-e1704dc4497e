package com.kuaikan.role.game.api.model.collection;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.bo.ImageInfo;

/**
 *
 * <AUTHOR>
 * @date 2024/7/10
 */

@Data
@Accessors(chain = true)
public class GroupRoleModel implements Serializable {

    private static final long serialVersionUID = -7389849500250090237L;

    private int id;

    /**
     * 名称
     */
    private String name;

    /**
     * 图片
     */
    private ImageInfo image;

    /**
     * 用户头像
     */
    private ImageInfo avatar;

    /**
     * 是否已领养
     */
    private boolean adopted;

    private int totalStoryCnt;

    private int userStoryCnt;
}
