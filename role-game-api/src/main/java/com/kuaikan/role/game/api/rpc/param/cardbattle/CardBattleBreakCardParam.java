package com.kuaikan.role.game.api.rpc.param.cardbattle;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 卡牌战斗突破卡牌参数
 *
 * <AUTHOR>
 * @date 2024/10/19
 */
@Data
@Accessors(chain = true)
public class CardBattleBreakCardParam implements Serializable {

    private static final long serialVersionUID = -753096644768780748L;

    private Integer userId;
    private Long userCardId;
    private List<Long> selectUserCardIds;
    private Integer breakAfterMaxLevel;
    private Integer spendSilverCoins;
    private Integer breakPropNum;

    public static CardBattleBreakCardParam init(Integer userId, Long userCardId, List<Long> selectUserCardIds, Integer breakAfterMaxLevel,
                                                Integer spendSilverCoins, Integer breakPropNum) {
        return new CardBattleBreakCardParam().setUserId(userId)
                .setUserCardId(userCardId)
                .setSelectUserCardIds(selectUserCardIds)
                .setBreakAfterMaxLevel(breakAfterMaxLevel)
                .setSpendSilverCoins(spendSilverCoins)
                .setBreakPropNum(breakPropNum);
    }

}
