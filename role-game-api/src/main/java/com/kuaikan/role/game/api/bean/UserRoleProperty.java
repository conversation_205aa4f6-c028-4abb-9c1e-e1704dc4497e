package com.kuaikan.role.game.api.bean;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * UserRoleProperty
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
@Data
@Accessors(chain = true)
public class UserRoleProperty implements Serializable {

    private static final long serialVersionUID = 9731214018868906L;
    private int id;
    private int userId;
    private int roleId;
    /**
     * 等级
     */
    private int roleLevel;
    /**
     * 经验
     */
    private int roleExp;
    /**
     * 疲劳
     */
    private int tiredness;
    /**
     * 心情
     */
    private int mood;
    /**
     * 行动力
     */
    private int energy;

    private int recoverDate;
    private Date createdAt;
    private Date updatedAt;
}
