package com.kuaikan.role.game.api.bean.cardbattle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import lombok.Data;

/**
 * 卡牌战斗通用配置表
 *
 * <AUTHOR>
 * @date 2024/6/13
 */
@Data
public class CardBattleCommonConfig implements Serializable {

    private static final long serialVersionUID = 9028229375769352050L;
    // 配置id
    private long id;
    // 主角加成
    private BigDecimal roleRatio;
    // 属性加成
    private BigDecimal attrRatio;
    // 战斗点数上限
    private int pointLimit;
    // 羁绊通用点数上限
    private int bondPointLimit;
    // 卡组战斗力系数
    private List<BigDecimal> cardGroupRatio;
    // 装扮等级生命力加成系数
    private List<BigDecimal> costumeLevelHpRatio;
    // 卡组生命值加成系数
    private List<BigDecimal> cardGroupHpRatio;
}