package com.kuaikan.role.game.api.rpc.param;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/11/28 16:14
 */
@Data
@Accessors(chain = true)
public class BlindBoxActivityRecordParam implements Serializable {

    private static final long serialVersionUID = -6974433224410451742L;

    /**
     * 唯一 id
     */
    private long bid;

    /**
     * 活动 id
     */
    private int activityId;

    /**
     * 活动名称-埋点用
     */
    private String activityName;

    /**
     * 自选礼包-子奖励
     * */
    private String selectedPrize;

    /**
     * 奖励档位
     */
    private int levelIndex;

    /**
     * 用户 id
     */
    private int userId;

    /**
     * 奖励 id
     */
    private int prizeId;

    /**
     * 奖励名称-埋点用
     */
    private String prizeName;

    /**
     * 奖励数量
     */
    private int num;

    /**
     * 状态
     */
    private int status;
}
