package com.kuaikan.role.game.api.bean;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.enums.UserCouponSourceType;

/**
 * <AUTHOR>
 * @date 2024/12/25 17:22
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class UserBlindBoxCouponRecord extends UserCouponBasicInfo implements Serializable {

    private static final long serialVersionUID = 1408397521397905301L;

    private Integer id;
    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 折扣券id
     */
    private Integer couponId;

    /**
     * 业务id
     */
    private Long bid;

    /**
     * 折扣券来源
     * @see UserCouponSourceType
     */
    private Integer source;
    /**
     * 是否已使用
     */
    private Boolean used;

    /**
     * 领取时间
     */
    private Date createdAt;

    /**
     * 过期时间
     */
    private Date expiredAt;

    /**
     * 使用状态
     * 更新时间
     */
    private Date updatedAt;
}
