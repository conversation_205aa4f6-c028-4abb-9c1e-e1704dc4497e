package com.kuaikan.role.game.api.model;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * StoryTriggerModel
 *
 * <AUTHOR>
 * @since 2024-03-13
 */
@Data
@Accessors(chain = true)
public class StoryTriggerModel implements Serializable {

    private static final long serialVersionUID = -4042722906222832222L;
    private int storyId;
    private boolean firstTime;
    private LevelUpModel levelUpModel;
    private SilverCoinDropRecordModel silverCoinDropRecordModel;
}
