package com.kuaikan.role.game.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/1/16
 */
@Getter
@AllArgsConstructor
public enum AvgFileOfDigitalAssetType {

    CONF_DOC("conf_doc", "最终配置文档"),
    UNKNOWN("unknown", "未知"),
    CG_BACKGROUND("cg_background", "CG背景"),
    CHARACTER_CREATION("character_creation", "角色立绘"),
    DIALOG_AVATAR("dialog_avatar", "对话框头像"),
    BGM_MUSIC("bgm_music", "BGM音乐"),
    SOUND_EFFECT("sound_effect", "音效"),
    CV_SPEECH("cv_speech", "CV语音"),
    DYNAMIC_BACKGROUND("dynamic_background", "动态背景"),
    DYNAMIC_DRAWING("dynamic_drawing", "动态立绘"),
    HIGHLIGHT_VIDEO("highlight_video", "高光视频"),
    TOUCH_HOT_ZONE("touch_hot_zone", "点触热区"),
    UI_STYLE("ui_style", "UI样式"),
    HIGHLIGHT_VIDEO_VOICE("highlight_audio", "高光视频"),
    SPINE_DRAWING("spine_drawing", "spine立绘"),
    Q_PLATE_DRAWING("q_plate_drawing", "Q版立绘"),
    STUFF("stuff", "物品"),

    ;
    private final String code;

    private final String desc;
}
