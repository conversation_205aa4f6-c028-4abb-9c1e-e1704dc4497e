package com.kuaikan.role.game.api.rpc.result.cardbattle;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

import com.fasterxml.jackson.annotation.JsonIgnore;

import com.kuaikan.common.ResponseCodeMsg;

/**
 * 卡牌战斗副本活动列表DTO
 * <AUTHOR>
 * @date 2024/10/29
 */
@Data
@Accessors(chain = true)
public class CardBattleActivityListDTO implements Serializable {

    private static final long serialVersionUID = -4985883877339375836L;

    @JsonIgnore
    private ResponseCodeMsg codeMsg;
    private List<CardBattleActivityDTO> activityList;

    public static CardBattleActivityListDTO init(ResponseCodeMsg codeMsg) {
        return init(codeMsg, null);
    }

    public static CardBattleActivityListDTO init(ResponseCodeMsg codeMsg, List<CardBattleActivityDTO> activityList) {
        return new CardBattleActivityListDTO().setCodeMsg(codeMsg).setActivityList(activityList);
    }

}
