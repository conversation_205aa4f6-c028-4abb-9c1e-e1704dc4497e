package com.kuaikan.role.game.api.model.mq;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

import org.apache.commons.lang3.StringUtils;

import com.kuaikan.common.enums.TargetType;
import com.kuaikan.reward.model.dto.RewardMessageDTO;
import com.kuaikan.role.game.api.bean.RewardOrder;

/**
 * <AUTHOR>
 * @date 2024/11/27 13:57
 */
@Data
@Accessors(chain = true)
public class FreeBlindBoxActivityMessage implements Serializable {

    private static final long serialVersionUID = -1093235224157710145L;

    public static RewardMessageDTO toRewardMessageDTO(RewardOrder record) {
        if (record == null || StringUtils.isBlank(record.getActivityId())) {
            return null;
        }

        RewardMessageDTO rewardMessageDTO = new RewardMessageDTO();
        rewardMessageDTO.setUserId(record.getUserId());
        rewardMessageDTO.setTargetType(TargetType.TOPIC.getCode());
        return rewardMessageDTO;
    }
}
