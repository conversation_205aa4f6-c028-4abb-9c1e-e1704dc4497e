package com.kuaikan.role.game.api.bean;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.common.tools.serialize.JsonUtils;

/**
 * <AUTHOR>
 * @date 2024/9/9 12:11
 */

@Data
@Accessors(chain = true)
public class ScheduleTimeLeveConfigList implements Serializable {

    private static final long serialVersionUID = -3337252898243613861L;

    private List<ScheduleTimeLeveConfig> scheduleTimeLeveConfigs;

    public static ScheduleTimeLeveConfigList valueOf(String value) {
        if (value == null) {
            return null;
        }
        ScheduleTimeLeveConfigList scheduleTimeLeveConfigList = new ScheduleTimeLeveConfigList();
        scheduleTimeLeveConfigList.setScheduleTimeLeveConfigs(JsonUtils.findList(value, ScheduleTimeLeveConfig.class));
        return scheduleTimeLeveConfigList;
    }

    @Data
    @Accessors(chain = true)
    public static class ScheduleTimeLeveConfig implements Serializable {

        private static final long serialVersionUID = -749978744354577702L;

        /**
         * ID
         */
        private int id;

        /**
         * 时间档位分钟
         */
        private int level;
    }

}

