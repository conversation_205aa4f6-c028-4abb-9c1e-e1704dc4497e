package com.kuaikan.role.game.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AdoptSource {

    UNKNOWN(0, "未知"),
    GRAIN_CABINET(1, "谷柜"),
    COSTUME_PAGE(2, "装扮页"),
    EMOTION_BOND(3, "羁绊等级页"),
    HOME(4, "首页"),
    INTERACTIVE(5, "互动"),
    CITY(6, "城市"),
    ADOPT_ANIMATION(7, "领养动画"),
    ACTIVITY(8, "活动");

    private final int code;

    private final String desc;

    public static AdoptSource getByCode(int code) {
        for (AdoptSource value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return UNKNOWN;
    }
}
