package com.kuaikan.role.game.api.bean.cardbattle;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * BattleDungeonConfig 副本配置
 *
 * <AUTHOR> yukuixing
 * @since : 2024-05-06 10:42
 */
@Data
@Document(collection = "battle_activity")
public class BattleDungeonConfig implements Serializable {

    private static final long serialVersionUID = 4061807948815404372L;
    @Id
    private String id;
    // 标题
    private String name;
    // 副标题
    private String desc;
    // 探索列表图片
    private String icon;
    // 探索banner
    private String banner;
    // 探索类型 @see CardBattleDungeonTypeEnum
    private Integer type;
    // 探索总次数
    private int taskCnt;
    // 背景图
    private List<String> exploreBgImgs;
    // 战斗背景图
    private String battleBgImg;
    // 探索事件概率
    private TaskProbability taskProbability;
    // 奖励掉落关卡奖励配置
    private PrizeConf awardTaskPrizeConf;
    // 战斗关卡 战斗对象可选择的怪兽列表
    private List<String> battleMonsterIds;// 战斗关卡 战斗对象可选择的怪兽列表
    private List<BattleMonsterConfig> battleMonsterConfig;
    // 战斗关卡奖励配置
    private PrizeConf battleTaskPrizeConf;
    // 指定探索关卡
    private List<SpecTask> specTasks;
    // 完成探索的奖励信息
    private List<Long> prizeIds;
    // 是否展示avg故事(探索页&活动页)
    private boolean hasAvg;
    private boolean displayAvg;
    private List<AvgConfig> avgConfig;
    // 探索生效状态
    private int status;
    // 顺序
    private int order;
    // 开始时间
    private Long startTime;
    // 结束时间
    private Long endTime;
    // 限定专题
    private boolean limitTopic;
    // 专题id
    private List<Integer> topicIds;
    // 照片配置
    private boolean hasPhoto;
    private StoryConfig photoConfig;
    // 解锁条件类型及等级
    private Integer unlockBondLevel;
    // 挂机1min消耗点数
    private Integer afkCost;
    private String operator;
    private long createAt;
    private long updateAt;

    @Data
    public static class TaskProbability implements Serializable {

        private static final long serialVersionUID = 3515161697430299654L;
        public static final int TOTAL_POTION = 10000;

        private int awardTask;
        private int battleTask;
        private int nothing;
    }

    @Data
    public static class SpecTask implements Serializable {

        private static final long serialVersionUID = -4956185779801090330L;
        private int taskSeq;

        /**
         * 任务模式
         *
         * @see com.kuaikan.role.game.api.constant.BattleActivityConstants.TaskMode
         */
        private int taskMode;

        private String monsterId;
        private int monsterLevel;
        private PrizeConf prizeConf;
    }

    @Data
    public static class PrizeConf implements Serializable {

        private static final long serialVersionUID = -3938479419140700102L;
        /**
         * 奖励模式
         *
         * @see com.kuaikan.role.game.api.constant.BattleActivityConstants.PrizeMode
         */
        private int prizeMode;

        private int lotteryId;
        private List<Long> prizeIds;
    }

    @Data
    public static class BattleMonsterConfig implements Serializable {

        private static final long serialVersionUID = -1362448408471350051L;

        private String monsterId;
        private int monsterLevel;
    }

    @Data
    public static class AvgConfig implements Serializable {

        private static final long serialVersionUID = -2857379384839911120L;
        // 类型，0：关卡前，1：关卡后
        private int type;
        // 关卡数
        private int level;
        // avg id
        private Integer avgId;
        // (羁绊副本) avg使用剧情id
        private Integer storyId;
        private String avgIcon;
    }

    @Data
    public static class StoryConfig implements Serializable {

        private static final long serialVersionUID = 4050980787595921581L;
        private String type = "photo";
        // 章节类型， 当前只能是照片, storyId
        private Integer storyId;
        // （副本通关时）照片初始概率， 50表示概率为 50%
        private int probability;
    }

}
