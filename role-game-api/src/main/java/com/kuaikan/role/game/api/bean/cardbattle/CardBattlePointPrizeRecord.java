package com.kuaikan.role.game.api.bean.cardbattle;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 卡牌战斗用户获取战斗点数奖励记录
 *
 * <AUTHOR>
 * @date 2024/7/16
 */
@Data
@Accessors(chain = true)
@Document(collection = "card_battle_point_prize_record")
public class CardBattlePointPrizeRecord implements Serializable {

    @Id
    private String id;

    // 订单ID
    private String orderId;

    // uid
    private Long userId;

    private int count;

    // 创建时间
    private Long createdTime;

    // 更新时间
    private Long updatedTime;

    public static CardBattlePointPrizeRecord init(String orderId, Long userId, int count) {
        return new CardBattlePointPrizeRecord().setOrderId(orderId)
                .setUserId(userId)
                .setCount(count)
                .setCreatedTime(System.currentTimeMillis())
                .setUpdatedTime(System.currentTimeMillis());
    }

}
