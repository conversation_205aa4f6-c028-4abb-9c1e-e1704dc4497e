package com.kuaikan.role.game.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * AvgUnlockCondition
 *
 * <AUTHOR>
 * @since 2024-06-21
 */
@AllArgsConstructor
@Getter
public enum AvgUnlockCondition {

    UNKNOWN(0, "未知", "未知", 10),
    CULTURE_LEVEL_UNLOCK(1, "知识等级解锁", "知识属性", 1),
    SPORT_LEVEL_UNLOCK(2, "体能等级解锁", "体能属性", 2),
    ART_LEVEL_UNLOCK(3, "艺术等级解锁", "艺术属性", 3),
    SOCIAL_LEVEL_UNLOCK(4, "魅力等级解锁", "魅力属性", 4),
    HOUSEWORK_LEVEL_UNLOCK(5, "厨艺等级解锁", "厨艺属性", 5),
    FREE_UNLOCK(6, "初始解锁", "初始", 0),
    ROLE_MAX_LEVEL_UNLOCK(7, "角色满级解锁", "满级", 8),
    WORK_UNLOCK(8, "打工解锁", "打工", 6),
    FUN_UNLOCK(9, "娱乐解锁", "娱乐", 7),
    SCHEDULE_UNLOCK(10, "日程解锁", "日程", 9),
    EMOTION_BOND_UNLOCK_FOR_FEED(11, "羁绊等级解锁(投喂)", "羁绊值提升至Lv.%d后喂养角色触发", 11),
    EMOTION_BOND_UNLOCK_FOR_DIRECTLY(12, "羁绊等级解锁(直接)", "羁绊值提升至Lv.%d解锁", 12),
    READ_COMIC_UNLOCK(13, "阅读指定章节解锁", "阅读漫画【%s】解锁", 10),
    INTERACTIVE_ITEM_UNLOCK(14, "互动道具解锁", "使用互动道具【%s】可解锁", 13),
    BOND_ACTIVITY_UNLOCK(15, "羁绊之旅解锁", "羁绊之旅通关解锁", 14),
    ;

    private int code;

    private String desc;

    private String unlockCondition;

    private int order;

    public static boolean isDimensionUnlock(Integer unlockCondition) {
        return unlockCondition != null && (unlockCondition >= CULTURE_LEVEL_UNLOCK.getCode() && unlockCondition <= HOUSEWORK_LEVEL_UNLOCK.getCode());
    }

    public static AvgUnlockCondition getByCode(Integer code) {
        if (code == null) {
            return UNKNOWN;
        }
        for (AvgUnlockCondition avgUnlockCondition : AvgUnlockCondition.values()) {
            if (code == avgUnlockCondition.getCode()) {
                return avgUnlockCondition;
            }
        }
        return UNKNOWN;
    }
}
