package com.kuaikan.role.game.api.bean;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

import org.apache.commons.lang3.StringUtils;

import com.kuaikan.role.game.api.bo.ImageInfo;
import com.kuaikan.role.game.api.enums.AvgUnlockCondition;
import com.kuaikan.role.game.api.enums.StoryLevel;
import com.kuaikan.role.game.api.enums.StoryType;
import com.kuaikan.role.game.api.util.GsonUtils;

/**
 * <AUTHOR>
 * @version 2024-03-11
 */
@Data
@Accessors(chain = true)
public class Story implements Serializable {

    // 如果拥有了该剧情，再次获得无论等级都获得6经验
    public static final int OWN_STORY_EXP = 6;

    private static final long serialVersionUID = 6272765682067126792L;

    private int id;

    private String name;
    /**
     * @see com.kuaikan.role.game.api.enums.StoryType
     */
    private String type;
    /**
     * 等级
     * @see com.kuaikan.role.game.api.enums.StoryLevel
     */
    private int level;

    /**
     * 剧情配置 {@link StoryConfig}
     */
    private String config;

    private boolean canRepeat;

    private int rewardCostumeId;

    private List<Integer> roleIds;

    private int status;

    private int scheduleId;

    private Date createdAt;

    private Date updatedAt;

    public Integer getAvgChapterId() {
        if (StringUtils.equals(this.type, StoryType.AVG.getCode())) {
            StoryAvgConfig storyAvgConfig = GsonUtils.tryParseObject(this.config, StoryAvgConfig.class);
            if (storyAvgConfig == null) {
                return null;
            }
            return storyAvgConfig.getAvgChapterId();
        }
        return null;
    }

    public int getStoryExp(boolean isOwned) {
        if (isOwned) {
            return OWN_STORY_EXP;
        }
        StoryLevel level = StoryLevel.getByCode(this.level);
        return level != null ? level.getExp() : 0;
    }

    public Integer getStoryUnlockCondition() {
        if (StringUtils.equals(this.type, StoryType.AVG.getCode())) {
            StoryAvgConfig storyAvgConfig = GsonUtils.tryParseObject(this.config, StoryAvgConfig.class);
            if (storyAvgConfig == null) {
                return AvgUnlockCondition.UNKNOWN.getCode();
            }
            return storyAvgConfig.getUnlockingConditions();
        }
        // letter
        if (StringUtils.equals(this.type, StoryType.LETTER.getCode())) {
            StoryLetterConfig storyLetterConfig = GsonUtils.tryParseObject(this.config, StoryLetterConfig.class);
            if (storyLetterConfig == null || storyLetterConfig.getUnlockingConditions() == null) {
                return AvgUnlockCondition.SCHEDULE_UNLOCK.getCode();
            }
            return storyLetterConfig.getUnlockingConditions();
        }

        if (StringUtils.equals(this.type, StoryType.ACTION.getCode()) || StringUtils.equals(this.type, StoryType.DAILY.getCode()) || StringUtils.equals(
                this.type, StoryType.EGG.getCode())) {
            StoryActionConfig storyActionConfig = GsonUtils.tryParseObject(this.config, StoryActionConfig.class);
            if (storyActionConfig != null) {
                return storyActionConfig.getUnlockingConditions();
            }
        }

        if (StringUtils.equals(this.type, StoryType.PHOTO.getCode())) {
            StoryPhotoConfig storyPhotoConfig = GsonUtils.tryParseObject(this.config, StoryPhotoConfig.class);
            if (storyPhotoConfig != null) {
                return storyPhotoConfig.getUnlockingConditions();
            }
        }

        if (StringUtils.equals(this.type, StoryType.VOICE.getCode())) {
            StoryVoiceConfig storyVoiceConfig = GsonUtils.tryParseObject(this.config, StoryVoiceConfig.class);
            if (storyVoiceConfig != null) {
                return storyVoiceConfig.getUnlockingConditions();
            }
        }

        return AvgUnlockCondition.UNKNOWN.getCode();
    }

    public ImageInfo getCoverImage() {
        if (type.equals(StoryType.AVG.getCode())) {
            StoryAvgConfig storyAvgConfig = GsonUtils.tryParseObject(config, StoryAvgConfig.class);
            return storyAvgConfig.getCollectionImage();
        }
        return null;
    }

}
