package com.kuaikan.role.game.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import org.apache.commons.lang3.StringUtils;

/**
 * RoleBubbleStyle
 *
 * 气泡样式
 *
 * <AUTHOR>
 * @since 2024/8/14
 */
@AllArgsConstructor
@Getter
public enum RoleBubbleStyle {
    //思考
    Thinking(1, "thinking"),
    //说话
    Talking(2, "talking"),
    ;

    private int code;
    private String desc;

    public static RoleBubbleStyle getByCode(int code) {
        for (RoleBubbleStyle value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }

    public static RoleBubbleStyle getByDesc(String desc) {
        for (RoleBubbleStyle value : values()) {
            if (StringUtils.equals(desc, value.desc)) {
                return value;
            }
        }
        return null;
    }
}
