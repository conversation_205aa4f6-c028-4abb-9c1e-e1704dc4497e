package com.kuaikan.role.game.common.bean;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/9/24 16:07
 */
@Data
@Accessors(chain = true)
public class RoleUnlockStoryNumerical implements Serializable {

    private static final long serialVersionUID = -6343904178233007342L;

    private Integer roleId;

    private String roleName;

    private Integer unlockLevel;

    private Integer storyId;
}
