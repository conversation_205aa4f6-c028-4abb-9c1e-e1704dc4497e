package com.kuaikan.role.game.common.bean;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * role_group_relation
 * <AUTHOR>
@Data
@Accessors(chain = true)
public class RoleGroupRelation implements Serializable {

    private static final long serialVersionUID = -6490368687051948738L;
    private int id;
    private int roleGroupId;
    private int roleId;
    private int orderNum;
    private Date createdAt;
    private Date updatedAt;
}