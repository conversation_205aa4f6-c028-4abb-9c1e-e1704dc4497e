package com.kuaikan.role.game.common.bean;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * UserCity
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@Data
@Accessors(chain = true)
public class UserCity implements Serializable {

    private static final long serialVersionUID = -6995326374316146174L;
    private int id;
    private int userId;
    private int level;
    private int exp;
    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

}
