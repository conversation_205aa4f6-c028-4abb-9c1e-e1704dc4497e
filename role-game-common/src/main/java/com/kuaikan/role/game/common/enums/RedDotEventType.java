package com.kuaikan.role.game.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 * <AUTHOR>
 * @date 2024/7/5
 */
@Getter
@AllArgsConstructor
public enum RedDotEventType {

    UNKNOWN(0, "未知"),
    ADD_STORY(1, "添加剧情"),
    ADD_ROLE(2, "添加角色"),
    OBTAIN_ITEM(3, "获得道具"),
    OBTAIN_FOOD(4,"获得食物"),
    ROLE_ADOPT_COUPON(5, "获取角色领养券"),
    ADD_COSTUME(6,"上新装扮"),
    ROLE_GROUP_ADD_COSTUME(7,"角色组上新装扮"),
    BLIND_BOX_COUPON(8, "获取盲盒折扣券"),
    ADD_STORY_TAB_AVG(9,"添加avg剧情tab"),
    ADD_STORY_TAB_LETTER(10,"添加letter剧情tab"),
    ADD_STORY_TAB_DAILY(11,"添加daily剧情tab"),
    ADD_STORY_TAB_PHOTO(12,"添加photo剧情tab"),
    ADD_STORY_TAB_VOICE(13,"添加voice剧情tab"),
    ADD_NEW_TASK(14, "任务上新"),
    ADD_MAP_STORY(22, "普通地图剧情上新"),
    ADD_MAP_STORY_TAG(24, "普通地图剧情上新TAG"),
    MAP_USER_ACCEPT_STORY_TAG(25, "用户获得新剧情TAG"),
    MAP_HOME_ADD_STORY(26, "地图首页剧情上新"),
    MAP_HOME_USER_ACCEPT_STORY(27, "用户首页获得新剧情")
    ;

    private final int code;
    private final String desc;

    public static RedDotEventType getByCode(int code) {
        for (RedDotEventType status : RedDotEventType.values()) {
            if (code == status.getCode()) {
                return status;
            }
        }
        return UNKNOWN;
    }
}
