package com.kuaikan.role.game.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum MapElementType {

    UNKNOWN(0, "未知"),
    COMMON_BUILDING(1, "通用建筑"),
    SPECIAL_BUILDING(2, "特殊建筑"),
    NPC(3, "NPC"),
    DECORATION(4, "装饰物"),
    NORMAL_BUILDING(6, "普通建筑");

    private final int code;
    private final String desc;

    public static MapElementType getByCode(int code) {
        for (MapElementType status : MapElementType.values()) {
            if (code == status.getCode()) {
                return status;
            }
        }
        return UNKNOWN;
    }
}
