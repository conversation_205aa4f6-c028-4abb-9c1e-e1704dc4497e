package com.kuaikan.role.game.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 * <AUTHOR>
 * @date 2024/7/5
 */
@Getter
@AllArgsConstructor
public enum CityExpSource {
    UNKNOWN(0, "未知"),
    COMPLETE_SCHEDULE(1, "完成日程"),
    ADOPT_ROLE(2, "领养一个角色"),
    UPGRADE_BUILDING(3, "升级一次建筑"),
    UPGRADE_FIVE_DIMENSIONAL_ATTRIBUTE(4, "每提升一级五维属性"),
    UPGRADE_ROLE_LEVEL(5, "每提升一次角色等级"),
    ;
    private final int code;
    private final String desc;

    public static CityExpSource getByCode(int code) {
        for (CityExpSource status : CityExpSource.values()) {
            if (code == status.getCode()) {
                return status;
            }
        }
        return UNKNOWN;
    }
}
