package com.kuaikan.role.game.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CityBuildingValueType {
    ACHIVEMENTSCHEDULE(1, "完成一次挂机", "ACHIVEMENTSCHEDULE"),
    ADOP<PERSON>OL<PERSON>(2, "领养一个角色", "ADOP<PERSON>OLE"),
    BUILDINGLEVELUP(3, "升级一次建筑", "BUILDINGLEVELUP"),
    FIVEATTRIBULT(4, "提升一次五维属性", "FIVEATTRIBULT"),
    ROLELEVELUP(5, "完成一次角色升级", "ROL<PERSON>EVEL<PERSON>");

    private int code;
    private final String actionDesc;
    private final String desc;

    public static boolean isValidType(String desc) {
        for (CityBuildingValueType type : CityBuildingValueType.values()) {
            if (type.getDesc().equals(desc)) {
                return true;
            }
        }
        return false;
    }

    public static CityBuildingValueType getByDesc(String desc) {
        for (CityBuildingValueType type : CityBuildingValueType.values()) {
            if (type.getDesc().equals(desc)) {
                return type;
            }
        }
        return null;
    }
}
