package com.kuaikan.role.game.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import org.apache.commons.lang3.StringUtils;

/**
 * RoleBubbleContentType
 *
 * <AUTHOR>
 * @since 2024/8/14
 */
@AllArgsConstructor
@Getter
public enum RoleBubbleContentType {
    //文字
    Text(1, "text"),
    //图片
    Image(2, "image"),
    ;

    private int code;
    private String desc;

    public static RoleBubbleContentType getByCode(int code) {
        for (RoleBubbleContentType value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }

    public static RoleBubbleContentType getByDesc(String desc) {
        for (RoleBubbleContentType value : values()) {
            if (StringUtils.equals(desc, value.desc)) {
                return value;
            }
        }
        return null;
    }
}
