package com.kuaikan.role.game.backend.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.enums.UserCostumePartStatus;
import com.kuaikan.role.game.api.model.CostumePartModel;
import com.kuaikan.role.game.api.model.UserCostumePartModel;
import com.kuaikan.role.game.api.rpc.result.UserSilverCoinAccountModel;

/**
 * <AUTHOR>
 * @version 2024-04-30
 */
@Data
@Accessors(chain = true)
public class UserCostumePartVO implements Serializable {

    private static final long serialVersionUID = 6985712419892385778L;

    private int costumePartId;

    private String costumePartName;

    private ImageInfoVO consumePartImage;

    private int ownedNum;
    /**
     * @see UserCostumePartStatus
     */
    private int status;
    /**
     * 未获得的如果可以合成显示「去合成」状态，不可合成显示[未获得]；已获得的显示「已拥有」文案
     */
    private String statusText;

    private ComposeInfoVO composeInfo;

    private String obtainText;

    private String url;

    public static UserCostumePartVO valueOf(UserCostumePartModel userCostumePartModel, UserSilverCoinAccountModel userSilverCoinAccountModel) {
        final UserCostumePartVO userCostumePartVO = new UserCostumePartVO();
        userCostumePartVO.setCostumePartId(userCostumePartModel.getCostumePartModel().getId());
        userCostumePartVO.setCostumePartName(userCostumePartModel.getCostumePartModel().getName());
        userCostumePartVO.setConsumePartImage(ImageInfoVO.valueOf(userCostumePartModel.getCostumePartModel().getConsumePartImage()));
        userCostumePartVO.setOwnedNum(userCostumePartModel.getBalance());
        final UserCostumePartStatus userCostumePartStatus = UserCostumePartStatus.getByOwnedNum(userCostumePartModel.getBalance());
        userCostumePartVO.setStatus(userCostumePartStatus.getCode());
        final boolean canCompose = userCostumePartModel.getCostumePartModel().isCanCompose();
        userCostumePartVO.setStatusText(userCostumePartStatus.getText());
        userCostumePartVO.setObtainText(userCostumePartModel.getCostumePartModel().getObtainText());
        userCostumePartVO.setUrl(userCostumePartModel.getCostumePartModel().getUrl());
        final CostumePartModel.ComposeConfigModel composeConfigModel = userCostumePartModel.getCostumePartModel().getComposeConfigModel();
        if (canCompose && composeConfigModel != null) {
            final ComposeInfoVO composeInfoVO = ComposeInfoVO.valueOf(userSilverCoinAccountModel, composeConfigModel);
            userCostumePartVO.setComposeInfo(composeInfoVO);
        }
        return userCostumePartVO;
    }

    @Data
    @Accessors(chain = true)
    public static class ComposeInfoVO implements Serializable {

        private UserSilverCoinVO userSilverCoin;

        private List<UserStuffVO> userStuffList;

        public static ComposeInfoVO valueOf(UserSilverCoinAccountModel userSilverCoinAccountModel, CostumePartModel.ComposeConfigModel composeConfigModel) {
            final ComposeInfoVO composeInfoVO = new ComposeInfoVO();
            List<UserStuffVO> userStuffVOS = new ArrayList<>();
            composeInfoVO.setUserStuffList(userStuffVOS);
            for (CostumePartModel.StuffConfigModel stuffConfig : composeConfigModel.getStuffConfigs()) {
                final int stuffId = stuffConfig.getStuffId();
                UserStuffVO userStuffVO = UserStuffVO.valueOf(stuffId, stuffConfig.getStuffCount());
                userStuffVOS.add(userStuffVO);
            }
            UserSilverCoinVO userSilverCoinVO = new UserSilverCoinVO();
            composeInfoVO.setUserSilverCoin(userSilverCoinVO);
            userSilverCoinVO.setNeedNum(composeConfigModel.getSilverCoinCount());
            userSilverCoinVO.setOwnedNum(userSilverCoinAccountModel == null ? 0 : userSilverCoinAccountModel.getBalance());
            return composeInfoVO;
        }
    }

    @Data
    @Accessors(chain = true)
    public static class UserSilverCoinVO implements Serializable {

        private int ownedNum;

        private int needNum;
    }

    @Data
    @Accessors(chain = true)
    public static class UserStuffVO implements Serializable {

        private int stuffId;

        private int needNum;

        public static UserStuffVO valueOf(int stuffId, int needNum) {
            return new UserStuffVO().setStuffId(stuffId).setNeedNum(needNum);
        }
    }

}
