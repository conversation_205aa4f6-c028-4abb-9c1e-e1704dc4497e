package com.kuaikan.role.game.backend.service;

import static com.kuaikan.role.game.backend.util.ThreadPoolConfig.ROLE_GAME_SERVICE_POOL;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import com.kuaikan.comic.common.concurrent.AsyncWork;
import com.kuaikan.comic.common.utils.ThreadPoolFactory;
import com.kuaikan.common.model.RpcResult;
import com.kuaikan.role.game.api.rpc.result.ResourceConfigModel;
import com.kuaikan.role.game.api.service.ResourceConfigService;

/**
 * <AUTHOR>
 * @date 2024/4/28
 */
@Service
@Slf4j
public class AsyncResourceConfigService {

    @Resource
    private ResourceConfigService resourceConfigService;

    public void getEffectResourceConfigList(Consumer<List<ResourceConfigModel>> callback) {
        AsyncWork.supplyAsync(() -> {
            RpcResult<List<ResourceConfigModel>> rpcResult = resourceConfigService.getEffectResourceConfigList();
            if (!rpcResult.isSuccess()) {
                log.warn("getEffectResourceConfigList failed, result:{}", rpcResult);
                return new ArrayList<>();
            }
            log.info("getEffectResourceConfigListAsync, resourceConfigModel:{}", rpcResult.getData());
            return rpcResult.getData();

        }, ThreadPoolFactory.acquire(ROLE_GAME_SERVICE_POOL.getPoolName()), callback);
    }
}
