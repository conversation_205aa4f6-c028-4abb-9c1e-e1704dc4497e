package com.kuaikan.role.game.backend.model;

import java.io.Serializable;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.kuaikan.comic.utils.CdnUtil;
import com.kuaikan.role.game.api.model.CommonScheduleResultModel;
import com.kuaikan.role.game.api.model.CommonScheduleResultModel.CommonPrizeModel;
import com.kuaikan.role.game.api.model.CommonScheduleResultModel.StoryPrizeModel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import com.google.common.collect.Lists;

@Data
@Accessors(chain = true)
@ApiModel("通用地图完成日程返回")
public class CommonScheduleResultVO implements Serializable{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("通用地图完成日程返回-日程ID")
    private int scheduleId;
    @ApiModelProperty("通用地图完成日程返回-日程名称")
    private String scheduleName;
    @ApiModelProperty("通用地图完成日程返回-消耗时间")
    private int consumeMinutes;
    @ApiModelProperty("通用地图完成日程返回-消耗行动力")
    private float consumeEnergy;
    @ApiModelProperty("通用地图完成日程返回-通用奖励")
    private List<CommonPrizeVO> commonPrizes;
    @ApiModelProperty("通用地图完成日程返回-剧情奖励")
    private StoryPrizeVO storyPrize;

    public static CommonScheduleResultVO of(CommonScheduleResultModel model) {
        return new CommonScheduleResultVO().setScheduleId(model.getScheduleId())
                .setScheduleName(model.getScheduleName())
                .setConsumeMinutes(model.getConsumeMinutes())
                .setConsumeEnergy(model.getConsumeEnergy())
                .setCommonPrizes(
                        Optional.ofNullable(model.getCommonPrizes()).orElse(Lists.newArrayList()).stream().map(CommonPrizeVO::of).collect(Collectors.toList()))
                .setStoryPrize(StoryPrizeVO.of(model.getStoryPrize()));
    }

    @Data
    @Accessors(chain = true)
    @ApiModel("通用地图完成日程返回-通用奖励")
    public static class CommonPrizeVO implements Serializable{
        private static final long serialVersionUID = 1L;

        @ApiModelProperty("通用地图完成日程返回-通用奖励-ID")
        private long id;
        @ApiModelProperty("通用地图完成日程返回-通用奖励-名称")
        private String name;
        @ApiModelProperty("通用地图完成日程返回-通用奖励-图标")
        private String icon;
        @ApiModelProperty("通用地图完成日程返回-通用奖励-数量")
        private int count;

        public static CommonPrizeVO of(CommonPrizeModel model) {
            String domain = CdnUtil.getDomainWithBackSlash();
            return new CommonPrizeVO().setId(model.getId()).setName(model.getName()).setIcon(domain + model.getIcon()).setCount(model.getCount());
        }
    }

    @Data
    @Accessors(chain = true)
    @ApiModel("通用地图完成日程返回-剧情奖励")
    public static class StoryPrizeVO implements Serializable{
        private static final long serialVersionUID = 1L;

        @ApiModelProperty("通用地图完成日程返回-剧情奖励-ID")
        private int id;
        @ApiModelProperty("通用地图完成日程返回-剧情奖励-名称")
        private String name;

        public static StoryPrizeVO of(StoryPrizeModel model) {
            if (model == null) {
                return null;
            }
            return new StoryPrizeVO().setId(model.getId()).setName(model.getName());
        }
    }
}