package com.kuaikan.role.game.backend.model;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.model.ActivityDetailModel;
import com.kuaikan.role.game.api.model.BlindBoxActivityModel;

/**
 * <AUTHOR>
 * @date 2024/11/18 18:13
 */
@Data
@Accessors(chain = true)
public class PrizeInfoVO implements Serializable {

    private static final long serialVersionUID = -5802739158254331401L;

    /**
     * 奖励图像
     */
    private ImageInfoVO image;

    /**
     * 文案
     */
    private String desc;

    /**
     * 奖励名称
     */
    private String name;

    /**
     * 奖励数量
     */
    private Integer num;

    /**
     * 盲盒活动奖励类型
     * {@see com.kuaikan.role.game.admin.biz.ActivityBiz#REWARD_TYPES}
     */
    private Integer type;

    public static PrizeInfoVO valueOf(ActivityDetailModel.PrizeInfo prizeInfo) {
        return new PrizeInfoVO().setImage(ImageInfoVO.valueOf(prizeInfo.getImage())).setDesc(prizeInfo.getDesc());
    }

    /**
     * 档位奖励转换VO
     */
    public static PrizeInfoVO modelToVO(BlindBoxActivityModel.PrizeInfoModel currentPrizeInfo) {
        return new PrizeInfoVO().setImage(ImageInfoVO.valueOf(currentPrizeInfo.getImage()))
                .setName(currentPrizeInfo.getName())
                .setNum(currentPrizeInfo.getRewardNum())
                .setType(currentPrizeInfo.getType());
    }
}

