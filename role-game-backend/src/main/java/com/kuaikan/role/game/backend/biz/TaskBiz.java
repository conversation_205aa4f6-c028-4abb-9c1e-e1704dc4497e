package com.kuaikan.role.game.backend.biz;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import com.kuaikan.account.api.model.PassportContext;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.comic.utils.CdnUtil;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.model.RpcResult;
import com.kuaikan.common.util.IpLocationHelper;
import com.kuaikan.game.gamecard.dubbo.dto.ClientInfoDTO;
import com.kuaikan.game.gamecard.questcontainer.def.dto.RewardObtainResultDto;
import com.kuaikan.role.game.api.rpc.param.TaskListParam;
import com.kuaikan.role.game.api.rpc.param.TaskRewardBatchParam;
import com.kuaikan.role.game.api.rpc.result.TaskModuleConfigModel;
import com.kuaikan.role.game.api.service.CommonMapService;
import com.kuaikan.role.game.api.service.RedDotService;
import com.kuaikan.role.game.api.service.TaskService;
import com.kuaikan.role.game.backend.model.RewardBatchParam;
import com.kuaikan.role.game.backend.model.TaskModuleConfigVO;
import com.kuaikan.role.game.backend.model.map.MapTaskVO;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class TaskBiz {

    @Resource
    private CommonMapService commonMapService;
    @Resource
    private TaskService taskService;
    @Resource
    private RedDotService redDotService;

    public BizResult<TaskModuleConfigVO> taskList() {
        ClientInfoDTO clientInfoDTO = getClientInfoDTO();
        RpcResult<TaskModuleConfigModel> rpcResult = taskService.taskList(new TaskListParam().setClientInfoDTO(clientInfoDTO));
        if (!rpcResult.isSuccess() || rpcResult.getData() == null) {
            return BizResult.result(rpcResult.getCode(), rpcResult.getMessage());
        }
        String domain = CdnUtil.getDomainWithBackSlash();
        List<TaskModuleConfigVO.TaskInfoVO> taskInfos = Optional.ofNullable(rpcResult.getData().getTaskConfigList())
                .orElse(new ArrayList<>())
                .stream()
                .map(item -> TaskModuleConfigVO.valueOf(item, domain))
                .collect(Collectors.toList());
        TaskModuleConfigVO taskModuleConfigVO = new TaskModuleConfigVO().setTaskList(taskInfos).setHost(CdnUtil.getDomainWithBackSlash());
        return BizResult.success(taskModuleConfigVO);
    }

    @NotNull
    private static ClientInfoDTO getClientInfoDTO() {
        ClientInfoDTO clientInfoDTO = new ClientInfoDTO();
        clientInfoDTO.setUserId(PassportContext.getUserId());
        clientInfoDTO.setXDeviceHeader(PassportContext.getXDevice());
        clientInfoDTO.setUserAgent(PassportContext.getUserAgent());
        clientInfoDTO.setDeviceRegisterTime(PassportContext.getDeviceRegisterTime());
        clientInfoDTO.setLocationInfo(IpLocationHelper.getIpLocation(PassportContext.getIp()));
        clientInfoDTO.setVisitor(PassportContext.isVisitor());
        return clientInfoDTO;
    }

    public BizResult<RewardObtainResultDto> rewardBatch(RewardBatchParam rewardBatchParam) {
        TaskRewardBatchParam taskRewardBatchParam = new TaskRewardBatchParam().setClientInfoDTO(getClientInfoDTO())
                .setQuestContainerId(NumberUtils.toLong(rewardBatchParam.getQuestContainerId()))
                .setQuestPrizeBatchDtoList(rewardBatchParam.getQuestPrizeBatchDtoList()
                        .stream()
                        .map(item -> new TaskRewardBatchParam.PrizeBatchParam().setQuestId(item.getQuestId()).setPrizes(item.getPrizes()))
                        .collect(Collectors.toList()));
        RpcResult<RewardObtainResultDto> rpcResult = taskService.rewardBatch(taskRewardBatchParam);
        if (!rpcResult.isSuccess() || rpcResult.getData() == null) {
            return BizResult.result(rpcResult.getCode(), rpcResult.getMessage());
        }
        RewardObtainResultDto result = rpcResult.getData();
        result.setHost(CdnUtil.getDomainWithBackSlash());
        return BizResult.success(result);
    }

    public BizResult<Void> clearRedDot(int userId, long taskId) {
        RpcResult<Void> clearNewTaskEventResult = redDotService.clearNewTaskEvent(userId, taskId);
        if (!clearNewTaskEventResult.isSuccess()) {
            log.error("questContainer clear red dot error, userId: {}, taskId: {}", userId, taskId);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "清除任务红点失败");
        }
        return BizResult.success();
    }

    public BizResult<MapTaskVO> getMapTaskList(int mapId) {
        ClientInfoDTO clientInfo = getClientInfoDTO();
        if (mapId <= 0 || clientInfo.getUserId() <= 0) {
            log.error("getMapTaskList failed, mapId is invalid: {}", mapId);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "地图ID无效or用户未登录");
        }
        RpcResult<TaskModuleConfigModel.TaskConfigModel> rpcResult = commonMapService.getMapTaskList(clientInfo, mapId);
        if (!rpcResult.isSuccess() || rpcResult.getData() == null) {
            return BizResult.result(rpcResult.getCode(), rpcResult.getMessage());
        }
        String domain = CdnUtil.getDomainWithBackSlash();
        TaskModuleConfigVO.TaskInfoVO taskInfoVO = TaskModuleConfigVO.valueOf(rpcResult.getData(), domain);
        MapTaskVO mapTaskVO = new MapTaskVO().setQuestContainerId(taskInfoVO.getQuestContainerId())
                .setModuleName(taskInfoVO.getModuleName())
                .setQuestContainerInfo(taskInfoVO.getQuestContainerInfo())
                .setShowRedDot(taskInfoVO.isShowRedDot());
        return BizResult.success(mapTaskVO);
    }

}