package com.kuaikan.role.game.backend;

import lombok.extern.slf4j.Slf4j;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jooq.JooqAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.boot.context.ApplicationPidFileWriter;

@SpringBootApplication(
        exclude = { DataSourceAutoConfiguration.class, JooqAutoConfiguration.class, MongoAutoConfiguration.class, MongoDataAutoConfiguration.class,
                RedisAutoConfiguration.class })
@Slf4j
public class RoleGameBackendApplication {

    public static void main(String[] args) {
        SpringApplication springApplication = new SpringApplication(RoleGameBackendApplication.class);
        springApplication.addListeners(new ApplicationPidFileWriter("role-game-backend.pid"));
        try {
            springApplication.run(args);
        } catch (Throwable t) {
            log.error("RoleGameBackendApplication startup exception", t);
        }
    }

}
