package com.kuaikan.role.game.backend.model.map;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import org.codehaus.jackson.annotate.JsonIgnore;

/**
 *<AUTHOR>
 *@date 2025/6/7
 */
@Data
@Accessors(chain = true)
@ApiModel("通用地图-收集簿剧情解锁日程VO")
public class MapScheduleStoryLibModelVO {

    @ApiModelProperty(value = "日程id")
    private int scheduleId;

    @ApiModelProperty(value = "日程名称")
    private String scheduleName;

    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private int storyLibId;
}
