package com.kuaikan.role.game.backend.controller;

import java.util.List;
import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.kuaikan.account.api.enums.Passport;
import com.kuaikan.account.api.model.PassportContext;
import com.kuaikan.common.bean.RequestInfo;
import com.kuaikan.role.game.api.rpc.param.cardbattle.CardBattleCreateAfkTaskParam;
import com.kuaikan.role.game.backend.biz.CardBattleAfkTaskBiz;

/**
 * 卡牌战斗挂机接口
 * <AUTHOR>
 * @date 2025/2/28
 */
@RestController
@RequestMapping("/v1/role/game/card_battle/afk/")
public class CardBattleAfkTaskController {

    @Resource
    private CardBattleAfkTaskBiz cardBattleAfkTaskBiz;

    /**
     * 获取挂机探索规则
     */
    @Passport()
    @GetMapping("exploreRule")
    public Object getAfkTaskRule(@RequestParam String activityId, @RequestParam String dungeonId) {
        RequestInfo requestInfo = PassportContext.get();
        return cardBattleAfkTaskBiz.getAfkTaskRule(requestInfo, activityId, dungeonId);
    }

    /**
     * 领取挂机奖励
     */
    @Passport()
    @GetMapping("obtainPrize")
    public Object obtainAfkTaskPrize(@RequestParam String afkTaskId) {
        RequestInfo requestInfo = PassportContext.get();
        return cardBattleAfkTaskBiz.obtainAfkTaskPrize(requestInfo, afkTaskId);
    }

    /**
     * 挂机选卡界面信息
     */
    @Passport()
    @GetMapping("prepareBattle")
    public Object afkTaskPrepareBattle(@RequestParam String activityId, @RequestParam String dungeonId,
                                       @RequestParam(defaultValue = "0") Integer battleTaskType) {
        RequestInfo requestInfo = PassportContext.get();
        return cardBattleAfkTaskBiz.afkTaskPrepareBattle(requestInfo, activityId, dungeonId, battleTaskType);
    }

    @Passport()
    @GetMapping("reportSelectedCard")
    public Object reportSelectedCard(@RequestParam String activityId, @RequestParam String monsterId, @RequestParam(defaultValue = "") List<Long> userCardIdList,
                                     @RequestParam(defaultValue = "0") Integer battleTaskType) {
        RequestInfo requestInfo = PassportContext.get();
        return cardBattleAfkTaskBiz.reportSelectedCard(requestInfo, activityId, monsterId, battleTaskType, userCardIdList);
    }

    /**
     * 创建挂机任务
     */
    @Passport()
    @PostMapping("createTask")
    public Object createAfkTask(@RequestBody CardBattleCreateAfkTaskParam param) {
        RequestInfo requestInfo = PassportContext.get();
        return cardBattleAfkTaskBiz.createAfkTask(requestInfo, param);
    }

    /**
     * 取消挂机任务
     */
    @Passport()
    @GetMapping("cancelTask")
    public Object cancelAfkTask(@RequestParam String afkTaskId) {
        RequestInfo requestInfo = PassportContext.get();
        return cardBattleAfkTaskBiz.cancelAfkTask(requestInfo, afkTaskId);
    }
}
