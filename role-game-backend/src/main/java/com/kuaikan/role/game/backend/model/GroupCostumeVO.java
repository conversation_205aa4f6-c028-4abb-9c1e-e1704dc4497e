package com.kuaikan.role.game.backend.model;

import java.io.Serializable;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.model.GroupCostumeModel;
import com.kuaikan.role.game.api.model.UserCostumePartModel;
import com.kuaikan.role.game.api.model.UserRoleCostumeModel;

@Data
@Accessors(chain = true)
public class GroupCostumeVO implements Serializable {

    private static final long serialVersionUID = 2121313184531909752L;

    private int roleId;

    private List<UserRoleCostumeVO> roleCostumes;

    public static GroupCostumeVO valueOf(GroupCostumeModel groupCostumeModel, List<UserCostumePartModel> userCostumePartModels) {
        int roleId = groupCostumeModel.getRoleId();
        List<UserRoleCostumeModel> roleCostumeModels = groupCostumeModel.getRoleCostumeModels();

        Map<Integer, List<UserCostumePartModel>> userCostumePartMap = userCostumePartModels.stream()
                .collect(Collectors.groupingBy(UserCostumePartModel::getCostumeId));
        return new GroupCostumeVO().setRoleId(roleId)
                .setRoleCostumes(roleCostumeModels.stream()
                        .map(o -> UserRoleCostumeVO.valueOf(o, userCostumePartMap.getOrDefault(o.getCostumeModel().getId(), Collections.emptyList())))
                        .sorted(Comparator.comparing(UserRoleCostumeVO::isHasOwned, Comparator.reverseOrder())
                                .thenComparingInt(UserRoleCostumeVO::getOrderNum)
                                .thenComparing(UserRoleCostumeVO::getRoleRelationCreatedAt, Comparator.reverseOrder()))
                        .collect(Collectors.toList()));
    }
}
