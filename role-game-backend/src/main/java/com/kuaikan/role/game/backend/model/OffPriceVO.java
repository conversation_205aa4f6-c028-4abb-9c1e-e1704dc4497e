package com.kuaikan.role.game.backend.model;

import com.kuaikan.role.game.api.rpc.result.OffPriceModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/10/29
 */
@Data
@Accessors(chain = true)
public class OffPriceVO implements Serializable {

    private static final long serialVersionUID = -2124013762314973838L;

    private int couponId;

    private String giftKkbOffPrice;

    private String discountRate;

    private int validDays;

    private boolean showRedDot;

    private String bid;

    public static OffPriceVO valueOf(OffPriceModel offPriceModel) {
        if (offPriceModel == null) {
            return null;
        }
        return new OffPriceVO()
                .setGiftKkbOffPrice(offPriceModel.getGiftKkbOffPrice())
                .setCouponId(offPriceModel.getCouponId())
                .setDiscountRate(offPriceModel.getDiscountRate())
                .setValidDays(offPriceModel.getValidDays())
                .setShowRedDot(offPriceModel.isShowRedDot())
                .setBid(String.valueOf(offPriceModel.getBid()));
    }
}
