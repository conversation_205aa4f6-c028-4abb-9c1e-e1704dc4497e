package com.kuaikan.role.game.backend.model;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.rpc.result.UserSilverCoinAccountModel;

/**
 * <AUTHOR>
 * @date 2024/4/24
 */
@Data
@Accessors(chain = true)
public class UserSilverCoinAccountVO implements Serializable {

    private int balance;

    public static UserSilverCoinAccountVO valueOf(UserSilverCoinAccountModel userSilverCoinAccountModel) {
        if (userSilverCoinAccountModel == null) {
            return null;
        }
        return new UserSilverCoinAccountVO().setBalance(userSilverCoinAccountModel.getBalance());
    }

}
