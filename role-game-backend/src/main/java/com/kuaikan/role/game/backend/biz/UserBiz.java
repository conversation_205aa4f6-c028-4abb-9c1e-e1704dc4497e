package com.kuaikan.role.game.backend.biz;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import com.kuaikan.account.api.model.PassportContext;
import com.kuaikan.comic.common.concurrent.Work;
import com.kuaikan.common.bean.UserAgent;
import com.kuaikan.common.model.RpcResult;
import com.kuaikan.kb.dmodel.UserAccountModel;
import com.kuaikan.kb.service.KbAccountService;
import com.kuaikan.role.game.api.bean.ClientInfo;
import com.kuaikan.role.game.api.model.ActivityEntryModel;
import com.kuaikan.role.game.api.model.UserInfoModel;
import com.kuaikan.role.game.api.rpc.param.UpdateGuideRecordParam;
import com.kuaikan.role.game.api.rpc.param.UserActionParam;
import com.kuaikan.role.game.api.service.GameUserService;
import com.kuaikan.role.game.backend.bo.UserEntryBO;
import com.kuaikan.role.game.backend.model.KbBalanceVO;
import com.kuaikan.role.game.backend.model.RedDotVO;
import com.kuaikan.role.game.backend.service.AsyncCommonService;
import com.kuaikan.role.game.backend.service.AsyncGameUserService;
import com.kuaikan.role.game.backend.service.AsyncGuideService;
import com.kuaikan.role.game.backend.service.AsyncRoleGameService;
import com.kuaikan.role.game.backend.service.AsyncRoleGroupService;
import com.kuaikan.role.game.backend.service.AsyncRoleService;
import com.kuaikan.role.game.backend.service.AsyncSettingService;
import com.kuaikan.role.game.backend.service.AsyncUserService;
import com.kuaikan.role.game.backend.service.AsyncUserStoryService;

/**
 * UserBiz
 *
 * <AUTHOR>
 * @since 2024-03-12
 */
@Component
@Slf4j
public class UserBiz {

    @Resource
    private AsyncUserService asyncUserService;
    @Resource
    private GameUserService gameUserService;
    @Resource
    private AsyncRoleGameService asyncRoleGameService;
    @Resource
    private AsyncGameUserService asyncGameUserService;
    @Resource
    private AsyncSettingService asyncSettingService;
    @Resource
    private KbAccountService kbAccountService;
    @Resource
    private AsyncCommonService asyncCommonService;
    @Resource
    private AsyncGuideService asyncGuideService;
    @Resource
    private AsyncUserStoryService asyncUserStoryService;
    @Resource
    private AsyncRoleService asyncRoleService;
    @Resource
    private AsyncRoleGroupService asyncRoleGroupService;

    public RedDotVO getRedDot(int userId) {
        RedDotVO redDotVO = new RedDotVO();
        RpcResult<ActivityEntryModel> activityEntryResult = gameUserService.getActivityEntry(userId);
        if (activityEntryResult.isSuccess()) {
            redDotVO = RedDotVO.valueOf(activityEntryResult.getData());
        }
        return redDotVO;
    }

    public UserEntryBO getUserEntry(int userId) {
        UserEntryBO userEntryBo = new UserEntryBO();
        RpcResult<UserInfoModel> rpcResult = gameUserService.getOrCreateUserInfo(userId);
        if (!rpcResult.isSuccess()) {
            return null;
        }
        UserInfoModel userInfoModel = rpcResult.getData();
        userEntryBo.setUserInfoModel(userInfoModel);
        asyncRoleGameService.queryHomePageRoleListV2(userId, userEntryBo::setGrainCabinetModelV2);
        asyncUserService.getUserProperty(userId, userEntryBo::setUserPropertyModel);
        ClientInfo clientInfo = new ClientInfo(userId);
        clientInfo.setXDevice(PassportContext.getXDevice());
        asyncSettingService.getSettingConfig(clientInfo, userEntryBo::setSystemSettingModel);
        asyncCommonService.getCommonSetting(userEntryBo::setCommonSettingModel);
        asyncGuideService.updateIgnoreGuideRecord(new UpdateGuideRecordParam().setUserId(userId).setCode(-1));
        asyncUserStoryService.supplyUnlockStories(userId);
        asyncRoleService.getRolePropertyConfig(userEntryBo::setRolePropertyConfigModel);
        asyncRoleGroupService.initEmotionBond(userId);
        Work.complete();

        // 小红点数据在前面接口处理完后，最后请求
        RpcResult<ActivityEntryModel> activityEntryResult = gameUserService.getActivityEntry(userId);
        if (activityEntryResult.isSuccess()) {
            userEntryBo.setActivityEntryModel(activityEntryResult.getData());
        }

        // 战斗力加成
        try {
            RpcResult<Double> roleLevelBonusBuff = gameUserService.getRoleLevelBonusBuff();
            if (roleLevelBonusBuff.isSuccess()) {
                userEntryBo.setAtk(roleLevelBonusBuff.getData());
            }
        } catch (Exception e) {
            log.error("Exception in gameUserService.getRoleLevelBonusBuff. userId={}", userId, e);
        }
        return userEntryBo;
    }

    public void reportUserAction(int userId, UserActionParam param) {
        asyncGameUserService.reportUserAction(userId, param);
        Work.complete();
    }

    public KbBalanceVO getKbBalance(int userId) {
        final UserAccountModel userAccount = kbAccountService.getUserAccount(userId);
        UserAgent userAgent = PassportContext.getUserAgent();
        final int kbBalance = userAccount == null ? 0 : userAccount.getBalance(userAgent);
        return new KbBalanceVO().setKbBalance(kbBalance);

    }
}
