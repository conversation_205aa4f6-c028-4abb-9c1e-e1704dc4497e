package com.kuaikan.role.game.backend.model;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.model.ScheduleAnimationInfoModel;

/**
 * <AUTHOR>
 * @version 2024-07-23
 */
@Data
@Accessors(chain = true)
public class ScheduleAnimationInfoVO implements Serializable {

    private static final long serialVersionUID = -7230609205778160032L;

    private int scheduleId;

    //角色日程动作
    private List<String> roleActionAnimations;

    //角色日程动画文字
    private List<String> roleActionWords;

    //角色日程动作持续时间范围
    private double roleActionStartSecond;
    private double roleActionEndSecond;

    //角色行走动作持续时间范围
    private double roleWalkStartSecond;
    private double roleWalkEndSecond;

    //每个角色征用NPC的个数
    private int npcCount;

    /**
     * NPC元素列表
     */
    private List<MapElementVO> npcElementList;

    //NPC日程动作
    private List<String> npcActionAnimations;

    //NPC日程动画文字
    private List<String> npcActionWords;

    //NPC站立时间范围
    private double npcStandStartSecond;
    private double npcStandEndSecond;

    //NPC行走时间范围
    private double npcWalkStartSecond;
    private double npcWalkEndSecond;
    private int roleToward;
    private int npcToward;

    public static ScheduleAnimationInfoVO valueOf(ScheduleAnimationInfoModel model) {
        if (model == null) {
            return null;
        }
        ScheduleAnimationInfoVO vo = new ScheduleAnimationInfoVO();
        vo.setScheduleId(model.getScheduleId());
        vo.setRoleActionAnimations(model.getRoleActionAnimations());
        vo.setRoleActionWords(model.getRoleActionWords());
        vo.setRoleActionStartSecond(model.getRoleActionStartSecond());
        vo.setRoleActionEndSecond(model.getRoleActionEndSecond());
        vo.setRoleWalkStartSecond(model.getRoleWalkStartSecond());
        vo.setRoleWalkEndSecond(model.getRoleWalkEndSecond());
        vo.setNpcCount(model.getNpcCount());
        vo.setNpcActionAnimations(model.getNpcActionAnimations());
        vo.setNpcActionWords(model.getNpcActionWords());
        vo.setNpcStandStartSecond(model.getNpcStandStartSecond());
        vo.setNpcStandEndSecond(model.getNpcStandEndSecond());
        vo.setNpcWalkStartSecond(model.getNpcWalkStartSecond());
        vo.setNpcWalkEndSecond(model.getNpcWalkEndSecond());
        vo.setRoleToward(model.getRoleToward());
        vo.setNpcToward(model.getNpcToward());
        return vo;
    }
}
