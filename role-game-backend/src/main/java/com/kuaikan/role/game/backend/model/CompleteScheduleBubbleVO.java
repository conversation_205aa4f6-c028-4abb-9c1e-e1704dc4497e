package com.kuaikan.role.game.backend.model;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import org.apache.commons.collections4.CollectionUtils;

import com.kuaikan.role.game.api.model.CompleteScheduleBubbleModel;

/**
 * <AUTHOR>
 * @version 2025-02-12
 */
@Data
@Accessors(chain = true)
@ApiModel("完成日程提醒气泡")
public class CompleteScheduleBubbleVO implements Serializable {

    private static final long serialVersionUID = -2436724707046485395L;
    @ApiModelProperty(value = "角色信息列表")
    private List<RoleInfoVO> roleInfos;

    public static CompleteScheduleBubbleVO valueOf(CompleteScheduleBubbleModel completeScheduleBubbleModel) {
        if (completeScheduleBubbleModel == null) {
            return null;
        }
        final List<CompleteScheduleBubbleModel.RoleInfo> roleInfos = completeScheduleBubbleModel.getRoleInfos();
        if (CollectionUtils.isEmpty(roleInfos)) {
            return null;
        }
        return new CompleteScheduleBubbleVO().setRoleInfos(roleInfos.stream().map(RoleInfoVO::valueOf).collect(Collectors.toList()));
    }

    @Data
    @Accessors(chain = true)
    @ApiModel("提醒气泡角色信息")
    public static class RoleInfoVO implements Serializable {

        private static final long serialVersionUID = -4973272000002835553L;
        @ApiModelProperty(value = "角色名称")
        private String roleName;
        @ApiModelProperty(value = "角色id")
        private Integer roleId;
        @ApiModelProperty(value = "用户日程id")
        private Integer userScheduleId;
        @ApiModelProperty(value = "日程id")
        private Integer scheduleId;
        @ApiModelProperty(value = "角色头像图片")
        private ImageInfoVO roleAvatarImage;

        public static RoleInfoVO valueOf(CompleteScheduleBubbleModel.RoleInfo roleInfo) {
            if (roleInfo == null) {
                return null;
            }
            return new RoleInfoVO().setRoleAvatarImage(ImageInfoVO.valueOf(roleInfo.getRoleAvatarImage()))
                    .setRoleName(roleInfo.getRoleName())
                    .setRoleId(roleInfo.getRoleId())
                    .setUserScheduleId(roleInfo.getUserScheduleId())
                    .setScheduleId(roleInfo.getScheduleId());
        }
    }
}
