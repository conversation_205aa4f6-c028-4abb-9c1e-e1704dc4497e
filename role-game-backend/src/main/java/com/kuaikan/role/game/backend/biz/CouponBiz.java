package com.kuaikan.role.game.backend.biz;

import java.util.Objects;
import java.util.Set;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.kuaikan.account.api.model.PassportContext;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.comic.common.concurrent.Work;
import com.kuaikan.common.model.RpcResult;
import com.kuaikan.role.game.api.service.RedDotService;
import com.kuaikan.role.game.backend.bo.CouponBO;
import com.kuaikan.role.game.backend.model.AdoptCouponListVO;
import com.kuaikan.role.game.backend.model.ShowRedDotVO;
import com.kuaikan.role.game.backend.model.UserBlindBoxCouponVO;
import com.kuaikan.role.game.backend.service.AsyncCouponService;


/**
 * <AUTHOR>
 * @since 2024/10/28
 */
@Slf4j
@Component
public class CouponBiz {

    @Resource
    private AsyncCouponService asyncCouponService;

    @Resource
    private RedDotService redDotService;

    /**
     * 查询该用户下在有效期内所有角色组的领养折扣券
     *
     * @return
     */
    public AdoptCouponListVO queryAdoptCouponList(boolean isCleanRedDot) {
        int userId = PassportContext.get().getUserId();
        CouponBO couponBO = new CouponBO();
        asyncCouponService.queryAdoptCouponList(userId, couponBO::setAdoptCouponModel);
        Work.complete();
        if (isCleanRedDot) {
            redDotService.clearAllAdoptCouponEvent(userId);
        }
        if (Objects.isNull(couponBO.getAdoptCouponModel())) {
            log.info("query adopt coupon list fail! userId:{}", userId);
            return new AdoptCouponListVO();
        }
        return AdoptCouponListVO.valueOf(couponBO.getAdoptCouponModel());
    }

    public ShowRedDotVO clearRedDot(int couponId) {
        int userId = PassportContext.get().getUserId();
        log.info("clear Red Dot! userId:{} couponId:{}", userId, couponId);
        RpcResult<Set<Integer>> result = redDotService.clearAdoptCouponEvent(userId, couponId);
        if (!result.isSuccess()){
            log.info("clear Red Dot fail ! userId:{} couponId:{}", userId, couponId);
            return new ShowRedDotVO();
        }
        return new ShowRedDotVO().setRoleAdoptCouponRedDot(CollectionUtils.isNotEmpty(result.getData()));
    }

    public BizResult<Boolean> getRedDot(int couponId) {
        int userId = PassportContext.get().getUserId();
        log.info("get Red Dot! userId:{} couponId:{}", userId, couponId);
        RpcResult<Boolean> result = redDotService.getAdoptCouponEvent(userId, couponId);
        if (!result.isSuccess()) {
            log.info("get Red Dot fail ! userId:{} couponId:{}", userId, couponId);
            return BizResult.success(false);
        }
        return BizResult.success(result.getData());
    }

    public BizResult<UserBlindBoxCouponVO> queryBlindBoxCouponInfo(int userId) {
        CouponBO couponBO = new CouponBO();
        asyncCouponService.queryBlindBoxCouponList(userId, couponBO::setBlindBoxCouponModel);
        Work.complete();
        UserBlindBoxCouponVO userBlindBoxCouponVO = UserBlindBoxCouponVO.valueOf(couponBO);
        return BizResult.success(userBlindBoxCouponVO);
    }
}
