package com.kuaikan.role.game.backend.model;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.model.BuildingAreaMapModel;

/**
 *
 * <AUTHOR>
 * @date 2024/6/25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class BuildingMapVO {
    private List<BuildingAreaMapVO> buildingAreaMapList;
    private String bgmUrl;
}
