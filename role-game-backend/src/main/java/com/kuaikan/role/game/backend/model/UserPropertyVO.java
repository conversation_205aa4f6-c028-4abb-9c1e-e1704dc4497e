package com.kuaikan.role.game.backend.model;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import com.kuaikan.role.game.api.model.UserPropertyModel;

/**
 * UserPropertyVO
 *
 * <AUTHOR>
 * @since 2024-04-19
 */
@Data
@Slf4j
@Accessors(chain = true)
public class UserPropertyVO implements Serializable {

    private static final long serialVersionUID = 4964621940809170714L;
    /** 领养角色数量 */
    private int adoptedRoleCount;
    /** 当前角色拥有的装扮数量 */
    private int ownedCostumeCount;
    /** 当前角色拥有的场景数量 */
    private int ownedSceneCount;
    /** 当前角色拥有的动作剧情数量 */
    private int ownedActionStoryCount;
    /** 当前角色拥有的新建剧情数量 */
    private int ownedLetterStoryCount;
    /** 用户城市等级 */
    private int cityLevel;

    public static UserPropertyVO valueOf(UserPropertyModel userPropertyModel) {
        if (userPropertyModel == null) {
            return new UserPropertyVO();
        }
        log.info("UserPropertyVO.valueOf: userPropertyModel={}", userPropertyModel);
        return new UserPropertyVO().setAdoptedRoleCount(userPropertyModel.getAdoptedRoleCount())
                .setOwnedSceneCount(userPropertyModel.getOwnedSceneCount())
                .setOwnedCostumeCount(userPropertyModel.getOwnedCostumeCount())
                .setOwnedActionStoryCount(userPropertyModel.getOwnedActionStoryCount())
                .setOwnedLetterStoryCount(userPropertyModel.getOwnedLetterStoryCount())
                .setCityLevel(userPropertyModel.getCityLevel());
    }
}
