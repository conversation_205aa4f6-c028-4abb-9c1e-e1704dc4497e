package com.kuaikan.role.game.backend.model;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.model.ActivityEntryModel;

/**
 * <AUTHOR>
 * @version 2024-11-27
 */
@Data
@Accessors(chain = true)
public class RedDotVO implements Serializable {

    private static final long serialVersionUID = 8068472732311250309L;

    private boolean costumeRedDot;
    private boolean collectionRedDot;
    private boolean addRoleRedDot;
    private boolean scheduleRedDot;
    private boolean questContainerRedDot;
    private boolean interactiveItemRedDot;
    private boolean feedRedDot;
    private boolean newTaskRedDot;

    public static RedDotVO valueOf(ActivityEntryModel activityEntryModel) {
        if (activityEntryModel == null) {
            return null;
        }
        RedDotVO redDotVO = new RedDotVO();
        redDotVO.setCostumeRedDot(activityEntryModel.isCostumeRedDot());
        redDotVO.setCollectionRedDot(activityEntryModel.isCollectionRedDot());
        redDotVO.setAddRoleRedDot(activityEntryModel.isAddRoleRedDot());
        redDotVO.setScheduleRedDot(activityEntryModel.isScheduleRedDot());
        redDotVO.setQuestContainerRedDot(activityEntryModel.isQuestContainerRedDot());
        redDotVO.setInteractiveItemRedDot(activityEntryModel.isInteractiveItemRedDot());
        redDotVO.setFeedRedDot(activityEntryModel.isFeedRedDot());
        redDotVO.setNewTaskRedDot(activityEntryModel.isNewTaskRedDot());
        return redDotVO;
    }
}
