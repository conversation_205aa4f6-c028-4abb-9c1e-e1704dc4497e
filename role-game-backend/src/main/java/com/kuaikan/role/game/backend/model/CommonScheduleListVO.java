package com.kuaikan.role.game.backend.model;
import com.kuaikan.comic.utils.CdnUtil;
import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

import com.kuaikan.role.game.api.model.CommonScheduleListModel;
import com.kuaikan.role.game.api.model.CommonScheduleListModel.CommonPrizeModel;
import com.kuaikan.role.game.api.model.CommonScheduleListModel.CommonScheduleModel;
import com.kuaikan.role.game.api.model.CommonScheduleListModel.StoryPrizeModel;

import lombok.Data;
import lombok.experimental.Accessors;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@Data
@Accessors(chain = true)
@ApiModel("通用地图日程列表")
public class CommonScheduleListVO implements Serializable{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("通用地图日程列表-日程列表")
    private List<CommonScheduleVO> schedules;

    public static CommonScheduleListVO of(CommonScheduleListModel model) {
        return new CommonScheduleListVO().setSchedules(model.getSchedules().stream().map(CommonScheduleVO::of).collect(Collectors.toList()));
    }
    
    @Data
    @Accessors(chain = true)
    @ApiModel("通用地图日程列表-通用日程")
    public static class CommonScheduleVO implements Serializable{
        @ApiModelProperty("通用地图日程列表-通用日程-日程ID")
        private int scheduleId;
        @ApiModelProperty("通用地图日程列表-通用日程-日程名称")
        private String scheduleName;
        /**
         * 状态
         * @see com.kuaikan.role.game.api.enums.ScheduleStatus
         */
        @ApiModelProperty("通用地图日程列表-通用日程-状态")
        private int status;
        @ApiModelProperty("通用地图日程列表-通用日程-建筑名称")
        private String buildingName;
        @ApiModelProperty("通用地图日程列表-通用日程-建筑ID")
        private int buildingId;
        @ApiModelProperty("通用地图日程列表-通用日程-区域名称")
        private String areaName;
        @ApiModelProperty("通用地图日程列表-通用日程-区域ID")
        private int areaId;
        @ApiModelProperty("通用地图日程列表-通用日程-每5分钟通用奖励")
        private List<CommonPrizeVO> commonPrizes;
        @ApiModelProperty("通用地图日程列表-通用日程-剧情奖励")
        private List<StoryPrizeVO> storyPrizes;
        @ApiModelProperty("通用地图日程列表-通用日程-进行中日程")
        private OngoingScheduleVO ongoingSchedule;
        @ApiModelProperty("通用地图日程列表-通用日程-每5分钟能量消耗")
        private float energyConsume;

        public static CommonScheduleVO of(CommonScheduleModel model) {
            return new CommonScheduleVO().setScheduleId(model.getScheduleId())
                    .setScheduleName(model.getScheduleName())
                    .setBuildingName(model.getBuildingName())
                    .setAreaId(model.getAreaId())
                    .setAreaName(model.getAreaName())
                    .setCommonPrizes(model.getCommonPrizes().stream().map(CommonPrizeVO::of).collect(Collectors.toList()))
                    .setStoryPrizes(model.getStoryPrizes().stream().map(StoryPrizeVO::of).collect(Collectors.toList()))
                    .setOngoingSchedule(model.getOngoingSchedule() == null ? null : OngoingScheduleVO.of(model.getOngoingSchedule()))
                    .setStatus(model.getStatus())
                    .setEnergyConsume(model.getEnergyConsume());
        }
    }

    @Data
    @Accessors(chain = true)
    @ApiModel("通用地图日程列表-通用奖励")
    public static class CommonPrizeVO implements Serializable {
        @ApiModelProperty("通用地图日程列表-通用奖励-ID")
        private int id;
        @ApiModelProperty("通用地图日程列表-通用奖励-名称")
        private String name;
        @ApiModelProperty("通用地图日程列表-通用奖励-图标")
        private String icon;
        @ApiModelProperty("通用地图日程列表-通用奖励-数量")
        private long unit;

        public static CommonPrizeVO of(CommonPrizeModel model) {
            String domain = CdnUtil.getDomainWithBackSlash();
            return new CommonPrizeVO().setId(model.getId()).setName(model.getName()).setIcon(domain + model.getIcon()).setUnit(model.getUnit());
        }
    }

    @Data
    @Accessors(chain = true)
    @ApiModel("通用地图日程列表-剧情奖励")
    public static class StoryPrizeVO implements Serializable {
        @ApiModelProperty("通用地图日程列表-剧情奖励-ID")
        private int id;
        @ApiModelProperty("通用地图日程列表-剧情奖励-名称")
        private String name;

        public static StoryPrizeVO of(StoryPrizeModel model) {
            return new StoryPrizeVO().setId(model.getId()).setName(model.getName());
        }
    }

    @Data
    @Accessors(chain = true)
    @ApiModel("通用地图日程列表-进行中日程")
    public static class OngoingScheduleVO implements Serializable {
        @ApiModelProperty("通用地图日程列表-进行中日程-用户日程ID")
        private int userScheduleId;
        @ApiModelProperty("通用地图日程列表-进行中日程-开始时间")
        private long startTime;
        @ApiModelProperty("通用地图日程列表-进行中日程-结束时间")
        private long endTime;

        public static OngoingScheduleVO of(CommonScheduleListModel.OngoingScheduleModel ongoingSchedule) {
            return new OngoingScheduleVO().setUserScheduleId(ongoingSchedule.getUserScheduleId())
                    .setStartTime(ongoingSchedule.getStartTime())
                    .setEndTime(ongoingSchedule.getEndTime());
        }
    }
}
