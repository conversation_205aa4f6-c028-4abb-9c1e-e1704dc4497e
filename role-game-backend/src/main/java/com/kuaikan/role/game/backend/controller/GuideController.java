package com.kuaikan.role.game.backend.controller;

import java.util.Map;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.kuaikan.account.api.enums.Passport;
import com.kuaikan.account.api.model.PassportContext;
import com.kuaikan.role.game.api.common.BaseResponse;
import com.kuaikan.role.game.backend.biz.GuideBiz;
import com.kuaikan.role.game.backend.model.CommonScheduleResultVO;
import com.kuaikan.role.game.backend.util.ResponseUtils;

@RestController
@RequestMapping("/v1/role/game/user/guide")
@Passport(loginRequired = false, allowVisitor = true)
@Slf4j
public class GuideController {

    @Resource
    private GuideBiz guideBiz;

    @GetMapping("")
    public Map<String, Object> getGuideRecord() {
        int userId = PassportContext.getUserId();
        log.info("getGuideRecord userId:{}", userId);
        return ResponseUtils.valueOf(guideBiz.getGuideRecord(userId));
    }

    @PostMapping("/update")
    public Map<String, Object> updateGuideRecord(@RequestParam("code") int code) {
        int userId = PassportContext.getUserId();
        log.info("updateGuideRecord userId:{}, code:{}", userId, code);
        guideBiz.updateGuideRecord(userId, code);
        return ResponseUtils.success();

    }

    @PostMapping("/finishSchedule")
    public Map<String, Object> finishSchedule(@RequestParam int userScheduleId) {
        int userId = PassportContext.getUserId();
        log.info("free finishSchedule userId:{}, userScheduleId:{}", userId, userScheduleId);
        return ResponseUtils.valueOf(guideBiz.finishSchedule(userId, userScheduleId));
    }

    @PostMapping("/skip")
    public Map<String, Object> skip() {
        int userId = PassportContext.getUserId();
        log.info("skip userId:{}", userId);
        return ResponseUtils.valueOf(guideBiz.skip(userId));
    }
}
