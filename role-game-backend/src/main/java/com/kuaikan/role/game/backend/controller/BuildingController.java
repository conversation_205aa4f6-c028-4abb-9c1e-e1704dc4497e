package com.kuaikan.role.game.backend.controller;

import java.util.Map;
import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kuaikan.role.game.backend.biz.BuildingBiz;
import com.kuaikan.role.game.backend.util.ResponseUtils;

/**
 * <AUTHOR>
 * @version 2024-07-19
 */
@RequestMapping("/v1/role/game/building")
@RestController
public class BuildingController {

    @Resource
    private BuildingBiz buildingBiz;

    @GetMapping("/allBuildingAreaList")
    public Map<String, Object> getAllBuildingAreaList() {
        return ResponseUtils.valueOf(buildingBiz.getAllBuildingAreaList());
    }
}
