package com.kuaikan.role.game.backend.controller;

import java.util.Map;
import javax.annotation.Resource;

import jnr.ffi.annotations.In;
import lombok.extern.slf4j.Slf4j;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.kuaikan.account.api.enums.Passport;
import com.kuaikan.account.api.model.PassportContext;
import com.kuaikan.role.game.backend.biz.MapBiz;
import com.kuaikan.role.game.backend.util.ResponseUtils;

/**
 *
 * <AUTHOR>
 * @date 2024/6/25
 */
@Slf4j
@RestController
@RequestMapping("/v1/role/game/map")
@Passport(allowVisitor = true)
public class MapController {

    @Resource
    private MapBiz mapBiz;

    @GetMapping("/city")
    public Map<String, Object> getCityMap() {
        return ResponseUtils.valueOf(mapBiz.cityMap(PassportContext.getUserId()));
    }

    /**
     * 获取通用地图
     * @param cityId
     * @return
     */
    @GetMapping("/getGeneralCityMap")
    public Map<String, Object> getGeneralCityMap(@RequestParam(required = false) Integer cityId) {
        return ResponseUtils.valueOf(mapBiz.getGeneralCityMap(PassportContext.getUserId(), cityId));
    }

    @GetMapping("/building")
    public Map<String, Object> getBuildingMap(int buildingId) {
        return ResponseUtils.valueOf(mapBiz.buildingMap(PassportContext.getUserId(), buildingId));
    }

    @GetMapping("/city/block_array")
    public Map<String, Object> getMapArray() {
        return ResponseUtils.valueOf(mapBiz.getCityMapArray());
    }

    @GetMapping("/userEnergy")
    public Map<String, Object> getUserEnergy(int mapId) {
        return ResponseUtils.valueOf(mapBiz.getUserEnergy(mapId));
    }
}
