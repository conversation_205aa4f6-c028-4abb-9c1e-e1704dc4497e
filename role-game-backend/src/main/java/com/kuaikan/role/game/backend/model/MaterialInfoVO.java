package com.kuaikan.role.game.backend.model;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.rpc.result.SpineMaterialModel;

/**
 * <AUTHOR>
 * @date 2024/6/6
 */
@Data
@Accessors(chain = true)
public class MaterialInfoVO {

    private String jsonFileUrl;

    private String atlasFileUrl;

    private String pngFileUrl;

    public static MaterialInfoVO valueOf(SpineMaterialModel spineMaterialModel, String domain) {
        if (spineMaterialModel == null) {
            return null;
        }
        return new MaterialInfoVO().setJsonFileUrl(domain + spineMaterialModel.getJsonFileKey())
                .setAtlasFileUrl(domain + spineMaterialModel.getAtlasFileKey())
                .setPngFileUrl(domain + spineMaterialModel.getPngFileKey());
    }
}
