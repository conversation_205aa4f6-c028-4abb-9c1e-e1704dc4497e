package com.kuaikan.role.game.backend.model;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.model.ItemOrderModel;

/**
 * ItemOrderVO
 *
 * <AUTHOR>
 * @since 2024-08-14
 */
@Data
@Accessors(chain = true)
public class ItemOrderVO implements Serializable {

    private static final long serialVersionUID = 3642615321083619836L;
    private int itemType;
    private int count;

    public static ItemOrderVO valueOf(ItemOrderModel model) {
        if (model == null) {
            return null;
        }
        ItemOrderVO itemOrderVo = new ItemOrderVO().setItemType(model.getItemType()).setCount(model.getCount());
        return itemOrderVo;
    }
}
