package com.kuaikan.role.game.backend.model;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * CompleteScheduleVO
 *
 * <AUTHOR>
 * @since 2024-06-18
 */
@Data
@Accessors(chain = true)
public class CompleteScheduleVO implements Serializable {

    private static final long serialVersionUID = 7112176614576315485L;
    private int consumeMinute;
    private int energyChange;
    private int moodChange;
    private List<Integer> moodCutPoints;
    private int tirednessChange;
    private List<Integer> tirednessCutPoints;
    private int silverCoinChange;

    private LevelUpVO levelUpModel;
    private DimensionLevelUpVO dimensionLevelUpModel;
    private List<UnlockStoryVO> unlockStories;
    private UnlockLetterStoryVO unlockLetterStory;
    private EmotionBondLevelUpVO emotionBondLevelUp;
    private List<ScheduleExtraAwardVO> extraAwardModels;

}
