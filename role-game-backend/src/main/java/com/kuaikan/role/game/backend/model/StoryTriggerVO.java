package com.kuaikan.role.game.backend.model;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.model.StoryTriggerModel;

/**
 * StoryTriggerVO
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@Data
@Accessors(chain = true)
public class StoryTriggerVO implements Serializable {

    private static final long serialVersionUID = -9209488961857297868L;
    private int storyId;
    private boolean firstTime;
    private LevelUpVO levelUpModel;
    private SilverCoinDropRecordVO silverCoinDropRecord;

    public static StoryTriggerVO valueOf(StoryTriggerModel model) {
        if (model == null) {
            return null;
        }
        StoryTriggerVO storyTriggerVo = new StoryTriggerVO();
        storyTriggerVo.setStoryId(model.getStoryId());
        storyTriggerVo.setFirstTime(model.isFirstTime());
        storyTriggerVo.setLevelUpModel(LevelUpVO.valueOf(model.getLevelUpModel()));
        storyTriggerVo.setSilverCoinDropRecord(SilverCoinDropRecordVO.valueOf(model.getSilverCoinDropRecordModel()));
        return storyTriggerVo;
    }
}
