package com.kuaikan.role.game.backend.model;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import com.kuaikan.role.game.api.model.ComposeCostumePartModel;
import com.kuaikan.role.game.backend.bo.ComposeCostumePartBO;

/**
 * <AUTHOR>
 * @version 2024-04-29
 */
@Data
@Accessors(chain = true)
@Slf4j
public class ComposeCostumePartVO implements Serializable {

    private static final long serialVersionUID = 5370098766875139022L;
    /**
     * 单品图片
     */
    private Integer costumePartId;
    /**
     * 装扮 id
     */
    private Integer costumeId;

    private List<UserStuffVO> userStuffs;

    public static ComposeCostumePartVO valueOf(ComposeCostumePartBO composeCostumePartBO) {
        final ComposeCostumePartModel model = composeCostumePartBO.getComposeCostumePartModel();
        if (model == null) {
            log.error("composeCostumePartModel is null");
            return null;
        }
        ComposeCostumePartVO vo = new ComposeCostumePartVO();
        vo.setCostumePartId(model.getCostumePartId());
        vo.setCostumeId(model.getCostumeId());
        vo.setUserStuffs(model.getUserStuffModels().stream().map(UserStuffVO::valueOf).collect(Collectors.toList()));
        return vo;
    }

}
