package com.kuaikan.role.game.backend.model;

import java.util.Date;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.model.UserRoleSceneModel;

/**
 * <AUTHOR>
 * @version 2024-03-04
 */
@Data
@Accessors(chain = true)
public class UserRoleSceneVO {

    private Integer sceneId;

    private String name;

    /**
     * 获取文案
     */
    private String obtainCopywriting;
    /**
     * 活动id
     */
    private int activityId;
    /**
     * 缩略图
     */
    private ImageInfoVO thumbnail;
    /**
     * 大图
     */
    private ImageInfoVO largeImage;
    /**
     * 角标
     */
    private ImageInfoVO cornerMark;
    /**
     * 排序
     */
    private int orderNum;
    /**
     * 当前是否使用
     */
    private boolean using;
    /**
     * 是否有小红点
     */
    private boolean showRedDot;

    /**
     * 用户是否已拥有
     */
    private boolean hasOwned;
    /**
     * 色值
     */
    private Float[] rgba;

    private Date roleRelationCreatedAt;

    private SpineMaterialVO largeImageSpineMaterial;

    public static UserRoleSceneVO valueOf(UserRoleSceneModel userRoleSceneModel) {
        if (userRoleSceneModel == null) {
            return null;
        }
        return new UserRoleSceneVO().setSceneId(userRoleSceneModel.getSceneModel().getId())
                .setName(userRoleSceneModel.getSceneModel().getName())
                .setObtainCopywriting(userRoleSceneModel.getSceneModel().getConfig().getObtainCopywriting())
                .setActivityId(userRoleSceneModel.getSceneModel().getConfig().getActivityId())
                .setThumbnail(ImageInfoVO.valueOf(userRoleSceneModel.getSceneModel().getConfig().getThumbnail()))
                .setLargeImage(ImageInfoVO.valueOf(userRoleSceneModel.getSceneModel().getConfig().getLargeImage()))
                .setCornerMark(ImageInfoVO.valueOf(userRoleSceneModel.getSceneModel().getConfig().getCornerMark()))
                .setOrderNum(userRoleSceneModel.getOrderNum())
                .setUsing(userRoleSceneModel.isUsing())
                .setShowRedDot(userRoleSceneModel.isShowRedDot())
                .setRoleRelationCreatedAt(userRoleSceneModel.getRoleRelationCreatedAt())
                .setHasOwned(userRoleSceneModel.isHasOwned())
                .setRgba(userRoleSceneModel.getSceneModel().getConfig().getRgba())
                .setLargeImageSpineMaterial(SpineMaterialVO.valueOf(userRoleSceneModel.getSceneModel().getConfig().getLargeImageSpineMaterial()));
    }

}
