package com.kuaikan.role.game.backend.model;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.comic.utils.CdnUtil;
import com.kuaikan.game.activityengine.def.dtos.signin.SignInInfoDto;
import com.kuaikan.game.activityengine.def.models.signin.SignInData;

/**
 * SignInInfoVO
 *
 * <AUTHOR>
 * @since 2024-05-08
 */
@Data
@Accessors(chain = true)
public class SignInInfoVO implements Serializable {

    private static final long serialVersionUID = -1313134048670099956L;
    private int signInType;
    private long startTime;
    private long endTime;
    private String signInInfoJson;
    private SignInData signIn;
    private String host;

    public static SignInInfoVO valueOf(SignInInfoDto signInInfoDto) {
        if (signInInfoDto == null) {
            return null;
        }
        SignInInfoVO signInInfoVo = new SignInInfoVO();
        signInInfoVo.setSignInType(signInInfoDto.getSignInType());
        signInInfoVo.setStartTime(signInInfoDto.getStartTime());
        signInInfoVo.setEndTime(signInInfoDto.getEndTime());
        signInInfoVo.setSignInInfoJson(signInInfoDto.getSignInInfoJson());
        signInInfoVo.setSignIn(signInInfoDto.getSignIn());
        signInInfoVo.setHost(CdnUtil.getDefaultDomainWithBackSlash());
        return signInInfoVo;
    }
}
