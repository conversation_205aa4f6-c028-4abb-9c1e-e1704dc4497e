package com.kuaikan.role.game.backend.controller;

import java.util.List;
import java.util.Map;
import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.kuaikan.account.api.enums.Passport;
import com.kuaikan.account.api.model.PassportContext;
import com.kuaikan.role.game.api.util.GsonUtils;
import com.kuaikan.role.game.backend.biz.EnergyBottleBiz;
import com.kuaikan.role.game.backend.util.ResponseUtils;

/**
 * EnergyBottleController
 *
 * <AUTHOR>
 * @since 2024-08-09
 */
@Passport(allowVisitor = true)
@RequestMapping("/v1/role/game/energy/bottle")
@RestController
public class EnergyBottleController {

    @Resource
    private EnergyBottleBiz energyBottleBiz;

    @GetMapping("/info")
    public Map<String, Object> getEnergyBottleInfo() {
        int userId = PassportContext.getUserId();
        return ResponseUtils.valueOf(energyBottleBiz.getEnergyBottleInfo(userId));
    }

    @PostMapping("/use")
    public Map<String, Object> completeSchedule(@RequestParam(name = "roleId") int roleId, @RequestParam(name = "count") int count) {
        int userId = PassportContext.getUserId();
        return ResponseUtils.valueOf(energyBottleBiz.useEnergyBottle(userId, roleId, count));
    }

    @PostMapping("/createOrder")
    public Map<String, Object> createOrder(@RequestParam(name = "giftId") String giftId, @RequestParam(name = "roleId") int roleId) {
        int userId = PassportContext.getUserId();
        return ResponseUtils.valueOf(energyBottleBiz.createOrder(userId, giftId, roleId));
    }

    @GetMapping("/queryOrder")
    public Map<String, Object> queryOrder(long orderId) {
        int userId = PassportContext.getUserId();
        return ResponseUtils.valueOf(energyBottleBiz.queryOrder(userId, orderId));
    }

    @PostMapping("/payed_notify")
    public Map<String, Object> payedNotify(long orderId) {
        int userId = PassportContext.getUserId();
        return ResponseUtils.valueOf(energyBottleBiz.payedNotify(userId, orderId));
    }

    @PostMapping("/convertBottleByAdoptCoupon")
    public Map<String, Object> convertBottleByAdoptCoupon(@RequestParam(name = "couponIds") String couponIds,
                                                          @RequestParam(name = "bids" , required = false) String bids) {
        List<Integer> ids = GsonUtils.tryParseListNotNull(couponIds, Integer.class);
        List<Long> couponRecordIds = GsonUtils.tryParseListNotNull(bids, Long.class);
        return ResponseUtils.valueOf(energyBottleBiz.convBottleByAdoptCoupon(ids, couponRecordIds));
    }
}
