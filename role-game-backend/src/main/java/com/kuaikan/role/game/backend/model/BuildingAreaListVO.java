package com.kuaikan.role.game.backend.model;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.model.BuildingAreaListModel;

/**
 * <AUTHOR>
 * @version 2024-07-19
 */
@Data
@Accessors(chain = true)
public class BuildingAreaListVO implements Serializable {

    private static final long serialVersionUID = -4942542883928078843L;

    private List<BuildingAreaVO> buildingAreas;

    @Data
    @Accessors(chain = true)
    public static class BuildingAreaVO implements Serializable {

        private Integer buildingId;

        private Integer areaId;

        private String areaName;

        private String buildingName;

        public static BuildingAreaVO valueOf(BuildingAreaListModel.BuildingAreaModel buildingAreaModel) {
            BuildingAreaVO buildingAreaVO = new BuildingAreaVO();
            buildingAreaVO.setBuildingId(buildingAreaModel.getBuildingId());
            buildingAreaVO.setAreaId(buildingAreaModel.getAreaId());
            buildingAreaVO.setAreaName(buildingAreaModel.getAreaName());
            buildingAreaVO.setBuildingName(buildingAreaModel.getBuildingName());
            return buildingAreaVO;
        }
    }

}
