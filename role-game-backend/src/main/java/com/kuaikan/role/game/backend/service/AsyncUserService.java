package com.kuaikan.role.game.backend.service;

import static com.kuaikan.role.game.backend.util.ThreadPoolConfig.ROLE_GAME_SERVICE_POOL;

import java.util.function.Consumer;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import com.kuaikan.comic.common.concurrent.AsyncWork;
import com.kuaikan.comic.common.utils.ThreadPoolFactory;
import com.kuaikan.common.model.RpcResult;
import com.kuaikan.role.game.api.model.ActivityEntryModel;
import com.kuaikan.role.game.api.model.UserPropertyModel;
import com.kuaikan.role.game.api.service.GameUserService;

/**
 * AsyncUserService
 *
 * <AUTHOR>
 * @since 2024-03-11
 */
@Slf4j
@Component
public class AsyncUserService {

    @Resource
    private GameUserService gameUserService;

    public void getUserProperty(int userId, Consumer<UserPropertyModel> callback) {
        AsyncWork.supplyAsync(() -> {
            RpcResult<UserPropertyModel> rpcResult = gameUserService.getUserProperty(userId);
            if (!rpcResult.isSuccess()) {
                log.warn("getUserProperty failed, userId:{}, result:{}", userId, rpcResult);
                return null;
            }
            return rpcResult.getData();

        }, ThreadPoolFactory.acquire(ROLE_GAME_SERVICE_POOL.getPoolName()), callback);
    }

    public void getActivityEntry(int userId, Consumer<ActivityEntryModel> callback) {
        AsyncWork.supplyAsync(() -> {
            RpcResult<ActivityEntryModel> rpcResult = gameUserService.getActivityEntry(userId);
            if (!rpcResult.isSuccess()) {
                log.warn("getActivityEntry failed, userId:{}, result:{}", userId, rpcResult);
                return null;
            }
            return rpcResult.getData();

        }, ThreadPoolFactory.acquire(ROLE_GAME_SERVICE_POOL.getPoolName()), callback);
    }

}
