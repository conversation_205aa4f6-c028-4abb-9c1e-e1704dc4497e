package com.kuaikan.role.game.backend.model;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.bo.OsTrapNumericalConfig;
import com.kuaikan.role.game.api.rpc.result.CommonSettingModel;

/**
 * <AUTHOR>
 * @version 2024-07-13
 */
@Data
@Accessors(chain = true)
public class CommonSettingVO implements Serializable {

    private static final long serialVersionUID = 1156695457559736636L;
    private float expBuffWhenHappy;
    private float expBuffWhenEmo;
    private int extraEnergyCostWhenTired;
    private float silverCoinBuffWhenHappy;
    private float energyCostBuffWhenTired;

    private int storyDist;
    /**
     * 定时触发动作剧情时间范围上界
     */
    private int actionTimeMax;
    /**
     * 定时触发动作剧情时间范围下界
     */
    private int actionTimeMin;
    private int actionIntervalSec;
    /**
     * 动作剧情触发概率
     */
    private float actionProb;
    /**
     * 领养页玩法介绍mp4视频url
     */
    private String adoptPageVideoUrl;
    /**
     * 受困时间最小间隔
     */
    private List<OsTrapNumericalConfig.RoleTrapSecond> trapTimeSecondList;

    /**
     * 定时触发互动彩蛋时间范围上界
     */
    private int eggTimeMax;
    /**
     * 定时触发互动彩蛋时间范围下界
     */
    private int eggTimeMin;
    /**
     * 互动彩蛋触发最小间隔时间
     */
    private int eggIntervalSec;

    public static CommonSettingVO valueOf(CommonSettingModel commonSettingModel) {
        if (commonSettingModel == null) {
            return null;
        }
        CommonSettingVO commonSettingVO = new CommonSettingVO();
        commonSettingVO.setExpBuffWhenHappy(commonSettingModel.getExpBuffWhenHappy());
        commonSettingVO.setExpBuffWhenEmo(commonSettingModel.getExpBuffWhenEmo());
        commonSettingVO.setExtraEnergyCostWhenTired(commonSettingModel.getExtraEnergyCostWhenTired());
        commonSettingVO.setEnergyCostBuffWhenTired(commonSettingModel.getEnergyCostBuffWhenTired());
        commonSettingVO.setSilverCoinBuffWhenHappy(commonSettingModel.getSilverCoinBuffWhenHappy());
        commonSettingVO.setActionIntervalSec(commonSettingModel.getActionIntervalSec());
        commonSettingVO.setActionTimeMax(commonSettingModel.getActionTimeMax());
        commonSettingVO.setActionTimeMin(commonSettingModel.getActionTimeMin());
        commonSettingVO.setStoryDist(commonSettingModel.getStoryDist());
        commonSettingVO.setActionProb(commonSettingModel.getActionProb());
        commonSettingVO.setAdoptPageVideoUrl(commonSettingModel.getAdoptPageVideoUrl());
        commonSettingVO.setEggTimeMax(commonSettingModel.getEggTimeMax());
        commonSettingVO.setEggTimeMin(commonSettingModel.getEggTimeMin());
        commonSettingVO.setEggIntervalSec(commonSettingModel.getEggIntervalSec());
        commonSettingVO.setTrapTimeSecondList(commonSettingModel.getTrapTimeSecondList());
        return commonSettingVO;
    }
}
