package com.kuaikan.role.game.backend.service;

import static com.kuaikan.role.game.backend.util.ThreadPoolConfig.GAME_CARD_SERVICE_POOL;

import java.util.function.Consumer;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import com.kuaikan.account.api.model.PassportContext;
import com.kuaikan.comic.common.concurrent.AsyncWork;
import com.kuaikan.comic.common.utils.ThreadPoolFactory;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.bean.UserAgent;
import com.kuaikan.game.activityengine.def.dtos.signin.SignInContextDto;
import com.kuaikan.game.activityengine.def.dtos.signin.SignInInfoDto;
import com.kuaikan.game.activityengine.def.dtos.signin.SignInPrizeResultDto;
import com.kuaikan.game.activityengine.def.services.GameCardModuleService;
import com.kuaikan.game.gamecard.dubbo.dto.ClientInfoDTO;

/**
 * AsyncSignInService
 *
 * <AUTHOR>
 * @since 2024-05-08
 */
@Slf4j
@Service
public class AsyncSignInService {

    @Resource
    private GameCardModuleService gameCardModuleService;

    public void getSignInInfo(int userId, int signInId, Consumer<SignInInfoDto> callback) {
        AsyncWork.supplyAsync(() -> {
            ClientInfoDTO clientInfo = new ClientInfoDTO(userId);
            SignInInfoDto signInInfoDto = gameCardModuleService.getSignInInfo(clientInfo, signInId);
            if (signInInfoDto == null || signInInfoDto.getCodeMsg().getCode() != ResponseCodeMsg.SUCCESS.getCode()) {
                log.warn("getSignInInfo failed, userId:{}, signInId:{}, result:{}", userId, signInId, signInInfoDto);
                return null;
            }
            return signInInfoDto;
        }, ThreadPoolFactory.acquire(GAME_CARD_SERVICE_POOL.getPoolName()), callback);
    }

    public void signIn(int userId, SignInContextDto signInContextDto, Consumer<SignInPrizeResultDto> callback) {
        UserAgent userAgent = PassportContext.getUserAgent();
        AsyncWork.supplyAsync(() -> {
            ClientInfoDTO clientInfo = new ClientInfoDTO(userId);
            clientInfo.setUserAgent(userAgent);
            SignInPrizeResultDto resultDto = gameCardModuleService.obtainSignInPrize(clientInfo, signInContextDto);
            if (resultDto == null || resultDto.getCodeMsg().getCode() != ResponseCodeMsg.SUCCESS.getCode()) {
                log.warn("signIn failed, userId:{}, signInContextDto:{}, result:{}", userId, signInContextDto, resultDto);
                return null;
            }
            return resultDto;
        }, ThreadPoolFactory.acquire(GAME_CARD_SERVICE_POOL.getPoolName()), callback);
    }
}
