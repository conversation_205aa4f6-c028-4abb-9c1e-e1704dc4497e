package com.kuaikan.role.game.backend.model;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.comic.utils.CdnUtil;
import com.kuaikan.role.game.api.model.RoleFullInfoModel;
import com.kuaikan.role.game.api.model.RoleModel;
import com.kuaikan.role.game.api.model.SceneModel;
import com.kuaikan.role.game.api.rpc.result.CostumeModel;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@Data
@Accessors(chain = true)
public class CostumeRoleVO implements Serializable {

    private static final long serialVersionUID = 1872050268437566491L;

    private int id;

    private int roleGroupId;

    private String name;

    private ImageInfoVO avatar;

    private Boolean showCostumeRedDot;

    private int defaultCostumeId;

}
