package com.kuaikan.role.game.backend.controller;

import java.util.HashMap;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerExceptionResolver;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.json.MappingJackson2JsonView;

import com.kuaikan.common.ResponseType;
import com.kuaikan.common.exception.IllegalParamKException;
import com.kuaikan.common.exception.IllegalParamKKException;
import com.kuaikan.common.exception.KException;
import com.kuaikan.common.exception.KKException;

/**
 * 统一定义的异常处理,主要处理api接口的异常,返回json Created by yuanyan<PERSON>@kuaikanmanhua.com on 16/5/24.
 */
@Component
@Slf4j
public class MyExceptionHandler implements HandlerExceptionResolver {

    private MappingJackson2JsonView jsonView = new MappingJackson2JsonView();

    @Override
    public ModelAndView resolveException(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e) {
        int code;
        String msg;
        if (e instanceof IllegalParamKKException) {
            code = ((IllegalParamKKException) e).getResponseType().getCode();
            msg = ((IllegalParamKKException) e).getResponseType().getMsg();
        } else if (e instanceof KKException) {
            code = ((KKException) e).getResponseType().getCode();
            msg = ((KKException) e).getResponseType().getMsg();
        } else if (e instanceof IllegalParamKException) {
            code = ((IllegalParamKException) e).getResponseCodeMsg().getCode();
            msg = ((IllegalParamKException) e).getResponseCodeMsg().getMessage();
        } else if (e instanceof KException) {
            code = ((KException) e).getResponseCodeMsg().getCode();
            msg = ((KException) e).getResponseCodeMsg().getMessage();
        } else {
            code = ResponseType.SYSTEM_ERROR.getCode();
            msg = ResponseType.SYSTEM_ERROR.getMsg();
        }

        log.error("kk exception, code={}, msg={}", code, msg, e);
        Map<String, Object> attributes = new HashMap();
        attributes.put("code", code);
        attributes.put("message", msg);
        return new ModelAndView(jsonView, attributes);
    }
}
