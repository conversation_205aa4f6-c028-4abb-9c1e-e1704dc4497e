package com.kuaikan.role.game.backend.biz;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.comic.common.concurrent.Work;
import com.kuaikan.game.activityengine.def.dtos.signin.SignInContextDto;
import com.kuaikan.game.activityengine.def.dtos.signin.SignInPrizeResultDto;
import com.kuaikan.role.game.api.common.RoleGameResponse;
import com.kuaikan.role.game.backend.bo.SignInBO;
import com.kuaikan.role.game.backend.bo.SignInInfoBO;
import com.kuaikan.role.game.backend.model.SignInInfoVO;
import com.kuaikan.role.game.backend.service.AsyncSignInService;

/**
 * SignInBiz
 *
 * <AUTHOR>
 * @since 2024-05-08
 */
@Service
public class SignInBiz {

    @Resource
    private AsyncSignInService asyncSignInService;

    public BizResult<SignInInfoVO> getSignInInfo(int userId, int signInId) {
        SignInInfoBO signInInfoBo = new SignInInfoBO();
        asyncSignInService.getSignInInfo(userId, signInId, signInInfoBo::setSignInInfoDto);
        Work.complete();

        SignInInfoVO signInInfoVo = SignInInfoVO.valueOf(signInInfoBo.getSignInInfoDto());
        return BizResult.success(signInInfoVo);
    }

    public BizResult<Void> signIn(int userId, int signInId, int offset) {
        SignInBO signInBo = new SignInBO();
        SignInContextDto signInContextDto = SignInContextDto.builder().signInId(signInId).offset(offset).build();
        asyncSignInService.signIn(userId, signInContextDto, signInBo::setSignInPrizeResultDto);
        Work.complete();

        SignInPrizeResultDto resultDto = signInBo.getSignInPrizeResultDto();
        if (resultDto == null) {
            return BizResult.result(RoleGameResponse.SIGN_IN_FAILED);
        }
        return BizResult.result(resultDto.getCodeMsg());
    }
}
