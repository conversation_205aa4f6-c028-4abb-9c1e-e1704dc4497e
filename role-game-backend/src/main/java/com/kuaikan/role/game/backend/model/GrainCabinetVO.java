package com.kuaikan.role.game.backend.model;

import java.util.List;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.model.RoleModel;
import com.kuaikan.role.game.api.rpc.result.GrainCabinetModel;

/**
 * <AUTHOR>
 * @date 2024/3/1
 */
@Data
@Accessors(chain = true)
public class GrainCabinetVO {

    private int maxRoleNum;

    private int realRoleNum;

    private int currentRoleId;

    private List<RoleVO> roleList;

    @Data
    @Accessors(chain = true)
    public static class RoleVO {

        private int roleId;

        private int level;

        private String roleName;

        /**
         * 专题id
         */
        private int topicId;

        private String topicName;

        private ImageInfoVO image;

        private ImageInfoVO avatar;

        private Boolean adopted;

        private Boolean canAdopt;

        private ImageInfoVO introductionImage;

        private ImageInfoVO personalityTabImage;

        private ImageInfoVO personalityLabel;

        private ImageInfoVO personalityLabelUnlock;

        public static RoleVO valueOf(RoleModel roleModel, boolean adopted, boolean hadNum) {
            return new RoleVO().setRoleId(roleModel.getId())
                    .setLevel(roleModel.getLevel())
                    .setRoleName(roleModel.getName())
                    .setTopicId(roleModel.getTopicId())
                    .setTopicName(roleModel.getTopicTitle())
                    .setImage(ImageInfoVO.valueOf(roleModel.getImage()))
                    .setAvatar(ImageInfoVO.valueOf(roleModel.getAvatar()))
                    .setAdopted(adopted)
                    .setCanAdopt(hadNum && !adopted)
                    .setIntroductionImage(ImageInfoVO.valueOf(roleModel.getIntroductionImage()))
                    .setPersonalityTabImage(ImageInfoVO.valueOf(roleModel.getPersonalityTabImage()))
                    .setPersonalityLabel(ImageInfoVO.valueOf(roleModel.getPersonalityLabel()))
                    .setPersonalityLabelUnlock(ImageInfoVO.valueOf(roleModel.getPersonalityLabelUnlock()));
        }

    }

    public static GrainCabinetVO valueOf(GrainCabinetModel grainCabinetModel) {
        if (grainCabinetModel == null) {
            return null;
        }
        GrainCabinetVO grainCabinetVO = new GrainCabinetVO().setMaxRoleNum(grainCabinetModel.getMaxRoleNum())
                .setRealRoleNum(grainCabinetModel.getRealRoleNum())
                .setCurrentRoleId(grainCabinetModel.getCurrentRoleId());
        boolean hadNum = grainCabinetModel.getMaxRoleNum() - grainCabinetModel.getRealRoleNum() >= 1;
        if (grainCabinetModel.getRoleModelList() != null) {
            List<RoleVO> roleList = grainCabinetModel.getRoleModelList().stream().map(roleModel -> {
                Boolean isAdopted = grainCabinetModel.getRoleAdoptMap().getOrDefault(roleModel.getId(), false);
                return RoleVO.valueOf(roleModel, isAdopted, hadNum);
            }).collect(Collectors.toList());
            grainCabinetVO.setRoleList(roleList);
        }
        return grainCabinetVO;
    }

}
