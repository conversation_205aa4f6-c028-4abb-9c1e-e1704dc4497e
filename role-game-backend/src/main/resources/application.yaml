spring:
  application:
    name: role-game-backend
server:
  port: 9208
  tomcat:
    accesslog:
      enabled: true
      directory: "/data/logs/role-game-backend"
      pattern: '%h %l %u %t %{x-request-id}i "%r" %s %b %D'
      prefix: "access"
      suffix: ".log"
    max-threads: 3000
    min-spare-threads: 100
    max-connections: 3000
    accept-count: 200
    uri-encoding: UTF-8
    max-http-post-size: 10485760

management:
  server:
    port: 2021
  endpoints:
    web:
      base-path: /
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always
  health:
    defaults:
      enabled: false

dubbo:
  application:
    name: role-game-backend
    qos-enable: true
    logger: slf4j
  protocols:
    dubbo:
      name: dubbo
      port: 29208
      threads: 500
    tri:
      name: tri
      port: 29218
      threads: 500
  registry:
    file: /data/dubbo-registry/role-game-backend.cache
    protocol: zookeeper
  config:
    multiple: false
  consumer:
    timeout: 2000
    check: false
    cluster: failfast
  remoting:
    server: tomcat

springfox:
  documentation:
    swagger:
      v2:
        path: /v2/role/game/api-docs
---

spring:
  profiles: stag
zookeeper:
  host: **********:2181,**********:2181,**********:2181,**********:2181,**********:2181
logging:
  config: classpath:log4j2-stag.xml

dubbo:
  registry:
    address: **********:2181,**********:2181,**********:2181,**********:2181,**********:2181


---

spring:
  profiles: prod
zookeeper:
  host: **********:2181,***********:2181,***********:2181,***********:2181,***********:2181
logging:
  config: classpath:log4j2-prod.xml

dubbo:
  registry:
    address: **********:2181,***********:2181,***********:2181,***********:2181,***********:2181
