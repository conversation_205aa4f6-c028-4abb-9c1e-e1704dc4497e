package com.kuaikan.role.game.timer.dao.mongo;

import java.util.Date;
import java.util.List;
import javax.annotation.Resource;

import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import com.kuaikan.role.game.api.bean.CommonAudio;
import com.kuaikan.role.game.api.enums.CommonAudioStatus;

@Service
public class CommonAudioDao {

    @Resource
    private MongoTemplate mongoTemplatePrimary;

    public CommonAudio findByAudioId(String audioId) {
        Query query = new Query(Criteria.where("audio_id").is(audioId));
        return mongoTemplatePrimary.findOne(query, CommonAudio.class);
    }

    public List<CommonAudio> findAllUnstartTask() {
        Criteria criteria = Criteria.where("status").is(CommonAudioStatus.WAITING.getCode());
        Query query = new Query(criteria);
        return mongoTemplatePrimary.find(query, CommonAudio.class);
    }

    public void updateTaskStatus(String audioId) {
        Criteria criteria = Criteria.where("audio_id").is(audioId);
        Query query = new Query(criteria);
        Update update = new Update();
        update.set("status", CommonAudioStatus.PROCESSING.getCode());
        update.set("commit_task_time", new Date());
        mongoTemplatePrimary.updateFirst(query, update, CommonAudio.class);
    }

    public void updateTaskSuccess(CommonAudio commonAudio) {
        Query query = new Query(Criteria.where("audio_id").is(commonAudio.getAudioId()));
        Update update = new Update();
        update.set("duration", commonAudio.getDuration());
        update.set("origin_audio", commonAudio.getOriginAudio());
        update.set("low_bit_audio", commonAudio.getLowBitAudio());
        update.set("status", CommonAudioStatus.SUCCESS.getCode());
        update.set("updated_at", new Date());
        mongoTemplatePrimary.updateFirst(query, update, CommonAudio.class);
    }

    public List<CommonAudio> getAllProcessingTask() {
        Query query = new Query(Criteria.where("status").is(CommonAudioStatus.PROCESSING.getCode()));
        return mongoTemplatePrimary.find(query, CommonAudio.class);
    }

    public void updateTaskFail(String audioId) {
        Query query = new Query(Criteria.where("audio_id").is(audioId));
        Update update = new Update();
        update.set("status", CommonAudioStatus.FAILED.getCode());
        update.set("updated_at", new Date());
        mongoTemplatePrimary.updateFirst(query, update, CommonAudio.class);
    }
}
