package com.kuaikan.role.game.timer.task;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import com.xxl.job.core.handler.annotation.XxlJob;

import com.kuaikan.common.config.Environment;
import com.kuaikan.common.config.Settings;
import com.kuaikan.common.tools.log.KKFilterUtils;
import com.kuaikan.game.gamecard.base.model.Prize;
import com.kuaikan.game.gamecard.dubbo.dto.ClientInfoDTO;
import com.kuaikan.game.gamecard.prize.def.service.GameCardPrizeService;
import com.kuaikan.role.game.api.bean.ClaimPrizeRecord;
import com.kuaikan.role.game.api.bean.ClientInfo;
import com.kuaikan.role.game.api.enums.UserTableEnum;
import com.kuaikan.role.game.api.model.ClaimPrizeRecordModel;
import com.kuaikan.role.game.api.rpc.param.AssignRewardRetryParam;
import com.kuaikan.role.game.api.service.AssignRewardService;
import com.kuaikan.role.game.timer.dao.rolegame.ClaimPrizeRecordMapper;

/**
 * <AUTHOR>
 * @date 2024/4/8
 */
@Component
@Slf4j
public class RetryClaimPrizeTask {

    private static final int LIMIT = 1000;

    @Resource
    private ClaimPrizeRecordMapper claimPrizeRecordMapper;

    @Resource
    private AssignRewardService assignRewardService;

    @Resource
    private GameCardPrizeService gameCardPrizeService;

    @XxlJob("retryClaimPrizeTask")
    public void retryClaimPrize() {
        String sessionId = KKFilterUtils.createNewSessionId();
        MDC.put(KKFilterUtils.SESSION_ID, sessionId);
        log.info("retryClaimPrize start");
        int totalTable = Settings.getEnvironment().le(Environment.PREVIEW) ? 2 : UserTableEnum.CLAIM_PRIZE_RECORD.getCount();
        for (int i = 0; i < totalTable; i++) {
            String tableName = String.format("%s%s", UserTableEnum.CLAIM_PRIZE_RECORD.getPrefix(), i);
            int offset = 0;
            while (true) {
                List<ClaimPrizeRecord> claimPrizeRecords = claimPrizeRecordMapper.queryNotFinishedPrizeRecordByPage(tableName, offset, LIMIT);
                if (CollectionUtils.isEmpty(claimPrizeRecords)) {
                    break;
                }
                List<Long> prizeIds = claimPrizeRecords.stream().map(ClaimPrizeRecord::getPrizeId).collect(Collectors.toList());
                Map<Long, Prize> prizeMap = gameCardPrizeService.getOnlinePrizeMapByPrizeIdList(prizeIds);
                for (ClaimPrizeRecord claimPrizeRecord : claimPrizeRecords) {
                    if (!prizeMap.containsKey(claimPrizeRecord.getPrizeId())) {
                        log.error("prize not found, claimPrizeRecord:{}", claimPrizeRecord);
                        continue;
                    }
                    final ClaimPrizeRecordModel claimPrizeRecordModel = ClaimPrizeRecordModel.valueOf(claimPrizeRecord);
                    Prize prize = claimPrizeRecordModel.getExtraInfo().getInParam().getPrizeContext().getPrize();
                    if (prize == null) {
                        prize = prizeMap.get(claimPrizeRecord.getPrizeId());
                    }
                    if (isLackParam(claimPrizeRecordModel)) {
                        ClientInfo defaultClientInfoModel = ClientInfo.getDefault(claimPrizeRecordModel.getUserId());
                        final ClientInfoDTO clientInfo = claimPrizeRecordModel.getExtraInfo().getInParam().getClientInfo();
                        clientInfo.setUserAgent(defaultClientInfoModel.getUserAgent());
                        clientInfo.setXDeviceHeader(defaultClientInfoModel.getXDevice());
                        log.warn("retryClaimPrize, lackParam claimPrizeRecordModel:{}, clientInfo:{}", claimPrizeRecordModel, clientInfo);
                    }

                    AssignRewardRetryParam assignRewardParam = new AssignRewardRetryParam().setPrize(prize).setClaimPrizeRecord(claimPrizeRecordModel);
                    try {
                        assignRewardService.assignRewardRetry(assignRewardParam);
                    } catch (Exception e) {
                        log.error("retryClaimPrize error, assignRewardParam:{}", assignRewardParam, e);
                    }
                }
                offset = claimPrizeRecords.get(0).getId();
            }
            log.info("retryClaimPrize finish, tableName:{}", tableName);
        }
        log.info("retryClaimPrize finish");
    }

    private static boolean isLackParam(ClaimPrizeRecordModel claimPrizeRecordModel) {
        return claimPrizeRecordModel != null
                && claimPrizeRecordModel.getExtraInfo() != null
                && claimPrizeRecordModel.getExtraInfo().getInParam() != null
                && claimPrizeRecordModel.getExtraInfo().getInParam().getClientInfo() != null
                && (claimPrizeRecordModel.getExtraInfo().getInParam().getClientInfo().getUserAgent() == null || StringUtils.isBlank(
                claimPrizeRecordModel.getExtraInfo().getInParam().getClientInfo().getXDeviceHeader()));
    }

}
