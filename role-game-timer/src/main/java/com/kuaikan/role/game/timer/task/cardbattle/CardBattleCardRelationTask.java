package com.kuaikan.role.game.timer.task.cardbattle;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import com.xxl.job.core.handler.annotation.XxlJob;

import com.kuaikan.role.game.api.service.cardbattle.CardBattleDataFixService;

/**
 * 卡牌映射关系拉取校验任务
 * <AUTHOR>
 * @date 2025/3/13
 */
@Slf4j
@Service
public class CardBattleCardRelationTask {

    @Resource
    private CardBattleDataFixService cardBattleDataFixService;

    @XxlJob("checkCardRelationTask")
    public void CardRelationCheck() {
        log.info("卡牌映射关系拉取校验任务开始");
        cardBattleDataFixService.checkCardRelation();
        log.info("卡牌映射关系拉取校验任务结束");
    }
}
