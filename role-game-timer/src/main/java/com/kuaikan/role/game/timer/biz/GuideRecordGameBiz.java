package com.kuaikan.role.game.timer.biz;

import com.google.common.collect.Lists;
import com.kuaikan.common.redis.KeyGenerator;
import com.kuaikan.common.redis.lettuce.LettuceClusterUtil;
import com.kuaikan.role.game.common.enums.CacheConfig;
import com.kuaikan.role.game.timer.model.DatasourceModel;
import com.kuaikan.role.game.timer.model.KafkaMessage;
import com.kuaikan.role.game.timer.util.RedisCacheClearUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.kuaikan.role.game.timer.constant.RoleGameCacheConstants.DDLEvent.ALL_UPDATE_EVENT;
import static com.kuaikan.role.game.timer.constant.RoleGameCacheConstants.Datasource.ROLE_BASIC;


@Slf4j
@Service
public class GuideRecordGameBiz extends AbstractBiz {

    private static final String FIELD_ID = "user_id";

    private static final String TABLE_NAME = "guide_record";

    @Override
    protected List<DatasourceModel> getTableName() {
        List<DatasourceModel> datasourceModelList = Lists.newArrayList();
        DatasourceModel datasourceModel = new DatasourceModel(ROLE_BASIC, Lists.newArrayList(TABLE_NAME));
        datasourceModelList.add(datasourceModel);
        return datasourceModelList;
    }

    @Override
    public Set<String> getEventType() {
        return ALL_UPDATE_EVENT;
    }

    @Override
    public void clearCache(List<KafkaMessage> kafkaMessages) {
        List<String> cacheKeys = new ArrayList<>();
        for (KafkaMessage kafkaMessage : kafkaMessages) {
            List<Map<String, String>> rowMaps = kafkaMessage.getData();
            for (Map<String, String> rowMap : rowMaps) {
                int userId = Integer.parseInt(rowMap.get(FIELD_ID));
                String userIdCacheKey = KeyGenerator.generate(CacheConfig.GUIDE_RECORD.getKeyPattern(), userId);
                cacheKeys.add(userIdCacheKey);
            }
        }
        RedisCacheClearUtil.clearRedisClusterCache(LettuceClusterUtil.getClusterClientByName(CacheConfig.GUIDE_RECORD.getReadWriteVip()),
                cacheKeys.toArray(new String[0]));
    }
}
