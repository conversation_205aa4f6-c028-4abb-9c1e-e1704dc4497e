package com.kuaikan.role.game.timer.dao.rolegame;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.kuaikan.role.game.api.bean.ClaimPrizeRecord;

/**
 * <AUTHOR>
 * @version 2024-03-27
 */

public interface ClaimPrizeRecordMapper {

    List<ClaimPrizeRecord> queryNotFinishedPrizeRecordByPage(@Param("tableName") String tableName, @Param("offset") int offset, @Param("limit") int limit);
}
