package com.kuaikan.role.game.timer.componet;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.Map;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.qiniu.common.Zone;
import com.qiniu.http.Response;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.UploadManager;

import com.kuaikan.common.config.Settings;
import com.kuaikan.third.qiniu.helper.AuthHelper;
import com.kuaikan.third.qiniu.helper.OperationManagerHelper;
import com.kuaikan.third.qiniu.model.RequestParamModel;
import com.kuaikan.third.qiniu.service.QiniuAuth;
import com.kuaikan.third.qiniu.service.QiniuOperationManager;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Slf4j
@Component
public class QiniuComponent {

    @Resource
    private QiniuAuth qiniuAuth;
    @Resource
    private QiniuOperationManager qiniuOperationManager;
    private OperationManagerHelper operationManager;
    private RequestParamModel requestParamModel;
    private AuthHelper authHelper;
    private UploadManager uploadManager;

    public static final String PAY_BUCKET_NAME = Settings.getEnvironment().isProd() ? "kuaikan-payment" : "kk-payment-stag";

    @PostConstruct
    private void init() {
        requestParamModel = new RequestParamModel("role-game", "AyFgZCqhjqqPwfmghspOAYfUitgjQxHt");
        operationManager = new OperationManagerHelper(requestParamModel, qiniuOperationManager);
        authHelper = new AuthHelper(requestParamModel, qiniuAuth);
        Configuration configuration = new Configuration(Zone.autoZone());
        uploadManager = new UploadManager(configuration);
    }

    public String uploadWithStream(String bucket, File file, String key) {
        try {
            InputStream inputStream = Files.newInputStream(file.toPath());
            String token = authHelper.uploadToken(bucket);
            byte[] data = IOUtils.toByteArray(inputStream);
            Response res = uploadManager.put(data, key, token, null, null, false);
            String jsonValue = res.bodyString();
            Map<String, Object> jsonMap = JSON.parseObject(jsonValue, Maps.newHashMap().getClass());
            return (String) jsonMap.get("key");
        } catch (IOException e) {
            log.error("uploadWithStream error", e);
            return "";
        }
    }

    public String uploadWithStream(String bucket, String filePath, String key) {
        try {
            InputStream inputStream = new FileInputStream(filePath);
            String token = authHelper.uploadToken(bucket);
            byte[] data = IOUtils.toByteArray(inputStream);
            Response res = uploadManager.put(data, key, token, null, null, false);
            String jsonValue = res.bodyString();
            Map<String, Object> jsonMap = JSON.parseObject(jsonValue, Maps.newHashMap().getClass());
            return (String) jsonMap.get("key");
        } catch (IOException e) {
            log.error("uploadWithStream error", e);
            return "";
        }
    }

    public String uploadWithStream(String bucket, byte[] data, String key) {
        try {
            String token = authHelper.uploadToken(bucket);
            Response res = uploadManager.put(data, key, token, null, null, false);
            String jsonValue = res.bodyString();
            Map<String, Object> jsonMap = JSON.parseObject(jsonValue, Maps.newHashMap().getClass());
            return (String) jsonMap.get("key");
        } catch (IOException e) {
            log.error("uploadWithStream error bucket {} key {}", bucket, key, e);
            return "";
        }
    }

}
