package com.kuaikan.role.game.timer.biz;

import com.kuaikan.role.game.timer.model.DatasourceModel;
import com.kuaikan.role.game.timer.model.KafkaMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import java.util.List;
import java.util.Set;

/**
 * Created on 2019-03-18.
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractBiz {

    /**
     * 实例创建时初始化，注册为数据变更的处理器
     */
    AbstractBiz() {
        HandleBinlogBizFactory.putTable2FactoryMap(getTableName(), this);
    }

    /**
     * 获取当前监听变更的数据库表名
     *
     * @return 数据库表名
     */
    protected abstract List<DatasourceModel> getTableName();

    /**
     * 获取待处理的数据变更类型
     *
     * @return 数据变更类型集合
     */
    public abstract Set<String> getEventType();

    /**
     * 数据变更后的处理函数
     *
     * @param kafkaMessages 数据变更消息
     */
    public abstract void clearCache(List<KafkaMessage> kafkaMessages);

    @Override
    public int hashCode() {
        return HashCodeBuilder.reflectionHashCode(this);
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof AbstractBiz) {
            return EqualsBuilder.reflectionEquals(this, obj);
        }
        return Boolean.FALSE;
    }
}
