<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kuaikan.role.game.dao.rolegame.RoleBuildingMoodMapper">

    <resultMap id="resultMap" type="com.kuaikan.role.game.api.bean.RoleBuildingMood">
        <id column="id" property="id"/>
        <result column="role_id" property="roleId"/>
        <result column="building_id" property="buildingId"/>
        <result column="configs" property="configs"
                typeHandler="com.kuaikan.common.db.mybatis.ListJsonTypeHandler"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="columns">
        id
        ,
        role_id,
        building_id,
        configs,
        created_at,
        updated_at
    </sql>

    <select id="queryByRoleId" resultMap="resultMap">
        select
        <include refid="columns"/>
        from role_building_mood
        where role_id=#{roleId}
    </select>

    <select id="queryByRoleIdAndBuildingId" resultMap="resultMap">
        select
        <include refid="columns"/>
        from role_building_mood
        where role_id=#{roleId} and building_id=#{buildingId}
    </select>

    <select id="queryAll" resultMap="resultMap">
        select
        <include refid="columns"/>
        from role_building_mood
    </select>
</mapper>

