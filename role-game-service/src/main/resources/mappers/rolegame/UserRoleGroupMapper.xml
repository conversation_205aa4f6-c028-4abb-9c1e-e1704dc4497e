<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kuaikan.role.game.dao.rolegame.UserRoleGroupMapper">
    <resultMap id="BaseResultMap" type="com.kuaikan.role.game.api.bean.UserRoleGroup">
        <!--@mbg.generated-->
        <!--@Table ${tableName}-->
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="role_group_id" property="roleGroupId"/>
        <result column="extra_info" property="extraInfo" javaType="com.kuaikan.role.game.api.bean.UserRoleGroup$ExtraInfo"
                typeHandler="com.kuaikan.common.db.mybatis.JsonTypeHandler"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, user_id, role_group_id, extra_info, created_at, updated_at
    </sql>
    <insert id="insert" keyColumn="record.id" keyProperty="record.id" parameterType="com.kuaikan.role.game.api.bean.UserRoleGroup"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into ${tableName} (user_id, role_group_id, extra_info)
        values (#{record.userId},
        #{record.roleGroupId},
        #{record.extraInfo,typeHandler=com.kuaikan.common.db.mybatis.JsonTypeHandler, javaType=com.kuaikan.role.game.api.bean.UserRoleGroup$ExtraInfo})
    </insert>

    <select id="queryByUserIdAndRoleGroupId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${tableName}
        where user_id=#{userId}
        and role_group_id=#{roleGroupId}
    </select>

    <insert id="batchInsert">
        <!--@mbg.generated-->
        insert ignore into ${tableName} (user_id, role_group_id, extra_info)
        values
        <foreach collection="records" item="record" separator=",">
            (#{record.userId}, #{record.roleGroupId},
            #{record.extraInfo,typeHandler=com.kuaikan.common.db.mybatis.JsonTypeHandler, javaType=com.kuaikan.role.game.api.bean.UserRoleGroup$ExtraInfo})
        </foreach>
    </insert>

    <update id="updateExtraInfoByUserIdAndRoleGroupId">
        update ${tableName}
        set extra_info =#{extraInfo,typeHandler=com.kuaikan.common.db.mybatis.JsonTypeHandler, javaType=com.kuaikan.role.game.api.bean.UserRoleGroup$ExtraInfo}
        where user_id = #{userId}
          and role_group_id = #{roleGroupId}
    </update>
</mapper>