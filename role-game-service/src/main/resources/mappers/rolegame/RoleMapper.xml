<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kuaikan.role.game.dao.rolegame.RoleMapper">
    <resultMap id="resultMap" type="com.kuaikan.role.game.api.bean.Role">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="image" property="image" javaType="com.kuaikan.role.game.api.bo.ImageInfo"
                typeHandler="com.kuaikan.common.db.mybatis.JsonTypeHandler"/>
        <result column="avatar" property="avatar" javaType="com.kuaikan.role.game.api.bo.ImageInfo"
                typeHandler="com.kuaikan.common.db.mybatis.JsonTypeHandler"/>
        <result column="topic_id" property="topicId"/>
        <result column="topic_name" property="topicName"/>
        <result column="order_num" property="orderNum"/>
        <result column="default_costume_id" property="defaultCostumeId"/>
        <result column="default_scene_id" property="defaultSceneId"/>
        <result column="config" property="config" typeHandler="com.kuaikan.common.db.mybatis.JsonTypeHandler"
                javaType="com.kuaikan.role.game.api.bean.Role$Config"/>
        <result column="status" property="status"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>
    <sql id="column">
        id
        , `name`, image,avatar, topic_id,topic_name, order_num,default_costume_id, default_scene_id, `status`,config, created_at, updated_at
    </sql>
    <select id="queryRoleListByTopicId" parameterType="java.lang.Integer" resultMap="resultMap">
        select
        <include refid="column"/>
        from role
        where topic_id = #{topicId}
    </select>

    <select id="queryByPage" parameterType="com.kuaikan.role.game.api.bean.Role" resultMap="resultMap">
        select
        <include refid="column"/>
        from role
        order by order_num
        LIMIT #{offset},#{limit}
    </select>

    <select id="count" parameterType="com.kuaikan.role.game.api.bean.Role" resultType="java.lang.Integer">
        select count(1)
        from role
    </select>

    <select id="queryAll" resultMap="resultMap">
        select
        <include refid="column"/>
        from role
    </select>
    <select id="queryByIds" parameterType="java.util.Collection" resultMap="resultMap">
        select
        <include refid="column"/>
        from role
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="queryById" resultMap="resultMap">
        select
        <include refid="column"/>
        from role
        where id = #{id}
    </select>

</mapper>