<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kuaikan.role.game.dao.rolegame.SceneMapper">
    <resultMap id="resultMap" type="com.kuaikan.role.game.api.bean.Scene">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="config" property="config" typeHandler="com.kuaikan.common.db.mybatis.JsonTypeHandler"
                javaType="com.kuaikan.role.game.api.bean.Scene$Config"/>
        <result column="status" property="status"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>
    <sql id="column">
        id
        , `name`, `config`, `status`, created_at, updated_at
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="resultMap">
        select
        <include refid="column"/>
        from scene
        where id = #{id}
    </select>
    <select id="queryByPage" resultMap="resultMap">
        select
        <include refid="column"/>
        from scene
        order by id desc
        limit #{offset}, #{limit}
    </select>

    <select id="queryByIds" parameterType="java.util.Collection" resultMap="resultMap">
        select
        <include refid="column"/>
        from scene
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

</mapper>