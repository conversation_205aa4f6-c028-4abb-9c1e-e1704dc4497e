<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kuaikan.role.game.dao.rolegame.UserRolePropertyMapper">

    <resultMap id="resultMap" type="com.kuaikan.role.game.api.bean.UserRoleProperty">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="role_id" property="roleId"/>
        <result column="role_level" property="roleLevel"/>
        <result column="role_exp" property="roleExp"/>
        <result column="tiredness" property="tiredness"/>
        <result column="mood" property="mood"/>
        <result column="energy" property="energy"/>
        <result column="recover_date" property="recoverDate"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="columns">
        id
        ,
        user_id,
        role_id,
        role_level,
        role_exp,
        tiredness,
        mood,
        energy,
        recover_date,
        created_at,
        updated_at
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="userRoleProperty.id">
        insert
        ignore into user_role_property
        (`user_id`, `role_id`, `role_level`, `role_exp`, `tiredness`, `mood`, `energy`)
        values (
        #{userRoleProperty.userId},
        #{userRoleProperty.roleId},
        #{userRoleProperty.roleLevel},
        #{userRoleProperty.roleExp},
        #{userRoleProperty.tiredness},
        #{userRoleProperty.mood},
        #{userRoleProperty.energy}
        )
    </insert>

    <select id="queryByUserIdAndRoleId" resultMap="resultMap">
        select
        <include refid="columns"/>
        from user_role_property
        where user_id=#{userId} and role_id=#{roleId}
    </select>

    <select id="queryByUserId" resultMap="resultMap">
        select
        <include refid="columns"/>
        from user_role_property
        where user_id=#{userId}
    </select>

    <update id="updateExp">
        update user_role_property
        set role_level=#{level},
            role_exp=#{exp}
        where user_id = #{userId}
          and role_id = #{roleId}
    </update>

    <update id="addExp">
        update user_role_property
        set role_exp = role_exp + #{exp}
        where user_id = #{userId}
          and role_id = #{roleId}
    </update>

    <update id="updateEnergy">
        update user_role_property
        set energy = #{energy}
        where user_id = #{userId}
          and role_id = #{roleId}
    </update>

    <update id="addEnergy">
        update user_role_property
        set energy = energy + #{energy}
        where user_id = #{userId}
          and role_id = #{roleId}
          and energy = #{beforeEnergy}
    </update>

    <update id="addMood">
        update user_role_property
        set mood = mood + #{mood}
        where user_id = #{userId}
          and role_id = #{roleId}
          and mood = #{beforeMood}
    </update>
    <update id="updateMood">
        update user_role_property
        set mood = #{mood}
        where user_id = #{userId}
          and role_id = #{roleId}
          and mood = #{beforeMood}
    </update>

    <update id="addTiredness">
        update user_role_property
        set tiredness = tiredness + #{tiredness}
        where user_id = #{userId}
          and role_id = #{roleId}
          and tiredness = #{beforeTiredness}
    </update>
    <update id="updateTiredness">
        update user_role_property
        set tiredness = #{tiredness}
        where user_id = #{userId}
          and role_id = #{roleId}
          and tiredness = #{beforeTiredness}
    </update>

    <update id="recoverEnergyAndTiredness">
        update user_role_property
        set tiredness    = (
            case
                WHEN tiredness > #{tirednessReduction} THEN tiredness - #{tirednessReduction}
                ELSE 0
                END
            ),
            energy       = #{energy},
            recover_date = #{recoverDate}
        where user_id = #{userId}
          and role_id = #{roleId}
    </update>
</mapper>

