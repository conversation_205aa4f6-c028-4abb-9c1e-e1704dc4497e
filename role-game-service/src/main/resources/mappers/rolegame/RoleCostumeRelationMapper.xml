<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kuaikan.role.game.dao.rolegame.RoleCostumeRelationMapper">
    <resultMap id="resultMap" type="com.kuaikan.role.game.api.bean.RoleCostumeRelation">
        <id column="id" property="id"/>
        <result column="role_id" property="roleId"/>
        <result column="costume_id" property="costumeId"/>
        <result column="config" property="config"
                typeHandler="com.kuaikan.common.db.mybatis.JsonTypeHandler"
                javaType="com.kuaikan.role.game.api.bean.RoleCostumeRelation$Config"/>
        <result column="order_num" property="orderNum"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>
    <sql id="column">
        id
        , role_id, costume_id, config, order_num, created_at, updated_at
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="resultMap">
        select
        <include refid="column"/>
        from role_costume_relation
        where id = #{id}
    </select>
    <select id="queryByRoleIds" parameterType="java.util.List" resultMap="resultMap">
        select
        <include refid="column"/>
        from role_costume_relation
        where role_id in
        <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </select>
    <select id="queryByRoleId" parameterType="java.lang.Integer" resultMap="resultMap">
        select
        <include refid="column"/>
        from role_costume_relation
        where role_id = #{roleId}
    </select>
    <select id="queryByCostumeId" parameterType="java.lang.Integer" resultMap="resultMap">
        select
        <include refid="column"/>
        from role_costume_relation
        where costume_id = #{costumeId}
    </select>

    <select id="queryByCostumeIds" parameterType="java.util.List" resultMap="resultMap">
        select
        <include refid="column"/>
        from role_costume_relation
        where costume_id in
        <foreach collection="costumeIds" item="costumeId" open="(" separator="," close=")">
            #{costumeId}
        </foreach>
    </select>

</mapper>