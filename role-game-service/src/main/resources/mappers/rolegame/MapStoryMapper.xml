<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kuaikan.role.game.dao.rolegame.MapStoryMapper">

    <resultMap id="MapStoryResultMap" type="com.kuaikan.role.game.api.bean.MapStory">
        <id column="id" property="id"/>
        <result column="map_id" property="mapId"/>
        <result column="library_id" property="libraryId"/>
        <result column="tag" property="tag"/>
        <result column="avg_chapter_id" property="avgChapterId"/>
        <result column="cover_img" property="coverImage"/>
        <result column="weight" property="weight"/>
        <result column="obtain_copywriting" property="obtainCopywriting"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , map_id, library_id, tag, avg_chapter_id, cover_img, weight, obtain_copywriting
    </sql>

    <select id="selectById" resultMap="MapStoryResultMap" parameterType="int">
        SELECT
        <include refid="Base_Column_List"/>
        FROM map_story
        WHERE id = #{id}
    </select>

    <select id="queryByMapId" resultMap="MapStoryResultMap" parameterType="int">
        SELECT
        <include refid="Base_Column_List"/>
        FROM map_story
        WHERE map_id = #{mapId}
        AND status = 0
    </select>
</mapper>