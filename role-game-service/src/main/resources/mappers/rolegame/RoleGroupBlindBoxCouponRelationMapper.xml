<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kuaikan.role.game.dao.rolegame.RoleGroupBlindBoxCouponRelationMapper">
    <resultMap id="BaseResultMap" type="com.kuaikan.role.game.common.bean.RoleGroupBlindBoxCouponRelation">
        <!--@mbg.generated-->
        <!--@Table role_group_blind_box_coupon_relation-->
        <id column="id" property="id"/>
        <result column="role_group_id" property="roleGroupId"/>
        <result column="coupon_id" property="couponId"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, role_group_id, coupon_id, created_at, updated_at
    </sql>

    <select id="queryByCouponId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from role_group_blind_box_coupon_relation
        where coupon_id=#{couponId}
    </select>

    <select id="queryByCouponIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from role_group_blind_box_coupon_relation
        where coupon_id in
        <foreach collection="couponIds" item="couponId" open="(" separator="," close=")">
            #{couponId}
        </foreach>
    </select>
</mapper>