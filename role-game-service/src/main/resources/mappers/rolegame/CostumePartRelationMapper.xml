<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kuaikan.role.game.dao.rolegame.CostumePartRelationMapper">
    <resultMap id="resultMap" type="com.kuaikan.role.game.api.bean.CostumePartRelation">
        <id column="id" property="id"/>
        <result column="costume_id" property="costumeId"/>
        <result column="costume_part_id" property="costumePartId"/>
        <result column="order_num" property="orderNum"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="column">
        id
        , costume_id, costume_part_id, order_num, created_at, updated_at
    </sql>

    <select id="selectByCostumeId" resultMap="resultMap">
        select
        <include refid="column"/>
        from costume_part_relation
        where costume_id = #{costumeId}
        order by order_num
    </select>

    <select id="selectByCostumeIdsFromDb" resultMap="resultMap">
        select
        <include refid="column"/>
        from costume_part_relation
        where costume_id in
        <foreach collection="costumeIds" item="costumeId" open="(" separator="," close=")">
            #{costumeId}
        </foreach>
    </select>

    <select id="selectByCostumePartId" resultMap="resultMap">
        select
        <include refid="column"/>
        from costume_part_relation
        where costume_part_id =#{costumePartId}
        limit 1
    </select>

    <select id="selectByCostumePartIds" resultMap="resultMap">
        select
        <include refid="column"/>
        from costume_part_relation
        where costume_part_id in
        <foreach collection="costumePartIds" item="costumePartId" open="(" separator="," close=")">
            #{costumePartId}
        </foreach>
    </select>

</mapper>