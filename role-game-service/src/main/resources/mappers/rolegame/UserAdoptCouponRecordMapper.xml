<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kuaikan.role.game.dao.rolegame.UserAdoptCouponRecordMapper">
    <resultMap id="BaseResultMap" type="com.kuaikan.role.game.api.bean.UserAdoptCouponRecord">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="coupon_id" property="couponId"/>
        <result column="bid" property="bid"/>
        <result column="source" property="source"/>
        <result column="used" property="used"/>
        <result column="created_at" property="createdAt"/>
        <result column="expired_at" property="expiredAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, user_id, coupon_id, bid, `source`, used, created_at, expired_at, updated_at
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from ${tableName}
        where id = #{id}
    </select>

    <select id="selectByBid" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${tableName}
        where bid = #{bid}
    </select>

    <select id="selectByBids" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${tableName}
        where `bid` in
        <foreach collection="bids" item="bid" index="index" open="(" close=")" separator=",">
            #{bid}
        </foreach>
    </select>

    <insert id="insert" keyColumn="record.id" keyProperty="record.id"
            parameterType="com.kuaikan.role.game.api.bean.UserAdoptCouponRecord"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into ${tableName} (user_id, coupon_id, bid,`source`, used, expired_at
        )
        values (#{record.userId}, #{record.couponId}, #{record.bid}, #{record.source}, #{record.used}, #{record.expiredAt}
        )
    </insert>

    <select id="selectByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${tableName}
        where user_id = #{userId}
    </select>

    <update id="updateStatusByUserIdAndIds">
        UPDATE ${tableName}
        SET used = #{used}
        WHERE user_id = #{userId}
        and coupon_id in
        <foreach collection="ids" item="couponId" index="index" open="(" close=")" separator=",">
            #{couponId}
        </foreach>
    </update>

    <insert id="insertBatch">
        insert
        ignore into
        ${tableName}
        (
        user_id,
        coupon_id,
        bid,
        source,
        used,
        expired_at
        )
        values
        <foreach collection="records" item="record" separator=",">
            (
            #{record.userId},
            #{record.couponId},
            #{record.bid},
            #{record.source},
            #{record.used},
            #{record.expiredAt}
            )
        </foreach>
    </insert>

    <update id="batchUpdateStatus">
        UPDATE ${tableName}
        SET used = #{used}
        WHERE bid in
        <foreach collection="bids" item="bid" index="index" open="(" close=")" separator=",">
            #{bid}
        </foreach>
    </update>

    <select id="batchSelectByUserIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${tableName}
        WHERE user_id in
        <foreach collection="userIds" item="userId" index="index" open="(" close=")" separator=",">
            #{userId}
        </foreach>
    </select>
</mapper>