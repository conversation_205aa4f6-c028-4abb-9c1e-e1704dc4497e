<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kuaikan.role.game.dao.rolegame.UserSceneMapper">
    <resultMap id="BaseResultMap" type="com.kuaikan.role.game.api.bean.UserScene">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="scene_id" property="sceneId"/>
        <result column="show_red_dot" property="showRedDot"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , user_id, scene_id, show_red_dot, created_at, updated_at
    </sql>

    <select id="selectByUserId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${tableName}
        where user_id = #{userId}
    </select>

    <insert id="insertSelective" keyColumn="record.id" keyProperty="record.id"
            parameterType="com.kuaikan.role.game.api.bean.UserScene"
            useGeneratedKeys="true">
        insert into ${tableName}
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="record.userId != null">
                user_id,
            </if>
            <if test="record.sceneId != null">
                scene_id,
            </if>
            <if test="record.showRedDot != null">
                show_red_dot,
            </if>
            <if test="record.createdAt != null">
                created_at,
            </if>
            <if test="record.updatedAt != null">
                updated_at,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="record.userId != null">
                #{record.userId},
            </if>
            <if test="record.sceneId != null">
                #{record.sceneId},
            </if>
            <if test="record.showRedDot != null">
                #{record.showRedDot},
            </if>
            <if test="record.createdAt != null">
                #{record.createdAt},
            </if>
            <if test="record.updatedAt != null">
                #{record.updatedAt},
            </if>
        </trim>
    </insert>

    <select id="selectByUserIdSceneId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${tableName}
        where user_id = #{userId} AND scene_id = #{sceneId}
    </select>


    <update id="updateByUserIdSceneId">
        update ${tableName}
        <set>
            <if test="showRedDot != null">
                show_red_dot = #{showRedDot}
            </if>
        </set>
        where user_id = #{userId} AND scene_id = #{sceneId}
    </update>

    <update id="updateByUserIdSceneIds">
        update ${tableName}
        <set>
            <if test="showRedDot != null">
                show_red_dot = #{showRedDot}
            </if>
        </set>
        where user_id = #{userId} AND scene_id IN
        <foreach collection="sceneIds" item="sceneId" open="(" separator="," close=")">
            #{sceneId}
        </foreach>
    </update>
</mapper>