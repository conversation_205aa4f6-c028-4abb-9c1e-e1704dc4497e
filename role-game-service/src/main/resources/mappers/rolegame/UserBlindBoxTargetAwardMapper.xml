<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kuaikan.role.game.dao.rolegame.UserBlindBoxTargetAwardMapper">
    <resultMap id="BaseResultMap" type="com.kuaikan.role.game.api.bean.UserBlindBoxTargetAward">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="target_id" property="targetId"/>
        <result column="type" property="type"/>
        <result column="costume_id" property="costumeId"/>
        <result column="costume_level" property="costumeLevel"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , user_id
        , target_id
        , type
        , costume_id
        , costume_level
        , created_at
        , updated_at
    </sql>

    <insert id="insert" keyColumn="record.id" keyProperty="record.id"
            parameterType="com.kuaikan.role.game.api.bean.UserBlindBoxTargetAward"
            useGeneratedKeys="true">
        insert into ${tableName}
        (user_id,
         target_id,
         type,
         costume_id,
         costume_level)
        values (#{record.userId},
                #{record.targetId},
                #{record.type},
                #{record.costumeId},
                #{record.costumeLevel})
    </insert>

    <select id="queryCurrentAward" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${tableName}
        where user_id=#{userId} and target_id=#{targetId} and type=#{type} and costume_level=#{costumeLevel}
    </select>

    <select id="queryCurrentAwards" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${tableName}
        where user_id=#{userId} and target_id=#{targetId} and type=#{type}
    </select>

    <select id="queryByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${tableName}
        where user_id=#{userId}
    </select>

    <update id="update" parameterType="com.kuaikan.role.game.api.bean.UserBlindBoxTargetAward">
        update ${tableName}
        set costume_id = #{record.costumeId},
        costume_level = #{record.costumeLevel}
        where user_id = #{record.userId}
          and target_id = #{record.targetId}
          and type = #{record.type}
          and costume_level = #{record.costumeLevel}
    </update>

    <update id="updateCostumeLevel">
        update ${tableName}
        set costume_level = #{costumeLevel}
        where id = #{id}
    </update>

    <delete id="deleteByCondition">
        delete
        from ${tableName}
        where user_id = #{record.userId}
          and target_id = #{record.targetId}
          and type = #{record.type}
          and costume_level = #{record.costumeLevel}
    </delete>

</mapper>