<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kuaikan.role.game.dao.rolegame.ResourceConfigMapper">
    <resultMap id="BaseResultMap" type="com.kuaikan.role.game.api.bean.ResourceConfig">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="start_at" property="startAt"/>
        <result column="end_at" property="endAt"/>
        <result column="config" property="config" typeHandler="com.kuaikan.common.db.mybatis.JsonTypeHandler"
                javaType="com.kuaikan.role.game.api.bean.ResourceConfig$Config"/>
        <result column="order_num" property="orderNum"/>
        <result column="status" property="status"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , `name`, start_at, end_at, config, order_num, `status`, created_at, updated_at
    </sql>
    <select id="queryAllPublishedResourceConfigs" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from resource_config
        where status = 2
    </select>
</mapper>