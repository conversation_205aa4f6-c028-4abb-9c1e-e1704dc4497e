<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kuaikan.role.game.dao.rolegame.SilverCoinDropRecordMapper">
    <resultMap id="BaseResultMap" type="com.kuaikan.role.game.api.bean.SilverCoinDropRecord">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="bid" property="bid"/>
        <result column="num" property="num"/>
        <result column="source" property="source"/>
        <result column="status" property="status"/>
        <result column="extra_info" property="extraInfo" typeHandler="com.kuaikan.common.db.mybatis.JsonTypeHandler"
                javaType="com.kuaikan.role.game.api.bean.SilverCoinDropRecord$ExtraInfo"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , user_id, bid, num, `source`, `status`, extra_info, created_at, updated_at
    </sql>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.kuaikan.role.game.api.bean.SilverCoinDropRecord"
            useGeneratedKeys="true">
        insert into ${tableName}
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                user_id,
            </if>
            <if test="bid != null">
                bid,
            </if>
            <if test="num != null">
                num,
            </if>
            <if test="source != null">
                `source`,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="extraInfo != null">
                extra_info,
            </if>
            <if test="createdAt != null">
                created_at,
            </if>
            <if test="updatedAt != null">
                updated_at,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                #{userId},
            </if>
            <if test="bid != null">
                #{bid},
            </if>
            <if test="num != null">
                #{num},
            </if>
            <if test="source != null">
                #{source},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="extraInfo != null">
                #{extraInfo,typeHandler=com.kuaikan.common.db.mybatis.JsonTypeHandler,javaType=com.kuaikan.role.game.api.bean.SilverCoinDropRecord$ExtraInfo},
            </if>
            <if test="createdAt != null">
                #{createdAt},
            </if>
            <if test="updatedAt != null">
                #{updatedAt},
            </if>
        </trim>
    </insert>
    <select id="getUserUnpickedSilverCoinDropRecord" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${tableName}
        where user_id = #{userId} and status = 1
        order by created_at desc
    </select>

    <update id="batchUpdateSilverCoinAssignRecordStatus" parameterType="java.util.List">
        update ${tableName}
        set status = #{status}
        where user_id = #{userId} and bid in
        <foreach collection="bids" item="bid" open="(" separator="," close=")">
            #{bid}
        </foreach>
    </update>

</mapper>