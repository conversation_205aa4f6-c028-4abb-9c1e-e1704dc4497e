<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.kuaikan.role.game.dao.rolegame.UserMapStoryMapper">

    <resultMap id="BaseResultMap" type="com.kuaikan.role.game.api.bean.UserMapStory">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="map_id" property="mapId"/>
        <result column="map_story_id" property="mapStoryId"/>
        <result column="show_red_dot" property="showRedDot"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <!-- 根据ID查询记录 -->
    <select id="queryByUserIdAndMapId" parameterType="int" resultMap="BaseResultMap">
        SELECT id, user_id, map_id, map_story_id, show_red_dot, created_at, updated_at
        FROM ${tableName}
        WHERE user_id = #{userId}
          AND map_id = #{mapId}
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.kuaikan.role.game.api.bean.UserMapStory">
        INSERT INTO ${tableName} (`user_id`, `map_id`, `map_story_id`, `show_red_dot`)
        VALUES (#{userMapStory.userId}, #{userMapStory.mapId}, #{userMapStory.mapStoryId}, #{userMapStory.showRedDot})
    </insert>

    <update id="batchUpdateRedDot">
        update ${tableName}
        set show_red_dot=#{showRedDot}
        where user_id = #{userId}
        and map_story_id in
        <foreach collection="mapStoryIds" item="mapStoryId" open="(" separator="," close=")">
            #{mapStoryId}
        </foreach>
    </update>

</mapper>