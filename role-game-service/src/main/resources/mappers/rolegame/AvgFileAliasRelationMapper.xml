<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kuaikan.role.game.dao.rolegame.AvgFileAliasRelationMapper">
    <resultMap id="BaseResultMap" type="com.kuaikan.role.game.api.bean.AvgFileAliasRelation">
        <!--@mbg.generated-->
        <!--@Table avg_file_alias_relation-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="file_id" jdbcType="INTEGER" property="fileId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, file_id, `name`, `type`, created_at, updated_at
    </sql>

    <!--auto generated on 2025-03-17-->
    <select id="findByNames" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from avg_file_alias_relation
        where `name` in
        <foreach item="item" index="index" collection="names"
                 open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
</mapper>