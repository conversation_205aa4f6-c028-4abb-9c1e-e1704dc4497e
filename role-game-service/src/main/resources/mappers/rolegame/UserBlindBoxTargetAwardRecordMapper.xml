<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kuaikan.role.game.dao.rolegame.UserBlindBoxTargetAwardRecordMapper">
    <resultMap id="BaseResultMap" type="com.kuaikan.role.game.api.bean.UserBlindBoxTargetAwardRecord">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="target_id" property="targetId"/>
        <result column="type" property="type"/>
        <result column="costume_id" property="costumeId"/>
        <result column="costume_level" property="costumeLevel"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , user_id, target_id, type, costume_id, created_at, updated_at
    </sql>

    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="records.id" keyColumn="id">
        insert into ${tableName}
        (user_id, target_id, type, costume_id, costume_level)
        values
        <foreach collection="records" item="record" separator=",">
            (#{record.userId}, #{record.targetId}, #{record.type}, #{record.costumeId}, #{record.costumeLevel})
        </foreach>
    </insert>


    <update id="updateCostumeLevel">
        update ${tableName}
        set costume_level = #{costumeLevel}
        where id = #{id}
    </update>

    <select id="queryByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${tableName}
        where user_id = #{userId}
    </select>

</mapper>