<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kuaikan.role.game.dao.rolegame.BuildingAreaMapper">

    <resultMap id="resultMap" type="com.kuaikan.role.game.api.bean.BuildingArea">
        <id column="id" property="id"/>
        <result column="building_id" property="buildingId"/>
        <result column="name" property="name"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="columns">
        id
        ,
        building_id,
        `name`,
        created_at,
        updated_at
    </sql>

    <select id="queryByBuildingId" resultMap="resultMap">
        select
        <include refid="columns"/>
        from building_area
        where building_id = #{buildingId}
        order by id desc
    </select>

    <select id="queryById" resultMap="resultMap">
        select
        <include refid="columns"/>
        from building_area
        where id = #{id}
    </select>

    <select id="queryAll" resultMap="resultMap">
        select
        <include refid="columns"/>
        from building_area
    </select>

</mapper>

