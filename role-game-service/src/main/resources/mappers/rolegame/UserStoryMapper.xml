<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kuaikan.role.game.dao.rolegame.UserStoryMapper">

    <resultMap id="resultMap" type="com.kuaikan.role.game.api.bean.UserStory">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="story_id" property="storyId"/>
        <result column="scene_id" property="sceneId"/>
        <result column="show_red_dot" property="showRedDot"/>
        <result column="viewed" property="viewed"/>
        <result column="config" property="config" typeHandler="com.kuaikan.common.db.mybatis.JsonTypeHandler"
                javaType="com.kuaikan.role.game.api.bean.UserStory$Config"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="columns">
        id
        ,
        config,
        user_id,
        story_id,
        scene_id,
        show_red_dot,
        viewed,
        created_at,
        updated_at
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="userStory.id">
        insert
        ignore into
        ${tableName}
        (
        user_id,
        story_id,
        scene_id,
        show_red_dot,
        viewed
        )
        values
        (
        #{userStory.userId},
        #{userStory.storyId},
        #{userStory.sceneId},
        #{userStory.showRedDot},
        #{userStory.viewed}
        )
    </insert>

    <insert id="batchInsert">
        insert
        ignore into
        ${tableName}
        (
        user_id,
        story_id,
        scene_id,
        show_red_dot,
        viewed
        )
        values
        <foreach collection="userStoryList" item="userStory" separator=",">
            (
            #{userStory.userId},
            #{userStory.storyId},
            #{userStory.sceneId},
            #{userStory.showRedDot},
            #{userStory.viewed}
            )
        </foreach>
    </insert>

    <select id="queryByUserId" resultMap="resultMap">
        select
        <include refid="columns"/>
        from ${tableName}
        where user_id=#{userId}
    </select>

    <select id="queryByUserIdAndStoryId" resultMap="resultMap">
        select
        <include refid="columns"/>
        from ${tableName}
        where user_id=#{userId} and story_id=#{storyId}
    </select>

    <select id="queryByUserIdAndStoryIds" resultMap="resultMap">
        select
        <include refid="columns"/>
        from ${tableName}
        where user_id=#{userId} and story_id in
        <foreach collection="storyIds" item="storyId" open="(" close=")" separator=",">
            #{storyId}
        </foreach>
    </select>

    <update id="updateConfig">
        update ${tableName}
        set config = #{config,typeHandler=com.kuaikan.common.db.mybatis.JsonTypeHandler,javaType=com.kuaikan.role.game.api.bean.UserStory$Config}
        where user_id = #{userId}
          and story_id = #{storyId}
    </update>

    <update id="updateViewed">
        update ${tableName}
        set viewed=#{viewed}
        where user_id = #{userId}
          and story_id = #{storyId}
    </update>

    <update id="updateRedDot">
        update ${tableName}
        set show_red_dot=#{showRedDot}
        where user_id = #{userId}
          and story_id = #{storyId}
    </update>
</mapper>

