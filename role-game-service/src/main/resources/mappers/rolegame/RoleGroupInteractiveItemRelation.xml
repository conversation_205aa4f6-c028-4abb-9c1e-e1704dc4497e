<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kuaikan.role.game.dao.rolegame.RoleGroupInteractiveItemRelationMapper">

    <resultMap id="resultMap" type="com.kuaikan.role.game.common.bean.RoleGroupInteractiveItemRelation">
        <id column="id" property="id"/>
        <result column="role_group_id" property="roleGroupId"/>
        <result column="item_id" property="itemId"/>
        <result column="order_num" property="orderNum"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="column">
        id
        , role_group_id, item_id, order_num, created_at, updated_at
    </sql>

    <select id="queryAll" parameterType="java.lang.Integer" resultMap="resultMap">
        select
        <include refid="column"/>
        from role_group_interactive_item_relation
    </select>

    <select id="queryByItemId" resultMap="resultMap">
        select
        <include refid="column"/>
        from role_group_interactive_item_relation
        where item_id = #{itemId}
    </select>

    <select id="queryByRoleGroupId" resultMap="resultMap">
        select
        <include refid="column"/>
        from role_group_interactive_item_relation
        where role_group_id = #{roleGroupId}
    </select>
    <select id="queryByItemIds" resultMap="resultMap">
        select
        <include refid="column"/>
        from role_group_interactive_item_relation
        where item_id in
        <foreach collection="itemIds" item="itemId" open="(" separator="," close=")">
            #{itemId}
        </foreach>
    </select>
</mapper>