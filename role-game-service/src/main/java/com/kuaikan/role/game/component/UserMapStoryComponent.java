package com.kuaikan.role.game.component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import cn.hutool.core.lang.WeightRandom;
import com.kuaikan.role.game.common.enums.RedDotEventType;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.kuaikan.role.game.api.bean.MapStory;
import com.kuaikan.role.game.api.bean.Schedule;
import com.kuaikan.role.game.api.bean.ScheduleNumericalConfig;
import com.kuaikan.role.game.api.bean.UserMapStory;
import com.kuaikan.role.game.api.model.CommonScheduleStoryLibModel;
import com.kuaikan.role.game.api.model.map.UnlockMapStoryModel;
import com.kuaikan.role.game.repository.MapStoryRepository;
import com.kuaikan.role.game.repository.ScheduleRepository;
import com.kuaikan.role.game.repository.UserMapStoryRepository;

/**
 *<AUTHOR>
 *@date 2025/6/5
 */
@Component
@Slf4j
public class UserMapStoryComponent {

    @Resource
    private MapStoryRepository mapStoryRepository;
    @Resource
    private UserMapStoryRepository userMapStoryRepository;
    @Resource
    private ScheduleRepository scheduleRepository;
    @Resource
    private RedDotComponent redDotComponent;


    public List<CommonScheduleStoryLibModel> getCommonScheduleStoryLibList(int mapId) {
        List<MapStory> mapStories = mapStoryRepository.queryListByMapId(mapId);
        List<Schedule> schedules = scheduleRepository.queryAll();
        List<CommonScheduleStoryLibModel> commonScheduleStoryLibModels = new ArrayList<>();
        for (MapStory mapStory : mapStories) {
            for (Schedule schedule : schedules) {
                Schedule.Config config = schedule.getConfig();
                if (config != null && config.getNumericalConfig() != null) {
                    ScheduleNumericalConfig numericalConfig = config.getNumericalConfig();
                    if (numericalConfig.getRepoId() != mapStory.getLibraryId()) {
                        continue;
                    }
                    commonScheduleStoryLibModels.add(new CommonScheduleStoryLibModel().setScheduleId(schedule.getId())
                        .setStoryLibId(mapStory.getLibraryId()).setScheduleName(schedule.getName()));
                }
            }
        }
        return commonScheduleStoryLibModels;
    }

    /**
     * 解锁地图剧情故事
     *
     * @param userId 用户id
     * @param mapId 地图id
     * @param libId 剧情库id
     * @return
     */
    public UnlockMapStoryModel unlockMapStoryModelByLibId(int userId, int mapId, int libId) {
        // 查询地图剧情配置
        List<MapStory> mapStories = mapStoryRepository.queryListByMapId(mapId);
        mapStories = mapStories.stream().filter(mapStory -> mapStory.getLibraryId() == libId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mapStories)) {
            log.warn("No map story found for userId: {}, mapId: {}, libId: {}", userId, mapId, libId);
            return null;
        }

        // 根据概率随机选择一个未解锁的剧情
        MapStory randomResult = getRandomResult(mapStories);
        // 解锁剧情
        UnlockMapStoryModel model = buildByMapStory(randomResult);

        // 查询map下用户解锁剧情列表
        List<UserMapStory> userMapStories = userMapStoryRepository.queryListByUserIdAndMapId(userId, mapId);
        Set<Integer> unlockedStoryIds = userMapStories.stream().map(UserMapStory::getMapStoryId).collect(Collectors.toSet());
        // 已解锁的剧情，直接返回
        if (unlockedStoryIds.contains(randomResult.getId())) {
            log.debug("unlockMapStoryModel already unlocked, userId:{}, mapId:{}, mapStoryId:{}", userId, mapId, randomResult.getId());
            return model;
        }

        int ret = userMapStoryRepository.insert(new UserMapStory().setUserId(userId).setMapId(mapId).setMapStoryId(randomResult.getId()).setShowRedDot(true));
        if (ret > 0) {
            log.info("unlockMapStoryModel success, userId:{}, mapStoryId:{}", userId, randomResult.getId());
            // map维度增加用户获得剧情的红点
            redDotComponent.sendUserAcceptStoryPoint(RedDotEventType.MAP_HOME_USER_ACCEPT_STORY, userId, mapId, null);
            // tag维度增加用户获得剧情的红点
            redDotComponent.sendUserAcceptStoryPoint(RedDotEventType.MAP_USER_ACCEPT_STORY_TAG, userId, mapId, randomResult.getTag());
            return model;
        }
        return null;
    }

    public void clearRedDotByAvgIds(int userId, int mapId, List<Integer> avgChapterIds) {
        if (CollectionUtils.isEmpty(avgChapterIds)) {
            log.warn("clearRedDot avgChapterId is null for userId: {}, mapId: {}", userId, mapId);
            return;
        }
        // 根据avgChapterIds获取mapStoryIds
        List<MapStory> mapStories = mapStoryRepository.queryListByMapId(mapId);
        List<Integer> mapStoryIds = mapStories.stream()
                .filter(mapStory -> avgChapterIds.contains(mapStory.getAvgChapterId()))
                .map(MapStory::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mapStoryIds)) {
            log.warn("clearRedDot no map stories found for userId: {}, mapId: {}, avgChapterIds: {}", userId, mapId, avgChapterIds);
            return;
        }
        // 清除用户地图剧情的红点
        int ret = userMapStoryRepository.batchUpdateRedDot(userId, mapId, mapStoryIds, false);
        if (ret > 0) {
            log.info("clearRedDot success, userId:{}, mapId:{}, avgChapterIds:{}, mapStoryIds:{}", userId, mapId, avgChapterIds, mapStoryIds);
        } else {
            log.warn("clearRedDot failed, userId:{}, mapId:{}, avgChapterIds:{}, mapStoryIds:{}", userId, mapId, avgChapterIds, mapStoryIds);
        }
    }

    private MapStory getRandomResult(List<MapStory> mapStories) {
        WeightRandom weightRandom = new WeightRandom();
        mapStories.forEach(mapStory -> {
            weightRandom.add(mapStory, mapStory.getWeight());
        });
        return (MapStory) weightRandom.next();
    }

    private UnlockMapStoryModel buildByMapStory(MapStory mapStory) {
        return new UnlockMapStoryModel().setMapId(mapStory.getMapId())
                .setLibraryId(mapStory.getLibraryId())
                .setTag(mapStory.getTag())
                .setAvgChapterId(mapStory.getAvgChapterId());
    }

}
