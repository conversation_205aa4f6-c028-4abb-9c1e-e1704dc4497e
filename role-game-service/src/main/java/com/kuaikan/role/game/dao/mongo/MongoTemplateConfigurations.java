package com.kuaikan.role.game.dao.mongo;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.mongodb.MongoClientOptions;
import com.mongodb.ReadPreference;
import com.mongodb.WriteConcern;

import com.kuaikan.common.db.DataSourceUtils;
import com.kuaikan.common.db.DynamicMongoTemplateBeanFactory;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/4/15
 */
@Configuration
public class MongoTemplateConfigurations {

    @Bean
    public static DynamicMongoTemplateBeanFactory roleGameMongoTemplateBeanFactory() {
        return DataSourceUtils.createMongoTemplateFactory("mongoTemplate", "mongo", "role_game");
    }

    @Bean
    public static DynamicMongoTemplateBeanFactory roleGameMongoTemplateBeanFactoryForPrimaryPreferred() {
        return DataSourceUtils.createMongoTemplateFactory("mongoTemplatePrimary", "mongo", "role_game");
    }

    static MongoClientOptions.Builder getMongoClientOptionsBuilder() {
        return MongoClientOptions.builder()
                .writeConcern(WriteConcern.ACKNOWLEDGED)
                .connectTimeout(60000)
                .socketTimeout(60000)
                .maxWaitTime(2000)
                .connectionsPerHost(100)
                .minConnectionsPerHost(30)
                .threadsAllowedToBlockForConnectionMultiplier(30)
                .maxConnectionIdleTime(600000)
                .readPreference(ReadPreference.secondaryPreferred());
    }

    static MongoClientOptions.Builder getMongoClientOptionsBuilderForPrimaryPreferred() {
        return MongoClientOptions.builder()
                .writeConcern(WriteConcern.ACKNOWLEDGED)
                .connectTimeout(60000)
                .socketTimeout(60000)
                .maxWaitTime(2000)
                .connectionsPerHost(100)
                .minConnectionsPerHost(30)
                .threadsAllowedToBlockForConnectionMultiplier(30)
                .maxConnectionIdleTime(600000)
                .readPreference(ReadPreference.primaryPreferred());
    }
}
