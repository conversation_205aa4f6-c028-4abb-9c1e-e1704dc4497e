package com.kuaikan.role.game.handler.action;

import java.util.Date;
import java.util.List;
import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;

import com.kuaikan.role.game.api.enums.FlowType;
import com.kuaikan.role.game.api.enums.UserActionType;
import com.kuaikan.role.game.api.rpc.param.UserActionQueryParam;
import com.kuaikan.role.game.repository.UserFoodFlowRepository;

/**
 * <AUTHOR>
 * @date 2024/9/7
 */
@Component
public class FeedRole2CountQueryHandler extends BaseActionCountQueryHandler {

    @Resource
    private UserFoodFlowRepository userFoodFlowRepository;

    @Override
    public int queryUserActionCount(UserActionQueryParam param) {
        return userFoodFlowRepository.countByUidAndTimeRangeAndType(param.getUserId(), new Date(param.getStartTime()), new Date(param.getEndTime()),
                FlowType.SUBTRACT.getCode());
    }

    @Override
    public List<UserActionType> getActionTypes() {
        return Lists.newArrayList(UserActionType.FEED_ROLE_2);
    }

}
