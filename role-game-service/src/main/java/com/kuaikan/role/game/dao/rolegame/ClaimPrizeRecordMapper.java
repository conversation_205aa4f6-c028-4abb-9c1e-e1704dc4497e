package com.kuaikan.role.game.dao.rolegame;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.kuaikan.role.game.api.bean.ClaimPrizeRecord;

/**
 * <AUTHOR>
 * @version 2024-03-27
 */

public interface ClaimPrizeRecordMapper {

    int insert(@Param("tableName") String tableName, @Param("record") ClaimPrizeRecord record);

    int insertBatch(@Param("tableName") String tableName, @Param("records") List<ClaimPrizeRecord> records);

    List<ClaimPrizeRecord> selectByUserId(@Param("tableName") String tableName, @Param("userId") int userId);

    ClaimPrizeRecord selectByBid(@Param("tableName") String tableName, @Param("bid") long bid);

    int updateExtraInfo(@Param("tableName") String tableName, @Param("id") int id, @Param("extraInfo") String extraInfo);

    int updateExtraInfoAndStatus(@Param("tableName") String tableName, @Param("id") int id, @Param("extraInfo") String extraInfo, @Param("status") int status);

    int updateStatus(@Param("tableName") String tableName, @Param("id") int id, @Param("status") int status);
}
