package com.kuaikan.role.game.handler.action;

import java.util.Date;
import java.util.List;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;

import com.kuaikan.common.util.JsonUtils;
import com.kuaikan.role.game.api.bean.RewardOrder;
import com.kuaikan.role.game.api.bean.RewardOrderBlindBoxExtraInfo;
import com.kuaikan.role.game.api.enums.RewardOrderStatus;
import com.kuaikan.role.game.api.enums.RewardOrderType;
import com.kuaikan.role.game.api.enums.UserActionType;
import com.kuaikan.role.game.api.rpc.param.UserActionQueryParam;
import com.kuaikan.role.game.repository.RewardOrderRepository;

/**
 * <AUTHOR>
 * @date 2024/9/7
 */
@Slf4j
@Component
public class CostumeBlindBoxCountQueryHandler extends BaseActionCountQueryHandler {

    private static final ImmutableList<Integer> TYPES = ImmutableList.of(RewardOrderType.COSTUME_BLIND_BOX.getCode(),
            RewardOrderType.BLIND_BOX_ACTIVITY.getCode());

    @Resource
    private RewardOrderRepository rewardOrderRepository;

    @Override
    public int queryUserActionCount(UserActionQueryParam param) {
        final List<RewardOrder> rewardOrders = rewardOrderRepository.selectByUidAndTimeRangeAndTypesAndStatus(param.getUserId(), new Date(param.getStartTime()),
                new Date(param.getEndTime()), TYPES, RewardOrderStatus.FINISHED.getCode());
        if (CollectionUtils.isEmpty(rewardOrders)) {
            log.info("queryUserActionCount rewardOrders is empty, userId:{}, param:{}", param.getUserId(), param);
            return 0;
        }
        int count = 0;
        for (RewardOrder rewardOrder : rewardOrders) {
            final String orderExtraInfo = rewardOrder.getExtraInfo();
            if (orderExtraInfo == null) {
                continue;
            }
            final RewardOrderBlindBoxExtraInfo extraInfo = JsonUtils.findObject(orderExtraInfo, RewardOrderBlindBoxExtraInfo.class);
            if (extraInfo == null) {
                continue;
            }
            final RewardOrderBlindBoxExtraInfo.GearConfig gearConfig = extraInfo.getCostumeBlindBoxConfig().getGearConfig();
            if (gearConfig == null) {
                continue;
            }
            final int lotteryNum = gearConfig.getLotteryNum();
            count += lotteryNum;
        }
        log.debug("queryUserActionCount count:{}, userId:{}, param:{}", count, param.getUserId(), param);
        return count;
    }

    @Override
    public List<UserActionType> getActionTypes() {
        return Lists.newArrayList(UserActionType.DRAW_COSTUME);
    }

}
