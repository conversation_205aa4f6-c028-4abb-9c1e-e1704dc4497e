package com.kuaikan.role.game.dao.rolegame;

import org.apache.ibatis.annotations.Param;

import com.kuaikan.role.game.api.bean.UserMap;

public interface UserMapMapper {

    UserMap queryByUserIdAndMapId(@Param("userId") int userId, @Param("mapId") int mapId, @Param("tableName") String tableName);

    int insert(@Param("userMap") UserMap userMap, @Param("tableName") String tableName);

    int addEnergy(@Param("userId") int userId, @Param("mapId") int mapId, @Param("energy") int energy, @Param("beforeEnergy") int beforeEnergy, @Param("tableName") String tableName);

    int recoverEnergy(@Param("userId") int userId, @Param("mapId") int mapId, @Param("energy") int energy, @Param("recoverDate") int recoverDate, @Param("tableName") String tableName);
}
