package com.kuaikan.role.game.dao.rolegame;

import java.util.Collection;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.kuaikan.role.game.api.bean.UserStuffFlow;

/**
 * <AUTHOR>
 * @version 2024-04-23
 */
public interface UserStuffFlowMapper {

    int insert(@Param("tableName") String tableName, @Param("record") UserStuffFlow userStuffFlow);

    int insertBatch(@Param("tableName") String tableName, @Param("records") Collection<UserStuffFlow> userStuffFlows);

    List<UserStuffFlow> selectByOrderId(@Param("tableName") String tableName, @Param("orderId") String orderId);
}
