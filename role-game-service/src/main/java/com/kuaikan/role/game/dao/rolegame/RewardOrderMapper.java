package com.kuaikan.role.game.dao.rolegame;

import java.util.Collection;
import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.kuaikan.role.game.api.bean.RewardOrder;

/**
 * <AUTHOR>
 * @version 2024-05-07
 */
public interface RewardOrderMapper {

    int insert(@Param("tableName") String tableName, @Param("record") RewardOrder record);

    int updateExtraInfoAndStatus(@Param("tableName") String tableName, @Param("orderId") long orderId, @Param("extraInfo") String extraInfo,
                                 @Param("status") int status);

    int updateExtraInfoAndAnalysisInfoAndStatus(@Param("tableName") String tableName, @Param("orderId") long orderId, @Param("extraInfo") String extraInfo,
                                                @Param("analysisInfo") String analysisInfo, @Param("status") int status);

    RewardOrder selectByOrderId(@Param("tableName") String tableName, @Param("orderId") long orderId);

    List<RewardOrder> selectByBizId(@Param("tableName") String tableName, @Param("bizId") String bizId, @Param("statuses") Collection<Integer> statuses,
                                    @Param("userId") int userId, @Param("type") int type);

    List<RewardOrder> selectRecent(@Param("tableName") String tableName, @Param("userId") int userId, @Param("limit") int limit, @Param("type") int type,
                                   @Param("status") int status, @Param("bizIds") Collection<String> bizIds, @Param("activityId") String activityId);

    int countByUidAndTimeRangeAndTypesStatus(@Param("tableName") String tableName, @Param("userId") int userId, @Param("startTime") Date startTime,
                                             @Param("endTime") Date endTime, @Param("types") Collection<Integer> types, @Param("status") int status);

    List<RewardOrder> selectByUidAndTimeRangeAndTypesAndStatus(@Param("tableName") String tableName, @Param("userId") int userId,
                                                               @Param("startTime") Date startTime, @Param("endTime") Date endTime,
                                                               @Param("types") Collection<Integer> types, @Param("status") int status);

    List<RewardOrder> queryUserBlindBoxActivityOrder(String tableName, int userId);

    int countFinishByActivityIdAndUserId(@Param("tableName") String tableName, @Param("userId") int userId, @Param("activityId") int activityId);

    int countFreeByActivityIdAndUserId(@Param("tableName") String tableName, @Param("userId") int userId, @Param("activityId") int activityId);

    List<RewardOrder> queryUserBlindBoxActivityOrder(@Param("tableName") String tableName, @Param("userId") int userId, @Param("activityId") int activityId);

    int countBlindBoxOrderByUserId(@Param("tableName") String tableName, @Param("userId") int userId);

    List<RewardOrder> queryBlindBoxOrderByUserId(@Param("tableName") String tableName, @Param("userId") int userId);
}
