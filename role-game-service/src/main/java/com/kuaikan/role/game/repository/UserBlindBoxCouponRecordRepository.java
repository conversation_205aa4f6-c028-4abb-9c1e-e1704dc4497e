package com.kuaikan.role.game.repository;

import java.util.List;
import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import com.kuaikan.role.game.api.bean.UserBlindBoxCouponRecord;
import com.kuaikan.role.game.api.enums.UserTableEnum;
import com.kuaikan.role.game.dao.rolegame.UserBlindBoxCouponRecordMapper;
import com.kuaikan.role.game.uitl.TablePartitionUtil;

/**
 * <AUTHOR>
 * @date 2024/12/26 15:35
 */

@Repository
public class UserBlindBoxCouponRecordRepository {

    @Resource
    private UserBlindBoxCouponRecordMapper userBlindBoxCouponRecordMapper;

    public void insertBatch(String tableName, List<UserBlindBoxCouponRecord> records) {
        userBlindBoxCouponRecordMapper.insertBatch(tableName, records);
    }

    // query record by userId
    public List<UserBlindBoxCouponRecord> queryByUserId(Integer userId) {
        String tableName = getTableName(userId);
        return userBlindBoxCouponRecordMapper.queryByUserId(tableName, userId);
    }

    // query record by bid
    public UserBlindBoxCouponRecord queryByBid(Integer userId, Long bid) {
        String tableName = getTableName(userId);
        return userBlindBoxCouponRecordMapper.queryByBid(tableName, bid);
    }

    // update record to used by bid
    public void updateUsedByBid(Integer userId, Boolean used, Long bid) {
        String tableName = getTableName(userId);
        userBlindBoxCouponRecordMapper.updateUsedByBid(tableName, used, bid);
    }

    // get table name
    private String getTableName(int userId) {
        return TablePartitionUtil.getTableName(UserTableEnum.USER_BLIND_BOX_COUPON_RECORD, userId);
    }

}
