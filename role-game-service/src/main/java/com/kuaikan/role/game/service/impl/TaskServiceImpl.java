package com.kuaikan.role.game.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.lang.NonNull;

import com.kuaikan.common.bean.RequestInfo;
import com.kuaikan.common.model.RpcResult;
import com.kuaikan.common.tools.serialize.JsonUtils;
import com.kuaikan.game.common.constants.PrizeStatus;
import com.kuaikan.game.common.constants.PrizeType;
import com.kuaikan.game.gamecard.base.dto.PrizeDto;
import com.kuaikan.game.gamecard.base.model.Prize;
import com.kuaikan.game.gamecard.dubbo.dto.ClientInfoDTO;
import com.kuaikan.game.gamecard.prize.def.service.GameCardPrizeService;
import com.kuaikan.game.gamecard.questcontainer.def.dto.QuestContainerDto;
import com.kuaikan.game.gamecard.questcontainer.def.dto.QuestDto;
import com.kuaikan.game.gamecard.questcontainer.def.dto.QuestPrizeContextBatchDto;
import com.kuaikan.game.gamecard.questcontainer.def.dto.QuestPrizeDto;
import com.kuaikan.game.gamecard.questcontainer.def.dto.QuestStagePrizeDto;
import com.kuaikan.game.gamecard.questcontainer.def.dto.RewardObtainResultDto;
import com.kuaikan.game.gamecard.questcontainer.def.enums.QuestTargetConditionEnum;
import com.kuaikan.game.gamecard.questcontainer.def.enums.QuestTargetFilterEnum;
import com.kuaikan.game.gamecard.questcontainer.def.model.json.TargetFilterJson;
import com.kuaikan.game.gamecard.questcontainer.def.service.QuestContainerService;
import com.kuaikan.role.game.api.bean.CostumeBlindBoxActivity;
import com.kuaikan.role.game.api.bean.KeyValueConfig;
import com.kuaikan.role.game.api.bean.Role;
import com.kuaikan.role.game.api.bean.RoleAdoptCouponConfig;
import com.kuaikan.role.game.api.bean.RoleCostumeRelation;
import com.kuaikan.role.game.api.bean.RoleGroup;
import com.kuaikan.role.game.api.bean.TaskModuleConfig;
import com.kuaikan.role.game.api.constant.BattleActivityConstants;
import com.kuaikan.role.game.api.model.RoleGroupModel;
import com.kuaikan.role.game.api.model.RoleModel;
import com.kuaikan.role.game.api.rpc.param.TaskListParam;
import com.kuaikan.role.game.api.rpc.param.TaskRewardBatchParam;
import com.kuaikan.role.game.api.rpc.result.TaskModuleConfigModel;
import com.kuaikan.role.game.api.service.TaskService;
import com.kuaikan.role.game.api.util.GsonUtils;
import com.kuaikan.role.game.common.bean.RoleGroupAdoptCouponRelation;
import com.kuaikan.role.game.common.bean.RoleGroupBlindBoxCouponRelation;
import com.kuaikan.role.game.common.bean.RoleGroupInteractiveItemRelation;
import com.kuaikan.role.game.common.bean.RoleGroupRelation;
import com.kuaikan.role.game.common.constant.KeyValueConfigKeys;
import com.kuaikan.role.game.component.RedDotComponent;
import com.kuaikan.role.game.repository.CostumeBlindBoxActivityRepository;
import com.kuaikan.role.game.repository.KeyValueConfigRepository;
import com.kuaikan.role.game.repository.RoleAdoptCouponConfigRepository;
import com.kuaikan.role.game.repository.RoleCostumeRelationRepository;
import com.kuaikan.role.game.repository.RoleGroupAdoptCouponRelationRepository;
import com.kuaikan.role.game.repository.RoleGroupBlindBoxCouponRelationRepository;
import com.kuaikan.role.game.repository.RoleGroupInteractiveItemRelationRepository;
import com.kuaikan.role.game.repository.RoleGroupRelationRepository;
import com.kuaikan.role.game.repository.RoleGroupRepository;
import com.kuaikan.role.game.repository.RoleRepository;

/**
 * <AUTHOR>
 * @date 2024/2/28
 */
@DubboService
@Slf4j
public class TaskServiceImpl implements TaskService {

    @Resource
    private KeyValueConfigRepository keyValueConfigRepository;

    @Resource
    private QuestContainerService questContainerService;

    @Resource
    private RoleRepository roleRepository;

    @Resource
    private RoleGroupRelationRepository roleGroupRelationRepository;
    @Resource
    private RoleCostumeRelationRepository roleCostumeRelationRepository;
    @Resource
    private CostumeBlindBoxActivityRepository costumeBlindBoxActivityRepository;
    @Resource
    private RoleGroupInteractiveItemRelationRepository roleGroupInteractiveItemRelationRepository;
    @Resource
    private RoleGroupRepository roleGroupRepository;
    @Resource
    private RoleGroupBlindBoxCouponRelationRepository roleGroupBlindBoxCouponRelationRepository;
    @Resource
    private RoleGroupAdoptCouponRelationRepository roleGroupAdoptCouponRelationRepository;
    @Resource
    private RoleAdoptCouponConfigRepository roleAdoptCouponConfigRepository;
    @Resource
    private GameCardPrizeService gameCardPrizeService;
    @Resource
    private RedDotComponent redDotComponent;

    //todo 银币
    public static final List<Integer> USE_ASSETS = Arrays.asList(PrizeType.KKB, PrizeType.SCORE, PrizeType.CARD_BATTLE_POINT, PrizeType.ROLE_PLAY_GEM);

    //todo 阅读折扣券
    public static final List<Integer> USE_DISCOUNT_COUPON = Arrays.asList(PrizeType.READING_VOUCHER, PrizeType.KB_VOUCHER, PrizeType.KK_FULL_COUPON);

    private static Set<Integer> BATTLE_QUEST_TYPE = new HashSet<Integer>() {{
        add(QuestTargetConditionEnum.ROLE_GAME_CARD_LEVEL_UP_COUNT.getCode());
        add(QuestTargetConditionEnum.ROLE_GAME_EXPLORE_COUNT.getCode());
        add(QuestTargetConditionEnum.ROLE_GAME_CARD_BATTLE_COUNT.getCode());
        add(QuestTargetConditionEnum.ROLE_GAME_PRACTICE_PARTICIPATE_COUNT.getCode());
        add(QuestTargetConditionEnum.ROLE_GAME_PRACTICE_COMPLETE_COUNT.getCode());
        add(QuestTargetConditionEnum.ROLE_GAME_CARD_BREAK_UP_COUNT.getCode());
        add(QuestTargetConditionEnum.ROLE_GAME_CARD_BATTLE_DUNGEON_PROGRESS.getCode());
        add(QuestTargetConditionEnum.ROLE_GAME_BATTLE_POINT_USED_NUM.getCode());
        add(QuestTargetConditionEnum.ROLE_GAME_BUY_BATTLE_POINT_COUNT.getCode());
    }};

    @Override
    public RpcResult<TaskModuleConfigModel> taskList(TaskListParam taskListParam) {
        KeyValueConfig keyValueConfig = keyValueConfigRepository.queryByKey(KeyValueConfigKeys.TASK_MODULE_CONFIG);
        if (keyValueConfig == null) {
            return RpcResult.success(null);
        }
        String value = keyValueConfig.getValue();
        TaskModuleConfig taskModuleConfig = JsonUtils.fromJson(value, TaskModuleConfig.class);
        if (taskModuleConfig == null) {
            return RpcResult.success(null);
        }
        List<Long> questContainerIds = Optional.ofNullable(taskModuleConfig.getTaskConfigList())
                .orElse(new ArrayList<>())
                .stream()
                .map(TaskModuleConfig.TaskConfig::getTaskId)
                .collect(Collectors.toList());
        List<QuestContainerDto> userQuestContainerDtoBatch = questContainerService.getUserQuestContainerDtoBatch(taskListParam.getClientInfoDTO(),
                questContainerIds);

        Map<Long, QuestContainerDto> questContainerDtoMap = userQuestContainerDtoBatch.stream()
                .filter(item -> item.getStartTime() < System.currentTimeMillis() && item.getEndTime() > System.currentTimeMillis())
                .filter(item -> CollectionUtils.size(item.getQuestDtos()) > 0)
                .collect(Collectors.toMap(QuestContainerDto::getId, item -> item, (a, b) -> a));

        TaskModuleConfigModel taskModuleConfigModel = new TaskModuleConfigModel();
        List<TaskModuleConfig.TaskConfig> taskConfigList = taskModuleConfig.getTaskConfigList();
        List<TaskModuleConfigModel.TaskConfigModel> taskConfigModels = new ArrayList<>();
        Map<Long, TargetFilterIds> questRolesMap = new HashMap<>();
        List<Long> userNewTaskIdList = redDotComponent.userNewTaskIdList((int) taskListParam.getClientInfoDTO().getUserId());
        for (TaskModuleConfig.TaskConfig taskConfig : taskConfigList) {
            long taskId = taskConfig.getTaskId();
            QuestContainerDto questContainerDto = questContainerDtoMap.get(taskId);
            if (questContainerDto == null) {
                continue;
            }
            List<QuestDto> questDtos = questContainerDto.getQuestDtos();
            List<TaskModuleConfigModel.QuestDtoInfo> questDtoInfos = questDtos.stream()
                    .map(item -> new TaskModuleConfigModel.QuestDtoInfo().setQuestDto(item))
                    .collect(Collectors.toList());
            for (QuestDto questDto : questDtos) {
                if (questDto.getType() == QuestTargetConditionEnum.ROLE_GAME_ADOPT_ROLE_COUNT.getCode()
                        || questDto.getType() == QuestTargetConditionEnum.ROLE_GAME_COLLECT_COSTUME_COUNT.getCode()) {
                    TargetFilterIds filterIds = getTFilterIds(questDto, QuestTargetFilterEnum.ROLE_ID.getCode());
                    if (filterIds != null) {
                        questRolesMap.put(questDto.getQuestId(), filterIds);
                    }
                }
                if (questDto.getType() == QuestTargetConditionEnum.ROLE_GAME_DRAW_COSTUME_BOX_COUNT.getCode()
                        || questDto.getType() == QuestTargetConditionEnum.ROLE_GAME_ADOPT_ROLE_COUNT.getCode()) {
                    TargetFilterIds filterIds = getTFilterIds(questDto, QuestTargetFilterEnum.ROLE_GROUP_ID.getCode());
                    if (filterIds != null) {
                        questRolesMap.put(questDto.getQuestId(), filterIds);
                    }
                }
                if (questDto.getType() == QuestTargetConditionEnum.ROLE_GAME_DRAW_COSTUME_BOX_COUNT.getCode()) {
                    TargetFilterIds filterIds = getTFilterIds(questDto, QuestTargetFilterEnum.COSTUME_BOX_ID.getCode());
                    if (filterIds != null) {
                        questRolesMap.put(questDto.getQuestId(), filterIds);
                    }
                }
                if (questDto.getType() == QuestTargetConditionEnum.ROLE_GAME_COLLECT_COSTUME_COUNT.getCode()) {
                    TargetFilterIds filterIds = getTFilterIds(questDto, QuestTargetFilterEnum.COSTUME_ID.getCode());
                    if (filterIds != null) {
                        questRolesMap.put(questDto.getQuestId(), filterIds);
                    }
                }
                if (questDto.getType() == QuestTargetConditionEnum.ROLE_GAME_OBTAIN_INTERACT_ITEM_COUNT.getCode()) {
                    TargetFilterIds filterIds = getTFilterIds(questDto, QuestTargetFilterEnum.ITEM_ID.getCode());
                    if (filterIds != null) {
                        questRolesMap.put(questDto.getQuestId(), filterIds);
                    }
                }
            }
            TaskModuleConfigModel.TaskConfigModel taskConfigModel = new TaskModuleConfigModel.TaskConfigModel();
            taskConfigModel.setTaskId(taskId)
                    .setModuleName(taskConfig.getModuleName())
                    .setBannerInfo(TaskModuleConfigModel.valueOf(taskConfig.getBannerInfo()))
                    .setQuestInfos(questDtoInfos)
                    .setShowRedDot(userNewTaskIdList.contains(taskId));
            taskConfigModels.add(taskConfigModel);
        }

        Map<Integer, Set<Integer>> type2FilterIds = questRolesMap.values()
                .stream()
                .collect(Collectors.toMap(TargetFilterIds::getType, TargetFilterIds::getIds, (existingIds, newIds) -> {
                    Set<Integer> combinedIds = new HashSet<>(existingIds);
                    combinedIds.addAll(newIds);
                    return combinedIds;
                }));
        //装扮id过滤
        Set<Integer> costumeIds = type2FilterIds.get(QuestTargetFilterEnum.COSTUME_ID.getCode());
        Map<Integer, List<RoleCostumeRelation>> costumeIds2RelationMap = roleCostumeRelationRepository.queryByCostumeIdsFromCache(costumeIds);
        Set<Integer> costumeRoleIds = costumeIds2RelationMap.values()
                .stream()
                .flatMap(List::stream)
                .map(RoleCostumeRelation::getRoleId)
                .collect(Collectors.toSet());

        //盲盒活动过滤
        Set<Integer> costumeBoxActivityIds = type2FilterIds.get(QuestTargetFilterEnum.COSTUME_BOX_ID.getCode());
        Map<Integer, CostumeBlindBoxActivity> costumeBoxActivityMap = costumeBlindBoxActivityRepository.queryByActivityIdFromCache(costumeBoxActivityIds);
        Set<Integer> costumeBoxRoleGroupIds = costumeBoxActivityMap.values().stream().map(CostumeBlindBoxActivity::getRoleGroupId).collect(Collectors.toSet());

        //互动道具
        Set<Integer> itemIds = type2FilterIds.get(QuestTargetFilterEnum.ITEM_ID.getCode());
        List<RoleGroupInteractiveItemRelation> roleGroupInteractiveItemRelations = roleGroupInteractiveItemRelationRepository.queryByItemIds(itemIds);
        Set<Integer> itemRoleGroupIds = roleGroupInteractiveItemRelations.stream()
                .map(RoleGroupInteractiveItemRelation::getRoleGroupId)
                .collect(Collectors.toSet());

        //角色组过滤
        Set<Integer> roleGroupIds = type2FilterIds.getOrDefault(QuestTargetFilterEnum.ROLE_GROUP_ID.getCode(), new HashSet<>());
        Set<Integer> allRoleGroupIds = new HashSet<>(roleGroupIds);
        allRoleGroupIds.addAll(costumeBoxRoleGroupIds);
        allRoleGroupIds.addAll(itemRoleGroupIds);

        Map<Integer, RoleGroup> roleGroupMap = roleGroupRepository.queryAll().stream().collect(Collectors.toMap(RoleGroup::getId, Function.identity()));
        Map<Integer, List<RoleGroupRelation>> roleGroup2RoleGroupRelationMap = roleGroupRelationRepository.queryByGroupIdsFromCache(allRoleGroupIds);
        Set<Integer> groupRoleIds = roleGroup2RoleGroupRelationMap.values()
                .stream()
                .flatMap(List::stream)
                .map(RoleGroupRelation::getRoleId)
                .collect(Collectors.toSet());

        Set<Integer> roleIds = type2FilterIds.getOrDefault(QuestTargetFilterEnum.ROLE_ID.getCode(), new HashSet<>());
        Set<Integer> allRoleIds = new HashSet<>(roleIds);
        allRoleIds.addAll(groupRoleIds);
        allRoleIds.addAll(costumeRoleIds);

        Map<Integer, RoleGroupRelation> role2RoleGroupRelationMap = roleGroupRelationRepository.queryByRoleIdsFromCache(allRoleIds);
        Map<Integer, List<RoleGroupRelation>> role2roleGroup2RoleGroupRelationMap = role2RoleGroupRelationMap.values()
                .stream()
                .collect(Collectors.groupingBy(RoleGroupRelation::getRoleGroupId));
        roleGroup2RoleGroupRelationMap.putAll(role2roleGroup2RoleGroupRelationMap);

        Map<Integer, Role> roleMap = roleRepository.getRoleListByIds(allRoleIds).stream().collect(Collectors.toMap(Role::getId, item -> item, (a, b) -> a));
        Map<Integer, RoleGroupRelation> roleId2RoleGroupRelationMap = roleGroupRelationRepository.queryByRoleIdsFromCache(allRoleIds);
        for (TaskModuleConfigModel.TaskConfigModel taskConfigModel : taskConfigModels) {
            List<TaskModuleConfigModel.QuestDtoInfo> questInfos = taskConfigModel.getQuestInfos();
            for (TaskModuleConfigModel.QuestDtoInfo questInfo : questInfos) {
                long questId = questInfo.getQuestDto().getQuestId();
                TargetFilterIds filterIds = questRolesMap.get(questId);
                if (filterIds == null) {
                    continue;
                }
                Integer type = filterIds.getType();
                if (type == QuestTargetFilterEnum.ROLE_ID.getCode() || type == QuestTargetFilterEnum.COSTUME_ID.getCode()) {
                    Set<Integer> questRoleIds = new HashSet<>();
                    if (type == QuestTargetFilterEnum.ROLE_ID.getCode()) {
                        questRoleIds = filterIds.getIds();
                    } else if (type == QuestTargetFilterEnum.COSTUME_ID.getCode()) {
                        Set<Integer> questCostumeIds = filterIds.getIds();
                        questRoleIds = costumeIds2RelationMap.values()
                                .stream()
                                .filter(list -> list.stream().anyMatch(relation -> questCostumeIds.contains(relation.getCostumeId())))
                                .flatMap(List::stream)
                                .map(RoleCostumeRelation::getRoleId)
                                .collect(Collectors.toSet());
                    }
                    if (CollectionUtils.isEmpty(questRoleIds)) {
                        continue;
                    }
                    List<RoleGroupRelation> roleGroupRelationList = questRoleIds.stream()
                            .map(roleId2RoleGroupRelationMap::get)
                            .filter(Objects::nonNull)
                            .map(item -> roleGroup2RoleGroupRelationMap.get(item.getRoleGroupId()))
                            .filter(CollectionUtils::isNotEmpty)
                            .flatMap(List::stream)
                            .distinct()
                            .collect(Collectors.toList());
                    List<RoleGroupModel> roleGroupModels = new ArrayList<>();
                    for (RoleGroupRelation roleGroupRelation : roleGroupRelationList) {
                        if (!questRoleIds.contains(roleGroupRelation.getRoleId())) {
                            continue;
                        }
                        Role role = roleMap.get(roleGroupRelation.getRoleId());
                        if (role == null) {
                            continue;
                        }
                        RoleModel roleModel = RoleModel.valueOf(role, null, null, 1).setGroupSort(roleGroupRelation.getOrderNum());
                        Optional<RoleGroupModel> any = roleGroupModels.stream().filter(item -> item.getId() == roleGroupRelation.getRoleGroupId()).findAny();
                        if (any.isPresent()) {
                            List<RoleModel> roleModelList = any.get().getRoleModelList();
                            roleModelList.add(roleModel);
                            roleModelList.sort(Comparator.comparingInt(RoleModel::getGroupSort));
                        } else {
                            List<RoleModel> roleModels = new ArrayList<>();
                            roleModels.add(roleModel);
                            RoleGroup roleGroup = roleGroupMap.get(roleGroupRelation.getRoleGroupId());
                            if (roleGroup != null) {
                                RoleGroupModel roleGroupModel = new RoleGroupModel().setId(roleGroupRelation.getRoleGroupId())
                                        .setRoleModelList(roleModels)
                                        .setOrderNum(roleGroup.getOrderNum());
                                roleGroupModels.add(roleGroupModel);
                            }
                        }
                    }
                    roleGroupModels.sort(Comparator.comparingInt(RoleGroupModel::getOrderNum).thenComparing(RoleGroupModel::getId));
                    questInfo.setRoleGroupModels(roleGroupModels);
                } else if (type == QuestTargetFilterEnum.ROLE_GROUP_ID.getCode()
                        || type == QuestTargetFilterEnum.COSTUME_BOX_ID.getCode()
                        || type == QuestTargetFilterEnum.ITEM_ID.getCode()) {
                    Set<Integer> questRoleGroupIds = new HashSet<>();
                    if (type == QuestTargetFilterEnum.ROLE_GROUP_ID.getCode()) {
                        questRoleGroupIds = filterIds.getIds();
                    } else if (type == QuestTargetFilterEnum.COSTUME_BOX_ID.getCode()) {
                        Set<Integer> questCostumeBoxIds = filterIds.getIds();
                        questRoleGroupIds = costumeBoxActivityMap.values()
                                .stream()
                                .filter(activity -> questCostumeBoxIds.contains(activity.getId()))
                                .map(CostumeBlindBoxActivity::getRoleGroupId)
                                .collect(Collectors.toSet());
                    } else if (type == QuestTargetFilterEnum.ITEM_ID.getCode()) {
                        Set<Integer> questItemIds = filterIds.getIds();
                        questRoleGroupIds = roleGroupInteractiveItemRelations.stream()
                                .filter(item -> questItemIds.contains(item.getItemId()))
                                .map(RoleGroupInteractiveItemRelation::getRoleGroupId)
                                .collect(Collectors.toSet());
                    }
                    if (CollectionUtils.isEmpty(questRoleGroupIds)) {
                        continue;
                    }
                    List<RoleGroupRelation> roleGroupRelationList = questRoleGroupIds.stream()
                            .map(roleGroup2RoleGroupRelationMap::get)
                            .flatMap(List::stream)
                            .distinct()
                            .collect(Collectors.toList());
                    List<RoleGroupModel> roleGroupModels = new ArrayList<>();
                    for (RoleGroupRelation roleGroupRelation : roleGroupRelationList) {
                        Role role = roleMap.get(roleGroupRelation.getRoleId());
                        if (role == null) {
                            continue;
                        }
                        RoleModel roleModel = RoleModel.valueOf(role, null, null, 1).setGroupSort(roleGroupRelation.getOrderNum());
                        Optional<RoleGroupModel> any = roleGroupModels.stream().filter(item -> item.getId() == roleGroupRelation.getRoleGroupId()).findAny();
                        if (any.isPresent()) {
                            List<RoleModel> roleModelList = any.get().getRoleModelList();
                            roleModelList.add(roleModel);
                            roleModelList.sort(Comparator.comparingInt(RoleModel::getGroupSort));
                        } else {
                            List<RoleModel> roleModels = new ArrayList<>();
                            roleModels.add(roleModel);
                            RoleGroup roleGroup = roleGroupMap.get(roleGroupRelation.getRoleGroupId());
                            if (roleGroup != null) {
                                RoleGroupModel roleGroupModel = new RoleGroupModel().setId(roleGroupRelation.getRoleGroupId())
                                        .setRoleModelList(roleModels)
                                        .setOrderNum(roleGroup.getOrderNum());
                                roleGroupModels.add(roleGroupModel);
                            }
                        }
                    }
                    roleGroupModels.sort(Comparator.comparingInt(RoleGroupModel::getOrderNum).thenComparing(RoleGroupModel::getId));
                    questInfo.setRoleGroupModels(roleGroupModels);
                }
            }
        }
        taskModuleConfigModel.setTaskConfigList(taskConfigModels);
        return RpcResult.success(taskModuleConfigModel);
    }

    @Data
    @Accessors(chain = true)
    private static class TargetFilterIds {

        private Integer type;

        private Set<Integer> ids;
    }

    private static TargetFilterIds getTFilterIds(QuestDto questDto, int filterType) {
        Set<String> filterValues = Optional.ofNullable(questDto.getTargetConditions())
                .orElse(new ArrayList<>())
                .stream()
                .filter(targetCondition -> CollectionUtils.size(targetCondition.getFilters()) > 0)
                .flatMap(targetCondition -> targetCondition.getFilters().stream())
                .filter(filter -> filter.getType() == filterType)
                .map(TargetFilterJson::getValue)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(filterValues)) {
            return null;
        }
        Set<Integer> ids = new HashSet<>();
        for (String filterValue : filterValues) {
            boolean number = NumberUtils.isNumber(filterValue);
            if (number) {
                Integer id = NumberUtils.toInt(filterValue);
                ids.add(id);
            } else {
                List<Integer> filterIds = JsonUtils.findList(filterValue, Integer.class);
                if (CollectionUtils.isEmpty(filterIds)) {
                    continue;
                }
                ids.addAll(filterIds);
            }
        }
        return new TargetFilterIds().setType(filterType).setIds(ids);
    }

    @Override
    public RpcResult<RewardObtainResultDto> rewardBatch(TaskRewardBatchParam taskRewardBatchParam) {
        ClientInfoDTO clientInfoDTO = taskRewardBatchParam.getClientInfoDTO();
        List<TaskRewardBatchParam.PrizeBatchParam> questPrizeBatchDtoList = taskRewardBatchParam.getQuestPrizeBatchDtoList();
        List<QuestPrizeContextBatchDto.QuestPrizeBatchDto> questPrizeBatchDtos = new ArrayList<>();
        for (TaskRewardBatchParam.PrizeBatchParam prizeBatchParam : questPrizeBatchDtoList) {
            String questId = prizeBatchParam.getQuestId();
            String prizes = prizeBatchParam.getPrizes();
            if (StringUtils.isBlank(questId) || StringUtils.isBlank(prizes)) {
                log.warn("questId or prizes is empty, questId:{}, prizes:{}", questId, prizes);
                continue;
            }
            String[] prizesStrs = prizes.split(",");
            if (prizesStrs.length == 0) {
                continue;
            }
            List<QuestPrizeDto> prizeList = new ArrayList<>();
            for (String prizesStr : prizesStrs) {
                QuestPrizeDto questPrizeDto = new QuestPrizeDto();
                questPrizeDto.setPrizeId(NumberUtils.toLong(prizesStr));
                prizeList.add(questPrizeDto);
            }

            QuestPrizeContextBatchDto.QuestPrizeBatchDto questPrizeBatchDto = new QuestPrizeContextBatchDto.QuestPrizeBatchDto();
            questPrizeBatchDto.setQuestId(NumberUtils.toLong(questId));
            questPrizeBatchDto.setPrizes(prizeList);
            questPrizeBatchDtos.add(questPrizeBatchDto);
        }
        QuestPrizeContextBatchDto questPrizeContextBatchDto = new QuestPrizeContextBatchDto();
        questPrizeContextBatchDto.setQuestContainerId(taskRewardBatchParam.getQuestContainerId());
        questPrizeContextBatchDto.setQuestPrizeBatchDtoList(questPrizeBatchDtos);
        RewardObtainResultDto rewardObtainResultDto = questContainerService.obtainRewardBatch(clientInfoDTO, questPrizeContextBatchDto);

        List<Integer> roleCouponIds = new ArrayList<>();
        List<Integer> costumeBoxCouponIds = new ArrayList<>();

        List<PrizeDto> prizes = rewardObtainResultDto.getPrizes();

        if (CollectionUtils.isNotEmpty(prizes)) {

            List<Long> prizeIdList = prizes.stream().map(PrizeDto::getPrizeId).collect(Collectors.toList());

            Map<Long, Prize> prizeMap = gameCardPrizeService.getOnlinePrizeMapByPrizeIdList(prizeIdList);

            for (PrizeDto prizeDto : prizes) {
                long prizeId = prizeDto.getPrizeId();
                Prize prize = prizeMap.get(prizeId);
                if (prize == null) {
                    log.warn("prize not exist, prizeId:{}", prizeId);
                    continue;
                }
                int type = prize.getType();
                if (type == PrizeType.ROLE_DISCOUNT_COUPON) {
                    RoleGamePrizeParam roleGamePrizeParam = GsonUtils.tryParseObject(prize.getContent(), RoleGamePrizeParam.class);
                    if (roleGamePrizeParam != null && StringUtils.isNotEmpty(roleGamePrizeParam.getRelatedId())) {
                        roleCouponIds.add(NumberUtils.toInt(roleGamePrizeParam.getRelatedId()));
                    }
                } else if (type == PrizeType.COSTUME_BOX_COUPON) {
                    RoleGamePrizeParam roleGamePrizeParam = GsonUtils.tryParseObject(prize.getContent(), RoleGamePrizeParam.class);
                    if (roleGamePrizeParam != null && StringUtils.isNotEmpty(roleGamePrizeParam.getRelatedId())) {
                        costumeBoxCouponIds.add(NumberUtils.toInt(roleGamePrizeParam.getRelatedId()));
                    }
                }
            }

            Map<Integer, RoleGroupAdoptCouponRelation> roleCouponRelationMap = new HashMap<>();
            Map<Integer, RoleAdoptCouponConfig> roleCouponMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(roleCouponIds)) {
                roleCouponRelationMap = roleGroupAdoptCouponRelationRepository.queryRelationByCouponIds(roleCouponIds)
                        .stream()
                        .collect(Collectors.toMap(RoleGroupAdoptCouponRelation::getCouponId, value -> value));
                roleCouponMap = roleAdoptCouponConfigRepository.selectByIds(roleCouponIds)
                        .stream()
                        .collect(Collectors.toMap(RoleAdoptCouponConfig::getId, value -> value));
            }
            Map<Integer, RoleGroupBlindBoxCouponRelation> blindBoxCouponRelationsMap = new HashMap<>();

            if (CollectionUtils.isNotEmpty(costumeBoxCouponIds)) {
                blindBoxCouponRelationsMap = roleGroupBlindBoxCouponRelationRepository.queryByCouponIds(costumeBoxCouponIds)
                        .stream()
                        .collect(Collectors.toMap(RoleGroupBlindBoxCouponRelation::getCouponId, value -> value));
            }

            Map<String, PrizeDto> unitMap = new HashMap<>();
            for (PrizeDto prizeDto : prizes) {
                Prize prize = prizeMap.get(prizeDto.getPrizeId());
                if (prize != null) {
                    String key = getKey(prize, roleCouponRelationMap, roleCouponMap, blindBoxCouponRelationsMap);
                    if (unitMap.containsKey(key)) {
                        PrizeDto tmp = unitMap.get(key);
                        tmp.setUnit(tmp.getUnit() + prizeDto.getUnit());
                        unitMap.put(key, tmp);
                    } else {
                        unitMap.put(key, prizeDto);
                    }
                }

            }
            rewardObtainResultDto.setPrizes(new ArrayList<>(unitMap.values()));
        }
        return RpcResult.success(rewardObtainResultDto);
    }

    private String getKey(Prize prize, Map<Integer, RoleGroupAdoptCouponRelation> roleCouponRelationMap, Map<Integer, RoleAdoptCouponConfig> roleCouponMap,
                          Map<Integer, RoleGroupBlindBoxCouponRelation> blindBoxCouponRelationsMap) {

        int type = prize.getType();
        if (type == PrizeType.ROLE_DISCOUNT_COUPON) {
            RoleGamePrizeParam roleGamePrizeParam = GsonUtils.tryParseObject(prize.getContent(), RoleGamePrizeParam.class);
            if (roleGamePrizeParam != null && StringUtils.isNotEmpty(roleGamePrizeParam.getRelatedId())) {
                Integer relatedId = NumberUtils.toInt(roleGamePrizeParam.getRelatedId());
                if (roleCouponRelationMap.containsKey(relatedId)) {
                    RoleGroupAdoptCouponRelation roleGroupAdoptCouponRelation = roleCouponRelationMap.get(relatedId);
                    if (roleCouponMap.containsKey(roleGroupAdoptCouponRelation.getCouponId())) {
                        RoleAdoptCouponConfig roleAdoptCouponConfig = roleCouponMap.get(roleGroupAdoptCouponRelation.getCouponId());
                        String discountRate = Optional.ofNullable(roleAdoptCouponConfig.getExtraInfo())
                                .map(RoleAdoptCouponConfig.ExtraInfo::getDiscountRate)
                                .orElse("");
                        return type + roleGroupAdoptCouponRelation.getRoleGroupId() + discountRate;
                    }
                }
            }
            return type + "";

        } else if (type == PrizeType.COSTUME_BOX_COUPON) {
            RoleGamePrizeParam roleGamePrizeParam = GsonUtils.tryParseObject(prize.getContent(), RoleGamePrizeParam.class);
            if (roleGamePrizeParam != null && StringUtils.isNotEmpty(roleGamePrizeParam.getRelatedId())) {
                Integer relatedId = NumberUtils.toInt(roleGamePrizeParam.getRelatedId());
                if (blindBoxCouponRelationsMap.containsKey(relatedId)) {
                    return type + blindBoxCouponRelationsMap.get(relatedId).getRoleGroupId() + "";
                }
            }
            return type + "";
        } else if (USE_ASSETS.contains(type)) {
            return type + "";
        } else if (USE_DISCOUNT_COUPON.contains(type)) {
            return type + "";
        }
        return prize.getType() + prize.getName();
    }

    @Data
    private static class RoleGamePrizeParam {

        // 关联id
        private String relatedId;
    }

    @Override
    public RpcResult<TaskModuleConfigModel.TaskConfigModel> getCardBattleTaskModel(RequestInfo requestInfo, String activityId) {
        // 配置查询
        KeyValueConfig keyValueConfig = keyValueConfigRepository.queryByKey(KeyValueConfigKeys.TASK_MODULE_CONFIG);
        if (keyValueConfig == null) {
            return RpcResult.success(null);
        }
        String value = keyValueConfig.getValue();
        TaskModuleConfig taskModuleConfig = JsonUtils.fromJson(value, TaskModuleConfig.class);
        if (taskModuleConfig == null) {
            log.debug("getCardBattleTaskModel taskModuleConfig is null");
            return RpcResult.success(null);
        }
        // 查询卡片任务
        List<Long> questContainerIds = Optional.ofNullable(taskModuleConfig.getTaskConfigList())
                .orElse(new ArrayList<>())
                .stream()
                .map(TaskModuleConfig.TaskConfig::getTaskId)
                .collect(Collectors.toList());
        ClientInfoDTO clientInfo = convert(requestInfo);
        List<QuestContainerDto> userQuestContainerDtoBatch = questContainerService.getUserQuestContainerDtoBatch(clientInfo, questContainerIds);

        Map<Long, QuestContainerDto> questContainerDtoMap = userQuestContainerDtoBatch.stream()
                .filter(item -> item.getStartTime() < System.currentTimeMillis() && item.getEndTime() > System.currentTimeMillis())
                .filter(item -> CollectionUtils.size(item.getQuestDtos()) > 0)
                .collect(Collectors.toMap(QuestContainerDto::getId, item -> item, (a, b) -> a));

        // 任务列表，排序信息
        List<TaskSimpleInfo> simpleTaskList = new ArrayList<>();
        List<TaskModuleConfig.TaskConfig> taskConfigList = taskModuleConfig.getTaskConfigList();
        for (int i = 0; i < taskConfigList.size(); i++) {
            TaskModuleConfig.TaskConfig taskConfig = taskConfigList.get(i);
            long taskId = taskConfig.getTaskId();
            QuestContainerDto questContainerDto = questContainerDtoMap.get(taskId);
            if (questContainerDto == null) {
                continue;
            }
            List<QuestDto> questDtos = questContainerDto.getQuestDtos();
            for (QuestDto questDto : questDtos) {
                int status;
                // 校验类型 和 是否完成
                if (!BATTLE_QUEST_TYPE.contains(questDto.getType()) || (status = getQuestStatus(questDto)) > 1) {
                    continue;
                }
                simpleTaskList.add(new TaskSimpleInfo(i, status, taskId, questDto));
            }
        }

        // 如果有传activityId，过滤任务
        if (StringUtils.isNotBlank(activityId)) {
            TaskSimpleInfo task = simpleTaskList.stream()
                    .filter(taskSimpleInfo -> checkActivityIdFilter(taskSimpleInfo.getQuestDto(), activityId))
                    .sorted(Comparator.comparingInt(TaskSimpleInfo::getOrder).thenComparing(TaskSimpleInfo::getStatus, Comparator.reverseOrder()))
                    .findFirst()
                    .orElse(null);
            if (task != null) {
                // 存在符合条件的任务，直接返回
                TaskModuleConfigModel.TaskConfigModel taskConfigModel = buildTaskConfigModel(task);
                return RpcResult.success(taskConfigModel);
            }
        }

        TaskSimpleInfo task = simpleTaskList.stream()
                .sorted(Comparator.comparingInt(TaskSimpleInfo::getOrder).thenComparing(TaskSimpleInfo::getStatus, Comparator.reverseOrder()))
                .findFirst()
                .orElse(null);
        if (task == null) {
            return RpcResult.success(null);
        }

        TaskModuleConfigModel.TaskConfigModel taskConfigModel = buildTaskConfigModel(task);
        return RpcResult.success(taskConfigModel);
    }

    private ClientInfoDTO convert(@NonNull RequestInfo requestInfo) {
        ClientInfoDTO info = new ClientInfoDTO();
        info.setUserId(requestInfo.getUserId());
        info.setUserIp(requestInfo.getIp());
        info.setUserAgent(requestInfo.getUserAgent());
        info.setXDeviceHeader(requestInfo.getXDevice());
        info.setDeviceRegisterTime(requestInfo.getDeviceRegisterTime());
        return info;
    }

    private int getQuestStatus(QuestDto questDto) {
        // 配置阶段奖励的260类型任务， 特殊处理
        if (QuestTargetConditionEnum.ROLE_GAME_CARD_BATTLE_DUNGEON_PROGRESS.getCode() == questDto.getType() && CollectionUtils.isEmpty(
                questDto.getStagePrizes())) {
            int progress = questDto.getProgress();
            boolean isFinishedAny = false;
            for (QuestStagePrizeDto stagePrizeDto : questDto.getStagePrizes()) {
                if (progress >= stagePrizeDto.getTargetProgress()) {
                    isFinishedAny = true;
                    if (stagePrizeDto.getPrize().getStatus() != PrizeStatus.SUCCESS) {
                        return BattleActivityConstants.TaskStatus.FINISHED;
                    }
                } else {
                    // 未达成该阶段奖励，后续阶段也不用判断，退出循环
                    isFinishedAny = false;
                    break;
                }
            }
            // 情况一isFinishedAny=false：一个阶段也未完成，或者最新阶段奖励未达成；
            if (!isFinishedAny) {
                return BattleActivityConstants.TaskStatus.UNFINISHED;
            }
            // isFinishedAny=true：所有阶段奖励都已领取, 按普通逻辑处理
        }

        int reachTarget = questDto.getReachTarget();
        List<PrizeDto> successPrize = questDto.getSuccessPrize();
        if (CollectionUtils.isEmpty(successPrize)) {
            log.warn("getQuestStatus successPrize is empty, questId:{}", questDto.getQuestId());
            // 这里按领奖过滤处理
            return BattleActivityConstants.TaskStatus.FINISHED_AND_GET;
        }
        int status = successPrize.get(0).getStatus();
        if (reachTarget == 1) {
            return status == PrizeStatus.SUCCESS ? BattleActivityConstants.TaskStatus.FINISHED_AND_GET : BattleActivityConstants.TaskStatus.FINISHED;
        }

        return BattleActivityConstants.TaskStatus.UNFINISHED;
    }

    private TaskModuleConfigModel.TaskConfigModel buildTaskConfigModel(TaskSimpleInfo taskSimpleInfo) {
        long taskId = taskSimpleInfo.getQuestContainerId() == null ? 0 : taskSimpleInfo.getQuestContainerId();
        return new TaskModuleConfigModel.TaskConfigModel().setTaskId(taskId)
                .setQuestInfos(Arrays.asList(new TaskModuleConfigModel.QuestDtoInfo().setQuestDto(taskSimpleInfo.getQuestDto())));
    }

    private boolean checkActivityIdFilter(QuestDto questDto, String activityId) {
        if (questDto.getType() == QuestTargetConditionEnum.ROLE_GAME_CARD_BATTLE_DUNGEON_PROGRESS.getCode()
                || questDto.getType() == QuestTargetConditionEnum.ROLE_GAME_CARD_BATTLE_COUNT.getCode()) {
            Set<String> ids = Optional.ofNullable(questDto.getTargetConditions())
                    .orElse(new ArrayList<>())
                    .stream()
                    .filter(targetCondition -> CollectionUtils.size(targetCondition.getFilters()) > 0)
                    .flatMap(targetCondition -> targetCondition.getFilters().stream())
                    .filter(filter -> filter.getType() == QuestTargetFilterEnum.BATTLE_ACTIVITY_ID.getCode())
                    .map(TargetFilterJson::getValue)
                    .map(item -> JsonUtils.findList(item, String.class))
                    .flatMap(List::stream)
                    .collect(Collectors.toSet());
            return ids.contains(activityId);
        }
        return false;
    }

    @Data
    private static class TaskSimpleInfo {

        private int order;
        private int status;
        private Long questContainerId;
        private QuestDto questDto;

        public TaskSimpleInfo(int order, int status, Long questContainerId, QuestDto questDto) {
            this.order = order;
            this.status = status;
            this.questContainerId = questContainerId;
            this.questDto = questDto;
        }
    }

}
