package com.kuaikan.role.game.uitl;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import com.kuaikan.role.game.api.util.GsonUtils;

/**
 * FunctionUtils
 *
 * <AUTHOR>
 * @since 2024-03-12
 */
public class FunctionUtils {

    public static <E, F> Set<F> toSet(Collection<E> collection, Function<E, F> function) {
        if (CollectionUtils.isEmpty(collection)) {
            return Sets.newHashSet();
        }
        return collection.stream().map(function).collect(Collectors.toSet());
    }

    public static <E, F> List<F> toDistinctList(Collection<E> collection, Function<E, F> function) {
        if (CollectionUtils.isEmpty(collection)) {
            return Lists.newArrayList();
        }
        return collection.stream().map(function).distinct().collect(Collectors.toList());
    }

    public static <K, V> Map<K, V> toMap(List<V> values, Function<V, K> keyMapper) {
        if (CollectionUtils.isEmpty(values)) {
            return Maps.newHashMap();
        }

        return values.stream().collect(Collectors.toMap(keyMapper, v -> v, (old, newValue) -> old));
    }

    public static <E, K, V> Map<K, V> toMap(List<E> values, Function<E, K> keyMapper, Function<E, V> valueMapper) {
        if (CollectionUtils.isEmpty(values)) {
            return Maps.newHashMap();
        }

        return values.stream().collect(Collectors.toMap(keyMapper, valueMapper, (old, newValue) -> old));
    }

    public static <K, V> Map<K, List<V>> groupBy(List<V> values, Function<V, K> keyMapper) {
        if (CollectionUtils.isEmpty(values)) {
            return Maps.newHashMap();
        }
        return values.stream().collect(Collectors.groupingBy(keyMapper));
    }

    public static <T, K, V> Map<K, List<V>> groupBy(List<T> values, Function<T, K> keyMapper, Function<T, V> valueMapper) {
        if (CollectionUtils.isEmpty(values)) {
            return Maps.newHashMap();
        }
        return values.stream().collect(Collectors.groupingBy(keyMapper, Collectors.mapping(valueMapper, Collectors.toList())));
    }

    public static <T> List<T> filter(List<T> values, Predicate<? super T> predicate) {
        if (CollectionUtils.isEmpty(values)) {
            return Lists.newArrayList();
        }

        return values.stream().filter(predicate).collect(Collectors.toList());
    }

    public static List<Integer> long2int(List<Long> numbers) {
        if (CollectionUtils.isEmpty(numbers)) {
            return Lists.newArrayList();
        }

        return numbers.stream().filter(Objects::nonNull).map(Long::intValue).collect(Collectors.toList());
    }

    public static List<Long> int2long(List<Integer> numbers) {
        if (CollectionUtils.isEmpty(numbers)) {
            return Lists.newArrayList();
        }

        return numbers.stream().filter(Objects::nonNull).map(Integer::longValue).collect(Collectors.toList());
    }

    public static <E> boolean hasIntersection(List<E> list1, List<E> list2) {
        List<E> intersection = ListUtils.intersection(list1, list2);
        return intersection.size() > 0;
    }

    // 判断两个string的列表是否有交集， String是英文都要分隔的不包含中括号
    public static <E> boolean hasIntersectionForStringList(String string1, String string2) {
        String[] contentArray1 = string1.split(",");
        List<String> list1 = GsonUtils.tryParseListNotNull(GsonUtils.toJson(contentArray1), String.class);
        String[] contentArray2 = string2.split(",");
        List<String> list2 = GsonUtils.tryParseListNotNull(GsonUtils.toJson(contentArray2), String.class);
        List<String> intersection = ListUtils.intersection(list1, list2);
        return intersection.size() > 0;
    }

    public static <T, E> E getPropOrElse(T object, Function<T, E> getPropMethod) {
        return getPropOrElse(object, getPropMethod, null);
    }

    // 获取某个对象的属性值,如果对象为空或者属性值为空,则返回默认值
    public static <T, E> E getPropOrElse(T object, Function<T, E> getPropMethod, E defaultPropValue) {
        return Optional.ofNullable(object).map(getPropMethod).orElse(defaultPropValue);
    }

    public static <E> List<E> flatMapToList(Collection<? extends Collection<E>> c) {
        if (CollectionUtils.isEmpty(c)) {
            return Lists.newArrayList();
        }
        return c.stream().flatMap(Collection::stream).collect(Collectors.toList());
    }

    public static <E> Set<E> flatMapToSet(Collection<? extends Collection<E>> c) {
        if (CollectionUtils.isEmpty(c)) {
            return Sets.newHashSet();
        }
        return c.stream().flatMap(Collection::stream).collect(Collectors.toSet());
    }
}
