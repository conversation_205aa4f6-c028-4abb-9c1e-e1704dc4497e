package com.kuaikan.role.game.service.impl;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.dubbo.config.annotation.DubboService;

import com.kuaikan.common.model.RpcResult;
import com.kuaikan.role.game.api.common.RoleGameResponse;
import com.kuaikan.role.game.api.model.SpiritStoneModel;
import com.kuaikan.role.game.api.service.SpiritStoneService;
import com.kuaikan.role.game.component.SpiritStoneComponent;
import com.kuaikan.user.model.BizResult;

/**
 * <AUTHOR>
 * @date 2025/3/25 15:01
 */

@Slf4j
@DubboService
public class SpiritStoneServiceImpl implements SpiritStoneService {

    @Resource
    private SpiritStoneComponent spiritStoneComponent;

    @Override
    public RpcResult<SpiritStoneModel> querySpiritStoneList(int userId) {
        BizResult<SpiritStoneModel> rpcResult = spiritStoneComponent.querySpiritStoneList(userId);
        if (!rpcResult.isSuccess()) {
            log.warn("query spirit stone fail ! userId = {}, msg = {}", userId, rpcResult.getMessage());
            return RpcResult.result(RoleGameResponse.QUERY_SPIRIT_STONE_LIST_FAIL);
        }
        return RpcResult.success(rpcResult.getData());
    }

}
