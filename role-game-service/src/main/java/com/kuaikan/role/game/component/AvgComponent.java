package com.kuaikan.role.game.component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.kuaikan.role.game.api.bean.AvgChapter;
import com.kuaikan.role.game.api.bean.AvgDir;
import com.kuaikan.role.game.api.bean.AvgHotZone;
import com.kuaikan.role.game.api.bean.AvgOriginFile;
import com.kuaikan.role.game.api.bean.CommonAudio;
import com.kuaikan.role.game.api.bean.CommonVideo;
import com.kuaikan.role.game.api.bean.UserAvgChapterRecord;
import com.kuaikan.role.game.api.enums.AvgChapterType;
import com.kuaikan.role.game.api.enums.AvgFileStatusType;
import com.kuaikan.role.game.api.enums.AvgFileType;
import com.kuaikan.role.game.api.rpc.result.AvgChapterModel;
import com.kuaikan.role.game.api.rpc.result.AvgChapterRecordModel;
import com.kuaikan.role.game.api.util.AvgOrderUtils;
import com.kuaikan.role.game.config.ApolloConfig;
import com.kuaikan.role.game.repository.AvgRepository;

/**
 * <AUTHOR>
 */
@Component
public class AvgComponent {

    @Resource
    private AvgRepository avgRepository;
    @Resource
    private ApolloConfig apolloConfig;

    public Map<Integer, AvgChapterModel> batchQueryAvgChapter(Collection<Integer> chapterIds) {
        List<AvgChapter> avgTextChapters = avgRepository.queryByChapterIds(chapterIds, AvgChapterType.TEXT.getCode());
        Set<Integer> allInsertChapterIds = avgTextChapters.stream()
                .map(AvgChapter::getTextList)
                .flatMap(List::stream)
                .map(AvgChapter.Text::getInsertChapter)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        List<AvgChapter> allInsertChapterList = avgRepository.queryByChapterIds(allInsertChapterIds, AvgChapterType.INSERT_CHAPTER.getCode());

        List<AvgChapter> allChapters = new ArrayList<>();
        allChapters.addAll(avgTextChapters);
        allChapters.addAll(allInsertChapterList);

        List<AvgChapter.DirFileContents> allDirFileContentList = allChapters.stream().map(AvgChapter::getDirFileContents).collect(Collectors.toList());
        Set<String> hotZoneNames = allDirFileContentList.stream()
                .map(AvgChapter.DirFileContents::getHotZoneNames)
                .flatMap(Set::stream)
                .collect(Collectors.toSet());
        Set<String> fileNames = allDirFileContentList.stream().map(AvgChapter.DirFileContents::getFileNames).flatMap(Set::stream).collect(Collectors.toSet());
        Set<String> dynamicRoleDirNames = allDirFileContentList.stream()
                .map(AvgChapter.DirFileContents::getDynamicRoleDirNames)
                .flatMap(Set::stream)
                .collect(Collectors.toSet());
        Set<String> spineNames = allDirFileContentList.stream().map(AvgChapter.DirFileContents::getSpineNames).flatMap(Set::stream).collect(Collectors.toSet());
        Set<String> qSpineNames = allDirFileContentList.stream()
                .map(AvgChapter.DirFileContents::getQSpineNames)
                .flatMap(Set::stream)
                .collect(Collectors.toSet());
        List<Integer> styleIds = allChapters.stream().map(AvgChapter::getStyleId).collect(Collectors.toList());
        // 筛选已上架的
        List<AvgDir> avgDirs = avgRepository.selectAvgDirByByIds(styleIds).stream().filter(v -> v.getStatus() == AvgFileStatusType.ONLINE.getCode()).collect(Collectors.toList());
        styleIds = avgDirs.stream().map(AvgDir::getId).collect(Collectors.toList());
        Map<String, AvgOriginFile> avgOriginFileMap = avgRepository.queryAvgOriginFileByTypes(AvgFileType.getAvgFileTypes())
                .entrySet()
                .stream()
                .filter(entry -> fileNames.contains(entry.getValue().getName()) || (entry.getValue().getConfig() != null
                        && entry.getValue().getConfig().getNickNames() != null
                        && entry.getValue()
                        .getConfig()
                        .getNickNames()
                        .stream()
                        .map(name -> name + "." + StringUtils.substringAfterLast(entry.getValue().getName(), "."))
                        .anyMatch(fileNames::contains)))
                .filter(entry -> entry.getValue().getStatus() == AvgFileStatusType.ONLINE.getCode())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        Map<String, AvgHotZone> avgHotZoneMap = avgRepository.queryAllHotZoneFromDB()
                .values()
                .stream()
                .filter(zone -> hotZoneNames.contains(zone.getName()) || (zone.getConfig() != null
                        && zone.getConfig().getNickNames() != null
                        && zone.getConfig().getNickNames().stream().anyMatch(hotZoneNames::contains)))
                .filter(zone -> zone.getStatus() == AvgFileStatusType.ONLINE.getCode())
                .collect(Collectors.toMap(AvgHotZone::getName, Function.identity()));
        Map<String, AvgDir> dynamicRoleName2DirIdMap = avgRepository.queryDirByType(AvgFileType.DYNAMIC_ROLE.getCode())
                .stream()
                .filter(avgDir -> dynamicRoleDirNames.contains(avgDir.getName()) || (avgDir.getConfig() != null
                        && avgDir.getConfig().getNickNames() != null
                        && avgDir.getConfig()
                        .getNickNames()
                        .stream()
                        .map(name -> name + "." + StringUtils.substringAfterLast(avgDir.getName(), "."))
                        .anyMatch(dynamicRoleDirNames::contains)))
                .filter(avgDir -> avgDir.getStatus() == AvgFileStatusType.ONLINE.getCode())
                .collect(Collectors.toMap(AvgDir::getName, Function.identity()));
        Map<String, AvgDir> spineNameMap = avgRepository.queryDirByType(AvgFileType.SPINE.getCode())
                .stream()
                .filter(avgDir -> spineNames.contains(avgDir.getName()) || (avgDir.getConfig() != null
                        && avgDir.getConfig().getNickNames() != null
                        && avgDir.getConfig()
                        .getNickNames()
                        .stream()
                        .map(name -> name + "." + StringUtils.substringAfterLast(avgDir.getName(), "."))
                        .anyMatch(spineNames::contains)))
                .filter(avgDir -> avgDir.getStatus() == AvgFileStatusType.ONLINE.getCode())
                .collect(Collectors.toMap(AvgDir::getName, Function.identity()));
        Map<String, AvgDir> qSpineNameMap = avgRepository.queryDirByType(AvgFileType.Q_SPINE.getCode())
                .stream()
                .filter(avgDir -> qSpineNames.contains(avgDir.getName()) || (avgDir.getConfig() != null
                        && avgDir.getConfig().getNickNames() != null
                        && avgDir.getConfig()
                        .getNickNames()
                        .stream()
                        .map(name -> name + "." + StringUtils.substringAfterLast(avgDir.getName(), "."))
                        .anyMatch(qSpineNames::contains)))
                .filter(avgDir -> avgDir.getStatus() == AvgFileStatusType.ONLINE.getCode())
                .collect(Collectors.toMap(AvgDir::getName, Function.identity()));
        Map<String, AvgDir> allSpineNameMap = new HashMap<>();
        allSpineNameMap.putAll(spineNameMap);
        allSpineNameMap.putAll(qSpineNameMap);
        List<Integer> allDirIds = dynamicRoleName2DirIdMap.values().stream().map(AvgDir::getId).collect(Collectors.toList());
        allDirIds.addAll(allSpineNameMap.values().stream().map(AvgDir::getId).collect(Collectors.toList()));
        allDirIds.addAll(styleIds);
        Map<Integer, List<AvgOriginFile>> parentId2FileList = avgRepository.queryAvgOriginFileByParentIdsFromCache(allDirIds);
        Map<String, CommonVideo> commonVideoMap = getVideoMap(avgOriginFileMap);
        Map<String, CommonAudio> commonAudioMap = getCommonAudioMap(avgOriginFileMap);
        Map<Integer, AvgChapterModel> avgTextChapterModelMap = new HashMap<>();
        Map<Integer, AvgChapterModel> avgInsertChapterModelMap = new HashMap<>();
        // 命令重的参数，需要替换成的url
        Map<String, String> linkUrlMap = Maps.newHashMapWithExpectedSize(2);
        linkUrlMap.put(AvgOrderUtils.CARD_BATTLE_PACKAGE_URL_KEY, apolloConfig.getCardBattlePackageUrl());
        linkUrlMap.put(AvgOrderUtils.CARD_BATTLE_EXPLORE_URL_KEY, apolloConfig.getCardBattleExploreUrl());
        // names可能是别名,map中都是资源名
        for (AvgChapter avgChapter : allChapters) {
            AvgChapter.DirFileContents dirFileContents = avgChapter.getDirFileContents();
            Set<String> chapterHotZoneNames = dirFileContents.getHotZoneNames();
            Set<String> chapterFileNames = dirFileContents.getFileNames();
            Set<String> chapterDynamicRoleDirNames = dirFileContents.getDynamicRoleDirNames();
            Set<String> chapterSpineNames = dirFileContents.getSpineNames();
            Set<String> chapterQSpineNames = dirFileContents.getQSpineNames();
            Set<String> chapterAllSpineNames = new HashSet<>();
            chapterAllSpineNames.addAll(chapterSpineNames);
            chapterAllSpineNames.addAll(chapterQSpineNames);
            // key=name,value=resource
            Map<String, AvgHotZone> chapterHotZoneMap = avgHotZoneMap.entrySet()
                    .stream()
                    .filter(entry -> chapterHotZoneNames.contains(entry.getKey()) || (entry.getValue().getConfig() != null
                            && entry.getValue().getConfig().getNickNames() != null
                            && entry.getValue().getConfig().getNickNames().stream().anyMatch(chapterHotZoneNames::contains)))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            Map<String, AvgOriginFile> chapterFileMap = avgOriginFileMap.entrySet()
                    .stream()
                    .filter(entry -> chapterFileNames.contains(entry.getKey()) || (entry.getValue().getConfig() != null
                            && entry.getValue().getConfig().getNickNames() != null
                            && entry.getValue()
                            .getConfig()
                            .getNickNames()
                            .stream()
                            .map(name -> name + "." + StringUtils.substringAfterLast(entry.getValue().getName(), "."))
                            .anyMatch(chapterFileNames::contains)))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            Map<String, AvgDir> chapterDynamicRoleName2DirIdMap = dynamicRoleName2DirIdMap.entrySet()
                    .stream()
                    .filter(entry -> chapterDynamicRoleDirNames.contains(entry.getKey()) || (entry.getValue().getConfig() != null
                            && entry.getValue().getConfig().getNickNames() != null
                            && entry.getValue()
                            .getConfig()
                            .getNickNames()
                            .stream()
                            .map(name -> name + "." + StringUtils.substringAfterLast(entry.getValue().getName(), "."))
                            .anyMatch(chapterDynamicRoleDirNames::contains)))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            Map<String, AvgDir> chapterSpineNameMap = allSpineNameMap.entrySet()
                    .stream()
                    .filter(entry -> chapterAllSpineNames.contains(entry.getKey()) || (entry.getValue().getConfig() != null
                            && entry.getValue().getConfig().getNickNames() != null
                            && entry.getValue()
                            .getConfig()
                            .getNickNames()
                            .stream()
                            .map(name -> name + "." + StringUtils.substringAfterLast(entry.getValue().getName(), "."))
                            .anyMatch(chapterAllSpineNames::contains)))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            List<Integer> chapterDirIds = chapterDynamicRoleName2DirIdMap.values().stream().map(AvgDir::getId).collect(Collectors.toList());
            chapterDirIds.addAll(chapterSpineNameMap.values().stream().map(AvgDir::getId).collect(Collectors.toList()));
            chapterDirIds.add(avgChapter.getStyleId());
            Map<Integer, List<AvgOriginFile>> chapterParentId2FileList = parentId2FileList.entrySet()
                    .stream()
                    .filter(entry -> chapterDirIds.contains(entry.getKey()))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            AvgChapterModel avgChapterModel = AvgChapterModel.valueOf(avgChapter, chapterParentId2FileList, chapterDynamicRoleName2DirIdMap, chapterFileMap,
                    chapterHotZoneMap, chapterSpineNameMap, commonVideoMap, commonAudioMap, linkUrlMap);
            if (avgChapter.getType() == AvgChapterType.TEXT.getCode()) {
                avgTextChapterModelMap.put(avgChapter.getChapterId(), avgChapterModel);
            } else {
                avgInsertChapterModelMap.put(avgChapter.getChapterId(), avgChapterModel);
            }
        }
        for (AvgChapterModel value : avgTextChapterModelMap.values()) {
            List<AvgChapterModel.TextModel> textList = value.getTextList();
            List<Pair<Integer, List<AvgChapterModel.TextModel>>> indexInsertChapterTextList = new ArrayList<>();
            List<AvgChapterModel.SpineFileModel> allInsertChapterSpineFileModels = new ArrayList<>();
            for (int i = textList.size() - 1; i >= 0; i--) {
                AvgChapterModel.TextModel textModel = textList.get(i);
                Integer insertChapter = textModel.getInsertChapter();
                String parentTextId = textModel.getTextId();
                List<String> parentNextIds = textModel.getNextIds();
                if (CollectionUtils.size(parentNextIds) != 1 || insertChapter == null) {
                    continue;
                }
                Optional<AvgChapterModel> insertAvgChapterModel = Optional.ofNullable(avgInsertChapterModelMap.get(insertChapter));
                List<AvgChapterModel.SpineFileModel> insertChapterSpineFileModels = insertAvgChapterModel.map(AvgChapterModel::getSpineFiles)
                        .orElse(new ArrayList<>());
                allInsertChapterSpineFileModels.addAll(insertChapterSpineFileModels);
                List<AvgChapterModel.TextModel> insertChapterTextModels = insertAvgChapterModel.map(AvgChapterModel::getTextList).orElse(new ArrayList<>());
                String childFirst = "";
                for (AvgChapterModel.TextModel insertChapterTextModel : insertChapterTextModels) {
                    String childTextId = insertChapterTextModel.getTextId();
                    insertChapterTextModel.setTextId(parentTextId + "-" + childTextId);
                    if (StringUtils.isBlank(childFirst)) {
                        childFirst = parentTextId + "-" + childTextId;
                        textModel.setNextIds(Lists.newArrayList(childFirst));
                    }
                    List<String> nextIds = insertChapterTextModel.getNextIds();
                    int nextSize = CollectionUtils.size(nextIds);
                    if (nextSize == 0) {
                        //到结尾了
                        insertChapterTextModel.setNextIds(parentNextIds);
                    } else {
                        List<String> newNextIds = new ArrayList<>();
                        for (String nextId : nextIds) {
                            newNextIds.add(parentTextId + "-" + nextId);
                        }
                        insertChapterTextModel.setNextIds(newNextIds);
                        List<AvgChapterModel.DialogueOption> dialogueOptions = insertChapterTextModel.getDialogueOptions();
                        if (CollectionUtils.isNotEmpty(dialogueOptions)) {
                            for (AvgChapterModel.DialogueOption dialogueOption : dialogueOptions) {
                                String optionNextId = dialogueOption.getNextId();
                                dialogueOption.setNextId(parentTextId + "-" + optionNextId);
                            }
                        }
                    }
                    boolean insertChapterBack = insertChapterTextModel.isInsertChapterBack();
                    if (insertChapterBack) {
                        insertChapterTextModel.setInsertChapterBackId(parentNextIds.get(0));
                    }
                    String loopNextTextId = insertChapterTextModel.getLoopNextTextId();
                    if (StringUtils.isNotBlank(loopNextTextId)) {
                        insertChapterTextModel.setLoopNextTextId(parentTextId + "-" + loopNextTextId);
                    }
                }
                Pair<Integer, List<AvgChapterModel.TextModel>> indexInsertChapterTexts = Pair.of(i, insertChapterTextModels);
                indexInsertChapterTextList.add(indexInsertChapterTexts);
            }
            for (Pair<Integer, List<AvgChapterModel.TextModel>> indexInsertChapterTexts : indexInsertChapterTextList) {
                Integer index = indexInsertChapterTexts.getLeft();
                List<AvgChapterModel.TextModel> insertChapterTextModels = indexInsertChapterTexts.getRight();
                textList.addAll(index + 1, insertChapterTextModels);
            }
            List<AvgChapterModel.SpineFileModel> spineFiles = value.getSpineFiles();
            if (CollectionUtils.isNotEmpty(spineFiles)) {
                spineFiles.addAll(allInsertChapterSpineFileModels);
            } else {
                value.setSpineFiles(allInsertChapterSpineFileModels);
            }
        }
        return avgTextChapterModelMap;
    }

    public Map<String, CommonVideo> getVideoMap(Map<String, AvgOriginFile> avgOriginFileMap) {
        List<String> videoIds = avgOriginFileMap.values()
                .stream()
                .filter(v -> v.getType() == AvgFileType.HIGHLIGHT_VIDEO.getCode() || v.getType() == AvgFileType.DYNAMIC_BACKGROUND.getCode())
                .map(AvgOriginFile::getConfig)
                .filter(Objects::nonNull)
                .map(AvgOriginFile.Config::getVideoId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return avgRepository.batchQueryAvgCommonVideo(videoIds).stream().collect(Collectors.toMap(CommonVideo::getVideoId, Function.identity()));
    }

    public Map<String, CommonAudio> getCommonAudioMap(Map<String, AvgOriginFile> avgOriginFileMap) {
        List<String> audioIds = avgOriginFileMap.values()
                .stream()
                .filter(value -> value.getType() == AvgFileType.BGM.getCode()
                        || value.getType() == AvgFileType.SOUND_EFFECT.getCode()
                        || value.getType() == AvgFileType.CV.getCode()
                        || value.getType() == AvgFileType.HIGHLIGHT_VIDEO_VOICE.getCode())
                .map(AvgOriginFile::getConfig)
                .filter(Objects::nonNull)
                .map(AvgOriginFile.Config::getAudioId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<CommonAudio> commonAudios = avgRepository.queryCommonAudioBatchByAudioIds(audioIds);
        return commonAudios.stream().collect(Collectors.toMap(CommonAudio::getAudioId, Function.identity()));
    }

    public AvgChapterRecordModel queryAvgRecord(int userId, int chapterId) {
        UserAvgChapterRecord userAvgChapterRecord = avgRepository.queryRecordByChapterId(userId, chapterId);
        return (AvgChapterRecordModel.valueOf(userAvgChapterRecord));
    }

}
