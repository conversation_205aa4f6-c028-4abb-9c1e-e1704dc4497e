package com.kuaikan.role.game.repository;

import static com.kuaikan.role.game.common.enums.CacheConfig.USER_COSTUME_PART;
import static com.kuaikan.role.game.common.enums.CacheConfig.USER_COSTUME_PART_LIST;
import static com.kuaikan.role.game.uitl.ThreadPoolConfig.REDIS_EXECUTOR;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Repository;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import com.kuaikan.comic.common.utils.CommonLettuceClusterUtil;
import com.kuaikan.common.redis.CacheHelper;
import com.kuaikan.common.redis.KeyGenerator;
import com.kuaikan.common.redis.lettuce.LettuceClusterClient;
import com.kuaikan.common.redis.lettuce.LettuceClusterUtil;
import com.kuaikan.role.game.api.bean.CostumePartNum;
import com.kuaikan.role.game.api.bean.UserCostumePart;
import com.kuaikan.role.game.common.enums.CacheConfig;
import com.kuaikan.role.game.api.enums.UserTableEnum;
import com.kuaikan.role.game.dao.rolegame.UserCostumePartMapper;
import com.kuaikan.role.game.uitl.FunctionUtils;
import com.kuaikan.role.game.uitl.TablePartitionUtil;

/**
 * <AUTHOR>
 * @version 2024-04-23
 */
@Repository
public class UserCostumePartRepository {

    @Resource
    private UserCostumePartMapper userCostumePartMapper;

    public List<UserCostumePart> selectByUserIdFromDb(int userId) {
        final String tableName = getTableName(userId);
        return userCostumePartMapper.selectByUserId(tableName, userId);
    }

    public List<UserCostumePart> selectByUserId(int userId) {
        return CommonLettuceClusterUtil.getList(userId, USER_COSTUME_PART_LIST, UserCostumePart.class, this::selectByUserIdFromDb, CacheHelper.FAKE_INFO,
                REDIS_EXECUTOR);
    }

    public int insert(UserCostumePart userCostumePart) {
        String tableName = getTableName(userCostumePart.getUserId());
        int rows = userCostumePartMapper.insert(tableName, userCostumePart);
        deleteCache(userCostumePart);
        return rows;
    }

    public int insertBatch(List<UserCostumePart> userCostumeParts) {
        if (CollectionUtils.isEmpty(userCostumeParts)) {
            return 0;
        }
        int userId = userCostumeParts.get(0).getUserId();
        String tableName = getTableName(userId);
        int rows = userCostumePartMapper.insertBatch(tableName, userCostumeParts);
        userCostumeParts.forEach(this::deleteCache);
        return rows;
    }

    public void updateBalance(int userId, int costumePartId, int balance) {
        final String tableName = getTableName(userId);
        userCostumePartMapper.updateBalance(tableName, userId, costumePartId, balance);
        deleteCache(new UserCostumePart().setUserId(userId));
    }

    public int addBalance(int userId, int costumePartId, int count) {
        String tableName = getTableName(userId);
        int rows = userCostumePartMapper.addBalance(tableName, userId, costumePartId, count);
        deleteCache(new UserCostumePart().setUserId(userId).setCostumePartId(costumePartId));
        return rows;
    }

    public int batchAddBalance(int userId, List<CostumePartNum> costumePartNums) {
        String tableName = getTableName(userId);
        int rows = userCostumePartMapper.batchAddBalance(tableName, userId, costumePartNums);
        costumePartNums.forEach(e -> {
            deleteCache(new UserCostumePart().setUserId(userId).setCostumePartId(e.getCostumePartId()));
        });
        return rows;
    }

    public UserCostumePart queryByUserIdPartId(int userId, int costumePartId) {
        return CommonLettuceClusterUtil.get(userId, costumePartId, USER_COSTUME_PART, UserCostumePart.class, this::queryByUserIdPartIdFromDb, REDIS_EXECUTOR);
    }

    public UserCostumePart queryByUserIdPartIdFromDb(int userId, int costumePartId) {
        String tableName = getTableName(userId);
        return userCostumePartMapper.queryByUserIdPartId(tableName, userId, costumePartId);
    }

    public Map<Integer, UserCostumePart> queryByCostumePartIds(int userId, Collection<Integer> costumePartIds) {
        if (CollectionUtils.isEmpty(costumePartIds)) {
            return Maps.newHashMap();
        }
        List<String> keys = costumePartIds.stream().map(e -> StringUtils.join(String.valueOf(userId), "_", e)).collect(Collectors.toList());
        Map<String, UserCostumePart> map = CommonLettuceClusterUtil.getMap(keys, UserCostumePart.class, this::queryByCostumePartIdsFromDb,
                CacheConfig.USER_COSTUME_PART_INFO_BY_IDS, REDIS_EXECUTOR);
        return FunctionUtils.toMap(Lists.newArrayList(map.values()), UserCostumePart::getCostumePartId);
    }

    public Map<String, UserCostumePart> queryByCostumePartIdsFromDb(Collection<String> userIdCostumePartIds) {
        if (CollectionUtils.isEmpty(userIdCostumePartIds)) {
            return Maps.newHashMap();
        }
        int userId = 0;
        List<Integer> costumePartIds = userIdCostumePartIds.stream().map(e -> NumberUtils.toInt(StringUtils.split(e, "_")[1])).collect(Collectors.toList());
        String tableName = getTableName(userId);
        List<UserCostumePart> parts = userCostumePartMapper.queryByCostumePartIds(tableName, userId, costumePartIds);
        return FunctionUtils.toMap(parts, e -> StringUtils.join(String.valueOf(userId), "_", e));
    }

    public Map<Integer, UserCostumePart> queryByCostumePartIdsFromDb(int userId, Collection<Integer> costumePartIds) {
        if (CollectionUtils.isEmpty(costumePartIds)) {
            return Maps.newHashMap();
        }
        String tableName = getTableName(userId);
        List<UserCostumePart> parts = userCostumePartMapper.queryByCostumePartIds(tableName, userId, costumePartIds);
        return FunctionUtils.toMap(parts, UserCostumePart::getCostumePartId);
    }

    private String getTableName(int userId) {
        return TablePartitionUtil.getTableName(UserTableEnum.USER_COSTUME_PART, userId);
    }

    private void deleteCache(UserCostumePart userCostumePart) {
        LettuceClusterClient redisClusterClient = LettuceClusterUtil.getClusterClientByName(USER_COSTUME_PART_LIST.getReadWriteVip());
        String key = KeyGenerator.generate(CacheConfig.USER_COSTUME_PART_LIST.getKeyPattern(), userCostumePart.getUserId());
        Set<String> keySet = Sets.newHashSet(key);
        if (userCostumePart.getCostumePartId() > 0) {
            String key1 = KeyGenerator.generate(CacheConfig.USER_COSTUME_PART.getKeyPattern(), userCostumePart.getUserId(), userCostumePart.getCostumePartId());
            String key2 = KeyGenerator.generate(CacheConfig.USER_COSTUME_PART_INFO_BY_IDS.getKeyPattern(),
                    StringUtils.join(userCostumePart.getUserId(), "_", userCostumePart.getCostumePartId()));
            keySet.add(key1);
            keySet.add(key2);
        }

        redisClusterClient.del(keySet.toArray(new String[0]));
    }

}
