package com.kuaikan.role.game.repository;

import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import com.kuaikan.role.game.dao.rolegame.UserMapOngoingScheduleMapper;
import com.kuaikan.role.game.api.enums.UserTableEnum;
import com.kuaikan.role.game.api.bean.UserMapOngoingSchedule;

import lombok.extern.slf4j.Slf4j;

@Repository
@Slf4j
public class UserMapOngoingScheduleRepository {

    @Resource
    private UserMapOngoingScheduleMapper userMapOngoingScheduleMapper;

    public UserMapOngoingSchedule queryById(int id) {   
        return userMapOngoingScheduleMapper.queryById(id);
    }

    public UserMapOngoingSchedule queryByUserIdAndMapId(int userId, int mapId) {
        return userMapOngoingScheduleMapper.queryByUserIdAndMapId(userId, mapId);
    }

    public int insert(UserMapOngoingSchedule userMapOngoingSchedule) {
        return userMapOngoingScheduleMapper.insert(userMapOngoingSchedule);
    }

    public int delete(int id) {
        return userMapOngoingScheduleMapper.delete(id);
    }

}
