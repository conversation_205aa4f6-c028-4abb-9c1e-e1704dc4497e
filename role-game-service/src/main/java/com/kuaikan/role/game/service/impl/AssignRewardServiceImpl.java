package com.kuaikan.role.game.service.impl;

import java.util.Optional;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.model.RpcResult;
import com.kuaikan.game.gamecard.dubbo.dto.ClientInfoDTO;
import com.kuaikan.role.game.api.bean.ClaimPrizeRecord;
import com.kuaikan.role.game.api.bean.ObtainPrizeInParam;
import com.kuaikan.role.game.api.enums.ClaimStatus;
import com.kuaikan.role.game.api.enums.PrizeSourceType;
import com.kuaikan.role.game.api.enums.RewardType;
import com.kuaikan.role.game.api.model.ClaimPrizeRecordModel;
import com.kuaikan.role.game.api.model.EmotionBondLevelUpModel;
import com.kuaikan.role.game.api.rpc.param.AssignRewardParam;
import com.kuaikan.role.game.api.rpc.param.AssignRewardRetryParam;
import com.kuaikan.role.game.api.service.AssignRewardService;
import com.kuaikan.role.game.component.AssignRewardComponent;
import com.kuaikan.role.game.component.ClaimPrizeComponent;
import com.kuaikan.role.game.repository.ClaimPrizeRecordRepository;

/**
 * <AUTHOR>
 * @version 2024-04-01
 */
@DubboService(version = "1.0", group = "role-game")
@Slf4j
public class AssignRewardServiceImpl implements AssignRewardService {

    @Resource
    private ClaimPrizeRecordRepository claimPrizeRecordRepository;
    @Resource
    private ClaimPrizeComponent claimPrizeComponent;
    @Resource
    private AssignRewardComponent assignRewardComponent;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public RpcResult<Void> assignReward(AssignRewardParam param) {
        final int type = param.getType();
        final RewardType rewardType = RewardType.getByCode(type);
        if (rewardType == null) {
            log.error("assignReward error, rewardType not found, param:{}", param);
            return RpcResult.result(-1, "rewardType not found");
        }
        final long bid = param.getBid();
        final Integer userId = param.getUserId();
        final ClaimPrizeRecord claimPrizeRecord = claimPrizeRecordRepository.selectByBid(userId, bid);
        if (claimPrizeRecord == null) {
            log.error("assignReward error, claimPrizeRecord not found, param:{}", param);
            return RpcResult.result(-1, "claimPrizeRecord not found");
        }
        final ClaimStatus claimStatus = ClaimStatus.getByCode(claimPrizeRecord.getStatus());
        if (claimStatus == null) {
            log.error("assignReward error, claimStatus not found, param:{}", param);
            return RpcResult.result(-1, "claimStatus not found");
        }

        if (claimStatus == ClaimStatus.CLAIMED || claimStatus == ClaimStatus.FINISHED) {
            log.warn("assignReward warn, claimPrizeRecord status is CLAIMED, param:{}", param);
            return RpcResult.result(ResponseCodeMsg.SUCCESS.getCode(), "claimPrizeRecord status is CLAIMED");
        }
        assignRewardComponent.acquireReward(param);
        claimPrizeRecordRepository.updateStatus(userId, claimPrizeRecord.getId(), ClaimStatus.CLAIMED.getCode());
        log.info("assignReward success, param:{}", param);
        return RpcResult.success();
    }

    @Override
    public RpcResult<Void> assignRewardRetry(AssignRewardRetryParam param) {
        ClaimPrizeRecord claimPrizeRecord = ClaimPrizeRecord.valueOf(param.getClaimPrizeRecord());
        if (claimPrizeRecord == null) {
            log.error("assignRewardRetry error, claimPrizeRecord not found, param:{}", param);
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST);
        }
        ClientInfoDTO clientInfoDTO = Optional.ofNullable(param.getClaimPrizeRecord())
                .map(ClaimPrizeRecordModel::getExtraInfo)
                .map(ClaimPrizeRecordModel.ExtraInfo::getInParam)
                .map(ObtainPrizeInParam::getClientInfo)
                .orElse(null);
        claimPrizeComponent.doClaimPrize(claimPrizeRecord, param.getPrize(), clientInfoDTO);
        return RpcResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RpcResult<Void> assignRewardAndRecord(AssignRewardParam param) {
        log.debug("assignRewardAndRecord param:{}", param);
        RewardType rewardType = RewardType.getByCode(param.getType());
        if (rewardType == null) {
            log.error("assignRewardAndRecord error, rewardType not found, param:{}", param);
            return RpcResult.result(ResponseCodeMsg.BAD_REQUEST);
        }
        assignRewardComponent.acquireReward(param);
        ClaimPrizeRecord claimPrizeRecord = new ClaimPrizeRecord().setUserId(param.getUserId())
                .setBid(param.getBid())
                .setPrizeId(param.getPrizeId())
                .setSource(PrizeSourceType.QUEST.getName())
                .setStatus(ClaimStatus.FINISHED.getCode());
        claimPrizeRecordRepository.insert(claimPrizeRecord);
        log.info("assignRewardAndRecord success, param:{}", param);
        return RpcResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RpcResult<EmotionBondLevelUpModel> assignRoleGroupBondReward(AssignRewardParam param) {
        final int type = param.getType();
        if (type != RewardType.EMOTION_BOND.getCode()) {
            log.error("assignRoleGroupBondReward error, rewardType not found, param:{}", param);
            return RpcResult.result(-1, "rewardType not found");
        }
        EmotionBondLevelUpModel emotionBondLevelUpModel = assignRewardComponent.acquireEmotionBondReward(param);
        ClaimPrizeRecord claimPrizeRecord = new ClaimPrizeRecord().setUserId(param.getUserId())
                .setBid(param.getBid())
                .setPrizeId(param.getPrizeId())
                .setSource(PrizeSourceType.QUEST.getName())
                .setStatus(ClaimStatus.FINISHED.getCode());
        claimPrizeRecordRepository.insert(claimPrizeRecord);
        log.info("assignRoleGroupBondReward success, param:{}, result:{}", param, emotionBondLevelUpModel);
        return RpcResult.success(emotionBondLevelUpModel);
    }
}
