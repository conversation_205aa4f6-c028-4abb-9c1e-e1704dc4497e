package com.kuaikan.role.game.service.impl;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.config.annotation.Method;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;

import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.model.RpcResult;
import com.kuaikan.common.tools.serialize.JsonUtils;
import com.kuaikan.idgenerator.sdk.BufferedIdGenerator;
import com.kuaikan.role.game.api.bean.Building;
import com.kuaikan.role.game.api.bean.BuildingArea;
import com.kuaikan.role.game.api.bean.ClientInfo;
import com.kuaikan.role.game.api.bean.ConsumptionSnapshot;
import com.kuaikan.role.game.api.bean.KeyValueConfig;
import com.kuaikan.role.game.api.bean.Role;
import com.kuaikan.role.game.api.bean.RoleBuildingScheduleConfig;
import com.kuaikan.role.game.api.bean.RoleGroup;
import com.kuaikan.role.game.api.bean.Schedule;
import com.kuaikan.role.game.api.bean.ScheduleGiftConfig;
import com.kuaikan.role.game.api.bean.ScheduleGiftConfigList;
import com.kuaikan.role.game.api.bean.ScheduleTimeLeveConfigList;
import com.kuaikan.role.game.api.bean.SettlementSnapshot;
import com.kuaikan.role.game.api.bean.UserInfo;
import com.kuaikan.role.game.api.bean.UserRole;
import com.kuaikan.role.game.api.bean.UserRoleDimension;
import com.kuaikan.role.game.api.bean.UserRoleFinishSchedule;
import com.kuaikan.role.game.api.bean.UserRoleOngoingSchedule;
import com.kuaikan.role.game.api.bean.UserRoleProperty;
import com.kuaikan.role.game.api.bean.UserSilverCoinAccount;
import com.kuaikan.role.game.api.bo.RoleCommonPropertyConfig;
import com.kuaikan.role.game.api.common.RoleGameResponse;
import com.kuaikan.role.game.api.enums.BuildingType;
import com.kuaikan.role.game.api.enums.DimensionType;
import com.kuaikan.role.game.api.enums.EnergyAssignSource;
import com.kuaikan.role.game.api.enums.MoodStatus;
import com.kuaikan.role.game.api.enums.ScheduleType;
import com.kuaikan.role.game.api.enums.ScheduleVariationType;
import com.kuaikan.role.game.api.enums.SilverCoinChargeSourceEnum;
import com.kuaikan.role.game.api.enums.SilverCoinConsumeSourceEnum;
import com.kuaikan.role.game.api.enums.TirednessStatus;
import com.kuaikan.role.game.api.enums.UserScheduleStatus;
import com.kuaikan.role.game.api.model.CompleteAllScheduleModel;
import com.kuaikan.role.game.api.model.CompleteScheduleBubbleModel;
import com.kuaikan.role.game.api.model.CompleteScheduleModel;
import com.kuaikan.role.game.api.model.CreateScheduleModel;
import com.kuaikan.role.game.api.model.DimensionLevelUpModel;
import com.kuaikan.role.game.api.model.LevelUpModel;
import com.kuaikan.role.game.api.model.MapElementModel;
import com.kuaikan.role.game.api.model.OngoingScheduleListModel;
import com.kuaikan.role.game.api.model.ScheduleAnimationInfoModel;
import com.kuaikan.role.game.api.model.ScheduleListModel;
import com.kuaikan.role.game.api.model.ScheduleListRoleModel;
import com.kuaikan.role.game.api.model.ScheduleSettlementModel;
import com.kuaikan.role.game.api.model.SchedulesSettlementModel;
import com.kuaikan.role.game.api.model.StopScheduleModel;
import com.kuaikan.role.game.api.model.UnlockStoryModel;
import com.kuaikan.role.game.api.rpc.param.EnergyAssignParam;
import com.kuaikan.role.game.api.rpc.param.ScheduleCreationParam;
import com.kuaikan.role.game.api.service.UserScheduleService;
import com.kuaikan.role.game.common.bean.RoleGroupRelation;
import com.kuaikan.role.game.common.constant.KeyValueConfigKeys;
import com.kuaikan.role.game.common.enums.FinishScheduleType;
import com.kuaikan.role.game.common.enums.ScheduleActionType;
import com.kuaikan.role.game.component.KvConfigComponent;
import com.kuaikan.role.game.component.LockComponent;
import com.kuaikan.role.game.component.RoleComponent;
import com.kuaikan.role.game.component.SaComponent;
import com.kuaikan.role.game.component.UserRolePropertyComponent;
import com.kuaikan.role.game.component.UserScheduleComponent;
import com.kuaikan.role.game.component.UserSilverCoinAccountComponent;
import com.kuaikan.role.game.converter.MapElementConverter;
import com.kuaikan.role.game.model.bo.RoleInfoBO;
import com.kuaikan.role.game.repository.BuildingAreaRepository;
import com.kuaikan.role.game.repository.BuildingRepository;
import com.kuaikan.role.game.repository.KeyValueConfigRepository;
import com.kuaikan.role.game.repository.MapElementRepository;
import com.kuaikan.role.game.repository.RoleBuildingScheduleConfigRepository;
import com.kuaikan.role.game.repository.RoleGroupRelationRepository;
import com.kuaikan.role.game.repository.RoleGroupRepository;
import com.kuaikan.role.game.repository.RoleRepository;
import com.kuaikan.role.game.repository.ScheduleRepository;
import com.kuaikan.role.game.repository.UserInfoRepository;
import com.kuaikan.role.game.repository.UserRoleDimensionRepository;
import com.kuaikan.role.game.repository.UserRoleFinishScheduleRepository;
import com.kuaikan.role.game.repository.UserRoleOngoingScheduleRepository;
import com.kuaikan.role.game.repository.UserRolePropertyRepository;
import com.kuaikan.role.game.repository.UserRoleRepository;
import com.kuaikan.role.game.repository.UserSilverCoinAccountRepository;
import com.kuaikan.role.game.uitl.FunctionUtils;

/**
 * UserScheduleServiceImpl
 *
 * <AUTHOR>
 * @since 2024-06-11
 */
@Slf4j
@DubboService(methods = { @Method(name = "completeAllSchedule", timeout = 10000) })
public class UserScheduleServiceImpl implements UserScheduleService {

    @Resource
    private UserRoleOngoingScheduleRepository userRoleOngoingScheduleRepository;
    @Resource
    private UserRoleFinishScheduleRepository userRoleFinishScheduleRepository;
    @Resource
    private ScheduleRepository scheduleRepository;
    @Resource
    private UserSilverCoinAccountRepository userSilverCoinAccountRepository;
    @Resource
    private RoleComponent roleComponent;
    @Resource
    private UserInfoRepository userInfoRepository;
    @Resource
    private LockComponent lockComponent;
    @Resource
    private UserSilverCoinAccountComponent userSilverCoinAccountComponent;
    @Resource
    private UserRolePropertyComponent userRolePropertyComponent;
    @Resource
    private UserRolePropertyRepository userRolePropertyRepository;
    @Resource
    private RoleBuildingScheduleConfigRepository roleBuildingScheduleConfigRepository;
    @Resource
    private BuildingRepository buildingRepository;
    @Resource
    private RoleRepository roleRepository;
    @Resource
    private UserScheduleComponent userScheduleComponent;
    @Resource
    private UserRoleRepository userRoleRepository;
    @Resource
    private UserRoleDimensionRepository userRoleDimensionRepository;
    @Resource
    private BuildingAreaRepository buildingAreaRepository;
    @Resource
    private KeyValueConfigRepository keyValueConfigRepository;
    @Resource
    private SaComponent saComponent;

    public static final int ONGOING_SCHEDULE_LIMIT = 2;
    // 每个周期长度是 5秒
    public static final int SECOND_OF_PERIOD = 5;

    @Resource
    private KvConfigComponent kvConfigComponent;

    @Resource
    private MapElementRepository mapElementRepository;

    @Resource
    private MapElementConverter mapElementConverter;
    @Resource
    private RoleGroupRepository roleGroupRepository;
    @Resource
    private RoleGroupRelationRepository roleGroupRelationRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RpcResult<CreateScheduleModel> createSchedule(int userId, int roleId, int scheduleId) {
        Schedule schedule = scheduleRepository.queryByIdFromCache(scheduleId);
        if (schedule == null) {
            log.error("createSchedule failed. schedule not found. scheduleId:{}", scheduleId);
            return RpcResult.result(RoleGameResponse.SCHEDULE_NOT_EXIST);
        }
        List<Integer> roleIds = Lists.newArrayList();
        roleIds.add(roleId);
        ScheduleCreationParam param = new ScheduleCreationParam().setRoleIds(roleIds)
                .setScheduleId(scheduleId)
                .setSeconds((int) TimeUnit.MINUTES.toSeconds(schedule.getConfig().getConsumeMinute()));
        RpcResult<List<CreateScheduleModel>> res = createScheduleV2(userId, param);
        if (!res.isSuccess()) {
            return RpcResult.result(res.getCode(), res.getMessage());
        }
        return RpcResult.success(res.getData().get(0));
    }

    @Override
    public RpcResult<List<CreateScheduleModel>> createScheduleV2(int userId, ScheduleCreationParam param) {
        int scheduleId = param.getScheduleId();
        List<Integer> roleIds = param.getRoleIds();
        if (CollectionUtils.isEmpty(roleIds)) {
            return RpcResult.success();
        }

        if (roleIds.size() > ONGOING_SCHEDULE_LIMIT) {
            return RpcResult.result(RoleGameResponse.SCHEDULE_ROLE_SIZE_LIMIT);
        }
        // 检查该日程id对应是否有正在进行的日程，上限是2个
        List<UserRoleOngoingSchedule> existOngoingSchedules = userRoleOngoingScheduleRepository.queryByUserIdAndScheduleId(userId, scheduleId);
        if (roleIds.size() + ListUtils.emptyIfNull(existOngoingSchedules).size() > ONGOING_SCHEDULE_LIMIT) {
            return RpcResult.result(RoleGameResponse.ONGOING_SCHEDULE_LIMIT);
        }
        Schedule schedule = scheduleRepository.queryByIdFromCache(scheduleId);
        if (schedule == null) {
            log.error("createSchedule failed. schedule not found. scheduleId:{}", scheduleId);
            return RpcResult.result(RoleGameResponse.SCHEDULE_NOT_EXIST);
        }
        Building building = buildingRepository.queryByIdFromCache(schedule.getBuildingId());
        if (building == null) {
            log.error("createSchedule failed. building not found. scheduleId:{}, buildingId:{}", scheduleId, schedule.getBuildingId());
            return RpcResult.result(RoleGameResponse.BUILDING_NOT_EXIST);
        }
        UserInfo userInfo = userInfoRepository.queryUserInfoByUid(userId);
        if (userInfo == null) {
            log.error("createSchedule failed. userInfo not found. userId:{}", userId);
            return RpcResult.result(RoleGameResponse.USER_NOT_FOUND);
        }
        List<UserRoleProperty> userRoleProperties = userRolePropertyRepository.queryByUserIdAndRoleIdsFromCache(userId, roleIds);
        if (CollectionUtils.isEmpty(userRoleProperties) || userRoleProperties.size() != roleIds.size()) {
            log.error("createSchedule failed. userRoleProperty not found. userId:{}", userId);
            return RpcResult.result(RoleGameResponse.ROLE_NOT_ADOPTED);
        }
        Map<Integer, UserRoleProperty> roleIdPropertyMap = FunctionUtils.toMap(userRoleProperties, UserRoleProperty::getRoleId);
        boolean anyRoleTooTired = userRoleProperties.stream().anyMatch(e -> e.getTiredness() > kvConfigComponent.getPointOfExhausted());
        // 筋疲力竭无法创建日程
        if (anyRoleTooTired && building.getType() != BuildingType.ENTERTAINMENT_BUILDING.getCode()) {
            log.error("createSchedule failed. user is too tired. userId:{}, userRoleProperties:{}", userId, userRoleProperties);
            return RpcResult.result(RoleGameResponse.ROLE_TOO_TIRED);
        }
        List<UserRoleOngoingSchedule> newOngoingSchedules = buildUserOngoingSchedules(param.getSeconds(), userRoleProperties, schedule, building,
                existOngoingSchedules);
        // 行动力是否充足
        boolean energyEnough = newOngoingSchedules.stream()
                .allMatch(e -> e.getConsumptionSnapshot().getEnergyCost() <= roleIdPropertyMap.getOrDefault(e.getRoleId(), new UserRoleProperty()).getEnergy());
        if (!energyEnough) {
            return RpcResult.result(RoleGameResponse.ENERGY_NOT_ENOUGH);
        }
        // 如果配置中的银币是负数，则需要校验银币是否充足
        UserSilverCoinAccount userSilverCoinAccount = userSilverCoinAccountRepository.getOrCreateUserSilverCoinAccount(userId);
        int totalSilverCoinCost = newOngoingSchedules.stream().mapToInt(e -> e.getConsumptionSnapshot().getSilverCoinCost()).sum();
        if (totalSilverCoinCost > 0 && userSilverCoinAccount.getBalance() < totalSilverCoinCost) {
            return RpcResult.result(RoleGameResponse.SILVER_COIN_NOT_ENOUGH);
        }
        // 检查该角色是否有正在进行的日程
        List<UserRoleOngoingSchedule> roleOngoingSchedules = userRoleOngoingScheduleRepository.queryByUserIdAndRoleIdsFromCache(userId, roleIds);
        if (CollectionUtils.isNotEmpty(roleOngoingSchedules)) {
            return RpcResult.result(RoleGameResponse.ROLE_ONGOING_SCHEDULE_EXIST);
        }
        boolean locked = false;
        try {
            locked = lockComponent.lockForCreateSchedule(userId, roleIds);
            if (!locked) {
                log.error("createSchedule failed. acquire lock failed. userId:{}", userId);
                return RpcResult.result(RoleGameResponse.OPERATION_TOO_FAST);
            }
            List<CreateScheduleModel> models = Lists.newArrayList();
            for (UserRoleOngoingSchedule newOngoingSchedule : newOngoingSchedules) {
                int roleId = newOngoingSchedule.getRoleId();
                UserRoleProperty userRoleProperty = roleIdPropertyMap.get(roleId);
                if (userRoleProperty == null) {
                    continue;
                }

                userRoleOngoingScheduleRepository.insert(newOngoingSchedule);
                // 扣减资源
                String orderId = String.valueOf(BufferedIdGenerator.getId());
                ConsumptionSnapshot consumptionSnapshot = newOngoingSchedule.getConsumptionSnapshot();
                int silverCoinCost = consumptionSnapshot.getSilverCoinCost();
                // 1. 银币
                if (silverCoinCost > 0) {
                    BizResult silverCoinResult = userSilverCoinAccountComponent.consumeSilverCoin(userId, orderId, silverCoinCost,
                            SilverCoinConsumeSourceEnum.CREATE_SCHEDULE.getCode());
                    if (!silverCoinResult.isSuccess()) {
                        log.error("createSchedule failed. return silverCoin failed. userId:{}, silverResult:{}", userId, silverCoinResult);
                        throw new RuntimeException("银币扣减失败");
                    }
                }

                // 2. 行动力
                if (consumptionSnapshot.getEnergyCost() > 0) {
                    EnergyAssignParam energyAssignParam = new EnergyAssignParam().setEnergyChange(-consumptionSnapshot.getEnergyCost())
                            .setRoleId(roleId)
                            .setOrderId(orderId)
                            .setSource(EnergyAssignSource.CREATE_SCHEDULE.getCode());
                    BizResult energyResult = userRolePropertyComponent.assignUserRoleEnergy(userId, energyAssignParam);
                    if (!energyResult.isSuccess()) {
                        log.error("createSchedule failed. energy change failed. userId:{}, energyResult:{}", userId, energyResult);
                        throw new RuntimeException("行动力扣减失败");
                    }
                }
                SettlementSnapshot settlementSnapshot = newOngoingSchedule.getSettlementSnapshot();
                CreateScheduleModel model = new CreateScheduleModel().setRoleId(roleId)
                        .setScheduleId(scheduleId)
                        .setUserScheduleId(newOngoingSchedule.getId())
                        .setFinishTime(newOngoingSchedule.getEndTime())
                        .setStartTime(newOngoingSchedule.getStartTime())
                        .setTotalCoins(settlementSnapshot.getSilverCoinCharge())
                        .setTotalExps(settlementSnapshot.getDimensionExp())
                        .setOrderNum(newOngoingSchedule.getExtraInfo().getOrderNum())
                        .setCoinsPerPeriod(settlementSnapshot.getActualCoinChangePerPeriod())
                        .setExpsPerPeriod(settlementSnapshot.getActualExpChangePerPeriod());
                trackingData(userId, newOngoingSchedule, ScheduleActionType.START.getDesc(), 0, 0);
                models.add(model);
            }

            return RpcResult.success(Lists.newArrayList(models));
        } finally {
            if (locked) {
                lockComponent.unlockForCreateSchedule(userId, roleIds);
            }
        }
    }

    /** 获取创建本次日程可以用的坑位。每个建筑有2个坑位，坑位的编号是1，2。如果已有正在进行中的日程占了坑位的话，再创建的时候是不可用的。*/
    private int getScheduleAvailableOrderNum(List<UserRoleOngoingSchedule> ongoingSchedules) {
        if (CollectionUtils.isEmpty(ongoingSchedules)) {
            return 1;
        }
        if (ongoingSchedules.size() > 1) {
            return -1;
        }
        UserRoleOngoingSchedule ongoingSchedule = ongoingSchedules.get(0);
        if (ongoingSchedule.getExtraInfo() == null) {
            return 2;
        } else if (ongoingSchedule.getExtraInfo().getOrderNum() == 1) {
            return 2;
        } else {
            return 1;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RpcResult<StopScheduleModel> stopSchedule(int userId, int userRoleScheduleId) {
        UserRoleOngoingSchedule ongoingSchedule = userRoleOngoingScheduleRepository.queryByIdFromDB(userRoleScheduleId);
        if (ongoingSchedule == null || ongoingSchedule.getUserId() != userId) {
            return RpcResult.result(RoleGameResponse.SCHEDULE_NOT_EXIST);
        }

        boolean locked = false;
        try {
            locked = lockComponent.lockForOperateSchedule(userId);
            if (!locked) {
                log.error("stopSchedule failed. acquire lock failed. userId:{}", userId);
                return RpcResult.result(RoleGameResponse.OPERATION_TOO_FAST);
            }

            String orderId = String.valueOf(BufferedIdGenerator.getId());
            UserRoleFinishSchedule userRoleFinishSchedule = UserRoleFinishSchedule.valueOf(ongoingSchedule);
            userRoleFinishSchedule.setStatus(UserScheduleStatus.CANCEL.getStatus());
            userRoleFinishSchedule.setThirdId(orderId);
            int deleteRows = userRoleOngoingScheduleRepository.delete(userId, userRoleScheduleId);
            if (deleteRows <= 0) {
                log.warn("stopSchedule already. userId:{}, userRoleScheduleId:{}", userId, userRoleScheduleId);
                return RpcResult.result(RoleGameResponse.SUCCESS);
            }
            userRoleFinishScheduleRepository.insert(userRoleFinishSchedule);
            userRoleRepository.updateLastScheduleTime(userId, ongoingSchedule.getRoleId());

            ConsumptionSnapshot consumptionSnapshot = ongoingSchedule.getConsumptionSnapshot();
            // 1. 银币
            int returnSilverCoin = consumptionSnapshot.getSilverCoinCost();
            if (returnSilverCoin > 0) {
                BizResult silverResult = userSilverCoinAccountComponent.chargeSilverCoin(userId, orderId, returnSilverCoin,
                        SilverCoinChargeSourceEnum.STOP_SCHEDULE.getCode());
                if (!silverResult.isSuccess()) {
                    log.error("stopSchedule failed. return silverCoin failed. userId:{}, ongoingSchedule:{}, silverResult:{}", userId, ongoingSchedule,
                            silverResult);
                    throw new RuntimeException("银币返回失败");
                }
            }

            // 2. 行动力
            int returnEnergy = consumptionSnapshot.getEnergyCost();
            if (returnEnergy > 0) {
                EnergyAssignParam param = new EnergyAssignParam().setEnergyChange(consumptionSnapshot.getEnergyCost())
                        .setRoleId(ongoingSchedule.getRoleId())
                        .setOrderId(orderId)
                        .setSource(EnergyAssignSource.STOP_SCHEDULE.getCode());
                BizResult energyResult = userRolePropertyComponent.assignUserRoleEnergy(userId, param);
                if (!energyResult.isSuccess()) {
                    log.error("stopSchedule failed. energy change failed. userId:{}, ongoingSchedule:{}, energyResult:{}", userId, ongoingSchedule,
                            energyResult);
                    throw new RuntimeException("行动力返回失败");
                }
            }

            StopScheduleModel model = new StopScheduleModel().setEnergyChange(consumptionSnapshot.getEnergyCost())
                    .setSilverCoinChange(consumptionSnapshot.getSilverCoinCost());
            // 上传埋点
            trackingData(userId, ongoingSchedule, ScheduleActionType.STOP.getDesc(), 0, 0);
            return RpcResult.success(model);
        } finally {
            if (locked) {
                lockComponent.unlockForOperateSchedule(userId);
            }
        }
    }

    @Override
    public RpcResult<StopScheduleModel> stopScheduleV2(ClientInfo clientInfo, int userRoleScheduleId) {
        int userId = clientInfo.getUserId();
        UserRoleOngoingSchedule ongoingSchedule = userRoleOngoingScheduleRepository.queryByIdFromDB(userRoleScheduleId);
        if (ongoingSchedule == null || ongoingSchedule.getUserId() != userId) {
            return RpcResult.result(RoleGameResponse.SCHEDULE_NOT_EXIST);
        }

        boolean locked = false;
        try {
            locked = lockComponent.lockForOperateSchedule(userId);
            if (!locked) {
                log.error("stopSchedule failed. acquire lock failed. userId:{}", userId);
                return RpcResult.result(RoleGameResponse.OPERATION_TOO_FAST);
            }

            String orderId = String.valueOf(BufferedIdGenerator.getId());

            // 第一步，结算一部分资源
            long currentTime = System.currentTimeMillis();
            int finishedPeriod = (int) TimeUnit.MILLISECONDS.toSeconds(currentTime - ongoingSchedule.getStartTime()) / SECOND_OF_PERIOD;
            SchedulesSettlementModel settlementModel = userScheduleComponent.settlementSchedules(clientInfo, Lists.newArrayList(ongoingSchedule), orderId,
                    FinishScheduleType.STOP);
            UserRoleFinishSchedule userRoleFinishSchedule = UserRoleFinishSchedule.valueOf(ongoingSchedule);
            userRoleFinishSchedule.setStatus(UserScheduleStatus.CANCEL.getStatus());
            userRoleFinishSchedule.setThirdId(orderId);
            int deleteRows = userRoleOngoingScheduleRepository.delete(userId, userRoleScheduleId);
            if (deleteRows <= 0) {
                log.warn("stopSchedule already. userId:{}, userRoleScheduleId:{}", userId, userRoleScheduleId);
                return RpcResult.result(RoleGameResponse.SUCCESS);
            }
            userRoleFinishScheduleRepository.insert(userRoleFinishSchedule);
            userRoleRepository.updateLastScheduleTime(userId, ongoingSchedule.getRoleId());

            final SchedulesSettlementModel.RoleSettlementModel roleSettlementModel = settlementModel.getRoleSettlementModels().get(0);
            final SchedulesSettlementModel.RoleGroupSettlementModel roleGroupSettlementModel = settlementModel.getRoleGroupSettlementModels().get(0);
            SettlementSnapshot settlementSnapshot = ongoingSchedule.getSettlementSnapshot();
            // 第二步，返回一部分预扣减的资源
            int allPeriod = (int) TimeUnit.MILLISECONDS.toSeconds(ongoingSchedule.getEndTime() - ongoingSchedule.getStartTime()) / SECOND_OF_PERIOD;
            ConsumptionSnapshot consumptionSnapshot = ongoingSchedule.getConsumptionSnapshot();
            // 1. 返还银币， 总扣除银币 - 已进行周期扣除的银币
            int actualCoinCost = (int) Math.ceil(finishedPeriod * Math.abs(settlementSnapshot.getCoinChangePerPeriod()));
            int returnSilverCoin = consumptionSnapshot.getSilverCoinCost() - actualCoinCost;
            if (returnSilverCoin > 0) {
                BizResult silverResult = userSilverCoinAccountComponent.chargeSilverCoin(userId, orderId, returnSilverCoin,
                        SilverCoinChargeSourceEnum.STOP_SCHEDULE.getCode());
                if (!silverResult.isSuccess()) {
                    log.error("stopSchedule failed. return silverCoin failed. userId:{}, ongoingSchedule:{}, silverResult:{}", userId, ongoingSchedule,
                            silverResult);
                    throw new RuntimeException("银币返回失败");
                }
            }

            // 2. 返还行动力
            int returnEnergy = consumptionSnapshot.getEnergyCost() - (int) Math.ceil(consumptionSnapshot.getEnergyCost() * 1.0d * finishedPeriod / allPeriod);
            if (returnEnergy > 0) {
                EnergyAssignParam param = new EnergyAssignParam().setEnergyChange(returnEnergy)
                        .setRoleId(ongoingSchedule.getRoleId())
                        .setOrderId(orderId)
                        .setSource(EnergyAssignSource.STOP_SCHEDULE.getCode());
                BizResult energyResult = userRolePropertyComponent.assignUserRoleEnergy(userId, param);
                if (!energyResult.isSuccess()) {
                    log.error("stopSchedule failed. energy change failed. userId:{}, ongoingSchedule:{}, energyResult:{}", userId, ongoingSchedule,
                            energyResult);
                    throw new RuntimeException("行动力返回失败");
                }
            }

            int silverCoinChange = 0;
            // 如果是消耗银币的话就返回实际扣除的银币
            if (consumptionSnapshot.getSilverCoinCost() > 0) {
                silverCoinChange = -actualCoinCost;
                // 如果是结算银币的话，就算出总共结算的银币数量
            } else if (settlementModel.getSilverCoinChange() > 0) {
                silverCoinChange = settlementModel.getSilverCoinChange() + settlementSnapshot.getPickUpCoins();
            }

            // settlementModel中返回的是本次实际获取的经验，而该接口下发的值应该是包含已拾取的经验，所以要加回来
            if (roleSettlementModel.getLevelUpModel() != null) {
                roleSettlementModel.getLevelUpModel().setObtainExp(roleSettlementModel.getLevelUpModel().getObtainExp() + settlementSnapshot.getPickUpExps());
            }
            if (roleSettlementModel.getDimensionLevelUpModel() != null) {
                roleSettlementModel.getDimensionLevelUpModel()
                        .setObtainExp(roleSettlementModel.getDimensionLevelUpModel().getObtainExp() + settlementSnapshot.getPickUpExps());
            }
            StopScheduleModel model = new StopScheduleModel().setEnergyChange(consumptionSnapshot.getEnergyCost() - returnEnergy)
                    .setSilverCoinChange(silverCoinChange)
                    .setMoodChange(roleSettlementModel.getMoodChange())
                    .setTirednessChange(roleSettlementModel.getTirednessChange())
                    .setMoodCutPoints(kvConfigComponent.getMoodCutPoints())
                    .setTirednessCutPoints(kvConfigComponent.getTirednessCutPoints())
                    .setConsumeMinute((int) TimeUnit.MILLISECONDS.toMinutes(currentTime - ongoingSchedule.getStartTime()))
                    .setLevelUpModel(roleSettlementModel.getLevelUpModel())
                    .setDimensionLevelUpModel(roleSettlementModel.getDimensionLevelUpModel())
                    .setUnlockStoryModelList(roleSettlementModel.getUnlockStoryModels())
                    .setEmotionBondLevelUpModel(roleGroupSettlementModel.getEmotionBondLevelUpModel())
                    .setExtraAwardModels(roleGroupSettlementModel.getExtraAwardModels());
            // 上传埋点
            trackingData(userId, ongoingSchedule, ScheduleActionType.STOP.getDesc(), 0, 0);
            return RpcResult.success(model);
        } finally {
            if (locked) {
                lockComponent.unlockForOperateSchedule(userId);
            }
        }
    }

    /**
     * 角色名称	OCName	STRING
     * 角色ID	OCID	NUMBER
     * 动作	Action	STRING	开始日程、完成日程、中断日程、加速完成日程
     * 日程名称	ScheduleName	STRING
     * 日程类型	ScheduleType	STRING	一阶成长、打工、娱乐
     * 建筑名称	BuildingName	STRING	上报对应的建筑名称
     * 奖励类型列表	RewardTypeList	LIST	报名称：五维属性经验值、心情值、银币
     * 奖励数量列表	RewardCountList	LIST
     * 消耗kkb数量	SpendKKB	NUMBER
     * 消费充值币数量	SpendRecharge	NUMBER
     */
    @Override
    public void trackingData(int userId, UserRoleOngoingSchedule ongoingSchedule, String actionType, int spendKKB, int spendRecharge) {
        Map<String, Object> properties = new HashMap<>();
        RoleInfoBO roleInfo = roleComponent.getRoleInfo(userId, ongoingSchedule.getRoleId());

        properties.put("OCName", roleInfo == null ? "" : roleInfo.getRoleModel().getName());
        properties.put("OCID", ongoingSchedule.getRoleId());
        properties.put("Action", actionType);
        Schedule schedule = scheduleRepository.queryByIdFromCache(ongoingSchedule.getScheduleId());

        properties.put("ScheduleName", schedule == null ? "" : schedule.getName());
        properties.put("ScheduleType", schedule == null ? "未知" : ScheduleType.getByType(schedule.getType()).getDesc());
        Building building = buildingRepository.queryByIdFromCache(ongoingSchedule.getBuildingId());
        properties.put("BuildingName", building == null ? "" : building.getName());
        List<String> rewardTypeList = Lists.newArrayList();
        List<String> rewardCountList = Lists.newArrayList();
        SettlementSnapshot settlementSnapshot = ongoingSchedule.getSettlementSnapshot();
        if (settlementSnapshot.getDimensionExp() > 0) {
            rewardTypeList.add(DimensionType.getByCode(settlementSnapshot.getDimensionType()).getDesc());
            rewardCountList.add(String.valueOf(Math.abs(settlementSnapshot.getDimensionExp())));
        }
        if (settlementSnapshot.getMoodChange() > 0) {
            rewardTypeList.add("心情值");
            rewardCountList.add(String.valueOf(settlementSnapshot.getMoodChange()));
        }
        if (settlementSnapshot.getSilverCoinCharge() > 0) {
            rewardTypeList.add("银币");
            rewardCountList.add(String.valueOf(settlementSnapshot.getSilverCoinCharge()));
        }
        if (settlementSnapshot.getTirednessChange() < 0) {
            rewardTypeList.add("疲劳值");
            rewardCountList.add(String.valueOf(settlementSnapshot.getTirednessChange()));
        }
        properties.put("RewardTypeList", rewardTypeList);
        properties.put("RewardCountList", rewardCountList);
        properties.put("SpendKKB", spendKKB);
        properties.put("SpendRecharge", spendRecharge);
        properties.put("ScheduleTime", (ongoingSchedule.getEndTime() - ongoingSchedule.getStartTime()) / 1000 / 60);
        saComponent.uploadEventData(userId, SaComponent.ROLE_SCHEDULE, properties);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RpcResult<CompleteScheduleModel> completeSchedule(ClientInfo clientInfo, int userRoleScheduleId) {
        int userId = clientInfo.getUserId();
        UserRoleOngoingSchedule ongoingSchedule = userRoleOngoingScheduleRepository.queryById(userRoleScheduleId);
        if (ongoingSchedule == null || ongoingSchedule.getUserId() != userId) {
            return RpcResult.result(RoleGameResponse.SCHEDULE_NOT_EXIST);
        }
        long currentTime = System.currentTimeMillis();
        if (currentTime < ongoingSchedule.getEndTime()) {
            return RpcResult.result(RoleGameResponse.ONGOING_SCHEDULE_NOT_OVER);
        }
        BizResult<CompleteScheduleModel> result = userScheduleComponent.completeSchedule(clientInfo, ongoingSchedule, false);
        // 上传埋点
        trackingData(userId, ongoingSchedule, ScheduleActionType.COMPLETE.getDesc(), 0, 0);
        return RpcResult.result(result.getData(), result.getCode(), result.getMessage());
    }

    @Override
    public RpcResult<CompleteAllScheduleModel> completeAllSchedule(ClientInfo clientInfo) {
        long currentTime = System.currentTimeMillis();
        final List<UserRoleOngoingSchedule> userRoleOngoingSchedules = userRoleOngoingScheduleRepository.queryByUserId(clientInfo.getUserId());
        List<UserRoleOngoingSchedule> ongoingSchedules = userRoleOngoingSchedules.stream()
                .filter(e -> e.getEndTime() <= currentTime)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ongoingSchedules)) {
            return RpcResult.result(RoleGameResponse.ONGOING_SCHEDULE_NOT_OVER);
        }
        BizResult<CompleteAllScheduleModel> result = userScheduleComponent.completeSchedule(clientInfo, ongoingSchedules);
        ongoingSchedules.forEach(e -> trackingData(clientInfo.getUserId(), e, ScheduleActionType.COMPLETE.getDesc(), 0, 0));
        log.debug("completeAllSchedule result:{}, exhaust:{}ms", result, System.currentTimeMillis() - currentTime);
        return RpcResult.result(result.getData(), result.getCode(), result.getMessage());
    }

    @Override
    @Deprecated
    public RpcResult<ScheduleListModel> list(int userId) {
        final List<Schedule> schedules = scheduleRepository.queryAll();
        final List<Building> buildings = buildingRepository.queryAllFromCache();
        final List<BuildingArea> buildingAreas = buildingAreaRepository.queryAllFromCache();
        final Map<Integer, BuildingArea> buildingAreaMap = buildingAreas.stream().collect(Collectors.toMap(BuildingArea::getId, Function.identity()));
        final Map<Integer, Building> buildingMap = buildings.stream().collect(Collectors.toMap(Building::getId, Function.identity()));
        ScheduleListModel scheduleListModel = new ScheduleListModel();
        List<ScheduleListModel.ScheduleModel> scheduleRoleModels = Lists.newArrayList();
        for (Schedule schedule : schedules) {
            final int type = schedule.getType();
            ScheduleType scheduleType = ScheduleType.getByType(type);
            if (scheduleType == null) {
                log.error("schedule type not found. scheduleId:{}", schedule.getId());
                continue;
            }
            ScheduleListModel.ScheduleModel scheduleRoleModel = new ScheduleListModel.ScheduleModel();
            scheduleRoleModel.setId(schedule.getId());
            scheduleRoleModels.add(scheduleRoleModel);
            scheduleRoleModel.setType(scheduleType.getType());
            scheduleRoleModel.setTypeName(scheduleType.getDesc());
            final int areaId = schedule.getAreaId();
            final BuildingArea buildingArea = buildingAreaMap.get(areaId);
            if (buildingArea != null) {
                scheduleRoleModel.setAreaName(buildingArea.getName());
            }
            scheduleRoleModel.setBuildingId(schedule.getBuildingId());
            scheduleRoleModel.setRequiredMinutes(schedule.getConfig().getConsumeMinute());
            List<ScheduleListModel.ScheduleModel.VariationModel> variationModels = Lists.newArrayList();
            ScheduleListModel.ScheduleModel.VariationModel energyVariation = new ScheduleListModel.ScheduleModel.VariationModel().setName(
                    ScheduleVariationType.ENERGY.getDesc()).setType(ScheduleVariationType.ENERGY.getType()).setValue(schedule.getConfig().getConsumeEnergy());
            variationModels.add(energyVariation);
            final int dimensionType = buildingMap.get(schedule.getBuildingId()).getDimensionType();
            if (dimensionType != 0) {
                final ScheduleVariationType byDimensionType = ScheduleVariationType.getByDimensionType(dimensionType);
                ScheduleListModel.ScheduleModel.VariationModel dimensionVariation = new ScheduleListModel.ScheduleModel.VariationModel().setName(
                        byDimensionType.getDesc()).setType(byDimensionType.getType()).setValue(schedule.getConfig().getDimensionExp());
                variationModels.add(dimensionVariation);
            }
            ScheduleListModel.ScheduleModel.VariationModel silverCoinVariation = new ScheduleListModel.ScheduleModel.VariationModel().setName(
                            ScheduleVariationType.SILVER_COIN.getDesc())
                    .setType(ScheduleVariationType.SILVER_COIN.getType())
                    .setValue(schedule.getConfig().getSilverCoinChange());
            variationModels.add(silverCoinVariation);
            scheduleRoleModel.setVariationModels(variationModels);
            scheduleRoleModel.setName(schedule.getName());
            log.debug("schedule list variationModels:{}", variationModels);
        }
        scheduleListModel.setScheduleRoleModels(scheduleRoleModels);
        List<ScheduleListModel.BuildingVariationModel> buildingVariationModels = Lists.newArrayList();
        scheduleListModel.setBuildingVariationModels(buildingVariationModels);

        final List<RoleBuildingScheduleConfig> roleBuildingScheduleConfigs = roleBuildingScheduleConfigRepository.queryAll();
        final Map<Integer, List<RoleBuildingScheduleConfig>> buildingIdToRoleConfigs = roleBuildingScheduleConfigs.stream()
                .collect(Collectors.groupingBy(RoleBuildingScheduleConfig::getBuildingId));
        for (Building building : buildings) {
            int buildingId = building.getId();
            final List<RoleBuildingScheduleConfig> roleBuildingScheduleConfigsList = buildingIdToRoleConfigs.get(buildingId);
            ScheduleListModel.BuildingVariationModel buildingVariationModel = new ScheduleListModel.BuildingVariationModel();
            List<ScheduleListModel.BuildingVariationModel.VariationInfoModel> variationInfos = new ArrayList<>();
            buildingVariationModel.setVariationInfos(variationInfos);
            buildingVariationModel.setBuildingId(buildingId);
            buildingVariationModel.setBuildingName(building.getName());
            buildingVariationModel.setOrderNum(building.getOrderNum());
            if (roleBuildingScheduleConfigsList != null) {
                for (RoleBuildingScheduleConfig roleBuildingScheduleConfig : roleBuildingScheduleConfigsList) {
                    ScheduleListModel.BuildingVariationModel.VariationInfoModel variationInfo = new ScheduleListModel.BuildingVariationModel.VariationInfoModel();
                    variationInfo.setRoleId(roleBuildingScheduleConfig.getRoleId());
                    List<ScheduleListModel.BuildingVariationModel.VariationInfoModel.VariationModel> variationModels = Lists.newArrayList();
                    for (RoleBuildingScheduleConfig.ScheduleTirednessAndMood config : roleBuildingScheduleConfig.getConfigs()) {
                        if (config.getMood() != 0) {
                            ScheduleListModel.BuildingVariationModel.VariationInfoModel.VariationModel variationModel = new ScheduleListModel.BuildingVariationModel.VariationInfoModel.VariationModel();
                            final int scheduleType = config.getScheduleType();
                            variationModel.setScheduleType(scheduleType);
                            variationModel.setScheduleTypeName(ScheduleType.getByType(scheduleType).getDesc());
                            variationModel.setPropertyType(ScheduleVariationType.MOOD.getType());
                            variationModel.setPropertyTypeName(ScheduleVariationType.MOOD.getDesc());
                            variationModel.setValue(config.getMood());
                            variationModels.add(variationModel);
                        }
                        if (config.getTiredness() != 0) {
                            ScheduleListModel.BuildingVariationModel.VariationInfoModel.VariationModel variationModel = new ScheduleListModel.BuildingVariationModel.VariationInfoModel.VariationModel();
                            final int scheduleType = config.getScheduleType();
                            variationModel.setScheduleType(scheduleType);
                            variationModel.setScheduleTypeName(ScheduleType.getByType(scheduleType).getDesc());
                            variationModel.setPropertyType(ScheduleVariationType.TIREDNESS.getType());
                            variationModel.setPropertyTypeName(ScheduleVariationType.TIREDNESS.getDesc());
                            variationModel.setValue(config.getTiredness());
                            variationModels.add(variationModel);
                        }
                    }
                    variationInfo.setVariationModels(variationModels);
                    variationInfos.add(variationInfo);
                }
            }
            buildingVariationModels.add(buildingVariationModel);
        }
        return RpcResult.success(scheduleListModel);
    }

    @Override
    public RpcResult<ScheduleListModel> listV2() {
        final List<Schedule> schedules = scheduleRepository.queryAll();
        final List<Building> buildings = buildingRepository.queryAllFromCache();
        final List<BuildingArea> buildingAreas = buildingAreaRepository.queryAllFromCache();
        final Map<Integer, BuildingArea> buildingAreaMap = buildingAreas.stream().collect(Collectors.toMap(BuildingArea::getId, Function.identity()));
        final Map<Integer, Building> buildingMap = buildings.stream().collect(Collectors.toMap(Building::getId, Function.identity()));
        ScheduleListModel scheduleListModel = new ScheduleListModel();
        List<ScheduleListModel.ScheduleModel> scheduleRoleModels = Lists.newArrayList();
        for (Schedule schedule : schedules) {
            final int type = schedule.getType();
            ScheduleType scheduleType = ScheduleType.getByType(type);
            if (scheduleType == null) {
                log.error("schedule type not found. scheduleId:{}", schedule.getId());
                continue;
            }
            ScheduleListModel.ScheduleModel scheduleRoleModel = new ScheduleListModel.ScheduleModel();
            scheduleRoleModel.setId(schedule.getId());
            scheduleRoleModels.add(scheduleRoleModel);
            scheduleRoleModel.setType(scheduleType.getType());
            scheduleRoleModel.setTypeName(scheduleType.getDesc());
            final int areaId = schedule.getAreaId();
            final BuildingArea buildingArea = buildingAreaMap.get(areaId);
            if (buildingArea != null) {
                scheduleRoleModel.setAreaName(buildingArea.getName());
            }
            scheduleRoleModel.setBuildingId(schedule.getBuildingId());
            scheduleRoleModel.setRequiredMinutes(schedule.getConfig().getConsumeMinute());
            List<ScheduleListModel.ScheduleModel.VariationModel> variationModels = Lists.newArrayList();
            ScheduleListModel.ScheduleModel.VariationModel energyVariation = new ScheduleListModel.ScheduleModel.VariationModel().setName(
                            ScheduleVariationType.ENERGY.getDesc())
                    .setType(ScheduleVariationType.ENERGY.getType())
                    .setValue(schedule.getConfig().getConsumeEnergyPerPeriod());
            variationModels.add(energyVariation);
            final int dimensionType = buildingMap.get(schedule.getBuildingId()).getDimensionType();
            if (dimensionType != 0) {
                final ScheduleVariationType byDimensionType = ScheduleVariationType.getByDimensionType(dimensionType);
                ScheduleListModel.ScheduleModel.VariationModel dimensionVariation = new ScheduleListModel.ScheduleModel.VariationModel().setName(
                        byDimensionType.getDesc()).setType(byDimensionType.getType()).setValue(schedule.getConfig().getDimensionExpPerPeriod());
                variationModels.add(dimensionVariation);
            }
            ScheduleListModel.ScheduleModel.VariationModel silverCoinVariation = new ScheduleListModel.ScheduleModel.VariationModel().setName(
                            ScheduleVariationType.SILVER_COIN.getDesc())
                    .setType(ScheduleVariationType.SILVER_COIN.getType())
                    .setValue(schedule.getConfig().getSilverCoinChangePerPeriod());
            variationModels.add(silverCoinVariation);
            scheduleRoleModel.setVariationModels(variationModels);
            scheduleRoleModel.setName(schedule.getName());
            log.debug("schedule list variationModels:{}", variationModels);
        }
        scheduleListModel.setScheduleRoleModels(scheduleRoleModels);
        List<ScheduleListModel.BuildingVariationModel> buildingVariationModels = Lists.newArrayList();
        scheduleListModel.setBuildingVariationModels(buildingVariationModels);

        final List<RoleBuildingScheduleConfig> roleBuildingScheduleConfigs = roleBuildingScheduleConfigRepository.queryAll();
        final Map<Integer, List<RoleBuildingScheduleConfig>> buildingIdToRoleConfigs = roleBuildingScheduleConfigs.stream()
                .collect(Collectors.groupingBy(RoleBuildingScheduleConfig::getBuildingId));
        for (Building building : buildings) {
            int buildingId = building.getId();
            final List<RoleBuildingScheduleConfig> roleBuildingScheduleConfigsList = buildingIdToRoleConfigs.get(buildingId);
            ScheduleListModel.BuildingVariationModel buildingVariationModel = new ScheduleListModel.BuildingVariationModel();
            List<ScheduleListModel.BuildingVariationModel.VariationInfoModel> variationInfos = new ArrayList<>();
            buildingVariationModel.setVariationInfos(variationInfos);
            buildingVariationModel.setBuildingId(buildingId);
            buildingVariationModel.setBuildingName(building.getName());
            buildingVariationModel.setOrderNum(building.getOrderNum());
            if (roleBuildingScheduleConfigsList != null) {
                for (RoleBuildingScheduleConfig roleBuildingScheduleConfig : roleBuildingScheduleConfigsList) {
                    ScheduleListModel.BuildingVariationModel.VariationInfoModel variationInfo = new ScheduleListModel.BuildingVariationModel.VariationInfoModel();
                    variationInfo.setRoleId(roleBuildingScheduleConfig.getRoleId());
                    List<ScheduleListModel.BuildingVariationModel.VariationInfoModel.VariationModel> variationModels = Lists.newArrayList();
                    for (RoleBuildingScheduleConfig.ScheduleTirednessAndMood config : roleBuildingScheduleConfig.getConfigs()) {
                        if (config.getMoodPerPeriod() != 0) {
                            ScheduleListModel.BuildingVariationModel.VariationInfoModel.VariationModel variationModel = new ScheduleListModel.BuildingVariationModel.VariationInfoModel.VariationModel();
                            final int scheduleType = config.getScheduleType();
                            variationModel.setScheduleType(scheduleType);
                            variationModel.setScheduleTypeName(ScheduleType.getByType(scheduleType).getDesc());
                            variationModel.setPropertyType(ScheduleVariationType.MOOD.getType());
                            variationModel.setPropertyTypeName(ScheduleVariationType.MOOD.getDesc());
                            variationModel.setValue(config.getMoodPerPeriod());
                            variationModels.add(variationModel);
                        }
                        if (config.getTirednessPerPeriod() != 0) {
                            ScheduleListModel.BuildingVariationModel.VariationInfoModel.VariationModel variationModel = new ScheduleListModel.BuildingVariationModel.VariationInfoModel.VariationModel();
                            final int scheduleType = config.getScheduleType();
                            variationModel.setScheduleType(scheduleType);
                            variationModel.setScheduleTypeName(ScheduleType.getByType(scheduleType).getDesc());
                            variationModel.setPropertyType(ScheduleVariationType.TIREDNESS.getType());
                            variationModel.setPropertyTypeName(ScheduleVariationType.TIREDNESS.getDesc());
                            variationModel.setValue(config.getTirednessPerPeriod());
                            variationModels.add(variationModel);
                        }
                    }
                    variationInfo.setVariationModels(variationModels);
                    variationInfos.add(variationInfo);
                }
            }
            buildingVariationModels.add(buildingVariationModel);
        }
        return RpcResult.success(scheduleListModel);
    }

    @Override
    public RpcResult<ScheduleListRoleModel> listRole(int userId, Integer roleId) {
        ScheduleListRoleModel scheduleListRoleModel = new ScheduleListRoleModel();
        final List<Role> roles = roleRepository.queryAllAvailableRoles(userId);
        List<Integer> roleIds = Lists.transform(roles, Role::getId);
        Map<Integer, Role> roleMap = roles.stream().collect(Collectors.toMap(Role::getId, Function.identity()));
        final List<UserRole> userRoles = userRoleRepository.queryUserRoleFromCache(userId);
        final List<UserRoleProperty> userRoleProperties = userRolePropertyRepository.queryByUserId(userId);
        final Map<Integer, UserRoleProperty> userRolePropertyMap = userRoleProperties.stream()
                .collect(Collectors.toMap(UserRoleProperty::getRoleId, Function.identity()));
        final List<UserRoleDimension> userRoleDimensions = userRoleDimensionRepository.queryByUserId(userId);
        final Map<Integer, List<UserRoleDimension>> roleIdToUserRoleDimensionMap = userRoleDimensions.stream()
                .collect(Collectors.groupingBy(UserRoleDimension::getRoleId));
        List<RoleGroupRelation> roleGroupRelations = roleGroupRelationRepository.queryAll();
        Map<Integer, RoleGroupRelation> roleGroupRelationMap = FunctionUtils.toMap(roleGroupRelations, RoleGroupRelation::getRoleId);
        List<Integer> groupIds = ListUtils.emptyIfNull(roleGroupRelations)
                .stream()
                .filter(e -> roleIds.contains(e.getRoleId()))
                .map(RoleGroupRelation::getRoleGroupId)
                .distinct()
                .collect(Collectors.toList());
        Map<Integer, RoleGroup> roleGroupMap = roleGroupRepository.queryByIds(groupIds);
        List<ScheduleListRoleModel.RoleModel> roleModels = Lists.newArrayList();
        for (UserRole userRole : userRoles) {
            ScheduleListRoleModel.RoleModel roleModel = new ScheduleListRoleModel.RoleModel();
            Role role = roleMap.get(userRole.getRoleId());
            if (role == null) {
                continue;
            }
            UserRoleProperty userRoleProperty = userRolePropertyMap.get(userRole.getRoleId());
            final List<UserRoleDimension> userRoleDimensionsByRoleId = roleIdToUserRoleDimensionMap.get(userRole.getRoleId());
            if (CollectionUtils.isEmpty(userRoleDimensionsByRoleId)) {
                continue;
            }
            final Map<Integer, UserRoleDimension> dimensionMap = userRoleDimensionsByRoleId.stream()
                    .collect(Collectors.toMap(UserRoleDimension::getDimensionType, Function.identity()));
            roleModel.setId(role.getId());
            roleModel.setName(role.getName());
            roleModel.setAvatar(role.getAvatar());
            if (userRoleProperty != null) {
                roleModel.setLevel(userRoleProperty.getRoleLevel());
                roleModel.setMood(userRoleProperty.getMood());
                final MoodStatus moodStatus = kvConfigComponent.getMoodStatus(userRoleProperty.getMood());
                roleModel.setMoodDesc(moodStatus == null ? StringUtils.EMPTY : moodStatus.getDesc());
                roleModel.setMoodType(moodStatus == null ? 0 : moodStatus.getCode());
                roleModel.setTiredness(userRoleProperty.getTiredness());
                final TirednessStatus tirednessStatus = kvConfigComponent.getTirednessStatus(userRoleProperty.getTiredness());
                roleModel.setTirednessDesc(tirednessStatus == null ? StringUtils.EMPTY : tirednessStatus.getDesc());
                roleModel.setTirednessType(tirednessStatus == null ? 0 : tirednessStatus.getCode());
                roleModel.setEnergy(userRoleProperty.getEnergy());
            }
            roleModel.setArtLevel(dimensionMap.get(DimensionType.ART.getType()).getLevel());
            roleModel.setHouseworkLevel(dimensionMap.get(DimensionType.HOUSEWORK.getType()).getLevel());
            roleModel.setSportLevel(dimensionMap.get(DimensionType.SPORT.getType()).getLevel());
            roleModel.setCultureLevel(dimensionMap.get(DimensionType.CULTURE.getType()).getLevel());
            roleModel.setSocialLevel(dimensionMap.get(DimensionType.SOCIAL.getType()).getLevel());
            roleModel.setAdoptionTime(userRole.getCreatedAt().getTime());

            RoleGroupRelation groupRelation = roleGroupRelationMap.get(role.getId());
            if (groupRelation != null) {
                RoleGroup roleGroup = roleGroupMap.get(groupRelation.getRoleGroupId());
                if (roleGroup != null) {
                    if (groupRelation.getOrderNum() % 2 == 1) {
                        roleModel.setOrderNum(roleGroup.getOrderNum() * 2);
                    } else {
                        roleModel.setOrderNum(roleGroup.getOrderNum() * 2 + 1);
                    }
                }
            }
            roleModels.add(roleModel);
        }
        if (roleId != null) {
            roleModels = roleModels.stream().filter(e -> e.getId() == roleId).collect(Collectors.toList());
        }
        scheduleListRoleModel.setRoles(roleModels);
        return RpcResult.success(scheduleListRoleModel);
    }

    @Override
    public RpcResult<OngoingScheduleListModel> ongoingList(int userId) {
        OngoingScheduleListModel ongoingScheduleListModel = new OngoingScheduleListModel();
        final List<UserRoleOngoingSchedule> ongoingSchedules = userScheduleComponent.queryOnlineRoleSchedules(userId);
        final List<Building> buildings = buildingRepository.queryAllFromCache();
        final Map<Integer, Building> buildingMap = buildings.stream().collect(Collectors.toMap(Building::getId, Function.identity()));
        List<OngoingScheduleListModel.OngoingScheduleModel> ongoingScheduleModels = Lists.newArrayList();
        ongoingScheduleListModel.setOngoingScheduleModels(ongoingScheduleModels);
        final Map<Integer, List<UserRoleOngoingSchedule>> scheduleIdMap = ongoingSchedules.stream()
                .collect(Collectors.groupingBy(UserRoleOngoingSchedule::getScheduleId, Collectors.toList()));
        for (UserRoleOngoingSchedule ongoingSchedule : ongoingSchedules) {
            OngoingScheduleListModel.OngoingScheduleModel ongoingScheduleModel = new OngoingScheduleListModel.OngoingScheduleModel();
            ongoingScheduleModel.setFinishTime(ongoingSchedule.getEndTime());
            ongoingScheduleModel.setRoleId(ongoingSchedule.getRoleId());
            ongoingScheduleModel.setScheduleId(ongoingSchedule.getScheduleId());
            ongoingScheduleModel.setUserScheduleId(ongoingSchedule.getId());
            final Pair<Integer, Integer> canPickUpPair = computeCurrentCoinsAndExpsCanPickUp(ongoingSchedule, buildingMap.get(ongoingSchedule.getBuildingId()));
            int canPickUpCoins = canPickUpPair.getLeft();
            int canPickUpExps = canPickUpPair.getRight();
            ongoingScheduleModel.setCanPickUpCoins(canPickUpCoins);
            ongoingScheduleModel.setCanPickUpExps(canPickUpExps);
            ongoingScheduleModel.setStartTime(ongoingSchedule.getStartTime());
            ongoingScheduleModel.setPickUpCoins(ongoingSchedule.getSettlementSnapshot().getPickUpCoins());
            ongoingScheduleModel.setPickUpExps(ongoingSchedule.getSettlementSnapshot().getPickUpExps());
            ongoingScheduleModel.setTotalCoins(ongoingSchedule.getSettlementSnapshot().getSilverCoinCharge());
            ongoingScheduleModel.setTotalExps(ongoingSchedule.getSettlementSnapshot().getDimensionExp());
            ongoingScheduleModel.setCoinsPerPeriod(ongoingSchedule.getSettlementSnapshot().getActualCoinChangePerPeriod());
            ongoingScheduleModel.setExpsPerPeriod(ongoingSchedule.getSettlementSnapshot().getActualExpChangePerPeriod());
            setOrderNum(ongoingSchedule, ongoingScheduleModel, scheduleIdMap);
            ongoingScheduleModels.add(ongoingScheduleModel);
        }
        return RpcResult.success(ongoingScheduleListModel);
    }

    @Override
    public RpcResult<ScheduleGiftConfigList> rewardGear() {
        final KeyValueConfig keyValueConfig = keyValueConfigRepository.queryByKey(KeyValueConfigKeys.SCHEDULE_GIFT_CONFIG);
        final List<ScheduleGiftConfig> scheduleGiftConfigs = JsonUtils.findList(keyValueConfig.getValue(), ScheduleGiftConfig.class);
        ScheduleGiftConfigList scheduleGiftConfigList = new ScheduleGiftConfigList();
        scheduleGiftConfigList.setGearConfigs(scheduleGiftConfigs);
        return RpcResult.success(scheduleGiftConfigList);
    }

    @Override
    public RpcResult<ScheduleAnimationInfoModel> animationInfo(int scheduleId) {
        Schedule schedule = scheduleRepository.queryByIdFromCache(scheduleId);
        if (schedule == null) {
            return RpcResult.result(RoleGameResponse.SCHEDULE_NOT_EXIST);
        }
        List<Integer> allNpcElementIds = Optional.ofNullable(schedule.getConfig()).map(Schedule.Config::getNpcElementIds).orElse(new ArrayList<>());
        Map<Integer, MapElementModel> npcElementMap = mapElementRepository.selectMapElementByIds(allNpcElementIds)
                .stream()
                .map(item -> mapElementConverter.toMapElementModel(item))
                .collect(Collectors.toMap(MapElementModel::getId, item -> item));
        ScheduleAnimationInfoModel scheduleAnimationInfoModel = ScheduleAnimationInfoModel.valueOf(scheduleId, schedule.getConfig(), npcElementMap);
        return RpcResult.success(scheduleAnimationInfoModel);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RpcResult<Void> pickUpCoins(int userId, int userRoleScheduleId, int count) {
        UserRoleOngoingSchedule ongoingSchedule = userRoleOngoingScheduleRepository.queryByIdFromDB(userRoleScheduleId);
        if (ongoingSchedule == null || ongoingSchedule.getUserId() != userId) {
            return RpcResult.result(RoleGameResponse.SCHEDULE_NOT_EXIST);
        }
        Building building = buildingRepository.queryByIdFromCache(ongoingSchedule.getBuildingId());
        if (building == null) {
            return RpcResult.result(RoleGameResponse.BUILDING_NOT_EXIST);
        }
        Pair<Integer, Integer> coinExpPair = computeCurrentCoinsAndExpsCanPickUp(ongoingSchedule, building);
        int coinsCanPickUp = coinExpPair.getLeft();
        if (count > coinsCanPickUp) {
            return RpcResult.result(RoleGameResponse.SILVER_COIN_NOT_ENOUGH);
        }
        SettlementSnapshot settlementSnapshot = ongoingSchedule.getSettlementSnapshot();
        String orderId = String.valueOf(BufferedIdGenerator.getId());
        BizResult<Void> coinChargeResult = userSilverCoinAccountComponent.chargeSilverCoin(userId, orderId, count,
                SilverCoinChargeSourceEnum.PICK_IN_SCHEDULE.getCode());
        if (!coinChargeResult.isSuccess()) {
            return RpcResult.result(RoleGameResponse.SILVER_COIN_PICK_UP_FAIL);
        }

        settlementSnapshot.setPickUpCoins(settlementSnapshot.getPickUpCoins() + count);
        userRoleOngoingScheduleRepository.updateOngoingScheduleSettlement(userRoleScheduleId, userId, settlementSnapshot);
        return RpcResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RpcResult<ScheduleSettlementModel> pickUpExps(ClientInfo clientInfo, int userRoleScheduleId, int count) {
        int userId = clientInfo.getUserId();
        if (count <= 0) {
            return RpcResult.success();
        }
        UserRoleOngoingSchedule ongoingSchedule = userRoleOngoingScheduleRepository.queryByIdFromDB(userRoleScheduleId);
        if (ongoingSchedule == null || ongoingSchedule.getUserId() != userId) {
            return RpcResult.result(RoleGameResponse.SCHEDULE_NOT_EXIST);
        }
        Building building = buildingRepository.queryByIdFromCache(ongoingSchedule.getBuildingId());
        if (building == null) {
            return RpcResult.result(RoleGameResponse.BUILDING_NOT_EXIST);
        }
        if (building.getType() != BuildingType.MAIN_BUILDING.getCode()) {
            return RpcResult.result(RoleGameResponse.NOT_MAIN_TYPE_SCHEDULE);
        }
        Pair<Integer, Integer> coinExpPair = computeCurrentCoinsAndExpsCanPickUp(ongoingSchedule, building);
        int expsCanPickUp = coinExpPair.getRight();
        if (expsCanPickUp < count) {
            return RpcResult.result(RoleGameResponse.PICK_COUNT_NOT_ENOUGH);
        }
        int roleId = ongoingSchedule.getRoleId();
        LevelUpModel roleLevelUpModel = roleComponent.handleRoleLevelUp(clientInfo, roleId, count);
        if (roleLevelUpModel == null) {
            throw new RuntimeException("角色升级失败");
        }
        SettlementSnapshot settlementSnapshot = ongoingSchedule.getSettlementSnapshot();
        settlementSnapshot.setPickUpExps(settlementSnapshot.getPickUpExps() + count);
        userRoleOngoingScheduleRepository.updateOngoingScheduleSettlement(userRoleScheduleId, userId, settlementSnapshot);

        DimensionLevelUpModel dimensionLevelUpModel = roleComponent.handleRoleDimensionLevelUp(userId, roleId, settlementSnapshot.getDimensionType(), count);
        if (dimensionLevelUpModel == null) {
            throw new RuntimeException("角色维度升级失败");
        }
        List<UnlockStoryModel> unlockAvgStoryModels = userScheduleComponent.unlockStory(userId, roleId, roleLevelUpModel, dimensionLevelUpModel);

        ScheduleSettlementModel model = new ScheduleSettlementModel().setLevelUpModel(roleLevelUpModel)
                .setDimensionLevelUpModel(dimensionLevelUpModel)
                .setUnlockStoryModelList(unlockAvgStoryModels);
        return RpcResult.success(model);
    }

    private List<UserRoleOngoingSchedule> buildUserOngoingSchedules(int scheduleDurationSecond, List<UserRoleProperty> userRoleProperties, Schedule schedule,
                                                                    Building building, List<UserRoleOngoingSchedule> existOngoingSchedules) {
        if (CollectionUtils.isEmpty(userRoleProperties)) {
            return Lists.newArrayList();
        }
        List<UserRoleOngoingSchedule> userRoleOngoingSchedules = Lists.newArrayList();
        for (UserRoleProperty userRoleProperty : userRoleProperties) {
            Pair<ConsumptionSnapshot, SettlementSnapshot> snapshotPair = buildConsumptionSettlementSnapshot(scheduleDurationSecond, userRoleProperty, schedule,
                    building);
            UserRoleOngoingSchedule userRoleOngoingSchedule = new UserRoleOngoingSchedule().setUserId(userRoleProperty.getUserId())
                    .setScheduleId(schedule.getId())
                    .setRoleId(userRoleProperty.getRoleId())
                    .setBuildingId(schedule.getBuildingId())
                    .setStartTime(System.currentTimeMillis())
                    .setEndTime(System.currentTimeMillis() + TimeUnit.SECONDS.toMillis(scheduleDurationSecond))
                    .setConsumptionSnapshot(snapshotPair.getLeft())
                    .setSettlementSnapshot(snapshotPair.getRight());
            int orderNum = getScheduleAvailableOrderNum(existOngoingSchedules);
            UserRoleOngoingSchedule.ExtraInfo extraInfo = new UserRoleOngoingSchedule.ExtraInfo().setOrderNum(orderNum);
            userRoleOngoingSchedule.setExtraInfo(extraInfo);
            existOngoingSchedules.add(userRoleOngoingSchedule);
            userRoleOngoingSchedules.add(userRoleOngoingSchedule);
        }
        return userRoleOngoingSchedules;
    }

    // 构建消耗和结算的快照，里面的相关值会根据心情和疲劳状态做计算
    private Pair<ConsumptionSnapshot, SettlementSnapshot> buildConsumptionSettlementSnapshot(int scheduleDurationSecond, UserRoleProperty userRoleProperty,
                                                                                             Schedule schedule, Building building) {
        int roleId = userRoleProperty.getRoleId();
        RoleBuildingScheduleConfig roleBuildingScheduleConfig = roleBuildingScheduleConfigRepository.queryByRoleIdAndBuildingIdFromCache(roleId,
                schedule.getBuildingId());
        List<RoleBuildingScheduleConfig.ScheduleTirednessAndMood> tirednessScheduleConfigs = roleBuildingScheduleConfig == null
                ? Lists.newArrayList()
                : roleBuildingScheduleConfig.getConfigs();
        int periodCount = scheduleDurationSecond / SECOND_OF_PERIOD;
        float tirednessChangePerPeriod = tirednessScheduleConfigs.stream()
                .filter(e -> e.getScheduleType() == schedule.getType())
                .map(RoleBuildingScheduleConfig.ScheduleTirednessAndMood::getTirednessPerPeriod)
                .findFirst()
                .orElse(0f);
        int tirednessChange = Math.round(tirednessChangePerPeriod * periodCount);
        float moodChangePerPeriod = tirednessScheduleConfigs.stream()
                .filter(e -> e.getScheduleType() == schedule.getType())
                .map(RoleBuildingScheduleConfig.ScheduleTirednessAndMood::getMoodPerPeriod)
                .findFirst()
                .orElse(0f);
        int moodChange = Math.round(moodChangePerPeriod * periodCount);
        RoleCommonPropertyConfig roleCommonConfig = roleComponent.getRoleCommonConfig();
        TirednessStatus tirednessStatus = kvConfigComponent.getTirednessStatus(userRoleProperty.getTiredness());
        MoodStatus moodStatus = kvConfigComponent.getMoodStatus(userRoleProperty.getMood());

        float actualCoinPerPeriod = userScheduleComponent.getActualSilverCoinPerPeriod(roleCommonConfig, moodStatus.getCode(),
                schedule.getConfig().getSilverCoinChangePerPeriod());
        int silverCoinChange = userScheduleComponent.computeSilverCoin(roleCommonConfig, moodStatus.getCode(),
                schedule.getConfig().getSilverCoinChangePerPeriod(), periodCount);
        float actualExpPerPeriod = userScheduleComponent.getActualExpPerPeriod(roleCommonConfig, building.getDimensionType(), moodStatus.getCode(),
                schedule.getConfig().getDimensionExpPerPeriod());
        int dimensionExp = userScheduleComponent.computeDimensionExp(roleCommonConfig, building.getDimensionType(), moodStatus.getCode(),
                schedule.getConfig().getDimensionExpPerPeriod(), periodCount);
        SettlementSnapshot settlementSnapshot = new SettlementSnapshot().setDimensionType(building.getDimensionType())
                .setDimensionExp(dimensionExp)
                .setSilverCoinCharge(silverCoinChange > 0 ? silverCoinChange : 0)
                .setMoodChange(moodChange)
                .setTirednessChange(tirednessChange)
                .setMoodStatus(moodStatus.getCode())
                .setTirednessStatus(tirednessStatus.getCode())
                .setPickUpCoins(0)
                .setPickUpExps(0)
                .setCoinChangePerPeriod(schedule.getConfig().getSilverCoinChangePerPeriod())
                .setExpChangePerPeriod(schedule.getConfig().getDimensionExpPerPeriod())
                .setTirednessChangePerPeriod(tirednessChangePerPeriod)
                .setMoodChangePerPeriod(moodChangePerPeriod)
                .setEnergyChangePerPeriod(schedule.getConfig().getConsumeEnergyPerPeriod())
                .setActualCoinChangePerPeriod(actualCoinPerPeriod)
                .setActualExpChangePerPeriod(actualExpPerPeriod);
        int energyCost = userScheduleComponent.computeEnergyCost(roleCommonConfig, tirednessStatus.getCode(), schedule.getConfig().getConsumeEnergyPerPeriod(),
                periodCount);
        ConsumptionSnapshot consumptionSnapshot = new ConsumptionSnapshot().setSilverCoinCost(silverCoinChange < 0 ? Math.abs(silverCoinChange) : 0)
                .setEnergyCost(energyCost)
                .setMoodStatus(moodStatus.getCode())
                .setTirednessStatus(tirednessStatus.getCode());
        return Pair.of(consumptionSnapshot, settlementSnapshot);
    }

    /**
     * 计算在进行中的日程，此时可以拾取的银币和经验
     */
    private Pair<Integer, Integer> computeCurrentCoinsAndExpsCanPickUp(UserRoleOngoingSchedule userRoleOngoingSchedule, Building building) {
        RoleCommonPropertyConfig commonConfig = roleComponent.getRoleCommonConfig();
        SettlementSnapshot settlementSnapshot = userRoleOngoingSchedule.getSettlementSnapshot();

        long currentTime = System.currentTimeMillis();
        long startTime = userRoleOngoingSchedule.getStartTime();
        int finishedPeriod = (int) TimeUnit.MILLISECONDS.toSeconds(Math.min(currentTime, userRoleOngoingSchedule.getEndTime()) - startTime) / SECOND_OF_PERIOD;

        int producedCoins = userScheduleComponent.computeSilverCoin(commonConfig, settlementSnapshot.getMoodStatus(),
                settlementSnapshot.getCoinChangePerPeriod(), finishedPeriod);
        int canPickUpCoins = Math.max(producedCoins - settlementSnapshot.getPickUpCoins(), 0);

        int producedExps = userScheduleComponent.computeDimensionExp(commonConfig, building.getDimensionType(), settlementSnapshot.getMoodStatus(),
                settlementSnapshot.getExpChangePerPeriod(), finishedPeriod);
        int canPickUpExps = Math.max(producedExps - settlementSnapshot.getPickUpExps(), 0);
        return Pair.of(canPickUpCoins, canPickUpExps);
    }

    private static void setOrderNum(UserRoleOngoingSchedule ongoingSchedule, OngoingScheduleListModel.OngoingScheduleModel ongoingScheduleModel,
                                    Map<Integer, List<UserRoleOngoingSchedule>> scheduleIdMap) {
        final UserRoleOngoingSchedule.ExtraInfo extraInfo = ongoingSchedule.getExtraInfo();
        if (extraInfo != null) {
            ongoingScheduleModel.setOrderNum(extraInfo.getOrderNum());
        } else {
            final List<UserRoleOngoingSchedule> otherUserRoleOngoingSchedules = scheduleIdMap.get(ongoingSchedule.getScheduleId())
                    .stream()
                    .filter(schedule -> schedule.getId() != ongoingSchedule.getId())
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(otherUserRoleOngoingSchedules)) {
                ongoingScheduleModel.setOrderNum(1);
            } else {
                final UserRoleOngoingSchedule.ExtraInfo otherExtraInfo = otherUserRoleOngoingSchedules.get(0).getExtraInfo();
                if (otherExtraInfo == null || otherExtraInfo.getOrderNum() == 1) {
                    ongoingScheduleModel.setOrderNum(2);
                } else {
                    ongoingScheduleModel.setOrderNum(1);
                }
            }
        }
    }

    @Override
    public RpcResult<ScheduleTimeLeveConfigList> getTimeLevelConfigs() {
        final KeyValueConfig keyValueConfig = keyValueConfigRepository.queryByKey(KeyValueConfigKeys.SCHEDULE_TIME_LEVEL_CONFIG);
        if (keyValueConfig == null) {
            return RpcResult.result(RoleGameResponse.SCHEDULE_TIME_LEVEL_CONFIG_NOT_FOUND);
        }
        return RpcResult.success(ScheduleTimeLeveConfigList.valueOf(keyValueConfig.getValue()));
    }

    @Override
    public RpcResult<CompleteScheduleBubbleModel> getCompleteBubble(int userId) {
        final List<UserRoleOngoingSchedule> userRoleOngoingSchedules = userRoleOngoingScheduleRepository.queryByUserId(userId)
                .stream()
                .filter(e -> e.getEndTime() <= System.currentTimeMillis())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userRoleOngoingSchedules)) {
            return RpcResult.success();
        }
        Map<Integer, Long> scheduleIdToEndTime = userRoleOngoingSchedules.stream()
                .collect(Collectors.toMap(UserRoleOngoingSchedule::getRoleId, UserRoleOngoingSchedule::getEndTime));
        final List<Role> roles = roleRepository.queryAllFromCache();
        final Map<Integer, Role> roleMap = roles.stream().collect(Collectors.toMap(Role::getId, Function.identity()));
        CompleteScheduleBubbleModel completeScheduleBubbleModel = new CompleteScheduleBubbleModel();
        List<CompleteScheduleBubbleModel.RoleInfo> roleInfos = Lists.newArrayList();
        for (UserRoleOngoingSchedule userRoleOngoingSchedule : userRoleOngoingSchedules) {
            Role role = roleMap.get(userRoleOngoingSchedule.getRoleId());
            if (role == null) {
                continue;
            }
            CompleteScheduleBubbleModel.RoleInfo roleInfo = new CompleteScheduleBubbleModel.RoleInfo();
            roleInfo.setRoleId(role.getId());
            roleInfo.setRoleName(role.getName());
            roleInfo.setRoleAvatarImage(role.getAvatar());
            roleInfo.setScheduleId(userRoleOngoingSchedule.getScheduleId());
            roleInfo.setUserScheduleId(userRoleOngoingSchedule.getId());
            roleInfos.add(roleInfo);
        }
        roleInfos.sort(Comparator.comparing(e -> scheduleIdToEndTime.get(e.getRoleId())));
        completeScheduleBubbleModel.setRoleInfos(roleInfos);
        return RpcResult.success(completeScheduleBubbleModel);
    }
}
