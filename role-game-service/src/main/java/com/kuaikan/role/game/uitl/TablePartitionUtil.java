package com.kuaikan.role.game.uitl;

import groovy.util.logging.Slf4j;

import com.kuaikan.common.config.Environment;
import com.kuaikan.common.config.Settings;
import com.kuaikan.role.game.api.enums.UserTableEnum;

/**
 * TablePartitionUtil
 *
 * <AUTHOR>
 * @since 2024-03-06
 */
@Slf4j
public class TablePartitionUtil {

    public static String getTableName(UserTableEnum tableEnum, int userId) {
        if (userId < 0) {
            throw new IllegalArgumentException(String.format("illegal uid %s", userId));
        }
        // 非线上环境分表为2个
        if (Settings.getEnvironment().le(Environment.PREVIEW)) {
            return String.format("%s%s", tableEnum.getPrefix(), (userId % 2));
        }

        return String.format("%s%s", tableEnum.getPrefix(), (userId % tableEnum.getCount()));
    }
}
