package com.kuaikan.role.game.repository;

import java.util.Date;
import java.util.List;
import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import com.kuaikan.role.game.api.bean.UserInteractiveItemFlow;
import com.kuaikan.role.game.api.enums.UserTableEnum;
import com.kuaikan.role.game.dao.rolegame.UserInteractiveItemFlowMapper;
import com.kuaikan.role.game.uitl.TablePartitionUtil;

/**
 * <AUTHOR>
 * @version 2024-09-11
 */
@Repository
public class UserInteractiveItemFlowRepository {

    @Resource
    private UserInteractiveItemFlowMapper userInteractiveItemFlowMapper;

    @Resource
    private UserInteractiveItemFlowMapper userInteractiveItemFlowMapperSlave;

    public int countByUserIdTimeRange(int userId, long startTime, long endTime, int type) {
        final String tableName = getTableName(userId);
        return userInteractiveItemFlowMapperSlave.countByUserIdTimeRange(tableName, userId, new Date(startTime), new Date(endTime), type);
    }

    public List<UserInteractiveItemFlow> queryByUserIdTimeRange(int userId, long startTime, long endTime, int type) {
        final String tableName = getTableName(userId);
        return userInteractiveItemFlowMapperSlave.queryByUserIdTimeRange(tableName, userId, new Date(startTime), new Date(endTime), type);
    }

    public int insert(UserInteractiveItemFlow record) {
        final String tableName = getTableName(record.getUserId());
        return userInteractiveItemFlowMapper.insert(tableName, record);
    }

    public UserInteractiveItemFlow queryByOrderId(int userId, int itemId, String thirdId, int source, int type) {
        final String tableName = getTableName(userId);
        return userInteractiveItemFlowMapperSlave.queryByItemIdAndThirdId(tableName, itemId, thirdId, source, type);
    }

    private String getTableName(int userId) {
        return TablePartitionUtil.getTableName(UserTableEnum.USER_INTERACTIVE_ITEM_FLOW, userId);
    }
}
