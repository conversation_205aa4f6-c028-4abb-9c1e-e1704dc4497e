package com.kuaikan.role.game.dao.rolegame;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.kuaikan.role.game.api.bean.RoleBubbleConfig;

/**
 * RoleBubbleMapper
 *
 * <AUTHOR>
 * @since 2024/8/14
 */
@Mapper
public interface RoleBubbleMapper {

    RoleBubbleConfig selectByRoleId(int roleId);

    RoleBubbleConfig selectByRoleIdAndType(@Param("roleId") int roleId, @Param("type") int type);

    List<RoleBubbleConfig> selectAll();

}
