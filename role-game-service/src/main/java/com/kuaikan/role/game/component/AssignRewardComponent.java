package com.kuaikan.role.game.component;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Maps;

import com.kuaikan.common.tools.serialize.JsonUtils;
import com.kuaikan.idgenerator.sdk.BizIdGenerator;
import com.kuaikan.role.game.api.bean.ClientInfo;
import com.kuaikan.role.game.api.bean.SilverCoinAssignRecord;
import com.kuaikan.role.game.api.bean.UserScene;
import com.kuaikan.role.game.api.enums.EmotionBondSource;
import com.kuaikan.role.game.api.enums.ItemAssignSource;
import com.kuaikan.role.game.api.enums.ItemType;
import com.kuaikan.role.game.api.enums.RewardType;
import com.kuaikan.role.game.api.enums.SilverCoinAssignRecordStatusEnum;
import com.kuaikan.role.game.api.enums.SilverCoinAssignSourceEnum;
import com.kuaikan.role.game.api.enums.SilverCoinChargeSourceEnum;
import com.kuaikan.role.game.api.enums.SpiritStoneEnum;
import com.kuaikan.role.game.api.enums.UserCostumePartSource;
import com.kuaikan.role.game.api.enums.UserCouponSourceType;
import com.kuaikan.role.game.api.enums.UserFoodFlowSource;
import com.kuaikan.role.game.api.enums.UserInteractiveActionRewardSource;
import com.kuaikan.role.game.api.enums.UserInteractiveItemFlowSource;
import com.kuaikan.role.game.api.enums.UserStuffFlowSource;
import com.kuaikan.role.game.api.model.EmotionBondLevelUpModel;
import com.kuaikan.role.game.api.rpc.param.AssignRewardParam;
import com.kuaikan.role.game.api.rpc.param.CostumePartAssignParam;
import com.kuaikan.role.game.api.rpc.param.ItemParam;
import com.kuaikan.role.game.api.rpc.param.SpiritStoneParam;
import com.kuaikan.role.game.api.rpc.param.UserCostumePartAcquiredInfo;
import com.kuaikan.role.game.component.coupon.UserCouponSendSelector;
import com.kuaikan.role.game.repository.RoleSpiritStoneRepository;
import com.kuaikan.role.game.repository.SilverCoinAssignRecordRepository;
import com.kuaikan.role.game.repository.UserCostumeRepository;
import com.kuaikan.role.game.repository.UserSceneRepository;

/**
 * <AUTHOR>
 * @date 2024/4/24
 */
@Component
@Slf4j
public class AssignRewardComponent {

    @Resource
    private UserSceneRepository userSceneRepository;
    @Resource
    private UserCostumeRepository userCostumeRepository;
    @Resource
    private UserCostumePartComponent userCostumePartComponent;
    @Resource
    private UserStuffComponent userStuffComponent;
    @Resource
    private UserSilverCoinAccountComponent userSilverCoinAccountComponent;
    @Resource
    private SilverCoinAssignRecordRepository silverCoinAssignRecordRepository;
    @Resource
    private CostumeComponent costumeComponent;
    @Resource
    private UserItemComponent userItemComponent;
    @Resource
    private InteractiveComponent interactiveComponent;
    @Resource
    private EmotionBondComponent emotionBondComponent;
    @Resource
    private FoodComponent foodComponent;
    @Resource
    private UserCouponSendSelector userCouponSendSelector;
    @Resource
    private UserAwardComponent userAwardComponent;
    @Resource
    private SpiritStoneComponent spiritStoneComponent;
    @Resource
    private LockComponent lockComponent;

    @Transactional(rollbackFor = Exception.class)
    public void acquireReward(AssignRewardParam assignRewardParam) {
        RewardType rewardType = RewardType.getByCode(assignRewardParam.getType());
        if (rewardType == null) {
            log.error("acquireReward error, rewardType not found, param:{}", assignRewardParam);
            return;
        }
        Integer userId = assignRewardParam.getUserId();
        int num = assignRewardParam.getNum();
        if (rewardType == RewardType.SILVER_COIN) {
            log.info("acquireReward, userId:{}, silverCoinNum:{}", userId, num);
        }
        boolean isLock = false;
        for (int i = 0; i < 3; i++) {
            try {
                isLock = lockComponent.lockForRewardType(userId, rewardType.getCode());
                if (!isLock) {
                    log.info("acquireReward locke, lockForRewardType retry, param:{}", assignRewardParam);
                    Thread.sleep(100);
                    continue;
                }
                switch (Objects.requireNonNull(rewardType)) {
                    case SCENE:
                        final UserScene userScene = userSceneRepository.queryByUserIdSceneId(userId, assignRewardParam.getRelatedId());
                        if (userScene == null) {
                            userSceneRepository.save(userId, assignRewardParam.getRelatedId(), true);
                        }
                        break;
                    case COSTUME:
                        costumeComponent.assignCostume(userId, assignRewardParam.getRelatedId(), true, UserCostumePartSource.PRIZE);
                        break;
                    case STUFF:
                        long orderId = BizIdGenerator.getId();
                        Map<Integer, BigDecimal> stuffMap = Maps.newHashMap();
                        stuffMap.put(assignRewardParam.getRelatedId(), BigDecimal.valueOf(num));
                        userStuffComponent.chargeStuff(stuffMap, userId, orderId, UserStuffFlowSource.QUEST);
                        break;
                    case COSTUME_PART:
                        CostumePartAssignParam costumePartAssignParam = new CostumePartAssignParam().setCostumePartId(assignRewardParam.getRelatedId())
                                .setNum(assignRewardParam.getNum())
                                .setOrderId(String.valueOf(assignRewardParam.getBid()))
                                .setSource(UserCostumePartSource.PRIZE)
                                .setUserCostumePartAcquiredInfo(new UserCostumePartAcquiredInfo());
                        userCostumePartComponent.assignCostumePart(userId, costumePartAssignParam);
                        break;
                    case SILVER_COIN:
                        SilverCoinAssignRecord.ExtraInfo extraInfo = new SilverCoinAssignRecord.ExtraInfo().setClaimPrizeRecordId(assignRewardParam.getBid());
                        long silverCoinAssignRecordId = BizIdGenerator.getId();
                        SilverCoinAssignRecord silverCoinAssignRecord = new SilverCoinAssignRecord().setSource(SilverCoinAssignSourceEnum.FINISH_TASK.getCode())
                                .setUserId(userId)
                                .setOrderId(silverCoinAssignRecordId)
                                .setNum(num)
                                .setStatus(SilverCoinAssignRecordStatusEnum.FINISHED.getCode())
                                .setExtraInfo(extraInfo);
                        silverCoinAssignRecordRepository.insertSilverCoinAssignRecord(silverCoinAssignRecord);
                        userSilverCoinAccountComponent.chargeSilverCoin(userId, String.valueOf(silverCoinAssignRecordId), num,
                                SilverCoinChargeSourceEnum.PRIZE.getCode());
                        break;
                    case ENERGY_BOTTLE:
                        ItemParam param = new ItemParam().setItemType(ItemType.ENERGY_BOTTLE.getCode())
                                .setSource(ItemAssignSource.PRIZE.getCode())
                                .setCount(assignRewardParam.getNum())
                                .setOrderId(String.valueOf(assignRewardParam.getBid()));
                        userItemComponent.assignUserItem(userId, param);
                        break;
                    case ACTION_ITEM:
                        interactiveComponent.acquireItem(userId, assignRewardParam.getRelatedId(), num, UserInteractiveItemFlowSource.QUEST.getCode(),
                                String.valueOf(assignRewardParam.getBid()));
                        break;
                    case EMOTION_BOND:
                        emotionBondComponent.handleLevelUp(ClientInfo.getDefault(userId), userId, assignRewardParam.getRelatedId(), num,
                                EmotionBondSource.REWARD.getCode());
                        break;
                    case FOOD:
                        foodComponent.chargeFood(userId, assignRewardParam.getRelatedId(), num, UserFoodFlowSource.PRIZE.getCode(),
                                String.valueOf(assignRewardParam.getBid()));
                        break;
                    case ADOPT_COUPON:
                    case BLIND_BOX_COUPON:
                        userCouponSendSelector.getUserCouponSender(rewardType)
                                .sendCouponToUser(userId, assignRewardParam.getRelatedId(), Math.max(1, num), UserCouponSourceType.PRIZE.getCode());
                        break;
                    case EMOTION_BOND_BOTTLE:
                        ItemParam emotionBondParam = new ItemParam().setItemType(ItemType.EMOTION_BOND_BOTTLE.getCode())
                                .setSource(ItemAssignSource.PRIZE.getCode())
                                .setCount(assignRewardParam.getNum())
                                .setOrderId(String.valueOf(assignRewardParam.getBid()))
                                .setItemId(assignRewardParam.getRelatedId())
                                .setSourceType(assignRewardParam.getSourceType())
                                .setSourceId(assignRewardParam.getSourceId());
                        userItemComponent.assignUserItem(userId, emotionBondParam);
                        break;
                    case INTERACTIVE_ACTION:
                        interactiveComponent.acquireAction(userId, assignRewardParam.getRelatedId(),
                                UserInteractiveActionRewardSource.BLIND_BOX_ACTIVITY.getCode(), String.valueOf(assignRewardParam.getBid()));
                        break;
                    case WISH_COUPON:
                    case KK_COIN:
                        userAwardComponent.doUserAward(userId, assignRewardParam.getActivityName(), assignRewardParam.getAwardName(), rewardType,
                                assignRewardParam.getClientInfoDTO(), String.valueOf(assignRewardParam.getBid()));
                        break;
                    case SPIRIT_STONE:
                        String awardSource = assignRewardParam.getAwardSource();
                        int source = StringUtils.isNotBlank(awardSource) ? SpiritStoneEnum.getByType(awardSource).getCode() : SpiritStoneEnum.PRIZE.getCode();
                        SpiritStoneParam spiritStoneParam = new SpiritStoneParam().setUserId(userId)
                                .setSpiritStoneId(assignRewardParam.getRelatedId())
                                .setAmount(num)
                                .setSource(source)
                                .setOrderId(String.valueOf(assignRewardParam.getBid()));
                        spiritStoneComponent.assignSpiritStone(spiritStoneParam);
                        break;
                    case PRIZE_BAG:
                        userAwardComponent.acquirePrizeBagPrize(assignRewardParam.getPrizeBagId(), assignRewardParam.getSubPrizeId(),
                                assignRewardParam.getSubCount(), assignRewardParam.getClientInfoDTO(), assignRewardParam.getBid());
                        break;
                    case COMMON_MAP_ENERGY_BOTTLE:
                        ItemParam itemParam = new ItemParam().setItemType(ItemType.COMMON_MAP_ENERGY_BOTTLE.getCode())
                                .setSource(ItemAssignSource.PRIZE.getCode())
                                .setCount(assignRewardParam.getNum())
                                .setOrderId(String.valueOf(assignRewardParam.getBid()))
                                .setItemId(assignRewardParam.getRelatedId());
                        userItemComponent.assignUserItem(userId, itemParam);
                        break;
                    default:
                        log.warn("assignReward warn, rewardType not found, rewardType:{}, relatedId:{}, num:{}, userId:{}", rewardType,
                                assignRewardParam.getRelatedId(), num, userId);
                        throw new IllegalArgumentException("rewardType not found:" + rewardType);
                }
                log.info("assignReward success, rewardType:{}, relatedId:{}, num:{}, userId:{}", rewardType, assignRewardParam.getRelatedId(), num, userId);
                break;
            } catch (InterruptedException e) {
                log.error("acquireReward assignRewardParam:{}", JsonUtils.toJson(assignRewardParam), e);
            } finally {
                if (isLock) {
                    lockComponent.unlockForRewardType(userId, rewardType.getCode());
                }
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public EmotionBondLevelUpModel acquireEmotionBondReward(AssignRewardParam assignRewardParam) {
        Integer userId = assignRewardParam.getUserId();
        int num = assignRewardParam.getNum();
        return emotionBondComponent.handleLevelUp(ClientInfo.getDefault(userId), userId, assignRewardParam.getRelatedId(), num,
                EmotionBondSource.GAME_CARD.getCode());
    }

}
