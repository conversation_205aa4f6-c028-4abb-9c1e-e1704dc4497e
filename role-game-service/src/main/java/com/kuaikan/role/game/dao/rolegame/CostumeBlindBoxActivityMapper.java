package com.kuaikan.role.game.dao.rolegame;

import java.util.Collection;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.kuaikan.role.game.api.bean.CostumeBlindBoxActivity;

public interface CostumeBlindBoxActivityMapper {

    CostumeBlindBoxActivity selectByPrimaryKey(Long id);

    List<CostumeBlindBoxActivity> queryAll();

    List<CostumeBlindBoxActivity> queryByGroupId(@Param("roleGroupId") int roleGroupId);

    CostumeBlindBoxActivity queryById(@Param("id") int id);

    List<CostumeBlindBoxActivity> queryByIds(@Param("ids") Collection<Integer> ids);
}
