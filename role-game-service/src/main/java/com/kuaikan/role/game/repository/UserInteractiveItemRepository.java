package com.kuaikan.role.game.repository;

import static com.kuaikan.role.game.common.enums.CacheConfig.USER_INTERACTIVE_ITEM_BY_USER;
import static com.kuaikan.role.game.uitl.ThreadPoolConfig.REDIS_EXECUTOR;

import java.util.List;
import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import com.kuaikan.comic.common.utils.CommonLettuceClusterUtil;
import com.kuaikan.common.redis.CacheHelper;
import com.kuaikan.common.redis.KeyGenerator;
import com.kuaikan.common.redis.lettuce.LettuceClusterClient;
import com.kuaikan.common.redis.lettuce.LettuceClusterUtil;
import com.kuaikan.role.game.api.bean.UserInteractiveItem;
import com.kuaikan.role.game.dao.rolegame.UserInteractiveItemMapper;

/**
 * <AUTHOR>
 * @version 2024-09-11
 */
@Repository
public class UserInteractiveItemRepository {

    @Resource
    private UserInteractiveItemMapper userInteractiveItemMapper;

    public int insert(UserInteractiveItem record) {
        final int insert = userInteractiveItemMapper.insert(record);
        deleteCache(record.getUserId());
        return insert;
    }

    public int updateBalance(int id, int userId, int afterBalance, int beforeBalance) {
        final int i = userInteractiveItemMapper.updateBalance(id, afterBalance, beforeBalance);
        deleteCache(userId);
        return i;
    }

    public UserInteractiveItem queryByUserIdAndItemId(int userId, int itemId) {
        final List<UserInteractiveItem> userInteractiveItems = userInteractiveItemMapper.queryByUserId(userId);
        if (CollectionUtils.isEmpty(userInteractiveItems)) {
            return null;
        }
        return userInteractiveItems.stream().filter(userInteractiveItem -> userInteractiveItem.getItemId() == itemId).findFirst().orElse(null);
    }

    public List<UserInteractiveItem> queryByUserId(int userId) {
        return CommonLettuceClusterUtil.getList(userId, USER_INTERACTIVE_ITEM_BY_USER, UserInteractiveItem.class, this::queryByUserIdFromDb,
                CacheHelper.FAKE_INFO, REDIS_EXECUTOR);
    }

    public List<UserInteractiveItem> queryByUserIdFromDb(int userId) {
        return userInteractiveItemMapper.queryByUserId(userId);
    }

    private void deleteCache(int userId) {
        LettuceClusterClient redisClient = LettuceClusterUtil.getClusterClientByName(USER_INTERACTIVE_ITEM_BY_USER.getReadWriteVip());
        String cacheKey = KeyGenerator.generate(USER_INTERACTIVE_ITEM_BY_USER.getKeyPattern(), userId);
        redisClient.del(cacheKey);
    }
}
