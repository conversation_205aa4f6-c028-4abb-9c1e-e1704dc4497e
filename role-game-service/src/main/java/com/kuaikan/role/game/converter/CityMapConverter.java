package com.kuaikan.role.game.converter;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.kuaikan.role.game.api.model.CityMapModel;
import com.kuaikan.role.game.api.model.MapElementModel;
import com.kuaikan.role.game.common.bean.MapCity;
import com.kuaikan.role.game.common.bean.MapElement;

/**
 *
 * <AUTHOR>
 * @date 2024/6/26
 */
@Mapper
public interface CityMapConverter extends BaseConverter{

    @Mapping(target = ".", source = "config")
    CityMapModel toCityMapModel(MapCity mapCity);

    List<MapElementModel> toMapElementModelList(List<MapElement> mapElementList);

}
