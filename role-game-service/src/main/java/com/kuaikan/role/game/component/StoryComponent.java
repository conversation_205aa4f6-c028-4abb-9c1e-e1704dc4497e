package com.kuaikan.role.game.component;

import static java.util.Comparator.comparingInt;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;

import com.kuaikan.comic.bean.Comic;
import com.kuaikan.comic.bean.ComicReadRecord;
import com.kuaikan.comic.service.ComicReadRecordService;
import com.kuaikan.comic.service.ComicService;
import com.kuaikan.common.tools.serialize.JsonUtils;
import com.kuaikan.role.game.api.bean.Building;
import com.kuaikan.role.game.api.bean.InteractiveItem;
import com.kuaikan.role.game.api.bean.KeyValueConfig;
import com.kuaikan.role.game.api.bean.Role;
import com.kuaikan.role.game.api.bean.Schedule;
import com.kuaikan.role.game.api.bean.Story;
import com.kuaikan.role.game.api.bean.StoryActionConfig;
import com.kuaikan.role.game.api.bean.StoryAvgConfig;
import com.kuaikan.role.game.api.bean.StoryLetterConfig;
import com.kuaikan.role.game.api.bean.StoryPhotoConfig;
import com.kuaikan.role.game.api.bean.StoryProbability;
import com.kuaikan.role.game.api.bean.StoryVoiceConfig;
import com.kuaikan.role.game.api.bean.UserStory;
import com.kuaikan.role.game.api.enums.AvgUnlockCondition;
import com.kuaikan.role.game.api.enums.DimensionType;
import com.kuaikan.role.game.api.enums.StoryType;
import com.kuaikan.role.game.api.model.collection.StoryModel;
import com.kuaikan.role.game.api.model.collection.StoryTypeModel;
import com.kuaikan.role.game.common.bean.RoleGroupRelation;
import com.kuaikan.role.game.common.constant.KeyValueConfigKeys;
import com.kuaikan.role.game.api.util.GsonUtils;
import com.kuaikan.role.game.model.bo.GetStoryTypeBO;
import com.kuaikan.role.game.repository.KeyValueConfigRepository;
import com.kuaikan.role.game.repository.UserStoryRepository;

/**
 * StoryComponent
 *
 * <AUTHOR>
 * @since 2024-03-11
 */
@Slf4j
@Component
public class StoryComponent {

    private Comparator<StoryModel> storyModelComparator = comparingInt(StoryModel::getRoleSortValue).thenComparingInt(x -> x.getAvgUnlockCondition().getOrder())
            .thenComparingLong(StoryModel::getSortValue);
    @Resource
    private UserStoryRepository userStoryRepository;
    @Resource
    private KvConfigComponent kvConfigComponent;
    @Resource
    private ComicReadRecordService comicReadRecordService;
    @Resource
    private ComicService comicService;

    @Resource
    private KeyValueConfigRepository keyValueConfigRepository;
    @Resource
    private RedDotComponent redDotComponent;

    public StoryProbability getStoryProbability() {
        KeyValueConfig keyValueConfig = keyValueConfigRepository.queryByKey(KeyValueConfigKeys.STORY_PROBABILITY);
        if (keyValueConfig == null) {
            return null;
        }
        StoryProbability storyProbability = GsonUtils.tryParseObject(keyValueConfig.getValue(), StoryProbability.class);
        return storyProbability;
    }

    public StoryTypeModel getAvgStoryModel(GetStoryTypeBO storyTypeBO) {
        int totalStoryCount = 0;
        int collectedStoryCount = 0;
        List<StoryModel> avgStoryList = Lists.newArrayList();
        Map<Integer, UserStory> storyId2userStoryMap = storyTypeBO.getStoryId2userStoryMap();
        List<Integer> roleIds = storyTypeBO.getGroupRoleIds();
        Map<Integer, RoleGroupRelation> id2RelationMap = storyTypeBO.getId2RelationMap();
        Map<Integer, Role> id2RoleMap = storyTypeBO.getId2RoleMap();
        Map<Integer, Integer> storyId2EmotionBondLevelMap = storyTypeBO.getStoryId2EmotionBondLevelMap();
        for (Story story : storyTypeBO.getStories()) {
            if (StoryType.AVG.getCode().equals(story.getType())) {
                totalStoryCount++;
                UserStory userStory = storyId2userStoryMap.get(story.getId());
                StoryModel storyModel = StoryModel.valueOf(story, userStory, storyTypeBO.getAquariumStoryIds(), storyTypeBO.isWhiteUser());
                storyModel.setCollectionImage(story.getCoverImage());
                Integer unlockingConditionCode = story.getStoryUnlockCondition();
                AvgUnlockCondition condition = AvgUnlockCondition.getByCode(unlockingConditionCode);
                storyModel.setUnlockCondition(condition.getUnlockCondition());
                StoryAvgConfig storyAvgConfig = JsonUtils.fromJson(story.getConfig(), StoryAvgConfig.class);
                storyModel.setAvgChapterId(storyAvgConfig.getAvgChapterId());
                if (userStory != null) {
                    collectedStoryCount++;
                }
                Optional<Integer> firstRole = story.getRoleIds().stream().filter(roleIds::contains).findFirst();
                if (firstRole.isPresent()) {
                    Integer firstRoleId = firstRole.get();
                    Role role = id2RoleMap.get(firstRoleId);
                    storyModel.setUnlockRoleName(role.getName());
                    storyModel.setRoleSortValue(id2RelationMap.get(firstRoleId).getOrderNum());
                    DimensionType dimension = DimensionType.getByUnlockAvgCondition(condition.getCode());
                    AvgUnlockCondition unlockCondition = AvgUnlockCondition.getByCode(unlockingConditionCode);
                    if (dimension != DimensionType.UNKNOWN) {
                        int unlockAvgLevel = kvConfigComponent.getUnlockLevelById(role.getId(), story.getId());
                        storyModel.setUnlockCondition(dimension.getDesc() + "Lv." + unlockAvgLevel);
                    } else if (unlockCondition == AvgUnlockCondition.EMOTION_BOND_UNLOCK_FOR_DIRECTLY) {
                        if (storyId2EmotionBondLevelMap.containsKey(story.getId())) {
                            storyModel.setUnlockCondition(String.format(condition.getUnlockCondition(), storyId2EmotionBondLevelMap.get(story.getId())));
                            storyModel.setSortValue(
                                    AvgUnlockCondition.EMOTION_BOND_UNLOCK_FOR_DIRECTLY.getCode() + storyId2EmotionBondLevelMap.get(story.getId()));
                        } else {
                            storyModel.setUnlockCondition("羁绊提升解锁");
                            //排序要在有等级的的后面
                            storyModel.setSortValue(Integer.MAX_VALUE - AvgUnlockCondition.EMOTION_BOND_UNLOCK_FOR_DIRECTLY.getCode());
                        }
                    } else if (unlockCondition == AvgUnlockCondition.BOND_ACTIVITY_UNLOCK) {
                        storyModel.setUnlockCondition(unlockCondition.getUnlockCondition());
                    }
                }
                avgStoryList.add(storyModel);
            }
        }
        avgStoryList.sort(storyModelComparator);
        final boolean showRedDot = redDotComponent.hasAddStoryTabEvent(storyTypeBO.getUserId(), StoryType.AVG, storyTypeBO.getRoleGroupId());
        return new StoryTypeModel(totalStoryCount, collectedStoryCount, avgStoryList, showRedDot);
    }

    public StoryTypeModel getPhotoStoryModel(GetStoryTypeBO storyTypeBO) {
        List<Story> photoStories = storyTypeBO.getStoriesByType(StoryType.PHOTO.getCode());
        List<Integer> comicIds = new ArrayList<>();
        Map<Integer, Story> comicId2StoryMap = new HashMap<>();
        Map<Integer, UserStory> storyId2userStoryMap = storyTypeBO.getStoryId2userStoryMap();
        for (Story photoStory : photoStories) {
            StoryPhotoConfig photoConfig = JsonUtils.fromJson(photoStory.getConfig(), StoryPhotoConfig.class);
            AvgUnlockCondition unlockCondition = AvgUnlockCondition.getByCode(photoConfig.getUnlockingConditions());
            if (unlockCondition == AvgUnlockCondition.READ_COMIC_UNLOCK && !storyId2userStoryMap.containsKey(photoStory.getId())) {
                comicIds.add(photoConfig.getUnlockComicId());
                comicId2StoryMap.put(photoConfig.getUnlockComicId(), photoStory);
            }
        }
        int userId = storyTypeBO.getUserId();
        Map<Integer, Comic> comicMap = comicService.getComicMapByIds(comicIds);
        if (CollectionUtils.isNotEmpty(comicIds)) {
            comicMap = comicService.getComicMapByIds(comicIds);
            Set<Integer> topicIds = comicMap.values().stream().map(Comic::getTopicId).collect(Collectors.toSet());
            List<ComicReadRecord> recordByUserAndTopic = comicReadRecordService.getRecordByUserAndTopics(userId, topicIds, true);
            Set<Integer> unlockComic = new HashSet<>();
            if (CollectionUtils.isNotEmpty(recordByUserAndTopic)) {
                unlockComic = recordByUserAndTopic.stream().filter(item -> {
                    int totalCount = item.getTotalCount();
                    int maxReadCount = item.getMaxReadCount();
                    boolean recordMatch = (double) maxReadCount / totalCount > 0.8;
                    return comicIds.contains(item.getComicId()) && recordMatch;
                }).map(ComicReadRecord::getComicId).collect(Collectors.toSet());
            }
            List<UserStory> unlockStories = new ArrayList<>();
            for (Map.Entry<Integer, Story> entry : comicId2StoryMap.entrySet()) {
                Integer key = entry.getKey();
                if (unlockComic.contains(key)) {
                    Story story = entry.getValue();
                    UserStory userStory = new UserStory().setUserId(userId).setStoryId(story.getId()).setShowRedDot(true);
                    unlockStories.add(userStory);
                    storyId2userStoryMap.put(story.getId(), userStory);
                }
            }
            userStoryRepository.batchInsert(unlockStories);
        }
        int totalStoryCount = 0;
        int collectedStoryCount = 0;
        Map<Integer, Integer> storyId2EmotionBondLevelMap = storyTypeBO.getStoryId2EmotionBondLevelMap();
        List<StoryModel> photoStoryList = Lists.newArrayList();
        for (Story story : photoStories) {
            totalStoryCount++;
            UserStory userStory = storyId2userStoryMap.get(story.getId());
            if (userStory != null) {
                collectedStoryCount++;
            }
            StoryModel storyModel = StoryModel.valueOf(story, userStory, storyTypeBO.getAquariumStoryIds(), storyTypeBO.isWhiteUser());
            String config = story.getConfig();
            StoryPhotoConfig photoConfig = JsonUtils.fromJson(config, StoryPhotoConfig.class);
            storyModel.setCollectionImage(photoConfig.getImageInfo());
            AvgUnlockCondition avgUnlockCondition = AvgUnlockCondition.getByCode(photoConfig.getUnlockingConditions());
            if (avgUnlockCondition == AvgUnlockCondition.EMOTION_BOND_UNLOCK_FOR_FEED) {
                if (storyId2EmotionBondLevelMap.containsKey(story.getId())) {
                    storyModel.setUnlockCondition(String.format(avgUnlockCondition.getUnlockCondition(), storyId2EmotionBondLevelMap.get(story.getId())));
                    storyModel.setSortValue(AvgUnlockCondition.EMOTION_BOND_UNLOCK_FOR_FEED.getCode() + storyId2EmotionBondLevelMap.get(story.getId()));
                } else {
                    storyModel.setUnlockCondition("羁绊值提升后喂养角色触发");
                    storyModel.setSortValue(Integer.MAX_VALUE - AvgUnlockCondition.EMOTION_BOND_UNLOCK_FOR_FEED.getCode());
                }
            } else if (avgUnlockCondition == AvgUnlockCondition.READ_COMIC_UNLOCK) {
                Comic comic = comicMap.get(photoConfig.getUnlockComicId());
                if (comic != null) {
                    storyModel.setUnlockCondition(String.format(avgUnlockCondition.getUnlockCondition(), comic.getTitle()));
                    storyModel.setSortValue((long) comic.getComicOrder());
                } else {
                    storyModel.setUnlockCondition("未知");
                }
            } else if (avgUnlockCondition == AvgUnlockCondition.BOND_ACTIVITY_UNLOCK) {
                storyModel.setUnlockCondition("羁绊之旅通关后继续探索获得");
            } else {
                storyModel.setUnlockCondition("未知");
            }
            photoStoryList.add(storyModel);
        }
        photoStoryList.sort(storyModelComparator);
        final boolean showRedDot = redDotComponent.hasAddStoryTabEvent(storyTypeBO.getUserId(), StoryType.PHOTO, storyTypeBO.getRoleGroupId());
        return new StoryTypeModel(totalStoryCount, collectedStoryCount, photoStoryList, showRedDot);
    }

    public StoryTypeModel getVoiceStoryModel(GetStoryTypeBO storyTypeBO) {
        int totalStoryCount = 0;
        int collectedStoryCount = 0;
        List<StoryModel> voiceStoryList = Lists.newArrayList();
        Map<Integer, UserStory> storyId2userStoryMap = storyTypeBO.getStoryId2userStoryMap();
        Map<Integer, Integer> storyId2EmotionBondLevelMap = storyTypeBO.getStoryId2EmotionBondLevelMap();
        for (Story story : storyTypeBO.getStoriesByType(StoryType.VOICE.getCode())) {
            totalStoryCount++;
            UserStory userStory = storyId2userStoryMap.get(story.getId());
            if (userStory != null) {
                collectedStoryCount++;
            }
            StoryModel storyModel = StoryModel.valueOf(story, userStory, storyTypeBO.getAquariumStoryIds(), storyTypeBO.isWhiteUser());
            String config = story.getConfig();
            StoryVoiceConfig storyVoiceConfig = JsonUtils.fromJson(config, StoryVoiceConfig.class);
            Integer unlockingConditions = storyVoiceConfig.getUnlockingConditions();
            AvgUnlockCondition avgUnlockCondition = AvgUnlockCondition.getByCode(unlockingConditions);
            if (avgUnlockCondition == AvgUnlockCondition.EMOTION_BOND_UNLOCK_FOR_FEED) {
                if (storyId2EmotionBondLevelMap.containsKey(story.getId())) {
                    storyModel.setUnlockCondition(String.format(avgUnlockCondition.getUnlockCondition(), storyId2EmotionBondLevelMap.get(story.getId())));
                    storyModel.setSortValue(AvgUnlockCondition.EMOTION_BOND_UNLOCK_FOR_FEED.getCode() + storyId2EmotionBondLevelMap.get(story.getId()));
                } else {
                    storyModel.setUnlockCondition("羁绊值提升后喂养角色触发");
                    storyModel.setSortValue(Integer.MAX_VALUE - AvgUnlockCondition.EMOTION_BOND_UNLOCK_FOR_FEED.getCode());
                }
            } else {
                storyModel.setUnlockCondition("未知");
            }
            voiceStoryList.add(storyModel);
        }
        voiceStoryList.sort(storyModelComparator);
        final boolean showRedDot = redDotComponent.hasAddStoryTabEvent(storyTypeBO.getUserId(), StoryType.VOICE, storyTypeBO.getRoleGroupId());
        return new StoryTypeModel(totalStoryCount, collectedStoryCount, voiceStoryList, showRedDot);
    }

    public StoryTypeModel getDailyStoryModel(GetStoryTypeBO storyTypeBO) {
        int totalStoryCount = 0;
        int collectedStoryCount = 0;
        List<StoryModel> dailyStoryList = Lists.newArrayList();
        Map<Integer, UserStory> storyId2userStoryMap = storyTypeBO.getStoryId2userStoryMap();
        Map<Integer, Integer> storyId2EmotionBondLevelMap = storyTypeBO.getStoryId2EmotionBondLevelMap();
        Map<Integer, InteractiveItem> story2InteractiveItemMap = storyTypeBO.getStory2InteractiveItemMap();
        for (Story story : storyTypeBO.getStoriesByType(StoryType.DAILY.getCode())) {
            totalStoryCount++;
            UserStory userStory = storyId2userStoryMap.get(story.getId());
            if (userStory != null) {
                collectedStoryCount++;
            }
            StoryModel storyModel = StoryModel.valueOf(story, userStory, storyTypeBO.getAquariumStoryIds(), storyTypeBO.isWhiteUser());
            String config = story.getConfig();
            StoryActionConfig storyActionConfig = JsonUtils.fromJson(config, StoryActionConfig.class);
            storyModel.setCollectionImage(storyActionConfig.getThumbnail());
            Integer unlockingConditions = story.getStoryUnlockCondition();
            AvgUnlockCondition unlockCondition = AvgUnlockCondition.getByCode(unlockingConditions);
            if (unlockCondition == AvgUnlockCondition.EMOTION_BOND_UNLOCK_FOR_FEED) {
                if (storyId2EmotionBondLevelMap.containsKey(story.getId())) {
                    storyModel.setUnlockCondition(String.format(unlockCondition.getUnlockCondition(), storyId2EmotionBondLevelMap.get(story.getId())));
                    storyModel.setSortValue(AvgUnlockCondition.EMOTION_BOND_UNLOCK_FOR_FEED.getCode() + storyId2EmotionBondLevelMap.get(story.getId()));
                } else {
                    storyModel.setUnlockCondition("羁绊值提升后喂养角色触发");
                    storyModel.setSortValue(Integer.MAX_VALUE - AvgUnlockCondition.EMOTION_BOND_UNLOCK_FOR_FEED.getCode());
                }
            } else if (unlockCondition == AvgUnlockCondition.INTERACTIVE_ITEM_UNLOCK) {
                InteractiveItem interactiveItem = story2InteractiveItemMap.get(story.getId());
                if (interactiveItem != null) {
                    storyModel.setUnlockCondition(String.format(unlockCondition.getUnlockCondition(), interactiveItem.getName()));
                } else {
                    storyModel.setUnlockCondition("使用互动道具可解锁");
                }
            } else {
                storyModel.setUnlockCondition("未知");
            }
            dailyStoryList.add(storyModel);
        }
        dailyStoryList.sort(storyModelComparator);
        final boolean showRedDot = redDotComponent.hasAddStoryTabEvent(storyTypeBO.getUserId(), StoryType.DAILY, storyTypeBO.getRoleGroupId());
        return new StoryTypeModel(totalStoryCount, collectedStoryCount, dailyStoryList, showRedDot);
    }

    public StoryTypeModel getLetterStoryModel(GetStoryTypeBO storyTypeBO) {
        int totalStoryCount = 0;
        int collectedStoryCount = 0;
        List<StoryModel> letterStoryList = Lists.newArrayList();
        Map<Integer, UserStory> storyId2userStoryMap = storyTypeBO.getStoryId2userStoryMap();
        List<Integer> groupRoleIds = storyTypeBO.getGroupRoleIds();
        Map<Integer, Role> id2RoleMap = storyTypeBO.getId2RoleMap();
        Map<Integer, RoleGroupRelation> id2RelationMap = storyTypeBO.getId2RelationMap();
        Map<Integer, Schedule> id2ScheduleMap = storyTypeBO.getId2ScheduleMap();
        Map<Integer, Building> id2BuildingMap = storyTypeBO.getId2BuildingMap();
        Map<Integer, Integer> storyId2EmotionBondLevelMap = storyTypeBO.getStoryId2EmotionBondLevelMap();
        for (Story story : storyTypeBO.getStoriesByType(StoryType.LETTER.getCode())) {
            totalStoryCount++;
            UserStory userStory = storyId2userStoryMap.get(story.getId());
            if (userStory != null) {
                collectedStoryCount++;
            }
            StoryModel storyModel = StoryModel.valueOf(story, userStory, storyTypeBO.getAquariumStoryIds(), storyTypeBO.isWhiteUser());

            String config = story.getConfig();
            StoryLetterConfig storyLetterConfig = JsonUtils.fromJson(config, StoryLetterConfig.class);
            Integer unlockingConditions = storyLetterConfig.getUnlockingConditions();
            AvgUnlockCondition unlockCondition = AvgUnlockCondition.getByCode(unlockingConditions);
            storyModel.setUnlockCondition(unlockCondition.getUnlockCondition());
            Optional<Integer> firstRole = story.getRoleIds().stream().filter(groupRoleIds::contains).findFirst();
            if (firstRole.isPresent()) {
                Integer firstRoleId = firstRole.get();
                Role role = id2RoleMap.get(firstRoleId);
                storyModel.setUnlockRoleName(role.getName());
                storyModel.setRoleSortValue(id2RelationMap.get(firstRoleId).getOrderNum());
                if (AvgUnlockCondition.SCHEDULE_UNLOCK == unlockCondition || AvgUnlockCondition.UNKNOWN == unlockCondition) {
                    Schedule schedule = id2ScheduleMap.get(story.getScheduleId());
                    if (schedule != null) {
                        Building building = id2BuildingMap.get(schedule.getBuildingId());
                        if (building != null) {
                            storyModel.setUnlockCondition(getScheduleCondition(building, schedule));
                            storyModel.setSortValue(building.getOrderNum() * 100000 - schedule.getId());
                        }
                    }
                } else if (unlockCondition == AvgUnlockCondition.EMOTION_BOND_UNLOCK_FOR_FEED) {
                    if (storyId2EmotionBondLevelMap.containsKey(story.getId())) {
                        storyModel.setUnlockCondition(String.format(unlockCondition.getUnlockCondition(), storyId2EmotionBondLevelMap.get(story.getId())));
                        storyModel.setSortValue(AvgUnlockCondition.EMOTION_BOND_UNLOCK_FOR_FEED.getCode() + storyId2EmotionBondLevelMap.get(story.getId()));
                    } else {
                        storyModel.setUnlockCondition("羁绊值提升后喂养角色触发");
                        storyModel.setSortValue(Integer.MAX_VALUE - AvgUnlockCondition.EMOTION_BOND_UNLOCK_FOR_FEED.getCode());
                    }
                } else {
                    DimensionType dimension = DimensionType.getByUnlockAvgCondition(unlockCondition.getCode());
                    if (dimension != DimensionType.UNKNOWN) {
                        int unlockLevel = kvConfigComponent.getUnlockLevelById(role.getId(), story.getId());
                        storyModel.setUnlockCondition(dimension.getDesc() + "Lv." + unlockLevel);
                    }
                }
            }
            letterStoryList.add(storyModel);
        }
        letterStoryList.sort(storyModelComparator);
        final boolean showRedDot = redDotComponent.hasAddStoryTabEvent(storyTypeBO.getUserId(), StoryType.LETTER, storyTypeBO.getRoleGroupId());
        return new StoryTypeModel(totalStoryCount, collectedStoryCount, letterStoryList, showRedDot);
    }

    @NotNull
    private static String getScheduleCondition(Building building, Schedule schedule) {
        return building.getName() + "·" + schedule.getName();
    }

}
