package com.kuaikan.role.game.handler.action;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;

import com.kuaikan.role.game.api.bean.UserInteractiveItemFlow;
import com.kuaikan.role.game.api.enums.FlowType;
import com.kuaikan.role.game.api.enums.UserActionTargetIdFilterType;
import com.kuaikan.role.game.api.enums.UserActionType;
import com.kuaikan.role.game.api.rpc.param.UserActionQueryParam;
import com.kuaikan.role.game.repository.UserInteractiveItemFlowRepository;

/**
 * <AUTHOR>
 * @date 2024/9/7
 */
@Component
public class UseInteractiveItemCountQueryHandler extends BaseActionCountQueryHandler {

    @Resource
    private UserInteractiveItemFlowRepository userInteractiveItemFlowRepository;

    @Override
    public int queryUserActionCount(UserActionQueryParam param) {
        return userInteractiveItemFlowRepository.countByUserIdTimeRange(param.getUserId(), param.getStartTime(), param.getEndTime(), FlowType.SUBTRACT.getCode());
    }

    @Override
    public List<Integer> getActionTypeFilter() {
        List<Integer> filterTypes = new ArrayList<>();
        filterTypes.add(UserActionTargetIdFilterType.INTERACTIVE_ITEM_IDS.getCode());
        return filterTypes;
    }

    @Override
    public Map<String, Long> queryTargetCount(UserActionQueryParam param) {
        List<UserInteractiveItemFlow> list = userInteractiveItemFlowRepository.queryByUserIdTimeRange(param.getUserId(), param.getStartTime(),
                param.getEndTime(), FlowType.SUBTRACT.getCode());

        Map<Integer, List<String>> filterTargetIdsMap = getFilterTargetIdsMap(param);
        Set<String> interactiveItemIds = new HashSet<>(
                filterTargetIdsMap.getOrDefault(UserActionTargetIdFilterType.INTERACTIVE_ITEM_IDS.getCode(), new ArrayList<>()));

        // 求和， 按照itemId分组，每项按afterBalance - beforeBalance的值累加
        return list.stream()
                .filter(userInteractiveItemFlow -> {
                    if (CollectionUtils.isNotEmpty(interactiveItemIds)) {
                        return interactiveItemIds.contains(String.valueOf(userInteractiveItemFlow.getItemId()));
                    }
                    return true;
                })
                .collect(Collectors.groupingBy(entry -> String.valueOf(entry.getItemId()), Collectors.summingLong(
                        userInteractiveItemFlow -> userInteractiveItemFlow.getAfterBalance() - userInteractiveItemFlow.getBeforeBalance())));
    }

    @Override
    public List<UserActionType> getActionTypes() {
        return Lists.newArrayList(UserActionType.USE_INTERACTIVE_ITEM);
    }
}
