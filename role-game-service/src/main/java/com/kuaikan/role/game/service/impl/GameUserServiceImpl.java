package com.kuaikan.role.game.service.impl;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import com.kuaikan.common.config.Environment;
import com.kuaikan.common.config.Settings;
import com.kuaikan.common.model.RpcResult;
import com.kuaikan.common.redis.KeyGenerator;
import com.kuaikan.common.redis.lettuce.LettuceClusterClient;
import com.kuaikan.common.redis.lettuce.LettuceClusterUtil;
import com.kuaikan.common.tools.serialize.JsonUtils;
import com.kuaikan.idgenerator.sdk.BufferedIdGenerator;
import com.kuaikan.push.api.bean.DeviceExtraDO;
import com.kuaikan.push.api.enums.DeviceNoticeState;
import com.kuaikan.push.api.service.PushUserService;
import com.kuaikan.role.game.api.bean.ClientInfo;
import com.kuaikan.role.game.api.bean.KeyValueConfig;
import com.kuaikan.role.game.api.bean.Role;
import com.kuaikan.role.game.api.bean.Story;
import com.kuaikan.role.game.api.bean.UserActionRecord;
import com.kuaikan.role.game.api.bean.UserCostume;
import com.kuaikan.role.game.api.bean.UserHeartbeat;
import com.kuaikan.role.game.api.bean.UserInfo;
import com.kuaikan.role.game.api.bean.UserRole;
import com.kuaikan.role.game.api.bean.UserRoleProperty;
import com.kuaikan.role.game.api.bean.UserScene;
import com.kuaikan.role.game.api.bean.UserStory;
import com.kuaikan.role.game.api.bean.UserSystemSetting;
import com.kuaikan.role.game.api.bo.RoleCommonPropertyConfig;
import com.kuaikan.role.game.api.constant.CommonConstant;
import com.kuaikan.role.game.api.enums.MoodAssignSource;
import com.kuaikan.role.game.api.enums.RoleBasicStatus;
import com.kuaikan.role.game.api.enums.StoryType;
import com.kuaikan.role.game.api.enums.UserSceneStatus;
import com.kuaikan.role.game.api.model.ActivityEntryModel;
import com.kuaikan.role.game.api.model.ComicBottomModel;
import com.kuaikan.role.game.api.model.HeartbeatModel;
import com.kuaikan.role.game.api.model.RoleGroupModel;
import com.kuaikan.role.game.api.model.UserCityModel;
import com.kuaikan.role.game.api.model.UserInfoModel;
import com.kuaikan.role.game.api.model.UserPropertyModel;
import com.kuaikan.role.game.api.rpc.param.MoodAssignParam;
import com.kuaikan.role.game.api.rpc.param.SystemSettingParam;
import com.kuaikan.role.game.api.rpc.param.UserActionParam;
import com.kuaikan.role.game.api.rpc.param.UserActionQueryParam;
import com.kuaikan.role.game.api.rpc.result.SystemSettingModel;
import com.kuaikan.role.game.api.service.GameUserService;
import com.kuaikan.role.game.api.service.SilverCoinService;
import com.kuaikan.role.game.api.util.GsonUtils;
import com.kuaikan.role.game.common.bean.RoleGroupRelation;
import com.kuaikan.role.game.common.constant.KeyValueConfigKeys;
import com.kuaikan.role.game.common.enums.CacheConfig;
import com.kuaikan.role.game.component.RedDotComponent;
import com.kuaikan.role.game.component.RoleComponent;
import com.kuaikan.role.game.component.UserCityComponent;
import com.kuaikan.role.game.component.UserComponent;
import com.kuaikan.role.game.component.UserRolePropertyComponent;
import com.kuaikan.role.game.component.UserScheduleComponent;
import com.kuaikan.role.game.config.ApolloConfig;
import com.kuaikan.role.game.dao.mongo.UserActionRecordDAO;
import com.kuaikan.role.game.dao.mongo.UserHeartbeatDAO;
import com.kuaikan.role.game.dao.mongo.UserSystemSettingDAO;
import com.kuaikan.role.game.handler.action.UserActionCountQueryFactory;
import com.kuaikan.role.game.handler.action.UserActionCountQueryHandler;
import com.kuaikan.role.game.repository.KeyValueConfigRepository;
import com.kuaikan.role.game.repository.RoleCostumeRelationRepository;
import com.kuaikan.role.game.repository.RoleGroupRelationRepository;
import com.kuaikan.role.game.repository.RoleGroupSceneRelationRepository;
import com.kuaikan.role.game.repository.RoleRepository;
import com.kuaikan.role.game.repository.StoryRepository;
import com.kuaikan.role.game.repository.UserCostumeRepository;
import com.kuaikan.role.game.repository.UserInfoRepository;
import com.kuaikan.role.game.repository.UserRolePropertyRepository;
import com.kuaikan.role.game.repository.UserRoleRepository;
import com.kuaikan.role.game.repository.UserSceneRepository;
import com.kuaikan.role.game.repository.UserStoryRepository;
import com.kuaikan.role.game.uitl.ThreadPoolConfig;

/**
 * 游戏用户
 * <AUTHOR>
 * @date 2024/2/28
 */
@DubboService(version = "1.0", group = "role-game")
@Slf4j
public class GameUserServiceImpl implements GameUserService {

    private static Map<Environment, String> TARGET_ACTION_MAP = new HashMap<>();

    {
        TARGET_ACTION_MAP.put(Environment.STAG,
                "{\"hybrid_url\":\"https://rolegame.quickcan.cn/character-game-main-client/?backgroundColor=ffecce&conf2scrollwhitearea=1&conf2fullscreen=1&statusbar=0&autoOpen=home&roleGroupId=%s&roleId=%s\",\"min_version_code\":0,\"target_web_url\":\"https://rolegame.quickcan.cn/character-game-main-client/?backgroundColor=ffecce&conf2scrollwhitearea=1&conf2fullscreen=1&statusbar=0&autoOpen=home&roleGroupId=%s&roleId=%s\",\"type\":18}");

        TARGET_ACTION_MAP.put(Environment.PROD,
                "{\"hybrid_url\":\"https://rolegame.kuaikanmanhua.com/character-game-main-client/index.html?backgroundColor=ffecce&conf2scrollwhitearea=1&conf2fullscreen=1&statusbar=0&autoOpen=home&roleGroupId=%s&roleId=%s\",\"min_version_code\":0,\"target_web_url\":\"https://rolegame.kuaikanmanhua.com/character-game-main-client/index.html?backgroundColor=ffecce&conf2scrollwhitearea=1&conf2fullscreen=1&statusbar=0&autoOpen=home&roleGroupId=%s&roleId=%s\",\"type\":18}");

    }

    /**
     * 默认最大角色数
     */
    public static final int MAX_ROLE_NUM = 3;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Resource
    private UserHeartbeatDAO userHeartbeatDAO;

    @Resource
    private UserInfoRepository userInfoRepository;

    @Resource
    private RoleComponent roleComponent;
    @Resource
    private UserRoleRepository userRoleRepository;
    @Resource
    private UserSceneRepository userSceneRepository;
    @Resource
    private UserStoryRepository userStoryRepository;
    @Resource
    private UserCostumeRepository userCostumeRepository;
    @Resource
    private StoryRepository storyRepository;
    @Resource
    private UserActionRecordDAO userActionRecordDAO;
    @Resource
    private ApolloConfig apolloConfig;
    @Resource
    private RoleCostumeRelationRepository roleCostumeRelationRepository;
    @Resource
    private UserSystemSettingDAO userSystemSettingDAO;
    @Resource
    private PushUserService pushUserService;
    @Resource
    private RedDotComponent redDotComponent;
    @Resource
    private RoleGroupRelationRepository roleGroupRelationRepository;
    @Resource
    private UserComponent userComponent;
    @Resource
    private UserScheduleComponent userScheduleComponent;
    @Resource
    private UserCityComponent userCityComponent;
    @Resource
    private RoleGroupSceneRelationRepository roleGroupSceneRelationRepository;
    @Resource
    private UserRolePropertyComponent userRolePropertyComponent;
    @Resource
    private UserRolePropertyRepository userRolePropertyRepository;
    @Resource
    private SilverCoinService silverCoinService;
    @Resource
    private KeyValueConfigRepository keyValueConfigRepository;
    @Resource
    private RoleRepository roleRepository;

    @Override
    public RpcResult<Void> heartbeat(int userId) {
        long lastOnlineTime = System.currentTimeMillis();
        String lastOnlineTimeFormat = DATE_TIME_FORMATTER.format(LocalDateTime.ofInstant(Instant.ofEpochMilli(lastOnlineTime), ZoneId.systemDefault()));
        userHeartbeatDAO.upsert(userId, lastOnlineTime, lastOnlineTimeFormat);
        return RpcResult.success();
    }

    @Override
    public RpcResult<HeartbeatModel> heartbeatV2(int userId) {
        final UserInfo userInfo = userInfoRepository.queryUserInfoByUid(userId);
        if (userInfo == null) {
            return RpcResult.success();
        }
        long lastOnlineTime = System.currentTimeMillis();
        String lastOnlineTimeFormat = DATE_TIME_FORMATTER.format(LocalDateTime.ofInstant(Instant.ofEpochMilli(lastOnlineTime), ZoneId.systemDefault()));
        userHeartbeatDAO.upsert(userId, lastOnlineTime, lastOnlineTimeFormat);
        final List<UserRoleProperty> userRoleProperties = userRolePropertyRepository.queryByUserId(userId);
        HeartbeatModel model = new HeartbeatModel();
        List<HeartbeatModel.MoodProperty> moodProperties = userRoleProperties.stream()
                .map(e -> new HeartbeatModel.MoodProperty().setRoleId(e.getRoleId()).setMood(e.getMood()))
                .collect(Collectors.toList());
        model.setMoods(moodProperties);
        ThreadPoolConfig.HEARTBEAT_EXECUTOR.submit(() -> naturalDecayMood(userId));
        return RpcResult.success(model);
    }

    public void naturalDecayMood(int userId) {
        final List<UserRole> userRoles = userRoleRepository.queryUserRoleFromCache(userId);
        final LettuceClusterClient redisClient = LettuceClusterUtil.getClusterClientByName(CacheConfig.USER_ROLE_MOOD_LAST_DECAY.getReadWriteVip());
        String key = KeyGenerator.generate(CacheConfig.USER_ROLE_MOOD_LAST_DECAY.getKeyPattern(), userId);
        final String moodLastDecay = redisClient.get(key);
        if (StringUtils.isBlank(moodLastDecay)) {
            redisClient.set(key, String.valueOf(System.currentTimeMillis()));
            return;
        }
        long lastDecayTime = Long.parseLong(moodLastDecay);
        long now = System.currentTimeMillis();
        final KeyValueConfig keyValueConfig = keyValueConfigRepository.queryByKey(KeyValueConfigKeys.ROLE_COMMON_PROPERTY);
        RoleCommonPropertyConfig roleCommonConfig = GsonUtils.tryParseObject(keyValueConfig.getValue(), RoleCommonPropertyConfig.class);
        final float roleMoodNaturalDecay = roleCommonConfig.getRoleMoodNaturalDecay();
        int hourDiff = getHourDiff(lastDecayTime);
        if (hourDiff <= 0) {
            return;
        }
        final int moodChange = (int) (-roleMoodNaturalDecay * hourDiff);
        if (moodChange == 0) {
            return;
        }
        for (UserRole userRole : userRoles) {
            final Integer roleId = userRole.getRoleId();

            MoodAssignParam moodAssignParam = new MoodAssignParam().setRoleId(roleId)
                    .setMoodChange(moodChange)
                    .setSource(MoodAssignSource.NATURAL_DECAY.getCode())
                    .setOrderId(String.valueOf(BufferedIdGenerator.getId()));
            userRolePropertyComponent.assignUserRoleMood(userId, moodAssignParam);
        }
        redisClient.set(key, String.valueOf(now));
    }

    // 根据时间戳，获取与当前时间相比差了多少小时
    private int getHourDiff(long lastDecayTime) {
        long now = System.currentTimeMillis();
        return (int) ((now - lastDecayTime) / 1000 / 60 / 60);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RpcResult<UserInfoModel> getOrCreateUserInfo(int userId) {
        final UserInfo userInfo = userInfoRepository.queryUserInfoByUid(userId);
        if (userInfo == null) {
            final UserInfo newUserInfo = new UserInfo().setUserId(userId).setSceneStatus(UserSceneStatus.NORMAL.getCode()).setMaxRoleNum(MAX_ROLE_NUM);
            userInfoRepository.insert(newUserInfo);
            silverCoinService.addInitSilverCoin(userId);
            return RpcResult.success(UserInfoModel.valueOf(newUserInfo, 0, null, null, null));
        }
        final UserHeartbeat userHeartbeat = userHeartbeatDAO.findByUserId(userId);
        long lastOnlineTime = userHeartbeat == null ? System.currentTimeMillis() : userHeartbeat.getLastOnlineTime();
        if (System.currentTimeMillis() - lastOnlineTime > CommonConstant.SCENE_DILAPIDATED_AND_UNHEALTHY_TIME && userInfo.getCurrentRoleGroupId() != 0) {
            userInfo.setSceneStatus(UserSceneStatus.DILAPIDATED.getCode());
            userInfoRepository.updateSceneStatus(userId, UserSceneStatus.DILAPIDATED.getCode());
            userRoleRepository.updateAllRoleHealthStatus(userId, RoleBasicStatus.UNHEALTHY.getCode());
            LettuceClusterClient redisClient = LettuceClusterUtil.getClusterClientByName(CacheConfig.HEALTH_SHOW_DIALOG.getReadWriteVip());
            redisClient.del(String.format(CacheConfig.HEALTH_SHOW_DIALOG.getKeyPattern(), userId));
            log.debug("getOrCreateUserInfo update status userId:{}, userInfo:{}, heartbeat:{}", userId, userInfo, userHeartbeat);
        }
        int currentRoleGroupId = userInfo.getCurrentRoleGroupId();
        RoleGroupModel roleGroupModel = roleComponent.queryRoleGroupInfo(userId, currentRoleGroupId);
        if (roleGroupModel == null || CollectionUtils.isEmpty(roleGroupModel.getRoleModelList())) {
            List<UserRole> roles = roleComponent.adoptedRoleList(userId);
            List<Integer> userRoleIds = roles.stream().map(UserRole::getRoleId).collect(Collectors.toList());
            Map<Integer, RoleGroupRelation> roleGroupRelationMap = roleGroupRelationRepository.queryByRoleIdsFromCache(userRoleIds);
            int newCurrentRoleGroupId = roleGroupRelationMap.values()
                    .stream()
                    .filter(relation -> relation.getRoleGroupId() != currentRoleGroupId)
                    .findAny()
                    .map(RoleGroupRelation::getRoleGroupId)
                    .orElse(currentRoleGroupId);
            userInfoRepository.setCurrentRoleGroup(userId, newCurrentRoleGroupId);
            userComponent.recordSwitchRoleGroupAction(userId);
            roleGroupModel = roleComponent.queryRoleGroupInfo(userId, newCurrentRoleGroupId);
        }
        final UserInfoModel userInfoModel = UserInfoModel.valueOf(userInfo, lastOnlineTime, null, null, null).setRoleGroupModel(roleGroupModel);
        log.debug("getOrCreateUserInfo userId:{} userInfoModel:{}, lastOnlineTime:{}, userInfo:{}, heartbeat:{}", userId, userInfoModel, lastOnlineTime,
                userInfo, userHeartbeat);
        return RpcResult.success(userInfoModel);
    }

    @Override
    public RpcResult<Void> recoverScene(int userId) {
        userInfoRepository.updateSceneStatus(userId, UserSceneStatus.NORMAL.getCode());
        return RpcResult.success();
    }

    @Override
    public RpcResult<UserPropertyModel> getUserProperty(int userId) {
        UserPropertyModel userPropertyModel = new UserPropertyModel();
        List<UserScene> userSceneList = userSceneRepository.queryByUserIdFromCache(userId);
        int userSceneCount = ListUtils.emptyIfNull(userSceneList).size();
        userPropertyModel.setOwnedSceneCount(userSceneCount);

        List<UserCostume> userCostumeList = userCostumeRepository.queryByUserIdFromCache(userId);
        int userCostumeCount = ListUtils.emptyIfNull(userCostumeList).size();
        userPropertyModel.setOwnedCostumeCount(userCostumeCount);

        List<UserStory> userStoryList = userStoryRepository.queryUserStoriesByUid(userId);
        List<Integer> storyIds = ListUtils.emptyIfNull(userStoryList).stream().map(UserStory::getStoryId).collect(Collectors.toList());
        Map<Integer, Story> storyMap = storyRepository.queryStoriesByStoryIds(storyIds);
        int ownedActionStoryCount = (int) userStoryList.stream()
                .filter(e -> storyMap.get(e.getStoryId()) != null)
                .filter(e -> storyMap.get(e.getStoryId()).getType().equals(StoryType.ACTION.getCode()))
                .count();
        userPropertyModel.setOwnedActionStoryCount(ownedActionStoryCount);
        int ownedLetterStoryCount = (int) userStoryList.stream()
                .filter(e -> storyMap.get(e.getStoryId()) != null)
                .filter(e -> storyMap.get(e.getStoryId()).getType().equals(StoryType.LETTER.getCode()))
                .count();
        userPropertyModel.setOwnedLetterStoryCount(ownedLetterStoryCount);
        final UserCityModel userCityModel = userCityComponent.getUserCityModel(userId);
        userPropertyModel.setCityLevel(userCityModel.getLevel());
        RoleCommonPropertyConfig roleCommonConfig = roleComponent.getRoleCommonConfig();
        if (roleCommonConfig != null) {
            userPropertyModel.setRecoverEnergyTimeStr(roleCommonConfig.getRecoverTime());
        }
        return RpcResult.success(userPropertyModel);
    }

    @Override
    public RpcResult<Void> reportUserAction(int userId, UserActionParam param) {

        UserInfo userInfo = userInfoRepository.queryUserInfoByUidFromCache(userId);
        if (userInfo == null) {
            return RpcResult.success();
        }
        UserActionRecord actionRecord = new UserActionRecord().setUserId(userId)
                .setActionType(param.getActionType())
                .setRoleId(userInfo.getCurrentRoleGroupId())
                .setCreatedAt(System.currentTimeMillis());
        userActionRecordDAO.insert(actionRecord);
        return RpcResult.success();
    }

    @Override
    public RpcResult<ActivityEntryModel> getActivityEntry(int userId) {
        return RpcResult.success(userComponent.getActivityEntry(userId, true));
    }

    @Override
    public RpcResult<Integer> getUserActionCount(UserActionQueryParam param) {
        UserActionCountQueryHandler handler = UserActionCountQueryFactory.getInstance(param.getActionType());
        if (handler == null) {
            log.error("getUserActionCount UserActionCountQueryHandler not found. param:{}", param);
            return RpcResult.success(0);
        }
        int result = handler.queryUserActionCount(param);
        log.debug("getUserActionCount, param:{}, result:{}", param, result);
        return RpcResult.success(result);
    }

    @Override
    public RpcResult<Map<String, Long>> getTargetNum(UserActionQueryParam param) {
        UserActionCountQueryHandler handler = UserActionCountQueryFactory.getInstance(param.getActionType());
        if (handler == null) {
            log.error("getTargetNum UserActionCountQueryHandler not found. param:{}", param);
            return RpcResult.success(new HashMap<>());
        }
        Map<String, Long> result = handler.queryTargetCount(param);
        return RpcResult.success(result);
    }

    @Override
    public RpcResult<SystemSettingModel> getUserSystemSetting(ClientInfo clientInfo) {
        int userId = clientInfo.getUserId();
        UserSystemSetting userSystemSetting = userSystemSettingDAO.getUserSystemSetting(userId);
        if (userSystemSetting == null) {
            userSystemSetting = new UserSystemSetting().setUserId(userId).setClickSound(true).setBackgroundBgm(true).setVisitRemind(false);
            try {
                DeviceExtraDO deviceExtraDo = pushUserService.fetchDeviceExtra(clientInfo.getXDevice());
                if (deviceExtraDo != null) {
                    userSystemSetting.setSignInRemind(deviceExtraDo.getAliveState() == DeviceNoticeState.OPENED.getCode());
                }
            } catch (Exception e) {
                log.error("fetchDeviceExtra failed. userId:{}, x-device:{}", userId, clientInfo.getXDevice(), e);
            }

            userSystemSettingDAO.upsert(userSystemSetting);
        }
        String visitRemindNotifyText = apolloConfig.getVisitRemindNotifyText();
        SystemSettingModel model = SystemSettingModel.valueOf(userSystemSetting, visitRemindNotifyText);
        return RpcResult.success(model);
    }

    @Override
    public RpcResult<Void> updateUserSystemSetting(int userId, SystemSettingParam param) {

        UserSystemSetting userSystemSetting = userSystemSettingDAO.getUserSystemSetting(userId);
        if (userSystemSetting == null) {
            userSystemSetting = new UserSystemSetting().setUserId(userId)
                    .setBackgroundBgm(true)
                    .setClickSound(true)
                    .setSignInRemind(false)
                    .setVisitRemind(false);
        }
        if (param.getBackgroundBgm() != null) {
            userSystemSetting.setBackgroundBgm(param.getBackgroundBgm());
        }
        if (param.getClickSound() != null) {
            userSystemSetting.setClickSound(param.getClickSound());
        }
        if (param.getSignInRemind() != null) {
            userSystemSetting.setSignInRemind(param.getSignInRemind());
        }
        if (param.getVisitRemind() != null) {
            userSystemSetting.setVisitRemind(param.getVisitRemind());
        }
        userSystemSettingDAO.upsert(userSystemSetting);
        return RpcResult.success();
    }

    @Override
    public RpcResult<Double> getRoleLevelBonusBuff() {
        double attackBonusBuffPerLevel = roleComponent.getRoleLevelBonusBuff();
        log.debug("getRoleLevelBonusBuff, attackBonusBuffPerLevel:{}", attackBonusBuffPerLevel);
        return RpcResult.success(attackBonusBuffPerLevel);
    }

    @Override
    public RpcResult<ComicBottomModel> getUserRoleGroupInfo(int userId, int topicId) {
        List<Role> availableRoles = roleRepository.queryAllAvailableRoles(userId);
        List<Role> topicAvailableRoles = availableRoles.stream().filter(item -> item.getTopicId() == topicId).collect(Collectors.toList());
        List<Integer> topicRoleIds = topicAvailableRoles.stream().map(Role::getId).collect(Collectors.toList());

        Map<Integer, RoleGroupRelation> roleGroupRelationMap = roleGroupRelationRepository.queryByRoleIdsFromCache(topicRoleIds);
        if (MapUtils.isEmpty(roleGroupRelationMap)) {
            return RpcResult.success(new ComicBottomModel().setHadRoleGroup(false));
        }
        Set<Integer> roleGroupIds = roleGroupRelationMap.values().stream().map(RoleGroupRelation::getRoleGroupId).collect(Collectors.toSet());
        Integer roleId;
        if (CollectionUtils.size(roleGroupIds) > 1) {
            //拿到创建时间最早的角色
            roleId = topicAvailableRoles.stream().min(Comparator.comparing(Role::getCreatedAt)).map(Role::getId).orElse(0);

        } else {
            roleId = roleGroupRelationMap.values()
                    .stream()
                    .min(Comparator.comparing(RoleGroupRelation::getOrderNum))
                    .map(RoleGroupRelation::getRoleId)
                    .orElse(0);
        }
        Integer roleGroupIdId = roleGroupRelationMap.get(roleId).getRoleGroupId();
        String targetUrl = TARGET_ACTION_MAP.get(Settings.getEnvironment());
        Map<String, Object> action = JsonUtils.fromJson(String.format(targetUrl, roleGroupIdId, roleId, roleGroupIdId, roleId));
        RoleGroupModel roleGroupModel = roleComponent.queryRoleGroupInfo(userId, roleGroupIdId);
        ComicBottomModel comicBottomModel = new ComicBottomModel().setRoleGroupModel(roleGroupModel).setHadRoleGroup(true).setDefaultAction(action);

        return RpcResult.success(comicBottomModel);
    }
}
