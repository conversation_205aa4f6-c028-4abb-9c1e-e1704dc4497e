package com.kuaikan.role.game.dao.rolegame;

import java.util.Collection;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.kuaikan.role.game.api.bean.UserBlindBoxTargetAwardRecord;

/**
 * <AUTHOR>
 * @version 2025-02-05
 */
public interface UserBlindBoxTargetAwardRecordMapper {

    int insertBatch(@Param("tableName") String tableName, @Param("records") Collection<UserBlindBoxTargetAwardRecord> records);

    int updateCostumeLevel(@Param("tableName") String tableName, @Param("id") int id, @Param("costumeLevel") int costumeLevel);

    List<UserBlindBoxTargetAwardRecord> queryByUserId(@Param("tableName") String tableName, @Param("userId") int userId);
}
