package com.kuaikan.role.game.converter;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;

import com.kuaikan.role.game.api.bean.AvgChapter;
import com.kuaikan.role.game.api.bean.AvgDir;
import com.kuaikan.role.game.api.bean.AvgHotZone;
import com.kuaikan.role.game.api.bean.AvgOriginFile;
import com.kuaikan.role.game.api.model.AvgConfigModel;
import com.kuaikan.role.game.api.bean.Story;
import com.kuaikan.role.game.api.rpc.result.AvgChapterModel;

/**
 *
 * <AUTHOR>
 * @date 2024/8/21
 */
@Mapper
public interface StoryConverter {

    default AvgConfigModel.TextModel toTextModel(AvgChapter.Text avgText, Map<Integer, List<AvgOriginFile>> parentId2FileMap, Map<String, AvgDir> dirNameMap,
                                                 Map<Integer, Story> avg2StoryMap, Map<String, AvgOriginFile> avgOriginFileMap,
                                                 Map<String, AvgHotZone> avgHotZoneMap) {
        if (avgText == null) {
            return null;
        }
        AvgConfigModel.TextModel textModel = new AvgConfigModel.TextModel();
        textModel.setTextId(avgText.getTextId());
        textModel.setDialogue(avgText.getDialogue());
        textModel.setNextIds(avgText.getNextIds());
        textModel.setBgEffectV2(AvgChapterModel.AvgEffectModel.valueOf(avgText.getBgEffectV2()));
        textModel.setBgEffectPlayConfig(AvgChapterModel.AvgPlayConfigModel.valueOf(avgText.getBgEffectPlayConfig()));
        textModel.setFace(avgText.getFace());
        textModel.setCharEffectV2(AvgChapterModel.AvgEffectModel.valueOf(avgText.getCharEffectV2()));
        textModel.setPosition(avgText.getPosition());
        textModel.setLoop(avgText.getLoop());
        if (StringUtils.isNotBlank(avgText.getBg()) && avgOriginFileMap.containsKey(avgText.getBg())) {
            textModel.setBg(avgOriginFileMap.get(avgText.getBg()).getKey());
        }
        if (StringUtils.isNotBlank(avgText.getCharacter()) && avgOriginFileMap.containsKey(avgText.getCharacter())) {
            textModel.setCharacter(avgOriginFileMap.get(avgText.getCharacter()).getKey());
        }
        if (StringUtils.isNotBlank(avgText.getCharacter()) & dirNameMap.containsKey(avgText.getCharacter())) {
            Integer id = dirNameMap.get(avgText.getCharacter()).getId();
            if (parentId2FileMap.containsKey(id)) {
                List<AvgOriginFile> avgOriginFiles = parentId2FileMap.get(id);
                textModel.setCharacters(avgOriginFiles.stream().map(AvgOriginFile::getKey).collect(Collectors.toList()));
            }
        }
        if (StringUtils.isNotBlank(avgText.getHead()) && avgOriginFileMap.containsKey(avgText.getHead())) {
            textModel.setHead(avgOriginFileMap.get(avgText.getHead()).getKey());
        }
        if (StringUtils.isNotBlank(avgText.getBgm()) && avgOriginFileMap.containsKey(avgText.getBgm())) {
            textModel.setBgm(avgOriginFileMap.get(avgText.getBgm()).getKey());
        }
        if (StringUtils.isNotBlank(avgText.getSound()) && avgOriginFileMap.containsKey(avgText.getSound())) {
            textModel.setSound(avgOriginFileMap.get(avgText.getSound()).getKey());
        }
        if (StringUtils.isNotBlank(avgText.getCv()) && avgOriginFileMap.containsKey(avgText.getCv())) {
            textModel.setCv(avgOriginFileMap.get(avgText.getCv()).getKey());
        }
        if (StringUtils.isNotBlank(avgText.getVideo()) && avgOriginFileMap.containsKey(avgText.getVideo())) {
            textModel.setVideo(avgOriginFileMap.get(avgText.getVideo()).getKey());
        }
        if (StringUtils.isNotBlank(avgText.getVideoVoice()) && avgOriginFileMap.containsKey(avgText.getVideoVoice())) {
            textModel.setVideoVoice(avgOriginFileMap.get(avgText.getVideoVoice()).getKey());
        }
        if (StringUtils.isNotBlank(avgText.getHotZone()) && avgHotZoneMap.containsKey(avgText.getHotZone())) {
            textModel.setHotZone(avgHotZoneMap.get(avgText.getHotZone()).getConfig().getHotZone());
        }
        textModel.setPlayMode(avgText.getPlayMode());
        textModel.setVideoPlayConfig(AvgChapterModel.AvgPlayConfigModel.valueOf(avgText.getVideoPlayConfig()));
        if (avgText.getNextChapter() != null && avg2StoryMap.containsKey(avgText.getNextChapter())) {
            Story story = avg2StoryMap.get(avgText.getNextChapter());
            textModel.setNextStoryId(story.getId());
        }
        return textModel;
    }
}
