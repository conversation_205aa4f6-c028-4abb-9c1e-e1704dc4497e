package com.kuaikan.role.game.repository;

import java.util.Collection;
import java.util.List;
import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import com.kuaikan.role.game.common.bean.RoleGroupBlindBoxCouponRelation;
import com.kuaikan.role.game.dao.rolegame.RoleGroupBlindBoxCouponRelationMapper;

/**
 * <AUTHOR>
 * @date 2024/12/26 14:05
 */

@Repository
public class RoleGroupBlindBoxCouponRelationRepository {

    @Resource
    private RoleGroupBlindBoxCouponRelationMapper roleGroupBlindBoxCouponRelationMapperSlave;

    public RoleGroupBlindBoxCouponRelation queryRelationByCouponId(int couponId) {
        return roleGroupBlindBoxCouponRelationMapperSlave.queryByCouponId(couponId);
    }

    public List<RoleGroupBlindBoxCouponRelation> queryByCouponIds(Collection<Integer> couponIds) {
        return roleGroupBlindBoxCouponRelationMapperSlave.queryByCouponIds(couponIds);
    }

}
