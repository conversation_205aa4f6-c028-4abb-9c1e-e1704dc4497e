package com.kuaikan.role.game.component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Range;
import com.google.common.collect.RangeMap;
import com.google.common.collect.TreeRangeMap;

import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.model.RpcResult;
import com.kuaikan.common.tools.lang.convert.SafeConverter;
import com.kuaikan.common.util.JsonUtils;
import com.kuaikan.idgenerator.sdk.BufferedIdGenerator;
import com.kuaikan.role.game.api.bean.BlindBoxDrawContext;
import com.kuaikan.role.game.api.bean.BlindBoxDrawResult;
import com.kuaikan.role.game.api.bean.ClientInfo;
import com.kuaikan.role.game.api.bean.Costume;
import com.kuaikan.role.game.api.bean.CostumeBlindBoxConfig;
import com.kuaikan.role.game.api.bean.CostumeBlindBoxLotteryRecord;
import com.kuaikan.role.game.api.bean.CostumePart;
import com.kuaikan.role.game.api.bean.CostumePartNum;
import com.kuaikan.role.game.api.bean.CostumePartRelation;
import com.kuaikan.role.game.api.bean.KeyValueConfig;
import com.kuaikan.role.game.api.bean.RewardOrder;
import com.kuaikan.role.game.api.bean.RewardOrderBlindBoxExtraInfo;
import com.kuaikan.role.game.api.bean.RewardOrderCommonScheduleExtraInfo;
import com.kuaikan.role.game.api.bean.RewardOrderItemExtraInfo;
import com.kuaikan.role.game.api.bean.RewardOrderScheduleExtraInfo;
import com.kuaikan.role.game.api.bean.RewardRoleAdoptExtraInfo;
import com.kuaikan.role.game.api.bean.Role;
import com.kuaikan.role.game.api.bean.Stuff;
import com.kuaikan.role.game.api.bean.UserAdoptCouponRecord;
import com.kuaikan.role.game.api.bean.UserBlindBoxTargetAward;
import com.kuaikan.role.game.api.bean.UserCostume;
import com.kuaikan.role.game.api.bean.UserCostumePart;
import com.kuaikan.role.game.api.bean.UserMapOngoingSchedule;
import com.kuaikan.role.game.api.bean.UserRoleGroup;
import com.kuaikan.role.game.api.bean.UserRoleOngoingSchedule;
import com.kuaikan.role.game.api.enums.AdoptSource;
import com.kuaikan.role.game.api.enums.CostumeLevel;
import com.kuaikan.role.game.api.enums.ItemAssignSource;
import com.kuaikan.role.game.api.enums.ItemType;
import com.kuaikan.role.game.api.enums.RewardOrderStatus;
import com.kuaikan.role.game.api.enums.RewardOrderType;
import com.kuaikan.role.game.api.enums.SpiritStoneEnum;
import com.kuaikan.role.game.api.enums.UserBlindBoxTargetAwardType;
import com.kuaikan.role.game.api.enums.UserCostumePartSource;
import com.kuaikan.role.game.api.exception.DecomposeCostumePartException;
import com.kuaikan.role.game.api.model.AdoptCouponModel;
import com.kuaikan.role.game.api.model.CommonScheduleResultModel;
import com.kuaikan.role.game.api.model.CompleteScheduleModel;
import com.kuaikan.role.game.api.model.DecomposeCostumePartModel;
import com.kuaikan.role.game.api.model.RoleFullInfoModelV2;
import com.kuaikan.role.game.api.rpc.param.CostumePartAssignBatchParam;
import com.kuaikan.role.game.api.rpc.param.ItemParam;
import com.kuaikan.role.game.api.rpc.param.SpiritStoneParam;
import com.kuaikan.role.game.api.rpc.param.UserCostumePartAcquiredInfo;
import com.kuaikan.role.game.api.rpc.result.CostumePartAssignResult;
import com.kuaikan.role.game.api.service.CommonScheduleService;
import com.kuaikan.role.game.api.service.UserScheduleService;
import com.kuaikan.role.game.api.util.GsonUtils;
import com.kuaikan.role.game.common.bean.RoleGroupRelation;
import com.kuaikan.role.game.common.constant.KeyValueConfigKeys;
import com.kuaikan.role.game.common.enums.ScheduleActionType;
import com.kuaikan.role.game.converter.CommonScheduleConverter;
import com.kuaikan.role.game.converter.ScheduleConverter;
import com.kuaikan.role.game.repository.BuildingRepository;
import com.kuaikan.role.game.repository.CostumeBlindBoxActivityRepository;
import com.kuaikan.role.game.repository.CostumeBlindBoxLotteryRecordRepository;
import com.kuaikan.role.game.repository.CostumePartRelationRepository;
import com.kuaikan.role.game.repository.CostumePartRepository;
import com.kuaikan.role.game.repository.CostumeRepository;
import com.kuaikan.role.game.repository.CouponRepository;
import com.kuaikan.role.game.repository.KeyValueConfigRepository;
import com.kuaikan.role.game.repository.RewardOrderRepository;
import com.kuaikan.role.game.repository.RoleGroupRelationRepository;
import com.kuaikan.role.game.repository.RoleRepository;
import com.kuaikan.role.game.repository.ScheduleRepository;
import com.kuaikan.role.game.repository.StuffRepository;
import com.kuaikan.role.game.repository.UserBlindBoxCouponRecordRepository;
import com.kuaikan.role.game.repository.UserBlindBoxTargetAwardRepository;
import com.kuaikan.role.game.repository.UserCostumePartRepository;
import com.kuaikan.role.game.repository.UserCostumeRepository;
import com.kuaikan.role.game.repository.UserMapOngoingScheduleRepository;
import com.kuaikan.role.game.repository.UserRoleGroupRepository;
import com.kuaikan.role.game.repository.UserRoleOngoingScheduleRepository;

/**
 * <AUTHOR>
 * @version 2024-05-23
 */
@Component
@Slf4j
public class RewardComponent {

    @Resource
    private UserScheduleComponent userScheduleComponent;
    @Resource
    private CostumeBlindBoxLotteryRecordRepository costumeBlindBoxLotteryRecordRepository;
    @Resource
    private StuffRepository stuffRepository;
    @Resource
    private CostumeRepository costumeRepository;
    @Resource
    private CostumePartRelationRepository costumePartRelationRepository;
    @Resource
    private UserCostumePartComponent userCostumePartComponent;
    @Resource
    private RewardOrderRepository rewardOrderRepository;
    @Resource
    private CostumePartRepository costumePartRepository;
    @Resource
    private KeyValueConfigRepository keyValueConfigRepository;
    @Resource
    private UserCostumePartRepository userCostumePartRepository;
    @Resource
    private UserRoleOngoingScheduleRepository userRoleOngoingScheduleRepository;
    @Resource
    private RoleComponent roleComponent;
    @Autowired
    private RoleRepository roleRepository;
    @Autowired
    private ScheduleRepository scheduleRepository;
    @Resource
    private BuildingRepository buildingRepository;
    @Resource
    private SaComponent saComponent;
    @Resource
    private UserScheduleService userScheduleService;
    @Resource
    private UserItemComponent userItemComponent;

    @Resource
    private ScheduleConverter scheduleConverter;
    @Resource
    private CostumeComponent costumeComponent;
    @Resource
    private CouponRepository couponRepository;
    @Resource
    private CouponComponent couponComponent;
    @Autowired
    private CostumeBlindBoxActivityRepository costumeBlindBoxActivityRepository;
    @Resource
    private UserRoleGroupRepository userRoleGroupRepository;
    @Autowired
    private RoleGroupRelationRepository roleGroupRelationRepository;
    @Resource
    private UserBlindBoxCouponRecordRepository userBlindBoxCouponRecordRepository;
    @Resource
    private CostumeBlindBoxComponent costumeBlindBoxComponent;
    @Resource
    private UserBlindBoxTargetAwardRepository userBlindBoxTargetAwardRepository;
    @Resource
    private UserCostumeRepository userCostumeRepository;
    @Resource
    private SpiritStoneComponent spiritStoneComponent;
    @Resource
    private UserMapOngoingScheduleRepository userMapOngoingScheduleRepository;
    @Resource
    private UserCommonScheduleComponent userCommonScheduleComponent;
    @Resource
    private CommonScheduleService commonScheduleService;
    @Resource
    private CommonScheduleConverter commonScheduleConverter;
    @Resource
    private CommonEnergyBottleComponent commonEnergyBottleComponent;

    private static BlindBoxDrawContext buildBlindBoxDrawContext(int userId, RewardOrderBlindBoxExtraInfo.CostumeBlindBoxConfigV2 costumeBlindBoxConfigV2,
                                                                Integer tenDrawMinimumStar, Set<Integer> userCostumeIds, Set<Integer> userCostumePartIds,
                                                                Map<Integer, Integer> targetAwardCostumeIdMap, int nonGuaranteedCount) {
        BlindBoxDrawContext context = new BlindBoxDrawContext().setGearConfig(BlindBoxDrawContext.GearConfig.valueOf(costumeBlindBoxConfigV2.getGearConfig()))
                .setOneStarPartProbability(costumeBlindBoxConfigV2.getOneStarPartProbability())
                .setTwoStarPartProbability(costumeBlindBoxConfigV2.getTwoStarPartProbability())
                .setThreeStarPartProbability(costumeBlindBoxConfigV2.getThreeStarPartProbability())
                .setFourStarPartProbability(costumeBlindBoxConfigV2.getFourStarPartProbability())
                .setFiveStarPartProbability(costumeBlindBoxConfigV2.getFiveStarPartProbability())
                .setNotAcquiredCostumePartProbability(costumeBlindBoxConfigV2.getNotAcquiredCostumePartProbability())
                .setLotteryConfigs(
                        costumeBlindBoxConfigV2.getLotteryConfigs().stream().map(BlindBoxDrawContext.LotteryConfig::valueOf).collect(Collectors.toList()))
                .setGuaranteedConfig(
                        tenDrawMinimumStar != null ? new BlindBoxDrawContext.GuaranteedConfig().setCount(10).setLeastLevel(tenDrawMinimumStar) : null)
                .setUpConfigs(Optional.ofNullable(costumeBlindBoxConfigV2.getUpCostumeRateList())
                        .orElse(Lists.newArrayList())
                        .stream()
                        .map(BlindBoxDrawContext.UpConfig::valueOf)
                        .collect(Collectors.toList()))
                .setUserContext(new BlindBoxDrawContext.UserContext().setUserId(userId)
                        .setOwnedCostumeIds(userCostumeIds)
                        .setOwnedCostumePartIds(userCostumePartIds)
                        .setTargetAwardCostumeIdMap(targetAwardCostumeIdMap)
                        .setNonGuaranteedCount(nonGuaranteedCount))
                .setTargetConfig(BlindBoxDrawContext.TargetConfig.valueOf(costumeBlindBoxConfigV2.getTargetedCostume()));
        return context;
    }

    @NotNull
    private static List<RewardOrderBlindBoxExtraInfo.CollectingCostumeInfo> getCollectingCostumeInfos(CostumePartAssignResult costumePartAssignResult,
                                                                                                      Map<Integer, Costume> costumeMap) {
        final Map<Integer, CostumePartAssignResult.CostumeProgress> costumeProgressMap = costumePartAssignResult.getCostumeProgressMap();
        List<RewardOrderBlindBoxExtraInfo.CollectingCostumeInfo> collectingCostumeInfos = Lists.newArrayListWithExpectedSize(costumeProgressMap.size());
        for (CostumePartAssignResult.CostumeProgress costumeProgress : costumeProgressMap.values()) {
            if (!costumeProgress.isFirstTime()) {
                continue;
            }
            if (costumeProgress.isCollected()) {
                continue;
            }
            RewardOrderBlindBoxExtraInfo.CollectingCostumeInfo collectingCostumeInfo = new RewardOrderBlindBoxExtraInfo.CollectingCostumeInfo();
            collectingCostumeInfo.setImage(costumeMap.get(costumeProgress.getCostumeId()).getConfig().getThumbnail());
            collectingCostumeInfo.setName(costumeMap.get(costumeProgress.getCostumeId()).getName());
            collectingCostumeInfo.setOwnCount(costumeProgress.getProgress());
            collectingCostumeInfo.setNeedCount(costumeProgress.getTotal());
            collectingCostumeInfo.setLevel(costumeMap.get(costumeProgress.getCostumeId()).getLevel());
            collectingCostumeInfo.setId(costumeProgress.getCostumeId());
            collectingCostumeInfos.add(collectingCostumeInfo);
        }
        return collectingCostumeInfos;
    }

    @NotNull
    private static RewardOrderBlindBoxExtraInfo.ComposeCostumeInfo getComposeCostumeInfo(Integer costumeId, Map<Integer, Costume> costumeMap,
                                                                                         Map<Integer, Double> customeLevelBonusMap) {
        RewardOrderBlindBoxExtraInfo.ComposeCostumeInfo composeCostumeInfo = new RewardOrderBlindBoxExtraInfo.ComposeCostumeInfo();
        final Costume costume = costumeMap.get(costumeId);
        composeCostumeInfo.setId(costumeId);
        composeCostumeInfo.setImage(costume.getConfig().getThumbnail());
        composeCostumeInfo.setName(costume.getName());
        composeCostumeInfo.setLevel(costume.getLevel());
        composeCostumeInfo.setHpBonusBuff(customeLevelBonusMap.get(costume.getLevel()));
        return composeCostumeInfo;
    }

    @NotNull
    private static CostumePartAssignBatchParam getCostumePartAssignBatchParam(String giftId, long orderId, Map<Integer, CostumePartNum> costumePartNumMaps,
                                                                              CostumeBlindBoxConfig configs, boolean isNewBlindBoxUser, String activityId,
                                                                              boolean isDressUpVoucher) {
        CostumePartAssignBatchParam costumePartAssignBatchParam = new CostumePartAssignBatchParam();
        costumePartAssignBatchParam.setCostumePartNums(new ArrayList<>(costumePartNumMaps.values()));
        costumePartAssignBatchParam.setSource(UserCostumePartSource.BLIND_BOX);
        costumePartAssignBatchParam.setOrderId(String.valueOf(orderId));
        String blindBoxLevel = "";
        if (configs != null) {
            for (int i = 0; i < configs.getGearConfigs().size(); i++) {
                CostumeBlindBoxConfig.GearConfig originGearConfig = configs.getGearConfigs().get(i);
                if (giftId.equals(originGearConfig.getGiftId())) {
                    blindBoxLevel = "加料档位" + (i + 1);
                }
            }
        }
        costumePartAssignBatchParam.setExtraInfo(new CostumePartAssignBatchParam.ExtraInfo().setBlindBoxLevel(blindBoxLevel));
        costumePartAssignBatchParam.setUserCostumePartAcquiredInfo(new UserCostumePartAcquiredInfo().setBlindBox(true)
                .setActivityId(activityId)
                .setNewBlindBoxUser(isNewBlindBoxUser)
                .setDressUpVoucher(isDressUpVoucher));
        return costumePartAssignBatchParam;
    }

    @NotNull
    private static CostumeBlindBoxLotteryRecord getCostumeBlindBoxLotteryRecord(int userId, long orderId, int level, int costumePartId) {
        CostumeBlindBoxLotteryRecord costumeBlindBoxLotteryRecord = new CostumeBlindBoxLotteryRecord();
        costumeBlindBoxLotteryRecord.setUserId(userId);
        costumeBlindBoxLotteryRecord.setBid(BufferedIdGenerator.getId());
        costumeBlindBoxLotteryRecord.setThirdId(String.valueOf(orderId));
        costumeBlindBoxLotteryRecord.setLevel(level);
        CostumeBlindBoxLotteryRecord.ExtraInfo extraInfo = new CostumeBlindBoxLotteryRecord.ExtraInfo();
        CostumeBlindBoxLotteryRecord.CostumePartConfig costumePartConfig = new CostumeBlindBoxLotteryRecord.CostumePartConfig();
        costumePartConfig.setCostumePartId(costumePartId);
        extraInfo.setCostumePartConfig(costumePartConfig);
        costumeBlindBoxLotteryRecord.setExtraInfo(extraInfo);
        return costumeBlindBoxLotteryRecord;
    }

    @Transactional(rollbackFor = Exception.class)
    public void doReward(int userId, long orderId) {
        final RewardOrder rewardOrder = rewardOrderRepository.selectByOrderId(userId, orderId);
        if (RewardOrderType.getBlindBoxTypes().contains(rewardOrder.getType())) {
            doBlindBoxRewardOrder(userId, orderId, rewardOrder);
        } else if (RewardOrderType.SCHEDULE.getCode() == rewardOrder.getType()) {
            doScheduleRewardOrder(userId, orderId, rewardOrder);
        } else if (RewardOrderType.ROLE_REWARD.getCode() == rewardOrder.getType()) {
            doAdoptionRole(userId, orderId, rewardOrder);
        } else if (RewardOrderType.ITEM.getCode() == rewardOrder.getType()) {
            doBuyItem(userId, orderId, rewardOrder);
        } else if (RewardOrderType.MAP_SCHEDULE.getCode() == rewardOrder.getType()) {
            doCommonScheduleRewardOrder(userId, orderId, rewardOrder);
        } else {
            log.error("TopicRewardConsumer doReward, userId:{}, orderId:{}, rewardOrder:{} ", userId, orderId, rewardOrder);
            throw new RuntimeException("TopicRewardConsumer doReward failed");
        }
    }

    private void doBuyItem(int userId, long orderId, RewardOrder rewardOrder) {
        RewardOrderItemExtraInfo extraInfo = JsonUtils.findObject(rewardOrder.getExtraInfo(), RewardOrderItemExtraInfo.class);
        ItemParam param = new ItemParam().setItemType(extraInfo.getItemType())
                .setOrderId(String.valueOf(rewardOrder.getOrderId()))
                .setCount(1)
                .setSource(ItemAssignSource.GIFT.getCode());
        // 通用地图直接增加行动力
        boolean isCommonMapEnergyBottle = ItemType.COMMON_MAP_ENERGY_BOTTLE.getCode() == param.getItemType();
        if (isCommonMapEnergyBottle) {
            param.setItemId(SafeConverter.toInt(rewardOrder.getBizId()));
            BizResult<Void> result = commonEnergyBottleComponent.assignUserCommonMapEnergy(userId, param);
            if (!result.isSuccess()) {
                log.error("TopicRewardConsumer doBuyItem assignUserCommonMapEnergy failed, userId:{}, orderId:{}, rewardOrder:{}, assignParam:{} ", userId,
                        orderId, rewardOrder, param);
                throw new RuntimeException("TopicRewardConsumer doBuyItem assignUserCommonMapEnergy failed");
            }
        } else {
            BizResult<Void> result = userItemComponent.assignUserItem(userId, param);
            if (!result.isSuccess()) {
                log.error("TopicRewardConsumer doBuyItem assign item failed, userId:{}, orderId:{}, rewardOrder:{}, assignParam:{} ", userId, orderId,
                        rewardOrder, param);
                throw new RuntimeException("TopicRewardConsumer doBuyItem assign item failed");
            }
        }

        int affectNum = rewardOrderRepository.updateExtraInfoAndStatus(userId, orderId, rewardOrder.getExtraInfo(), RewardOrderStatus.FINISHED.getCode());
        if (affectNum != 1) {
            log.error("TopicRewardConsumer doBuyItem updateExtraInfoAndStatus failed, userId:{}, orderId:{}, rewardOrder:{} ", userId, orderId, rewardOrder);
            throw new RuntimeException("TopicRewardConsumer doBuyItem updateExtraInfoAndStatus failed");
        }

        if (isCommonMapEnergyBottle) {
            return;
        }
        Map<String, Object> properties = new HashMap<>();
        Role role = roleRepository.queryByIdFromCache(Integer.parseInt(rewardOrder.getBizId()));
        properties.put("OCName", role == null ? "" : role.getName());
        properties.put("OCID", Integer.parseInt(rewardOrder.getBizId()));
        properties.put("Action", "加料购买");
        properties.put("SpendKKB", extraInfo.getConsume());
        properties.put("SpendRecharge", extraInfo.getKbConsume());
        saComponent.uploadEventData(userId, SaComponent.ENERGY_BOTTLE, properties);
    }

    private void doAdoptionRole(int userId, long orderId, RewardOrder rewardOrder) {
        com.kuaikan.user.model.BizResult<AdoptCouponModel> couponList = couponComponent.queryAdoptDiscountCouponList(userId);
        if (!couponList.isSuccess()) {
            log.error("get user adoptCoupon fail, userId={}}", userId);
            throw new RuntimeException("TopicRewardConsumer doAdoptionRole queryAdoptDiscountCouponList failed");
        }
        RpcResult<RoleFullInfoModelV2> roleFullInfoModelV2RpcResult = roleComponent.adoptionRoleV2(ClientInfo.getDefault(userId), userId,
                Integer.parseInt(rewardOrder.getBizId()), true, AdoptSource.GRAIN_CABINET.getCode());
        if (roleFullInfoModelV2RpcResult.isSuccess()) {
            int affectNum = rewardOrderRepository.updateExtraInfoAndStatus(userId, orderId, rewardOrder.getExtraInfo(), RewardOrderStatus.FINISHED.getCode());
            if (affectNum != 1) {
                log.error("TopicRewardConsumer doAdoptionRole updateExtraInfoAndStatus failed, userId:{}, orderId:{}, rewardOrder:{} ", userId, orderId,
                        rewardOrder);
                throw new RuntimeException("TopicRewardConsumer doAdoptionRole updateExtraInfoAndStatus failed");
            }
            // 扣券
            boolean ifCouponuse = false;
            RewardRoleAdoptExtraInfo extraInfo = JsonUtils.findObject(rewardOrder.getExtraInfo(), RewardRoleAdoptExtraInfo.class);
            if (extraInfo.getBid() != null && extraInfo.getBid() > 0) {
                log.info("user coupon adopt, userId:{}, orderId:{}， bid:{}", userId, orderId, extraInfo.getBid());
                UserAdoptCouponRecord record = couponRepository.selectAdoptCouponRecordByBId(userId, extraInfo.getBid());
                if (record != null) {
                    couponRepository.batchUpdateAdoptCouponRecordUsedStatus(userId, Lists.newArrayList(extraInfo.getBid()), true);
                    ifCouponuse = true;
                }
            } else {
                log.info("old methed ,user coupon adopt userId:{}, orderId:{} ", userId, orderId);
                List<AdoptCouponModel.CouponModel> availableCoupons = couponList.getData().getAvailableList();
                if (Objects.nonNull(availableCoupons)) {
                    List<Long> bids = availableCoupons.stream().filter(couponModel -> {
                        AdoptCouponModel.RoleGroupsModel roleGroups = couponModel.getRoleGroupsModel();
                        return roleGroups.getRoleList().stream().anyMatch(v -> String.valueOf(v.getId()).equals(rewardOrder.getBizId()));
                    }).map(couponModel -> couponModel.getRoleAdoptCoupon().getBid()).collect(Collectors.toList());

                    couponRepository.batchUpdateAdoptCouponRecordUsedStatus(userId, bids, true);
                    ifCouponuse = CollectionUtils.isNotEmpty(bids);
                    log.info("success use adopt coupon ,userId={}, bids={}", userId, bids);
                }
            }
            // 扣漫灵石
            if (extraInfo.getSpendMLS() > 0) {
                spiritStoneComponent.assignSpiritStone(new SpiritStoneParam().setUserId(userId)
                        .setRoleId(Integer.parseInt(rewardOrder.getBizId()))
                        .setOrderId(String.valueOf(orderId))
                        .setAmount(-extraInfo.getSpendMLS())
                        .setSource(SpiritStoneEnum.ADOPT.getCode()));
            }
            // 领养角色记录用户角色组,历史用户需要弹一次
            Integer roleGroupId = roleGroupRelationRepository.queryRoleGroupIdByRoleId(Integer.parseInt(rewardOrder.getBizId()));
            if (roleGroupId == null) {
                log.error("roleGroupRelationRepository queryRoleGroupIdByRoleId failed,roleId:{}", rewardOrder.getBizId());
                throw new RuntimeException("roleGroupRelationRepository queryRoleGroupIdByRoleId failed, roleGroupId is null");
            }
            UserRoleGroup userRoleGroup = userRoleGroupRepository.queryUserRoleGroupFromDB(userId, roleGroupId);
            if (userRoleGroup == null) {
                int count = userRoleGroupRepository.insert(
                        new UserRoleGroup().setUserId(userId).setRoleGroupId(roleGroupId).setExtraInfo(new UserRoleGroup.ExtraInfo().setPopup(Boolean.TRUE)));
                if (count != 1) {
                    log.error("TopicRewardConsumer doAdoptionRole insert userRoleGroup failed, userId:{}, orderId:{}, rewardOrder:{} ", userId, orderId,
                            rewardOrder);
                    throw new RuntimeException("TopicRewardConsumer doAdoptionRole insert userRoleGroup failed");
                }
            }
            //埋点
            Map<String, Object> properties = new HashMap<>();
            Role role = roleRepository.queryByIdFromCache(Integer.parseInt(rewardOrder.getBizId()));
            properties.put("OCName", role == null ? "" : role.getName());
            properties.put("OCID", Integer.parseInt(rewardOrder.getBizId()));
            properties.put("IsRewardAdopt", true);
            properties.put("SpendKKB", extraInfo.getConsume());
            properties.put("SpendMLS", extraInfo.getSpendMLS());
            properties.put("SpendRecharge", extraInfo.getKbConsume());
            if (extraInfo.getSource() != AdoptSource.UNKNOWN.getCode()) {
                properties.put("AdoptionSource", AdoptSource.getByCode(extraInfo.getSource()).getDesc());
            }
            properties.put("Ifcouponuse", ifCouponuse);
            saComponent.uploadEventData(userId, SaComponent.ADOPT_ROLE, properties);
            log.info("TopicRewardConsumer doAdoptionRole success, userId:{}, orderId:{}, rewardOrder:{} ", userId, orderId, rewardOrder);
        } else {
            log.error("TopicRewardConsumer doReward, userId:{}, orderId:{}, rewardOrder:{} message:{}", userId, orderId, rewardOrder,
                    roleFullInfoModelV2RpcResult.getMessage());
            throw new RuntimeException("TopicRewardConsumer doReward failed");
        }
    }

    private void doScheduleRewardOrder(int userId, long orderId, RewardOrder rewardOrder) {
        log.info("TopicRewardConsumer doReward, userId:{}, orderId:{}, rewardOrder:{} ", userId, orderId, rewardOrder);
        final String extraInfo = rewardOrder.getExtraInfo();
        final RewardOrderScheduleExtraInfo scheduleExtraInfo = JsonUtils.findObject(extraInfo, RewardOrderScheduleExtraInfo.class);
        final UserRoleOngoingSchedule userRoleOngoingSchedule = userRoleOngoingScheduleRepository.queryById(Integer.parseInt(rewardOrder.getBizId()));
        final BizResult<CompleteScheduleModel> completeScheduleModelBizResult = userScheduleComponent.completeSchedule(scheduleExtraInfo.getClientInfo(),
                userRoleOngoingSchedule, true);
        final boolean success = completeScheduleModelBizResult.isSuccess();
        if (!success) {
            log.error("TopicRewardConsumer doReward schedule, userId:{}, orderId:{}, rewardOrder:{}, completeScheduleModelBizResult:{} ", userId, orderId,
                    rewardOrder, completeScheduleModelBizResult);
            throw new RuntimeException("TopicRewardConsumer doReward schedule failed");
        }
        final CompleteScheduleModel completeScheduleModel = completeScheduleModelBizResult.getData();
        RewardOrderScheduleExtraInfo.CompleteSchedule completeSchedule = scheduleConverter.toCompleteSchedule(completeScheduleModel);
        scheduleExtraInfo.setCompleteSchedule(completeSchedule);
        // 更新订单前打印scheduleExtraInfo日志
        log.info("before update, userId:{}, orderId:{}, scheduleExtraInfo:{} ", userId, orderId, scheduleExtraInfo);
        final int affectNum = rewardOrderRepository.updateExtraInfoAndStatus(userId, orderId, JsonUtils.writeValueAsString(scheduleExtraInfo),
                RewardOrderStatus.FINISHED.getCode());
        if (affectNum != 1) {
            log.error("TopicRewardConsumer scheduleOrder updateExtraInfoAndStatus failed, userId:{}, orderId:{}, extraInfo:{}, affectNum:{}", userId, orderId,
                    completeScheduleModel, affectNum);
            throw new RuntimeException("TopicRewardConsumer scheduleOrder updateExtraInfoAndStatus failed");
        }
        log.info("TopicRewardConsumer scheduleOrder updateExtraInfoAndStatus success, userId:{}, orderId:{}, extraInfo:{}, affectNum:{}", userId, orderId,
                completeScheduleModel, affectNum);
        //埋点
        userScheduleService.trackingData(userId, userRoleOngoingSchedule, ScheduleActionType.ACCELERATE.getDesc(), scheduleExtraInfo.getConsume(),
                scheduleExtraInfo.getKbConsume());
    }

    private void doCommonScheduleRewardOrder(int userId, long orderId, RewardOrder rewardOrder) {
        log.info("TopicRewardConsumer doReward common schedule, userId:{}, orderId:{}, rewardOrder:{} ", userId, orderId, rewardOrder);

        // 1. 获取订单额外信息
        final String extraInfo = rewardOrder.getExtraInfo();
        final RewardOrderCommonScheduleExtraInfo scheduleExtraInfo = JsonUtils.findObject(extraInfo, RewardOrderCommonScheduleExtraInfo.class);
        if (scheduleExtraInfo == null) {
            log.error("TopicRewardConsumer doReward common schedule extraInfo is null, userId:{}, orderId:{}, rewardOrder:{}", userId, orderId, rewardOrder);
            throw new RuntimeException("TopicRewardConsumer doReward common schedule failed: extraInfo is null");
        }
        // 2. 获取进行中的地图日程
        final UserMapOngoingSchedule userMapOngoingSchedule = userMapOngoingScheduleRepository.queryById(Integer.parseInt(rewardOrder.getBizId()));
        if (userMapOngoingSchedule == null) {
            log.error("TopicRewardConsumer doReward common schedule userMapOngoingSchedule not found, userId:{}, orderId:{}, bizId:{}", userId, orderId,
                    rewardOrder.getBizId());
            throw new RuntimeException("TopicRewardConsumer doReward common schedule failed: schedule not found");
        }
        // 3. 完成日程
        final RpcResult<CommonScheduleResultModel> completeScheduleModelBizResult = userCommonScheduleComponent.complete(scheduleExtraInfo.getClientInfo(),
                userId, userMapOngoingSchedule, String.valueOf(orderId));
        // 4. 验证完成结果
        final boolean success = completeScheduleModelBizResult.isSuccess();
        if (!success) {
            log.error("TopicRewardConsumer doReward common schedule failed, userId:{}, orderId:{}, rewardOrder:{}, result:{} ", userId, orderId, rewardOrder,
                    completeScheduleModelBizResult);
            throw new RuntimeException("TopicRewardConsumer doReward common schedule failed: complete failed");
        }
        // 5. 获取完成结果并转换
        final CommonScheduleResultModel completeScheduleModel = completeScheduleModelBizResult.getData();
        // 使用转换器进行转换
        RewardOrderCommonScheduleExtraInfo.CompleteCommonSchedule completeSchedule = commonScheduleConverter.toCompleteCommonSchedule(completeScheduleModel);
        scheduleExtraInfo.setCompleteSchedule(completeSchedule);
        // 6. 更新订单状态
        log.info("before update common schedule order, userId:{}, orderId:{}, scheduleExtraInfo:{} ", userId, orderId, scheduleExtraInfo);
        final int affectNum = rewardOrderRepository.updateExtraInfoAndStatus(userId, orderId, JsonUtils.writeValueAsString(scheduleExtraInfo),
                RewardOrderStatus.FINISHED.getCode());
        // 7. 验证更新结果
        if (affectNum != 1) {
            log.error("TopicRewardConsumer common scheduleOrder updateExtraInfoAndStatus failed, userId:{}, orderId:{}, extraInfo:{}, affectNum:{}", userId,
                    orderId, completeScheduleModel, affectNum);
            throw new RuntimeException("TopicRewardConsumer common scheduleOrder updateExtraInfoAndStatus failed");
        }
        log.info("TopicRewardConsumer common scheduleOrder updateExtraInfoAndStatus success, userId:{}, orderId:{}, extraInfo:{}, affectNum:{}", userId,
                orderId, completeScheduleModel, affectNum);
        // 8. 埋点
        commonScheduleService.trackingData(userId, userMapOngoingSchedule, ScheduleActionType.ACCELERATE.getDesc(), scheduleExtraInfo.getConsume(),
                scheduleExtraInfo.getKbConsume());
    }

    private void doBlindBoxRewardOrder(int userId, long orderId, RewardOrder rewardOrder) {
        final RewardOrderBlindBoxExtraInfo extraInfo = JsonUtils.findObject(rewardOrder.getExtraInfo(), RewardOrderBlindBoxExtraInfo.class);
        int roleId = extraInfo.getRoleId();
        final List<Pair<Integer, Integer>> drewCostumePartIdPairs;
        final int lotteryNum;
        final String giftId;
        final RewardOrderBlindBoxExtraInfo.CostumeBlindBoxConfigV2 costumeBlindBoxConfigV2 = extraInfo.getCostumeBlindBoxConfigV2();
        final String activityId = rewardOrder.getActivityId();
        BlindBoxDrawResult drawResult = null;
        if (costumeBlindBoxConfigV2 != null) {
            int type;
            int roleGroupId;
            if (StringUtils.isNotBlank(activityId)) {
                type = UserBlindBoxTargetAwardType.ACTIVITY.getCode();
            } else {
                type = UserBlindBoxTargetAwardType.ROLE_GROUP.getCode();
            }
            roleGroupId = roleGroupRelationRepository.queryRoleGroupIdByRoleId(roleId);
            lotteryNum = costumeBlindBoxConfigV2.getGearConfig().getLotteryNum();
            giftId = costumeBlindBoxConfigV2.getGearConfig().getGiftId();
            int targetId = type == UserBlindBoxTargetAwardType.ACTIVITY.getCode() ? Integer.parseInt(activityId) : roleGroupId;
            final BlindBoxDrawContext context = getBlindBoxDrawContext(costumeBlindBoxConfigV2, userId, type, roleGroupId, activityId, targetId);
            drawResult = costumeBlindBoxComponent.draw(context);
            log.info("TopicRewardConsumer drawV2, drawResult userId:{}, context:{}, drawResult:{}, costumeBlindBoxConfigV2:{}", userId,
                    JsonUtils.writeValueAsString(context), JsonUtils.writeValueAsString(drawResult), JsonUtils.writeValueAsString(costumeBlindBoxConfigV2));
            final List<BlindBoxDrawResult.DrawInfo> drawInfos = drawResult.getDrawInfos();
            drewCostumePartIdPairs = Lists.newArrayListWithExpectedSize(CollectionUtils.size(drawInfos));
            Map<Integer, List<Integer>> targetCostumeLevelToIdsMap = Maps.newHashMap();
            for (BlindBoxDrawResult.DrawInfo drawInfo : drawInfos) {
                drewCostumePartIdPairs.add(Pair.of(drawInfo.getCostumePartId(), drawInfo.getCostumeId()));
                final Integer newTargetAwardCostumeId = drawInfo.getNewTargetCostumeId();
                targetCostumeLevelToIdsMap.computeIfAbsent(drawInfo.getCostumeLevel().getLevel(), k -> Lists.newArrayList()).add(newTargetAwardCostumeId);
            }
            userBlindBoxTargetAwardRepository.setTargetAward(userId, targetId, type, targetCostumeLevelToIdsMap);
            log.info(
                    "TopicRewardConsumer doReward doBlindBoxRewardOrder, userId:{}, orderId:{}, roleId:{}, costumeBlindBoxConfigV2:{}, result:{} , context:{}, drawResult:{}, targetCostumeLevelToIdsMap:{}, drewCostumePartIdPairs:{} ",
                    userId, rewardOrder, roleId, costumeBlindBoxConfigV2, drewCostumePartIdPairs, JsonUtils.writeValueAsString(context),
                    JsonUtils.writeValueAsString(drawResult), targetCostumeLevelToIdsMap, drewCostumePartIdPairs);
        } else {
            final RewardOrderBlindBoxExtraInfo.CostumeBlindBoxConfig costumeBlindBoxConfig = extraInfo.getCostumeBlindBoxConfig();
            log.info("TopicRewardConsumer doReward, userId:{}, orderId:{}, roleId:{}, costumeBlindBoxConfig:{} ", userId, rewardOrder, roleId,
                    costumeBlindBoxConfig);
            final RewardOrderBlindBoxExtraInfo.GearConfig gearConfig = costumeBlindBoxConfig.getGearConfig();
            giftId = gearConfig.getGiftId();
            lotteryNum = gearConfig.getLotteryNum();
            drewCostumePartIdPairs = draw(costumeBlindBoxConfig, lotteryNum, userId);
        }
        Map<Integer, CostumePartNum> costumePartNumMap = Maps.newHashMapWithExpectedSize(lotteryNum);
        final Set<Integer> drewCostumePartIds = drewCostumePartIdPairs.stream().map(Pair::getLeft).collect(Collectors.toSet());
        for (Integer drewCostumePartId : drewCostumePartIds) {
            final CostumePartNum costumePartNum = costumePartNumMap.get(drewCostumePartId);
            if (costumePartNum == null) {
                costumePartNumMap.put(drewCostumePartId, new CostumePartNum().setCostumePartId(drewCostumePartId).setNum(1));
            } else {
                costumePartNum.setNum(costumePartNum.getNum() + 1);
            }
        }
        final Map<Integer, Costume> costumeMap = costumeRepository.queryByIds(drewCostumePartIdPairs.stream().map(Pair::getRight).collect(Collectors.toSet()));
        final Map<Integer, CostumePart> costumePartMap = costumePartRepository.selectByIds(drewCostumePartIds);
        List<RewardOrderBlindBoxExtraInfo.CostumePartInfo> costumePartInfos = Lists.newArrayListWithExpectedSize(lotteryNum);
        for (Pair<Integer, Integer> pair : drewCostumePartIdPairs) {
            final Integer costumePartId = pair.getLeft();
            final CostumePart costumePart = costumePartMap.get(costumePartId);
            final Integer costumeId = pair.getRight();
            RewardOrderBlindBoxExtraInfo.CostumePartInfo costumePartInfo = new RewardOrderBlindBoxExtraInfo.CostumePartInfo().setCostumePartId(costumePartId)
                    .setLevel(costumeMap.get(costumeId).getLevel())
                    .setName(costumePart.getName())
                    .setImage(costumePart.getConfig().getConsumePartImage());
            costumePartInfos.add(costumePartInfo);
        }
        Map<Integer, CostumePartNum> costumePartNumMaps = Maps.newHashMapWithExpectedSize(lotteryNum);
        for (Integer costumePartId : drewCostumePartIdPairs.stream().map(Pair::getLeft).collect(Collectors.toList())) {
            final CostumePartNum costumePartNum = costumePartNumMaps.computeIfAbsent(costumePartId, k -> new CostumePartNum().setCostumePartId(k).setNum(0));
            costumePartNum.setNum(costumePartNum.getNum() + 1);
        }
        log.info("TopicRewardConsumer drawLotteries, userId:{}, orderId:{}, roleId:{},extraInfo:{},drewCostumePartIds:{},costumePartNumMap:{}", userId, orderId,
                roleId, extraInfo, drewCostumePartIds, costumePartNumMap);
        final List<CostumeBlindBoxLotteryRecord> records = drewCostumePartIdPairs.stream()
                .map(entry -> getCostumeBlindBoxLotteryRecord(userId, orderId, costumeMap.get(entry.getValue()).getLevel(), entry.getKey()))
                .collect(Collectors.toList());
        KeyValueConfig keyValueConfig = keyValueConfigRepository.queryByKey(KeyValueConfigKeys.COSTUME_BLIND_BOX_CONFIG);
        CostumeBlindBoxConfig originCostumeBlindBoxConfig = Optional.ofNullable(keyValueConfig)
                .map(KeyValueConfig::getValue)
                .map(item -> GsonUtils.tryParseObject(keyValueConfig.getValue(), CostumeBlindBoxConfig.class))
                .orElse(null);
        // 是否是盲盒新用户
        boolean isNewBlindBoxUser = rewardOrderRepository.isFirstBlindBoxOrder(userId);
        // 是否使用盲盒券
        boolean isDressUpVoucher = extraInfo.getCouponBid() != null;
        final CostumePartAssignBatchParam costumePartAssignBatchParam = getCostumePartAssignBatchParam(giftId, orderId, costumePartNumMaps,
                originCostumeBlindBoxConfig, isNewBlindBoxUser, activityId, isDressUpVoucher);
        // 下发装扮单品
        final CostumePartAssignResult costumePartAssignResult = userCostumePartComponent.assignCostumePartBatch(userId, costumePartAssignBatchParam);
        if (!costumePartAssignResult.isSuccess()) {
            log.error("TopicRewardConsumer assignCostumePartBatch failed, userId:{}, orderId:{}, costumePartAssignBatchParam:{}, costumePartAssignResult:{}",
                    userId, orderId, costumePartAssignBatchParam, costumePartAssignResult);
            throw new RuntimeException("TopicRewardConsumer assignCostumePartBatch failed");
        }
        log.info("TopicRewardConsumer assignCostumePartBatch success, userId:{}, orderId:{}, costumePartAssignBatchParam:{}, costumePartAssignResult:{}",
                userId, orderId, costumePartAssignBatchParam, costumePartAssignResult);
        costumeBlindBoxLotteryRecordRepository.insertBatch(userId, records);
        final Set<Integer> collectedCostumeIds = costumePartAssignResult.getCollectedCostumeIds();
        final Map<Integer, Integer> costumePartExtraCountMap = costumePartAssignResult.getCostumePartExtraCountMap();
        final DecomposeCostumePartModel decomposeCostumePartModel;
        try {
            decomposeCostumePartModel = userCostumePartComponent.decomposeCostumeParts(userId, costumePartExtraCountMap);
        } catch (DecomposeCostumePartException e) {
            log.error("TopicRewardConsumer decomposeCostumeParts failed, userId:{}, orderId:{}, costumePartExtraCountMap:{}, e:{}", userId, orderId,
                    costumePartExtraCountMap, e);
            throw new RuntimeException("TopicRewardConsumer decomposeCostumeParts failed");
        }
        RewardOrderBlindBoxExtraInfo.DecomposeInfo decomposeInfo = getDecomposeInfo(decomposeCostumePartModel);
        final List<RewardOrderBlindBoxExtraInfo.CollectingCostumeInfo> collectingCostumeInfos = getCollectingCostumeInfos(costumePartAssignResult, costumeMap);

        Map<Integer, Double> customeLevelBonusMap = costumeComponent.getCustomeLevelBonusMap();
        extraInfo.setLotteryRecordBids(records.stream().map(CostumeBlindBoxLotteryRecord::getBid).collect(Collectors.toList()))
                .setComposeCostumeInfos(collectedCostumeIds.stream()
                        .map(costumeId -> getComposeCostumeInfo(costumeId, costumeMap, customeLevelBonusMap))
                        .collect(Collectors.toList()))
                .setCostumePartInfos(new ArrayList<>(costumePartInfos))
                .setDecomposeInfo(decomposeInfo)
                .setCollectingCostumeInfos(collectingCostumeInfos)
                .setCouponBid(extraInfo.getCouponBid())
                .setDressUpVoucher(isDressUpVoucher)
                .setNewBlindBoxUser(isNewBlindBoxUser);
        if (null != extraInfo.getCouponBid()) {
            userBlindBoxCouponRecordRepository.updateUsedByBid(userId, true, extraInfo.getCouponBid());
        }
        final int affectNum = rewardOrderRepository.updateExtraInfoAndAnalysisInfoAndStatus(userId, orderId, JsonUtils.writeValueAsString(extraInfo),
                drawResult != null ? JsonUtils.writeValueAsString(drawResult) : "{}", RewardOrderStatus.FINISHED.getCode());
        if (affectNum != 1) {
            log.error("TopicRewardConsumer costumeBlindBoxOrder updateExtraInfoAndStatus failed, userId:{}, orderId:{}, extraInfo:{}, affectNum:{}", userId,
                    orderId, extraInfo, affectNum);
            throw new RuntimeException("TopicRewardConsumer costumeBlindBoxOrder updateExtraInfoAndStatus failed");
        }
        log.info("TopicRewardConsumer costumeBlindBoxOrder updateExtraInfoAndStatus success, userId:{}, orderId:{}, extraInfo:{}, affectNum:{}", userId,
                orderId, extraInfo, affectNum);
    }

    private int getNonGuaranteedCount(int userId, String activityId, Set<Integer> relationRoleIds, int tenDrawMinimumStar) {
        // 十抽保底
        int guaranteedCountConfig = 10;
        log.info("getNonGuaranteedCount, userId:{}, activityId:{}, relationRoleIds:{}, tenDrawMinimumStar:{}", userId, activityId, relationRoleIds,
                tenDrawMinimumStar);
        final List<RewardOrder> recentRewardOrders = rewardOrderRepository.selectRecent(userId, guaranteedCountConfig - 1,
                StringUtils.isNotBlank(activityId) ? RewardOrderType.BLIND_BOX_ACTIVITY.getCode() : RewardOrderType.COSTUME_BLIND_BOX.getCode(),
                RewardOrderStatus.FINISHED.getCode(), relationRoleIds.stream().map(String::valueOf).collect(Collectors.toSet()), activityId);
        // 列表按倒序记录最近抽取的星级
        List<Integer> orderCostumeLevels = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(recentRewardOrders)) {
            for (RewardOrder recentRewardOrder : recentRewardOrders) {
                final String orderExtraInfoStr = recentRewardOrder.getExtraInfo();
                final RewardOrderBlindBoxExtraInfo recentOrderExtraInfo = JsonUtils.findObject(orderExtraInfoStr, RewardOrderBlindBoxExtraInfo.class);
                final List<Integer> costumeLevels = recentOrderExtraInfo.getCostumePartInfos()
                        .stream()
                        .map(RewardOrderBlindBoxExtraInfo.CostumePartInfo::getLevel)
                        .collect(Collectors.toList());
                Collections.reverse(costumeLevels);
                orderCostumeLevels.addAll(costumeLevels);
            }
        }
        int nonGuaranteedCount = 0;
        for (Integer orderCostumeLevel : orderCostumeLevels) {
            if (orderCostumeLevel < tenDrawMinimumStar) {
                nonGuaranteedCount++;
            } else {
                break;
            }
        }
        return nonGuaranteedCount;
    }

    private RewardOrderBlindBoxExtraInfo.DecomposeInfo getDecomposeInfo(DecomposeCostumePartModel decomposeCostumePartModel) {
        if (decomposeCostumePartModel == null) {
            return null;
        }
        RewardOrderBlindBoxExtraInfo.DecomposeInfo decomposeInfo = new RewardOrderBlindBoxExtraInfo.DecomposeInfo();
        final Map<Integer, Stuff> stuffMap = stuffRepository.selectAll().stream().collect(Collectors.toMap(Stuff::getId, Function.identity()));
        final List<DecomposeCostumePartModel.DecomposedStuffInfo> decomposedStuffInfos = decomposeCostumePartModel.getDecomposedStuffInfos();
        List<RewardOrderBlindBoxExtraInfo.StuffInfo> stuffInfos = Lists.newArrayList();
        decomposeInfo.setStuffInfos(stuffInfos);
        for (DecomposeCostumePartModel.DecomposedStuffInfo decomposedStuffInfo : decomposedStuffInfos) {
            RewardOrderBlindBoxExtraInfo.StuffInfo stuffInfo = new RewardOrderBlindBoxExtraInfo.StuffInfo();
            stuffInfo.setId(decomposedStuffInfo.getStuffId());
            stuffInfo.setCount(decomposedStuffInfo.getDecomposedCount());
            stuffInfo.setName(stuffMap.get(decomposedStuffInfo.getStuffId()).getName());
            stuffInfo.setImage(stuffMap.get(decomposedStuffInfo.getStuffId()).getConfig().getStuffImage());
            stuffInfos.add(stuffInfo);
        }
        decomposeInfo.setCostumePartCount(decomposeCostumePartModel.getDecomposeCostumePartCount());
        return decomposeInfo;
    }

    private BlindBoxDrawContext getBlindBoxDrawContext(RewardOrderBlindBoxExtraInfo.CostumeBlindBoxConfigV2 costumeBlindBoxConfigV2, int userId, int type,
                                                       int roleGroupId, String activityId, int targetId) {
        final Integer tenDrawMinimumStar = costumeBlindBoxConfigV2.getTenDrawMinimumStar();
        final Set<Integer> userCostumePartIds = userCostumePartRepository.selectByUserId(userId)
                .stream()
                .map(UserCostumePart::getCostumePartId)
                .collect(Collectors.toSet());
        final Set<Integer> userCostumeIds = userCostumeRepository.queryByUserIdFromCache(userId)
                .stream()
                .map(UserCostume::getCostumeId)
                .collect(Collectors.toSet());

        final List<UserBlindBoxTargetAward> userBlindBoxTargetAwards = userBlindBoxTargetAwardRepository.queryCurrentAwards(userId, targetId, type);
        final Map<Integer, Integer> targetAwardCostumeIdMap = userBlindBoxTargetAwards.stream()
                .collect(Collectors.toMap(UserBlindBoxTargetAward::getCostumeLevel, UserBlindBoxTargetAward::getCostumeId));
        final Set<Integer> relationRoleIds = roleGroupRelationRepository.queryByGroupIdFromCache(roleGroupId)
                .stream()
                .map(RoleGroupRelation::getRoleId)
                .collect(Collectors.toSet());
        final int nonGuaranteedCount = tenDrawMinimumStar != null ? getNonGuaranteedCount(userId, activityId, relationRoleIds, tenDrawMinimumStar) : 0;
        final BlindBoxDrawContext context = buildBlindBoxDrawContext(userId, costumeBlindBoxConfigV2, tenDrawMinimumStar, userCostumeIds, userCostumePartIds,
                targetAwardCostumeIdMap, nonGuaranteedCount);
        return context;
    }

    /**
     * 获取抽取的装扮单品id
     * @param costumeBlindBoxConfig
     * @param lotteryNum
     * @param userId
     * @return key是单品id，value是装扮id
     */
    @Deprecated
    @NotNull
    private List<Pair<Integer, Integer>> draw(RewardOrderBlindBoxExtraInfo.CostumeBlindBoxConfig costumeBlindBoxConfig, int lotteryNum, int userId) {

        final List<UserCostumePart> userCostumeParts = userCostumePartRepository.selectByUserId(userId);
        List<Pair<Integer, Integer>> drewCostumePartIds = Lists.newArrayListWithExpectedSize(lotteryNum);
        final List<RewardOrderBlindBoxExtraInfo.LotteryConfig> lotteryConfigs = costumeBlindBoxConfig.getLotteryConfigs()
                .stream()
                .filter(RewardOrderBlindBoxExtraInfo.LotteryConfig::isCanLottery)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(lotteryConfigs)) {
            log.warn("TopicRewardConsumer draw lotteryConfigs is empty, costumeBlindBoxConfig:{}, lotteryNum:{}, userCostumeParts:{}, userId:{}",
                    costumeBlindBoxConfig, lotteryNum, userCostumeParts, userId);
            IntStream.range(0, lotteryNum).forEach(i -> drawFallback(costumeBlindBoxConfig.getLotteryConfigs(), drewCostumePartIds));
            return drewCostumePartIds;
        }
        final Set<CostumeLevel> costumeLevels = lotteryConfigs.stream().map(lotteryConfig -> {
            final int level = lotteryConfig.getLevel();
            return CostumeLevel.getByLevel(level);
        }).filter(Objects::nonNull).collect(Collectors.toSet());
        final Pair<RangeMap<Double, CostumeLevel>, Double> costumeLevelRangeMapPair = initLevelProbabilityMap(costumeBlindBoxConfig, costumeLevels);
        final RangeMap<Double, CostumeLevel> costumeLevelRangeMap = costumeLevelRangeMapPair.getLeft();
        double maxLevelProbability = costumeLevelRangeMapPair.getRight();
        final Map<CostumeLevel, Pair<RangeMap<Double, Integer>, Double>> costumeRangeMapPair = initCostumeProbabilityMap(lotteryConfigs, costumeLevels);
        if (maxLevelProbability == 0) {
            log.warn("TopicRewardConsumer draw maxLevelProbability is 0, costumeBlindBoxConfig:{}, lotteryNum:{}, userCostumeParts:{}, userId:{}",
                    costumeBlindBoxConfig, lotteryNum, userCostumeParts, userId);
            IntStream.range(0, lotteryNum).forEach(i -> drawFallback(costumeBlindBoxConfig.getLotteryConfigs(), drewCostumePartIds));
            return drewCostumePartIds;
        }
        // 已获得及未获得装扮单品列表，key是装扮id，value是pair，left是已获得单品列表，right是未获得单品列表
        Map<Integer, Pair<Set<Integer>, Set<Integer>>> costumeAcquisitionMap = Maps.newHashMapWithExpectedSize(lotteryNum);

        for (int i = 0; i < lotteryNum; i++) {
            // 确定星级
            double levelRandom = new Random().nextDouble() * maxLevelProbability;
            CostumeLevel costumeLevel = costumeLevelRangeMap.get(levelRandom);
            validateCostumeLevel(costumeLevel);
            //确定装扮
            final Pair<RangeMap<Double, Integer>, Double> costumeMapProbabilityPair = costumeRangeMapPair.get(costumeLevel);
            if (costumeMapProbabilityPair.getRight() == 0) {
                log.warn("TopicRewardConsumer draw costumeMapProbability max is 0, costumeBlindBoxConfig:{}, lotteryNum:{}, userCostumeParts:{},i:{},userId:{}",
                        costumeBlindBoxConfig, lotteryNum, userCostumeParts, i, userId);
                drawFallback(costumeBlindBoxConfig.getLotteryConfigs(), drewCostumePartIds);
                continue;
            }
            final RangeMap<Double, Integer> costumeRangeMap = costumeMapProbabilityPair.getLeft();
            final double maxCostumeProbability = costumeMapProbabilityPair.getRight();
            final double costumeRandom = new Random().nextDouble() * maxCostumeProbability;
            final Integer costumeId = costumeRangeMap.get(costumeRandom);
            validateCostume(costumeId);
            final double notAcquiredCostumePartProbability = costumeBlindBoxConfig.getNotAcquiredCostumePartProbability();
            final Pair<Set<Integer>, Set<Integer>> costumeAcquisitionPair = getCostumeAcquisitionPair(costumeAcquisitionMap, costumeId, userCostumeParts);
            final boolean drawAcquiredCostumePart = isDrawAcquiredCostumePart(notAcquiredCostumePartProbability, costumeAcquisitionPair);
            //最终抽取的单品id
            int drewCostumePartId;
            if (drawAcquiredCostumePart) {
                // 抽取已获得的单品
                final Set<Integer> acquiredCostumePartIds = costumeAcquisitionPair.getLeft();
                drewCostumePartId = new ArrayList<>(acquiredCostumePartIds).get(new Random().nextInt(acquiredCostumePartIds.size()));
                log.info("TopicRewardConsumer draw acquiredCostumePart, costumeBlindBoxConfig:{}, costumeId:{}, drewCostumePartId:{},userId:{}",
                        costumeBlindBoxConfig, costumeId, drewCostumePartId, userId);
            } else {
                //抽取未获得的单品
                final Set<Integer> notAcquiredCostumePartIds = costumeAcquisitionPair.getRight();
                drewCostumePartId = new ArrayList<>(notAcquiredCostumePartIds).get(new Random().nextInt(notAcquiredCostumePartIds.size()));
                notAcquiredCostumePartIds.remove(drewCostumePartId);
                final Set<Integer> acquiredCostumePartIds = costumeAcquisitionPair.getLeft();
                acquiredCostumePartIds.add(drewCostumePartId);
                log.info("TopicRewardConsumer draw notAcquiredCostumePart, costumeBlindBoxConfig:{}, costumeId:{}, drewCostumePartId:{},userId:{}",
                        costumeBlindBoxConfig, costumeId, drewCostumePartId, userId);
            }
            drewCostumePartIds.add(Pair.of(drewCostumePartId, costumeId));
        }
        return drewCostumePartIds;
    }

    private void drawFallback(List<RewardOrderBlindBoxExtraInfo.LotteryConfig> lotteryConfigs, List<Pair<Integer, Integer>> drewCostumePartIds) {
        // 兜底，随机获取一个装扮的单品
        final RewardOrderBlindBoxExtraInfo.LotteryConfig lotteryConfig = lotteryConfigs.get(new Random().nextInt(lotteryConfigs.size()));
        final int costumeId = lotteryConfig.getCostumeId();
        final List<CostumePartRelation> costumePartRelations = costumePartRelationRepository.selectByCostumeId(costumeId);
        final List<Integer> costumePartIds = costumePartRelations.stream().map(CostumePartRelation::getCostumePartId).collect(Collectors.toList());
        final Integer costumePartId = costumePartIds.get(new Random().nextInt(costumePartIds.size()));
        drewCostumePartIds.add(Pair.of(costumePartId, costumeId));
    }

    /**
     * 获取指定装扮对应的已获得和未获得单品列表
     * @param costumeAcquisitionMap
     * @param costumeId
     * @param userCostumeParts
     * @return 返回已获得及未获得装扮单品列表，left是已获得单品列表，right是未获得单品列表
     */
    private Pair<Set<Integer>, Set<Integer>> getCostumeAcquisitionPair(Map<Integer, Pair<Set<Integer>, Set<Integer>>> costumeAcquisitionMap, Integer costumeId,
                                                                       List<UserCostumePart> userCostumeParts) {
        Pair<Set<Integer>, Set<Integer>> costumeAcquisitionPair = costumeAcquisitionMap.get(costumeId);
        if (costumeAcquisitionPair == null) {
            final List<CostumePartRelation> costumePartRelations = costumePartRelationRepository.selectByCostumeId(costumeId);
            // 本次抽取装扮下的所有单品id
            final Set<Integer> costumePartIds = costumePartRelations.stream().map(CostumePartRelation::getCostumePartId).collect(Collectors.toSet());
            // 用户拥有的装扮单品id
            final Set<UserCostumePart> userCostumePartSet = userCostumeParts.stream()
                    .filter(userCostumePart -> costumePartIds.contains(userCostumePart.getCostumePartId()))
                    .filter(userCostumePart -> userCostumePart.getBalance() > 0)
                    .collect(Collectors.toSet());
            Set<Integer> acquiredCostumeIds = userCostumePartSet.stream().map(UserCostumePart::getCostumePartId).collect(Collectors.toSet());
            Set<Integer> notAcquiredCostumeIds = costumePartIds.stream()
                    .filter(costumePartId -> !acquiredCostumeIds.contains(costumePartId))
                    .collect(Collectors.toSet());
            costumeAcquisitionPair = Pair.of(acquiredCostumeIds, notAcquiredCostumeIds);
            costumeAcquisitionMap.put(costumeId, costumeAcquisitionPair);
        }
        return costumeAcquisitionPair;
    }

    /**
     * 初始化装扮概率map
     * @param lotteryConfigs
     * @param costumeLevels
     * @return 返回每个等级的装扮概率map和最大概率
     */
    private Map<CostumeLevel, Pair<RangeMap<Double, Integer>, Double>> initCostumeProbabilityMap(
            List<RewardOrderBlindBoxExtraInfo.LotteryConfig> lotteryConfigs, Set<CostumeLevel> costumeLevels) {
        Map<CostumeLevel, Pair<RangeMap<Double, Integer>, Double>> result = Maps.newHashMapWithExpectedSize(costumeLevels.size());
        for (CostumeLevel costumeLevel : costumeLevels) {
            RangeMap<Double, Integer> rangeMap = TreeRangeMap.create();
            double start = 0;
            double end = 0;
            for (RewardOrderBlindBoxExtraInfo.LotteryConfig lotteryConfig : lotteryConfigs) {
                if (lotteryConfig.getLevel() != costumeLevel.getLevel()) {
                    continue;
                }
                end = start + lotteryConfig.getProbability();
                rangeMap.put(Range.closedOpen(start, end), lotteryConfig.getCostumeId());
                start = end;
            }
            result.put(costumeLevel, Pair.of(rangeMap, end));
        }
        return result;
    }

    /**
     * 是否抽取到获得过的单品
     */
    private boolean isDrawAcquiredCostumePart(double notAcquiredCostumePartProbability, Pair<Set<Integer>, Set<Integer>> costumeAcquisitionPair) {
        if (CollectionUtils.isEmpty(costumeAcquisitionPair.getLeft())) {
            return false;
        }
        if (CollectionUtils.isEmpty(costumeAcquisitionPair.getRight())) {
            return true;
        }
        return new Random().nextDouble() * 100 > notAcquiredCostumePartProbability;
    }

    private Pair<RangeMap<Double, CostumeLevel>, Double> initLevelProbabilityMap(RewardOrderBlindBoxExtraInfo.CostumeBlindBoxConfig costumeBlindBoxConfig,
                                                                                 Set<CostumeLevel> costumeLevels) {
        RangeMap<Double, CostumeLevel> rangeMap = TreeRangeMap.create();
        double start = 0;
        double end = 0;
        if (costumeLevels.contains(CostumeLevel.ONE_STAR)) {
            end = costumeBlindBoxConfig.getOneStarPartProbability();
            rangeMap.put(Range.closedOpen(start, end), CostumeLevel.ONE_STAR);
            start = end;
        }
        if (costumeLevels.contains(CostumeLevel.TWO_STAR)) {
            end = start + costumeBlindBoxConfig.getTwoStarPartProbability();
            rangeMap.put(Range.closedOpen(start, end), CostumeLevel.TWO_STAR);
            start = end;
        }
        if (costumeLevels.contains(CostumeLevel.THREE_STAR)) {
            end = start + costumeBlindBoxConfig.getThreeStarPartProbability();
            rangeMap.put(Range.closedOpen(start, end), CostumeLevel.THREE_STAR);
            start = end;
        }
        if (costumeLevels.contains(CostumeLevel.FOUR_STAR)) {
            end = start + costumeBlindBoxConfig.getFourStarPartProbability();
            rangeMap.put(Range.closedOpen(start, end), CostumeLevel.FOUR_STAR);
            start = end;
        }
        if (costumeLevels.contains(CostumeLevel.FIVE_STAR)) {
            end = start + costumeBlindBoxConfig.getFiveStarPartProbability();
            rangeMap.put(Range.closedOpen(start, end), CostumeLevel.FIVE_STAR);
        }
        return Pair.of(rangeMap, end);
    }

    private void validateCostumeLevel(CostumeLevel costumeLevel) {
        if (costumeLevel == null) {
            log.error("TopicRewardConsumer costumeLevel is null");
            throw new RuntimeException("TopicRewardConsumer costumeLevel is null");
        }
    }

    private void validateCostume(Integer costumeId) {
        if (costumeId == null) {
            log.error("TopicRewardConsumer costumeId is null");
            throw new RuntimeException("TopicRewardConsumer costumeId is null");
        }
    }

}
