package com.kuaikan.role.game.handler.action;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;

import org.springframework.stereotype.Component;

import com.kuaikan.role.game.api.enums.UserActionType;
import com.kuaikan.role.game.api.rpc.param.UserActionQueryParam;

/**
 * <AUTHOR>
 * @date 2024/9/7
 */
@Component
public abstract class BaseActionCountQueryHandler implements UserActionCountQueryHandler {

    public abstract List<UserActionType> getActionTypes();

    @Override
    public List<Integer> getActionTypeFilter() {
        return new ArrayList<>();
    }

    public Map<Integer, List<String>> getFilterTargetIdsMap(UserActionQueryParam userActionQueryParam) {
        Map<Integer, List<String>> targetIdMap = userActionQueryParam.getTargetIdMap();
        if (targetIdMap == null) {
            return new HashMap<>();
        }
        List<Integer> filterTypes = getActionTypeFilter();
        if (filterTypes.isEmpty()) {
            return targetIdMap;
        }
        return targetIdMap.entrySet()
                .stream()
                .filter(entry -> filterTypes.contains(entry.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    @Override
    public int queryUserActionCount(UserActionQueryParam param) {
        return 0;
    }

    @Override
    public Map<String, Long> queryTargetCount(UserActionQueryParam param) {
        return Collections.emptyMap();
    }

    @PostConstruct
    private void register() {
        getActionTypes().forEach(actionType -> UserActionCountQueryFactory.register(actionType, this));
    }

}
