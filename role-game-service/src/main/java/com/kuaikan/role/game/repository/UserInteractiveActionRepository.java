package com.kuaikan.role.game.repository;

import static com.kuaikan.role.game.common.enums.CacheConfig.USER_INTERACTIVE_ACTION_BY_USER;
import static com.kuaikan.role.game.uitl.ThreadPoolConfig.REDIS_EXECUTOR;

import java.util.Collection;
import java.util.List;
import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import com.kuaikan.comic.common.utils.CommonLettuceClusterUtil;
import com.kuaikan.common.redis.CacheHelper;
import com.kuaikan.common.redis.KeyGenerator;
import com.kuaikan.common.redis.lettuce.LettuceClusterClient;
import com.kuaikan.common.redis.lettuce.LettuceClusterUtil;
import com.kuaikan.role.game.api.bean.UserInteractiveAction;
import com.kuaikan.role.game.dao.rolegame.UserInteractiveActionMapper;

/**
 * <AUTHOR>
 * @version 2024-09-11
 */
@Repository
public class UserInteractiveActionRepository {

    @Resource
    private UserInteractiveActionMapper userInteractiveActionMapper;

    public int insert(UserInteractiveAction record) {
        final int insert = userInteractiveActionMapper.insert(record);
        deleteCache(record.getUserId());
        return insert;
    }

    public int batchInsert(List<UserInteractiveAction> records) {
        if (CollectionUtils.isEmpty(records)) {
            return 0;
        }
        final int insert = userInteractiveActionMapper.batchInsert(records);
        deleteCache(records.get(0).getUserId());
        return insert;
    }

    public List<UserInteractiveAction> queryByUserId(int userId) {
        return CommonLettuceClusterUtil.getList(userId, USER_INTERACTIVE_ACTION_BY_USER, UserInteractiveAction.class, this::queryByUserIdFromDb,
                CacheHelper.FAKE_INFO, REDIS_EXECUTOR);
    }

    public List<UserInteractiveAction> queryByUserIdFromDb(int userId) {
        return userInteractiveActionMapper.queryByUserId(userId);
    }

    private void deleteCache(int userId) {
        LettuceClusterClient redisClient = LettuceClusterUtil.getClusterClientByName(USER_INTERACTIVE_ACTION_BY_USER.getReadWriteVip());
        String cacheKey = KeyGenerator.generate(USER_INTERACTIVE_ACTION_BY_USER.getKeyPattern(), userId);
        redisClient.del(cacheKey);
    }

    public UserInteractiveAction queryByUserIdAndActionId(int userId, int actionId) {
        final List<UserInteractiveAction> userInteractiveActions = userInteractiveActionMapper.queryByUserId(userId);
        if (CollectionUtils.isEmpty(userInteractiveActions)) {
            return null;
        }
        return userInteractiveActions.stream().filter(e -> e.getActionId() == actionId).findFirst().orElse(null);
    }

    public int deletes(Collection<Integer> ids, int userId) {
        final int delete = userInteractiveActionMapper.deletes(ids, userId);
        deleteCache(userId);
        return delete;
    }
}
