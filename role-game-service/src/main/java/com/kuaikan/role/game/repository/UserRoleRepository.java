package com.kuaikan.role.game.repository;

import static com.kuaikan.role.game.common.enums.CacheConfig.USER_ROLE_LIST;
import static com.kuaikan.role.game.uitl.ThreadPoolConfig.REDIS_EXECUTOR;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import com.kuaikan.comic.common.utils.CommonLettuceClusterUtil;
import com.kuaikan.common.redis.CacheHelper;
import com.kuaikan.common.redis.KeyGenerator;
import com.kuaikan.common.redis.lettuce.LettuceClusterClient;
import com.kuaikan.common.redis.lettuce.LettuceClusterUtil;
import com.kuaikan.role.game.api.bean.UserRole;
import com.kuaikan.role.game.common.enums.CacheConfig;
import com.kuaikan.role.game.api.enums.UserTableEnum;
import com.kuaikan.role.game.dao.rolegame.UserRoleMapper;
import com.kuaikan.role.game.uitl.TablePartitionUtil;

/**
 * <AUTHOR>
 * @date 2024/2/29
 */
@Repository
public class UserRoleRepository {

    @Resource
    private UserRoleMapper userRoleMapper;
    @Resource
    private UserRoleMapper userRoleMapperSlave;

    public void save(int uid, int roleId, int costumeId, int sceneId) {
        UserRole userRole = new UserRole().setUserId(uid).setRoleId(roleId).setEffectiveCostumeId(costumeId).setEffectiveSceneId(sceneId);
        String tableName = getTableName(uid);
        userRoleMapper.insertSelective(tableName, userRole);
        deleteCache(uid);
    }

    public void updateByUserIdAndRoleIdSelective(UserRole userRole) {
        String tableName = getTableName(userRole.getUserId());
        userRoleMapper.updateByUserIdAndRoleIdSelective(tableName, userRole);
        deleteCache(userRole.getUserId());
    }

    public void updateHealthStatus(int userId, int roleId, int healthStatus) {
        String tableName = getTableName(userId);
        userRoleMapper.updateHealthStatus(tableName, userId, roleId, healthStatus);
        deleteCache(userId);
    }

    public void updateAllRoleHealthStatus(int userId, int healthStatus) {
        String tableName = getTableName(userId);
        userRoleMapper.updateAllRoleHealthStatus(tableName, userId, healthStatus);
        deleteCache(userId);
    }

    public void updateLastScheduleTime(int userId, int roleId) {
        String tableName = getTableName(userId);
        userRoleMapper.updateLastScheduleTime(tableName, userId, roleId, System.currentTimeMillis());
        deleteCache(userId);
    }

    public void updateLastScheduleTimeByRoleIds(int userId, Collection<Integer> roleIds) {
        String tableName = getTableName(userId);
        userRoleMapper.updateLastScheduleTimeByRoleIds(tableName, userId, roleIds, System.currentTimeMillis());
        deleteCache(userId);
    }

    public List<UserRole> queryUserRoleFromCache(int userId) {
        return CommonLettuceClusterUtil.getList(userId, USER_ROLE_LIST, UserRole.class, this::queryUserRoleFromDB, CacheHelper.FAKE_INFO, REDIS_EXECUTOR);
    }

    public UserRole queryUserRoleFromCache(int userId, int roleId) {
        return queryUserRoleFromCache(userId).stream().filter(userRole -> userRole.getRoleId() == roleId).findAny().orElse(null);
    }

    public List<UserRole> queryUserRoleFromDB(int userId) {
        String tableName = getTableName(userId);
        return userRoleMapper.queryUserRole(tableName, userId);
    }

    public Map<Integer, List<UserRole>> queryByUserIdsFromDB(List<Integer> userIds) {
        if (CollectionUtils.isEmpty(userIds)){
            return new HashMap<>();
        }

        Map<Integer, List<UserRole>> resultMap = new HashMap<>();
        Map<String, List<Integer>> queryMap = userIds.stream().collect(Collectors.groupingBy(this::getTableName));
        queryMap.forEach((tableName, records) -> {
            Map<Integer, List<UserRole>> map = userRoleMapperSlave.batchSelectByUserIds(tableName, records)
                    .stream()
                    .collect(Collectors.groupingBy(UserRole::getUserId));
            resultMap.putAll(map);
        });
        return resultMap;
    }

    public List<UserRole> queryAllUserRoleFormDB(String tableName) {
        return userRoleMapper.queryAllUserRole(tableName);
    }

    public void updateLevelExp(int userId, int roleId, int level, int exp) {
        String tableName = getTableName(userId);
        userRoleMapper.updateLevelExp(tableName, userId, roleId, level, exp);
        deleteCache(userId);
    }

    public void addExp(int userId, int roleId, int obtainExp) {
        String tableName = getTableName(userId);
        userRoleMapper.addExp(tableName, userId, roleId, obtainExp);
        deleteCache(userId);
    }

    private void deleteCache(int userId) {
        LettuceClusterClient redisClient = LettuceClusterUtil.getClusterClientByName(USER_ROLE_LIST.getReadWriteVip());
        String cacheKey = KeyGenerator.generate(CacheConfig.USER_ROLE_LIST.getKeyPattern(), userId);
        redisClient.del(cacheKey);
    }

    public List<UserRole> batchQuery(String tableName, int offset, int limit) {
        return userRoleMapper.batchQuery(tableName, offset, limit);
    }

    private String getTableName(int userId) {
        return TablePartitionUtil.getTableName(UserTableEnum.USER_ROLE, userId);
    }

}
