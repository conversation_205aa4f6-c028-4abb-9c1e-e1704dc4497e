package com.kuaikan.role.game.config;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;

import com.kuaikan.role.game.api.model.ActivityEntryModel;
import com.kuaikan.role.game.api.model.BlindBoxCouponImageUrlModel;
import com.kuaikan.role.game.api.util.GsonUtils;

/**
 * <AUTHOR>
 * @version 2024-04-17
 */
@Slf4j
@Component
@EnableApolloConfig
public class ApolloConfig {

    @com.ctrip.framework.apollo.spring.annotation.ApolloConfig
    private Config config;

    private volatile List<String> backgroundImgList;

    private volatile List<Integer> notShowCostumeIds;

    @ApolloConfigChangeListener
    private void onChange(ConfigChangeEvent event) {
        refresh();
    }

    @PostConstruct
    private void refresh() {
        String backgroundImgListStr = config.getProperty("background.img.list", "");
        List<String> backgroundImgListTmp = Arrays.asList(backgroundImgListStr.split(","));
        this.backgroundImgList = backgroundImgListTmp;
        final String notShowCostumeIdsStr = config.getProperty("not_show_costume_ids", "");
        List<Integer> notShowCostumeIdsTmp = Arrays.stream(notShowCostumeIdsStr.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        this.notShowCostumeIds = notShowCostumeIdsTmp;
        log.info("backgroundImgList changed, old={},new={}", this.backgroundImgList, backgroundImgListTmp);
        log.info("notShowCostumeIds changed, old={},new={}", this.notShowCostumeIds, notShowCostumeIdsTmp);

    }

    public ActivityEntryModel getActivityEntryModel() {
        String value = config.getProperty("activity_entry", "{}");
        return GsonUtils.tryParseObject(value, ActivityEntryModel.class);
    }

    public String getPopupWindowActionTarget() {
        return config.getProperty("popup_window_action_target", StringUtils.EMPTY);
    }

    public String groupEmotionPopupWindowConfig() {
        return config.getProperty("popup_window_config", StringUtils.EMPTY);
    }

    public Boolean getGuideRecordIgnore() {
        return config.getBooleanProperty("guide_record_ignore", false);
    }

    public List<String> getBackgroundImgList() {
        return backgroundImgList;
    }

    public int getCityId() {
        return config.getIntProperty("use.city.id", 1);
    }

    public String getBgmUrl() {
        return config.getProperty("bgm.url", "");
    }

    public String getAdoptPageVideoUrl() {
        return config.getProperty("adopt_page_video_url", "");
    }

    public String getVisitRemindNotifyText() {
        return config.getProperty("visit_remind_notify_text", "");
    }

    public List<Integer> getNotShowCostumeIds() {
        return notShowCostumeIds;
    }

    public Boolean getGroupCostumeBlindBox() {
        return config.getBooleanProperty("group_costume_blind_box", true);
    }

    public Boolean getScheduleIgnoreCostume() {
        return config.getBooleanProperty("schedule_ignore_costume", true);
    }

    public BlindBoxCouponImageUrlModel getBlindBoxCouponImageUrlModel() {
        String value = config.getProperty("blind_box_coupon_image_url", "{}");
        return GsonUtils.tryParseObject(value, BlindBoxCouponImageUrlModel.class);
    }

    public String getRoleAdoptGiftId() {
        return config.getProperty("role_adopt_gift_id", "66960e8a48f7f5000168b33d");
    }

    public String getCardBattlePackageUrl() {
        return config.getProperty("card_battle_package_url", "");
    }

    public String getCardBattleExploreUrl() {
        return config.getProperty("card_battle_explore_url", "");
    }
}