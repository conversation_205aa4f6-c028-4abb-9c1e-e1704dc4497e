package com.kuaikan.role.game.component;

import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import com.kuaikan.common.redis.KeyGenerator;
import com.kuaikan.common.redis.lettuce.LettuceClusterClient;
import com.kuaikan.common.redis.lettuce.LettuceClusterUtil;
import com.kuaikan.role.game.api.bo.RoleCommonPropertyConfig;
import com.kuaikan.role.game.common.enums.CacheConfig;
import com.kuaikan.role.game.uitl.DateUtil;

/**
 * EnergyBottleComponent
 *
 * <AUTHOR>
 * @since 2024-08-13
 */
@Component
public class EnergyBottleComponent {

    /**
     * 获取用户在当天使用优惠的次数
     */
    public int getTodayDiscountCount(int userId, RoleCommonPropertyConfig roleCommonConfig) {
        int date = DateUtil.getCurrentRecoverDate(roleCommonConfig.getRecoverTime());
        LettuceClusterClient redisClient = LettuceClusterUtil.getClusterClientByName(CacheConfig.USER_ENERGY_BOTTLE_DISCOUNT_COUNT.getReadWriteVip());
        String cacheKey = KeyGenerator.generate(CacheConfig.USER_ENERGY_BOTTLE_DISCOUNT_COUNT.getKeyPattern(), userId, date);

        String count = redisClient.get(cacheKey);
        if (StringUtils.isEmpty(count)) {
            redisClient.setex(cacheKey, TimeUnit.DAYS.toSeconds(1), "0");
        }
        return NumberUtils.toInt(count);
    }

    /**
     * 增加用户在当天使用优惠的次数
     */
    public boolean incrTodayDiscountCount(int userId, RoleCommonPropertyConfig roleCommonConfig) {
        int date = DateUtil.getCurrentRecoverDate(roleCommonConfig.getRecoverTime());
        LettuceClusterClient redisClient = LettuceClusterUtil.getClusterClientByName(CacheConfig.USER_ENERGY_BOTTLE_DISCOUNT_COUNT.getReadWriteVip());
        String cacheKey = KeyGenerator.generate(CacheConfig.USER_ENERGY_BOTTLE_DISCOUNT_COUNT.getKeyPattern(), userId, date);
        Long count = redisClient.incr(cacheKey);
        return count != null && count > 0;
    }
}
