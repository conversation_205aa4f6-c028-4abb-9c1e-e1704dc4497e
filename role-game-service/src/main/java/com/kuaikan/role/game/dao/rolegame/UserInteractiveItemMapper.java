package com.kuaikan.role.game.dao.rolegame;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.kuaikan.role.game.api.bean.UserInteractiveItem;

/**
 * <AUTHOR>
 * @version 2024-09-10
 */
public interface UserInteractiveItemMapper {

    int insert(@Param("record") UserInteractiveItem record);

    int updateBalance(@Param("id") int id, @Param("afterBalance") int afterBalance, @Param("beforeBalance") int beforeBalance);

    List<UserInteractiveItem> queryByUserId(@Param("userId") int userId);
}
