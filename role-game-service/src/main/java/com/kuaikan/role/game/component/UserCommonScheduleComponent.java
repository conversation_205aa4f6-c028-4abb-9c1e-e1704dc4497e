package com.kuaikan.role.game.component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;
import com.kuaikan.common.model.RpcResult;
import com.kuaikan.role.game.api.service.AvgService;
import com.kuaikan.game.gamecard.base.model.Prize;
import com.kuaikan.game.gamecard.prize.def.service.GameCardPrizeService;
import com.kuaikan.idgenerator.sdk.BufferedIdGenerator;
import com.kuaikan.role.game.api.bean.Building;
import com.kuaikan.role.game.api.bean.ClientInfo;
import com.kuaikan.role.game.api.bean.CommonScheduleConsumptionSnapshot;
import com.kuaikan.role.game.api.bean.CommonScheduleSettlementSnapshot;
import com.kuaikan.role.game.api.bean.CommonScheduleSettlementSnapshot.CommonPrize;
import com.kuaikan.role.game.api.bean.CommonScheduleSettlementSnapshot.StoryPrize;
import com.kuaikan.role.game.api.bean.MapBuildingRelation;
import com.kuaikan.role.game.api.bean.Schedule;
import com.kuaikan.role.game.api.bean.UserMap;
import com.kuaikan.role.game.api.bean.UserMapFinishSchedule;
import com.kuaikan.role.game.api.bean.UserMapOngoingSchedule;
import com.kuaikan.role.game.api.common.RoleGameResponse;
import com.kuaikan.role.game.api.enums.BuildingType;
import com.kuaikan.role.game.api.enums.EnergyAssignSource;
import com.kuaikan.role.game.api.enums.PrizeSourceType;
import com.kuaikan.role.game.api.enums.UserScheduleStatus;
import com.kuaikan.role.game.api.model.CommonScheduleResultModel;
import com.kuaikan.role.game.api.model.map.UnlockMapStoryModel;
import com.kuaikan.role.game.api.model.CommonScheduleCreateModel;
import com.kuaikan.role.game.api.rpc.param.ClaimPrizeParamV2;
import com.kuaikan.role.game.api.rpc.result.AvgChapterModel;
import com.kuaikan.role.game.repository.BuildingRepository;
import com.kuaikan.role.game.repository.MapBuildingRelationRepository;
import com.kuaikan.role.game.repository.ScheduleRepository;
import com.kuaikan.role.game.repository.UserMapFinishScheduleRepository;
import com.kuaikan.role.game.repository.UserMapOngoingScheduleRepository;
import com.kuaikan.role.game.repository.UserMapRepository;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class UserCommonScheduleComponent {


    @Resource
    private UserMapOngoingScheduleRepository userMapOngoingScheduleRepository;

    @Resource
    private UserMapFinishScheduleRepository userMapFinishScheduleRepository;

    @Resource
    private ClaimPrizeComponent claimPrizeComponent;

    @Resource
    private BuildingRepository buildingRepository;

    @Resource
    private MapBuildingRelationRepository mapBuildingRelationRepository;

    @Resource
    private ScheduleRepository scheduleRepository;

    @Resource
    private UserMapRepository userMapRepository;

    @Resource
    private UserMapComponent userMapComponent;

    @Resource
    private GameCardPrizeService gameCardPrizeService;

    @Resource
    private UserMapStoryComponent userMapStoryComponent;

    @Resource
    private AvgService avgService;

    @Transactional(rollbackFor = Exception.class)
    public RpcResult<CommonScheduleCreateModel> create(int userId, int mapId, int scheduleId,
            int minutes) {
        Schedule schedule = scheduleRepository.queryByIdFromCache(scheduleId);
        
        if (schedule == null) {
            log.error("CommonScheduleService create schedule not found, userId:{}, mapId:{}, scheduleId:{}", userId, mapId, scheduleId);
            return RpcResult.result(RoleGameResponse.SCHEDULE_NOT_EXIST);
        }

        Building building = buildingRepository.queryByIdFromCache(schedule.getBuildingId());
        if (building == null) {
            log.error("CommonScheduleService create building not found, userId:{}, mapId:{}, scheduleId:{}", userId, mapId, scheduleId);
            return RpcResult.result(RoleGameResponse.BUILDING_NOT_EXIST);
        }
        int periodCount = minutes / 5;
        if (building.getType() == BuildingType.REWARD_BUILDING.getCode()) {
            periodCount = 1;
        }
        int energy = (int) (periodCount * schedule.getConfig().getConsumeEnergyPerPeriod() * 60);
        log.debug("CommonScheduleService create schedule {} energy {}", scheduleId, energy,periodCount,schedule.getConfig().getConsumeEnergyPerPeriod());
        List<Long> prizeIds = schedule.getConfig().getNumericalConfig().getPrizeIds();
        
        MapBuildingRelation mapBuildingRelation = mapBuildingRelationRepository.queryByMapIdAndBuildingId(mapId, schedule.getBuildingId());
        if (mapBuildingRelation == null) {
            log.error("CommonScheduleService create map {} building {} not found, userId:{}, mapId:{}, scheduleId:{}", mapId, schedule.getBuildingId(), userId, mapId, scheduleId);
            return RpcResult.result(RoleGameResponse.MAP_BUILDING_NOT_EXIST);
        }
        
        UserMapOngoingSchedule userMapOngoingSchedule = userMapOngoingScheduleRepository.queryByUserIdAndMapId(userId, mapId);
        if (userMapOngoingSchedule != null) {
            log.error("CommonScheduleService create user {} is already in map {} schedule", userId, mapId);
            return RpcResult.result(RoleGameResponse.USER_ALREADY_IN_MAP_SCHEDULE);
        }
        
        UserMap userMap = userMapRepository.queryByUserIdAndMapId(userId, mapId);
        if (userMap == null) {
            // 初始化userMap
            userMap = userMapComponent.initUserMap(userId, mapId);
        }
    
        userMapOngoingSchedule = new UserMapOngoingSchedule();
        userMapOngoingSchedule.setUserId(userId);
        userMapOngoingSchedule.setMapId(mapId);
        userMapOngoingSchedule.setBuildingId(schedule.getBuildingId());
        userMapOngoingSchedule.setScheduleId(scheduleId);
        userMapOngoingSchedule.setStartTime(System.currentTimeMillis());
        userMapOngoingSchedule.setEndTime(System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(minutes));
        CommonScheduleConsumptionSnapshot consumptionSnapshot = new CommonScheduleConsumptionSnapshot();
        consumptionSnapshot.setEnergyCost(energy);
        userMapOngoingSchedule.setConsumptionSnapshot(consumptionSnapshot);
        CommonScheduleSettlementSnapshot settlementSnapshot = new CommonScheduleSettlementSnapshot();
        settlementSnapshot.setPeriodCount(periodCount);
        List<CommonPrize> commonPrizeList = new ArrayList<>();
        Map<Long, Prize> prizeMap = gameCardPrizeService.getOnlinePrizeMapByPrizeIdList(prizeIds);
        for (Long prizeId : prizeIds) {
            Prize prize = prizeMap.get(prizeId);
            if (prize == null) {
                log.error("CommonScheduleService create schedule {} prize {} not found, userId:{}, mapId:{}, scheduleId:{}", scheduleId, prizeId, userId, mapId, scheduleId);
                throw new RuntimeException(String.format("CommonScheduleService create schedule %d prize %d not found, userId:%d, mapId:%d, scheduleId:%d", scheduleId, prizeId, userId, mapId, scheduleId));
            }
            CommonPrize commonPrize = new CommonPrize();
            commonPrize.setPrizeId(prizeId);
            commonPrize.setPrizeName(prize.getName());
            commonPrize.setPrizeIcon(prize.getImgUrl());
            commonPrizeList.add(commonPrize);
        }
        settlementSnapshot.setCommonPrizes(commonPrizeList);
        StoryPrize storyPrize = new StoryPrize();
        storyPrize.setRepoId(schedule.getConfig().getNumericalConfig().getRepoId());
        storyPrize.setProbability(schedule.getConfig().getNumericalConfig().getProbability() / 100.0f);
        settlementSnapshot.setStoryPrize(storyPrize);
        userMapOngoingSchedule.setSettlementSnapshot(settlementSnapshot);
        userMapOngoingScheduleRepository.insert(userMapOngoingSchedule);
        // 扣减行动力
        String orderId = String.valueOf(BufferedIdGenerator.getId());
        userMapComponent.updateUserMapEnergy(userId, mapId, -energy, BufferedIdGenerator.getId(), EnergyAssignSource.CREATE_SCHEDULE, orderId);

        CommonScheduleCreateModel commonScheduleCreateModel = new CommonScheduleCreateModel();
        commonScheduleCreateModel.setMapId(mapId);
        commonScheduleCreateModel.setUserScheduleId(userMapOngoingSchedule.getId());
        commonScheduleCreateModel.setScheduleId(scheduleId);
        commonScheduleCreateModel.setStartTime(userMapOngoingSchedule.getStartTime());
        commonScheduleCreateModel.setEndTime(userMapOngoingSchedule.getEndTime());
        return RpcResult.success(commonScheduleCreateModel);
    }

    @Transactional(rollbackFor = Exception.class)
    public RpcResult<CommonScheduleResultModel> stop(int userId, UserMapOngoingSchedule userMapOngoingSchedule, String orderId) {
        int userScheduleId = userMapOngoingSchedule.getId();
        // 判断是否达到完成时间
        if (userMapOngoingSchedule.getEndTime() <= System.currentTimeMillis()) {
            log.error("UserCommonScheduleComponent stopSchedule user {} schedule {} already over", userId, userScheduleId);
            return RpcResult.result(RoleGameResponse.ONGOING_SCHEDULE_NOT_OVER);
        }
        int result = userMapOngoingScheduleRepository.delete(userScheduleId);
        if (result != 1) {
            log.error("UserCommonScheduleComponent stopSchedule user {} schedule {} delete failed", userId, userScheduleId);
            throw new RuntimeException("UserCommonScheduleComponent stopSchedule user " + userId + " schedule " + userScheduleId + " delete failed");
        }
        // 插入 finish 表
        UserMapFinishSchedule userMapFinishSchedule = getUserMapFinishSchedule(userId, userMapOngoingSchedule, UserScheduleStatus.CANCEL, orderId);
        userMapFinishScheduleRepository.insert(userMapFinishSchedule);
        // 返回行动力
        int energyCost = userMapOngoingSchedule.getConsumptionSnapshot().getEnergyCost();
        userMapComponent.updateUserMapEnergy(userId, userMapOngoingSchedule.getMapId(), energyCost, BufferedIdGenerator.getId(),
                EnergyAssignSource.STOP_SCHEDULE, String.valueOf(BufferedIdGenerator.getId()));
        CommonScheduleResultModel commonScheduleResultModel = new CommonScheduleResultModel();
        commonScheduleResultModel.setScheduleId(userMapOngoingSchedule.getScheduleId());
        commonScheduleResultModel.setConsumeMinutes(0);
        commonScheduleResultModel.setConsumeEnergy(0);
        return RpcResult.success(commonScheduleResultModel);
    }

    @Transactional(rollbackFor = Exception.class)
    public RpcResult<CommonScheduleResultModel> complete(ClientInfo clientInfo, int userId, UserMapOngoingSchedule userMapOngoingSchedule, String orderId) {
        int userScheduleId = userMapOngoingSchedule.getId();
        if (userMapOngoingSchedule == null) {
            log.error("UserCommonScheduleComponent finishSchedule user {} schedule {} not found", userId, userScheduleId);
            return RpcResult.result(RoleGameResponse.SCHEDULE_NOT_EXIST);
        }
        // 判断是否达到完成时间
        if (userMapOngoingSchedule.getEndTime() > System.currentTimeMillis()) {
            log.error("UserCommonScheduleComponent finishSchedule user {} schedule {} not reach end time", userId, userScheduleId);
            return RpcResult.result(RoleGameResponse.ONGOING_SCHEDULE_NOT_OVER);
        }
        int result = userMapOngoingScheduleRepository.delete(userScheduleId);
        if (result != 1) {
            log.error("UserCommonScheduleComponent finishSchedule user {} schedule {} delete failed", userId, userScheduleId);
            throw new RuntimeException("UserCommonScheduleComponent finishSchedule user " + userId + " schedule " + userScheduleId + " delete failed");
        }
        // 写入 finish 表
        UserMapFinishSchedule userMapFinishSchedule = getUserMapFinishSchedule(userId, userMapOngoingSchedule, UserScheduleStatus.FINISH, orderId);
        userMapFinishScheduleRepository.insert(userMapFinishSchedule);
        // 发放奖励
        CommonScheduleSettlementSnapshot settlementSnapshot = userMapOngoingSchedule.getSettlementSnapshot();
        List<CommonScheduleResultModel.CommonPrizeModel> commonPrizeModels = Lists.newArrayList();
        CommonScheduleResultModel.StoryPrizeModel storyPrizeModel = null;
        if (settlementSnapshot != null) {
            int periodCount = settlementSnapshot.getPeriodCount();
            List<CommonPrize> commonPrizes = settlementSnapshot.getCommonPrizes();
            if (commonPrizes != null) {
                ClaimPrizeParamV2 claimPrizeParam = new ClaimPrizeParamV2();
                claimPrizeParam.setUserId(userId);
                Map<Long, Integer> prizeCountMap = new HashMap<>();
                commonPrizes.forEach(commonPrize -> {
                    prizeCountMap.put(commonPrize.getPrizeId(), periodCount);
                    commonPrizeModels.add(CommonScheduleResultModel.CommonPrizeModel.valueOf(commonPrize.getPrizeId(), commonPrize.getPrizeName(), commonPrize.getPrizeIcon(), periodCount));
                });
                claimPrizeParam.setPrizeCountMap(prizeCountMap);
                claimPrizeParam.setXDevice(clientInfo.getXDevice());
                claimPrizeParam.setUserAgent(clientInfo.getUserAgent());
                claimPrizeParam.setPrizeSourceType(PrizeSourceType.SCHEDULE);
                claimPrizeComponent.claimPrize(claimPrizeParam);
            }
            StoryPrize storyPrize = settlementSnapshot.getStoryPrize();
            if (storyPrize != null) {
                storyPrizeModel = getAndUnlockMapStory(userId, userMapOngoingSchedule, storyPrizeModel, periodCount,
                        storyPrize);
            }
        }
        CommonScheduleResultModel commonScheduleResultModel = new CommonScheduleResultModel();
        commonScheduleResultModel.setScheduleId(userMapOngoingSchedule.getScheduleId());
        commonScheduleResultModel.setConsumeMinutes(0);
        commonScheduleResultModel.setConsumeEnergy(userMapOngoingSchedule.getConsumptionSnapshot().getEnergyCost());
        commonScheduleResultModel.setCommonPrizes(commonPrizeModels);
        commonScheduleResultModel.setStoryPrize(storyPrizeModel);
        return RpcResult.success(commonScheduleResultModel);
    }

    private CommonScheduleResultModel.StoryPrizeModel getAndUnlockMapStory(int userId,
            UserMapOngoingSchedule userMapOngoingSchedule, CommonScheduleResultModel.StoryPrizeModel storyPrizeModel,
            int periodCount, StoryPrize storyPrize) {
        float probability = storyPrize.getProbability();
        float finalProbability = probability * periodCount;
        if (finalProbability > 0) {
            // 随机数计算概率
            Random random = new Random();
            if (random.nextFloat() < finalProbability) {
                UnlockMapStoryModel unlockMapStoryModel = userMapStoryComponent.unlockMapStoryModelByLibId(userId, userMapOngoingSchedule.getMapId(), storyPrize.getRepoId());
                if (unlockMapStoryModel != null) {
                    RpcResult<Map<Integer, AvgChapterModel>> avgChapterBasicInfoList = avgService.batchQueryAvgChapterBasicInfo(Lists.newArrayList(unlockMapStoryModel.getAvgChapterId()));
                    if (avgChapterBasicInfoList.isSuccess() && avgChapterBasicInfoList.getData() != null) {
                        AvgChapterModel avgChapterModel = avgChapterBasicInfoList.getData().get(unlockMapStoryModel.getAvgChapterId());
                        if (avgChapterModel != null) {
                            storyPrizeModel = CommonScheduleResultModel.StoryPrizeModel.valueOf(avgChapterModel.getChapterId(), avgChapterModel.getChapterName());
                        }
                    }
                }
                log.info("getAndUnlockMapStory userId:{} mapId:{} libId:{} storyPrizeModel:{},unlockMapStoryModel:{}", userId, userMapOngoingSchedule.getMapId(), storyPrize.getRepoId(), storyPrizeModel, unlockMapStoryModel);
            }
        }
        return storyPrizeModel;
    }

    private UserMapFinishSchedule getUserMapFinishSchedule(int userId, UserMapOngoingSchedule userMapOngoingSchedule, UserScheduleStatus status,
                                                           String orderId) {
        UserMapFinishSchedule userMapFinishSchedule = new UserMapFinishSchedule();
        userMapFinishSchedule.setUserId(userId);
        userMapFinishSchedule.setMapId(userMapOngoingSchedule.getMapId());
        userMapFinishSchedule.setBuildingId(userMapOngoingSchedule.getBuildingId());
        userMapFinishSchedule.setScheduleId(userMapOngoingSchedule.getScheduleId());
        userMapFinishSchedule.setStartTime(userMapOngoingSchedule.getStartTime());
        userMapFinishSchedule.setEndTime(userMapOngoingSchedule.getEndTime());
        userMapFinishSchedule.setConsumptionSnapshot(userMapOngoingSchedule.getConsumptionSnapshot());
        userMapFinishSchedule.setSettlementSnapshot(userMapOngoingSchedule.getSettlementSnapshot());
        userMapFinishSchedule.setExtraInfo(new UserMapFinishSchedule.ExtraInfo());
        userMapFinishSchedule.setStatus(status.getStatus());
        userMapFinishSchedule.setThirdId(orderId);
        return userMapFinishSchedule;
    }
}
