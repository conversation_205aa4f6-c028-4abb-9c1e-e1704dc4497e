package com.kuaikan.role.game.repository;

import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import com.kuaikan.role.game.api.bean.UserItemFlow;
import com.kuaikan.role.game.api.enums.UserTableEnum;
import com.kuaikan.role.game.dao.rolegame.UserItemFlowMapper;
import com.kuaikan.role.game.uitl.TablePartitionUtil;

/**
 * UserItemFlowRepository
 *
 * <AUTHOR>
 * @since 2024-08-07
 */
@Repository
public class UserItemFlowRepository {

    @Resource
    private UserItemFlowMapper userItemFlowMapper;

    public int insert(UserItemFlow userItemFlow) {
        int userId = userItemFlow.getUserId();
        String tableName = getTableName(userId);
        return userItemFlowMapper.insert(tableName, userItemFlow);
    }

    public UserItemFlow queryByUserIdAndOrderId(int userId, int itemType, String orderId) {
        String tableName = getTableName(userId);
        return userItemFlowMapper.queryByUserIdAndOrderId(tableName, userId, itemType, orderId);
    }

    private String getTableName(int userId) {
        return TablePartitionUtil.getTableName(UserTableEnum.USER_ITEM_FLOW, userId);
    }
}
