package com.kuaikan.role.game.dao.rolegame;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.kuaikan.role.game.api.bean.BuildingArea;

/**
 * BuildingAreaMapper
 *
 * <AUTHOR>
 * @since 2024-06-07
 */
public interface BuildingAreaMapper {

    List<BuildingArea> queryByBuildingId(@Param("buildingId") int buildingId);

    BuildingArea queryById(@Param("id") int id);

    List<BuildingArea> queryAll();
}
