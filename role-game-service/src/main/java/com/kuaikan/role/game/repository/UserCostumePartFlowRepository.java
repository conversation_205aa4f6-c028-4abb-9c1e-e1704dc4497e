package com.kuaikan.role.game.repository;

import java.util.List;
import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import com.kuaikan.role.game.api.bean.UserCostumePartFlow;
import com.kuaikan.role.game.api.enums.UserTableEnum;
import com.kuaikan.role.game.dao.rolegame.UserCostumePartFlowMapper;
import com.kuaikan.role.game.uitl.TablePartitionUtil;

/**
 * UserCostumePartFlowRepository
 *
 * <AUTHOR>
 * @since 2024-04-25
 */
@Repository
public class UserCostumePartFlowRepository {

    @Resource
    private UserCostumePartFlowMapper userCostumePartFlowMapper;

    public int insert(UserCostumePartFlow userCostumePartFlow) {
        String tableName = getTableName(userCostumePartFlow.getUserId());
        return userCostumePartFlowMapper.insert(tableName, userCostumePartFlow);
    }

    public int insertBatch(List<UserCostumePartFlow> userCostumePartFlows) {
        if (CollectionUtils.isEmpty(userCostumePartFlows)) {
            return 0;
        }
        int userId = userCostumePartFlows.get(0).getUserId();
        String tableName = getTableName(userId);
        return userCostumePartFlowMapper.insertBatch(tableName, userCostumePartFlows);
    }

    public UserCostumePartFlow queryUserCostumePartFlow(int userId, int costumePartId, String thirdId) {
        String tableName = getTableName(userId);
        return userCostumePartFlowMapper.queryUserCostumePartFlow(tableName, userId, costumePartId, thirdId);
    }

    private String getTableName(int userId) {
        return TablePartitionUtil.getTableName(UserTableEnum.USER_COSTUME_PART_FLOW, userId);
    }

}
