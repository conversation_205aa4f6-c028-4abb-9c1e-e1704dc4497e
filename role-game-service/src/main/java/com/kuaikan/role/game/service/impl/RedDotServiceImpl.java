package com.kuaikan.role.game.service.impl;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.dubbo.config.annotation.DubboService;
import org.jooq.tools.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import com.kuaikan.common.model.RpcResult;
import com.kuaikan.role.game.api.bean.RoleAdoptCouponConfig;
import com.kuaikan.role.game.api.model.ActivityEntryModel;
import com.kuaikan.role.game.api.model.AdoptCouponModel;
import com.kuaikan.role.game.api.rpc.param.AppBarRedDotParam;
import com.kuaikan.role.game.api.rpc.result.AppBarRedDotModel;
import com.kuaikan.role.game.api.rpc.result.RedDotEventInfoModel;
import com.kuaikan.role.game.api.service.RedDotService;
import com.kuaikan.role.game.common.bean.RedDotEventInfo;
import com.kuaikan.role.game.common.bean.RoleGroupAdoptCouponRelation;
import com.kuaikan.role.game.common.enums.RedDotEventType;
import com.kuaikan.role.game.component.CouponComponent;
import com.kuaikan.role.game.component.RedDotComponent;
import com.kuaikan.role.game.component.UserComponent;
import com.kuaikan.role.game.repository.CouponRepository;
import com.kuaikan.role.game.uitl.ThreadPoolConfig;
import com.kuaikan.user.model.BizResult;

/**
 * <AUTHOR>
 * @version 2024-07-17
 */
@Slf4j
@DubboService(version = "1.0", group = "role-game")
public class RedDotServiceImpl implements RedDotService {

    @Resource
    private UserComponent userComponent;
    @Resource
    private RedDotComponent redDotComponent;
    @Resource
    private CouponRepository couponRepository;
    @Resource
    private CouponComponent couponComponent;
    @Resource
    private GameUserServiceImpl gameUserServiceImpl;

    @Override
    public RpcResult<AppBarRedDotModel> getAppBarRedDot(AppBarRedDotParam param) {
        boolean costumeRedDot = false;
        boolean collectionRedDot = false;
        boolean scheduleRedDot = false;
        boolean grainCabinetRedDot = false;
        boolean questContainerRedDot = false;
        boolean itemRedDot = false;
        boolean feedRedDot = false;
        boolean roleMoodEmoRedDot = false;
        // 处理心情自然衰减
        ThreadPoolConfig.HEARTBEAT_EXECUTOR.submit(() -> gameUserServiceImpl.naturalDecayMood(param.getUserId()));
        ActivityEntryModel activityEntryModel = userComponent.getActivityEntry(param.getUserId(), false);
        if (activityEntryModel != null) {
            costumeRedDot = activityEntryModel.isCostumeRedDot();
            collectionRedDot = activityEntryModel.isCollectionRedDot();
            scheduleRedDot = activityEntryModel.isScheduleRedDot();
            grainCabinetRedDot = activityEntryModel.isAddRoleRedDot();
            questContainerRedDot = activityEntryModel.isQuestContainerRedDot();
            itemRedDot = activityEntryModel.isInteractiveItemRedDot();
            feedRedDot = activityEntryModel.isFeedRedDot();
            roleMoodEmoRedDot = activityEntryModel.isRoleMoodEmoRedDot();
        }
        boolean hasRedDot = costumeRedDot || collectionRedDot || scheduleRedDot || grainCabinetRedDot || questContainerRedDot || itemRedDot || feedRedDot || roleMoodEmoRedDot;
        AppBarRedDotModel appBarRedDotModel = new AppBarRedDotModel();
        appBarRedDotModel.setMessage(StringUtils.EMPTY);
        appBarRedDotModel.setUnread(hasRedDot ? 1 : 0);
        log.debug(
                "getAppBarRedDot success costumeRedDot:{}, collectionRedDot:{},scheduleRedDot:{},grainCabinetRedDot:{}, itemRedDot:{}, feedRedDot:{}, roleMoodEmoRedDot:{}. userId:{}",
                costumeRedDot, collectionRedDot, scheduleRedDot, grainCabinetRedDot, itemRedDot, feedRedDot, roleMoodEmoRedDot, param.getUserId());
        return RpcResult.success(appBarRedDotModel);
    }

    @Override
    public RpcResult<Void> clearAddRoleEvent(Integer userId) {
        redDotComponent.clearAddRoleEvent(userId);
        return RpcResult.success();
    }

    @Override
    public RpcResult<Void> sendAddRoleEvent() {
        redDotComponent.sendAddRoleEvent();
        return RpcResult.success();
    }

    @Override
    public RpcResult<Void> clearAddCostumeEvent(Integer userId) {
        redDotComponent.clearAddCostumeEvent(userId);
        return RpcResult.success();
    }

    @Override
    public RpcResult<Void> clearCostumeNewEvent(Integer userId, Integer costumeId) {
        redDotComponent.clearCostumeNewEvent(userId, costumeId);
        return RpcResult.success();
    }

    @Override
    public RpcResult<Void> sendAddCostumeEvent(Integer costumeId) {
        redDotComponent.sendAddCostumeEvent(costumeId);
        return RpcResult.success();
    }

    @Override
    public RpcResult<Void> sendAdoptCouponEvent(List<Integer> userIds, int couponId, RedDotEventInfoModel redDotEventInfoModel) {
        log.debug("start add coupon red dot, userIds:{}, couponId:{}", userIds, couponId);
        // 判断该券是最优折扣的时候才增加红点事件
        RoleGroupAdoptCouponRelation relation = couponRepository.queryRelationByCouponId(couponId);
        if (Objects.isNull(relation)) {
            log.debug("this coupon config is null, couponId={}", couponId);
            return RpcResult.success();
        }
        int newCouponGroupId = relation.getRoleGroupId();
        RoleAdoptCouponConfig newCouponConfig = couponRepository.selectAdoptCouponConfigById(couponId);
        RedDotEventInfo redDotEventInfo = new RedDotEventInfo();
        BeanUtils.copyProperties(redDotEventInfoModel, redDotEventInfo);
        userIds.forEach(userId -> {
                log.debug("send coupon red dot, userId:{}, couponId:{}", userId, couponId);
                BizResult<AdoptCouponModel> couponList = couponComponent.queryAdoptDiscountCouponList(userId);
                if (!couponList.isSuccess()) {
                    log.debug("this coupon list is null, couponId={}", couponId);
                    return ;
                }
                List<AdoptCouponModel.CouponModel> available = couponList.getData().getAvailableList();
                if (CollectionUtils.isEmpty(available)) {
                    log.debug("not coupon , userId={}, couponId={}, coupons={}", userId, couponId, available);
                    return ;
                }
                boolean isLaunch = true;
                for (AdoptCouponModel.CouponModel model : available) {
                    if (model.getRoleGroupsModel().getId() == newCouponGroupId && model.getRoleAdoptCoupon().getCouponId() != couponId) {
                        int rateSort = Double.compare(Double.parseDouble(newCouponConfig.getExtraInfo().getDiscountRate()), Double.parseDouble(model.getRoleAdoptCoupon().getDiscountRate()));
                        if (rateSort >= 0) {
                            isLaunch = false;
                            break;
                        }
                    }
                }
                log.debug("send redDot or no ? ,userId:{}, couponId:{},isLaunch={}", userId, couponId, isLaunch);
                if (isLaunch) {
                    redDotComponent.sendP2PUserRecord(userId, couponId, redDotEventInfo, RedDotEventType.ROLE_ADOPT_COUPON.getCode());
                }
        });
        return RpcResult.success();
    }

    @Override
    public RpcResult<Set<Integer>> clearAdoptCouponEvent(int userId, int couponId) {
        redDotComponent.clearAdoptCouponEvent(userId, couponId);
        return RpcResult.success(redDotComponent.getAdoptCouponEventGroups(userId));
    }

    @Override
    public RpcResult<Boolean> getAdoptCouponEvent(int userId, int couponId) {
        Boolean couponRedEvent = redDotComponent.checkAdoptCouponEventByCouponId(userId, couponId);
        return RpcResult.success(couponRedEvent);
    }

    @Override
    public RpcResult<Void> clearAllAdoptCouponEvent(int userId) {
        redDotComponent.clearAllAdoptCouponEvent(userId);
        return RpcResult.success();
    }

    @Override
    public RpcResult<RedDotEventInfoModel> sendP2PEventInfo(int eventType, int couponId) {
        RedDotEventType redDotEventType = RedDotEventType.getByCode(eventType);
        RedDotEventInfo redDotEventInfo = redDotComponent.sendP2PEventInfo(redDotEventType, couponId);
        RedDotEventInfoModel model = new RedDotEventInfoModel();
        BeanUtils.copyProperties(redDotEventInfo, model);
        return RpcResult.success(model);
    }

    @Override
    public RpcResult<Boolean> cleaRoleMoodEmoEvent(int userId) {
        return RpcResult.success(redDotComponent.clearRoleMoodEmoRedDot(userId));
    }

    @Override
    public RpcResult<Void> sendNewTaskEvent(List<Long> taskIdList) {
        redDotComponent.sendNewTaskEvent(taskIdList);
        return RpcResult.success();
    }

    @Override
    public RpcResult<Void> clearNewTaskEvent(Integer userId, Long taskId) {
        redDotComponent.clearNewTaskEvent(userId, taskId);
        return RpcResult.success();
    }

    @Override
    public RpcResult<Void> clearAddMapStoryEvent(Integer userId, Integer mapId,  Integer avgChapterId) {
        redDotComponent.clearAddMapStoryEvent(userId, mapId, avgChapterId);
        return RpcResult.success();
    }

    @Override
    public RpcResult<Void> sendAddMapStoryEvent(Integer mapId, Integer avgChapterId) {
        redDotComponent.sendAddMapStoryEvent(mapId, avgChapterId);
        return RpcResult.success();
    }

    public RpcResult<Boolean> userHasAddMapStoryEventPoint(Integer userId, Integer mapId, Integer avgChapterId) {
        Boolean is = redDotComponent.hasAddMapStoryEvent(userId, mapId, avgChapterId);
        return RpcResult.success(is);
    }

    @Override
    public RpcResult<Void> sendMapHomeAddStoryEvent(Integer mapId) {
        redDotComponent.sendBroadcastEvent(RedDotEventType.MAP_HOME_ADD_STORY, mapId, null);
        return RpcResult.success();
    }

    @Override
    public RpcResult<Void> clearMapHomeAddStoryAndAcceptStoryEvent(Integer userId, Integer mapId) {
        redDotComponent.clearBroadcastEvent(userId, RedDotEventType.MAP_HOME_ADD_STORY, mapId);
        redDotComponent.clearUserAcceptStoryPoint(RedDotEventType.MAP_HOME_USER_ACCEPT_STORY, userId, mapId, null);
        return RpcResult.success();
    }

    @Override
    public RpcResult<Void> sendAddMapStoryTagEvent(Integer mapId, String tag) {
        redDotComponent.sendAddStoryTagEvent(mapId, tag);
        return RpcResult.success();
    }

    @Override
    public RpcResult<Void> clearMapTagAddStoryAndAcceptStoryEvent(Integer userId, Integer mapId, String tag) {
        redDotComponent.clearAddStoryTagEvent(userId, mapId, tag);
        redDotComponent.clearUserAcceptStoryPoint(RedDotEventType.MAP_USER_ACCEPT_STORY_TAG, userId, mapId, tag);
        return RpcResult.success();
    }
}
