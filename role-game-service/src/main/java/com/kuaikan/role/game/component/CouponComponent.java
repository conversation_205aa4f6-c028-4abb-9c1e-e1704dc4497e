package com.kuaikan.role.game.component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.beust.jcommander.internal.Lists;

import com.kuaikan.role.game.api.bean.Role;
import com.kuaikan.role.game.api.bean.RoleAdoptCouponConfig;
import com.kuaikan.role.game.api.bean.UserAdoptCouponRecord;
import com.kuaikan.role.game.api.bean.UserBlindBoxCouponRecord;
import com.kuaikan.role.game.api.enums.CouponConfigType;
import com.kuaikan.role.game.api.enums.CouponStatusType;
import com.kuaikan.role.game.api.model.AdoptCouponModel;
import com.kuaikan.role.game.api.model.BlindBoxCouponImageUrlModel;
import com.kuaikan.role.game.api.model.BlindBoxCouponModel;
import com.kuaikan.role.game.api.model.CouponModel;
import com.kuaikan.role.game.api.rpc.result.OffPriceModel;
import com.kuaikan.role.game.common.bean.RoleGroupAdoptCouponRelation;
import com.kuaikan.role.game.common.bean.RoleGroupBlindBoxCouponRelation;
import com.kuaikan.role.game.common.bean.RoleGroupRelation;
import com.kuaikan.role.game.config.ApolloConfig;
import com.kuaikan.role.game.repository.CouponRepository;
import com.kuaikan.role.game.repository.RoleGroupBlindBoxCouponRelationRepository;
import com.kuaikan.role.game.repository.RoleGroupRelationRepository;
import com.kuaikan.role.game.repository.RoleRepository;
import com.kuaikan.role.game.repository.UserBlindBoxCouponRecordRepository;
import com.kuaikan.role.game.uitl.DateUtil;
import com.kuaikan.user.model.BizResult;

/**
 * <AUTHOR>
 * @date 2024/10/28
 */
@Slf4j
@Component
public class CouponComponent {

    private static final String ADOPT_COUPON_TIP = "领养第2个角色可享折扣";

    @Resource
    private RoleGroupRelationRepository roleGroupRelationRepository;
    @Resource
    private RoleRepository roleRepository;
    @Resource
    private CouponRepository couponRepository;
    @Resource
    private RoleGroupComponent roleGroupComponent;
    @Resource
    private RedDotComponent redDotComponent;
    @Resource
    private ApolloConfig apolloConfig;
    @Resource
    private UserBlindBoxCouponRecordRepository userBlindBoxCouponRecordRepository;
    @Resource
    private RoleGroupBlindBoxCouponRelationRepository roleGroupBlindBoxCouponRelationRepository;

    public BizResult<AdoptCouponModel> queryAdoptDiscountCouponList(int userId) {
        List<UserAdoptCouponRecord> userAdoptCouponRecords = couponRepository.selectAdoptCouponRecordByUserId(userId)
                .stream()
                .filter(coupon -> coupon.getExpiredAt().after(new Date()) && !coupon.getUsed())
                .sorted(Comparator.comparing(UserAdoptCouponRecord::getCreatedAt).reversed())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userAdoptCouponRecords)) {
            return BizResult.success(new AdoptCouponModel());
        }
        List<Integer> distinctAdoptCouponIds = userAdoptCouponRecords.stream().map(UserAdoptCouponRecord::getCouponId).distinct().collect(Collectors.toList());
        // 2.查询折扣券信息
        List<RoleAdoptCouponConfig> coupons = couponRepository.selectAdoptCouponConfigByIds(distinctAdoptCouponIds);
        Map<Integer, RoleAdoptCouponConfig> configMap = coupons.stream()
                .filter(couponConfig -> couponConfig.getStatus().equals(CouponStatusType.ONLINE.getCode()))
                .collect(Collectors.toMap(RoleAdoptCouponConfig::getId, config -> config));

        List<CouponModel> couponModels = userAdoptCouponRecords.stream().filter(record -> configMap.get(record.getCouponId()) != null).map(record -> {
            CouponModel couponModel = CouponModel.valueOf(record);
            RoleAdoptCouponConfig config = configMap.get(record.getCouponId());
            return couponModel.setRoleAdoptCouponConfig(config);
        }).collect(Collectors.toList());
        // 查询用户未完全领养的角色组id
        List<Integer> notAllAdoptRoleGroupIds = roleGroupComponent.getNotAllAdoptRoleGroupIdList(userId);

        // 3.区分完全领养和未完全领养
        List<CouponModel> notAllAdoptCoupons = Lists.newArrayList();
        List<Integer> couponIds = coupons.stream().map(RoleAdoptCouponConfig::getId).collect(Collectors.toList());
        // 该用户所有可用券的关联表数据
        Map<Integer, RoleGroupAdoptCouponRelation> relationMap = couponRepository.selectRoleGroupAdoptCouponRelationByCouponIds(couponIds)
                .stream()
                .collect(Collectors.toMap(RoleGroupAdoptCouponRelation::getCouponId, v -> v));
        for (CouponModel coupon : couponModels) {
            RoleGroupAdoptCouponRelation relation = relationMap.get(coupon.getCouponId());
            if (notAllAdoptRoleGroupIds.contains(relation.getRoleGroupId())) {
                notAllAdoptCoupons.add(coupon);
            }
        }

        // 4.从未完全领养的数据里选出最大折扣的券
        notAllAdoptCoupons.sort((o1, o2) -> {
            int rateSort = Double.compare(Double.parseDouble(o1.getRoleAdoptCouponConfig().getExtraInfo().getDiscountRate()),
                    Double.parseDouble(o2.getRoleAdoptCouponConfig().getExtraInfo().getDiscountRate()));
            if (rateSort != 0) {
                return rateSort;
            } else {
                return o2.getExpiredAt().compareTo(o1.getExpiredAt());
            }
        });
        List<CouponModel> uniqueCoupons = Lists.newArrayList();
        for (Integer id : notAllAdoptRoleGroupIds) {
            Iterator<CouponModel> iterator = notAllAdoptCoupons.iterator();
            while (iterator.hasNext()) {
                CouponModel couponModel = iterator.next();
                RoleAdoptCouponConfig coupon = couponModel.getRoleAdoptCouponConfig();
                RoleGroupAdoptCouponRelation relation = relationMap.get(coupon.getId());
                if (id.equals(relation.getRoleGroupId())) {
                    uniqueCoupons.add(couponModel);
                    iterator.remove();
                    break;
                }
            }
        }

        // 5.全部可用的券-最大折扣券=重复的券
        List<CouponModel> duplicateAdoptCoupons = Lists.newArrayList(couponModels);
        duplicateAdoptCoupons.removeAll(uniqueCoupons);
        List<CouponModel> rankUniqueCoupons = uniqueCoupons.stream()
                .sorted(Comparator.comparing(CouponModel::getCreatedAt).reversed())
                .collect(Collectors.toList());

        // 6.对两种类型的券进行处理，查询出图片的数据
        List<AdoptCouponModel.CouponModel> available = queryAdoptCouponInfo(rankUniqueCoupons);
        List<AdoptCouponModel.CouponModel> duplicate = queryAdoptCouponInfo(duplicateAdoptCoupons);
        return BizResult.success(new AdoptCouponModel().setAvailableList(available).setDuplicateList(duplicate));
    }

    /**
     * 计算角色折后价格
     *
     * @param uid
     * @param groupId       角色组id
     * @param originalPrice 角色未打折前的价格
     * @return
     */
    public OffPriceModel calRoleOffPrice(int uid, int groupId, int originalPrice) {
        BizResult<AdoptCouponModel> couponResult = queryAdoptDiscountCouponList(uid);
        if (!couponResult.isSuccess() || couponResult.getData() == null) {
            log.debug("this user has no coupon , uid={}", uid);
            return null;
        }
        AdoptCouponModel couponModel = couponResult.getData();
        List<AdoptCouponModel.CouponModel> availableList = couponModel.getAvailableList();
        if (CollectionUtils.isEmpty(availableList)) {
            return null;
        }

        Map<Integer, Long> couponMap = availableList.stream()
                .map(AdoptCouponModel.CouponModel::getRoleAdoptCoupon)
                .collect(Collectors.toMap(AdoptCouponModel.RoleAdoptCouponModel::getCouponId, AdoptCouponModel.RoleAdoptCouponModel::getBid));
        Set<Integer> couponIds = couponMap.keySet();

        Map<Integer, Integer> couponGroupRelationMap = couponRepository.selectRoleGroupAdoptCouponRelationByCouponIds(couponIds)
                .stream()
                .collect(Collectors.toMap(RoleGroupAdoptCouponRelation::getRoleGroupId, RoleGroupAdoptCouponRelation::getCouponId));
        Integer couponId = couponGroupRelationMap.get(groupId);
        if (couponId == null || couponId <= 0) {
            log.debug("this coupon is null  , uid={}，couponId={}", uid, couponId);
            return null;
        }
        Long bid = couponMap.get(couponId);
        UserAdoptCouponRecord userAdoptCouponRecord = couponRepository.selectAdoptCouponRecordByBId(uid, bid);
        if (userAdoptCouponRecord == null) {
            log.debug("user no this coupon , uid={}，bid= {} ,couponId={}", uid, bid, couponId);
            return null;
        }
        RoleAdoptCouponConfig config = couponRepository.selectAdoptCouponConfigById(couponId);
        if (config == null) {
            log.debug(" this coupon config id null , uid={} ,couponId={}", uid, couponId);
            return null;
        }

        LocalDateTime expiredAt = userAdoptCouponRecord.getExpiredAt().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();

        boolean hasBestCoupon = redDotComponent.getAdoptCouponEventGroups(uid).contains(groupId);
        OffPriceModel model = new OffPriceModel();
        model.setCouponId(config.getId());
        model.setGiftKkbOffPrice(
                new BigDecimal(originalPrice).multiply(new BigDecimal(config.getExtraInfo().getDiscountRate()).divide(BigDecimal.TEN, 2, RoundingMode.HALF_UP))
                        .toString());
        model.setDiscountRate(config.getExtraInfo().getDiscountRate());
        model.setValidDays(DateUtil.calculateDaysBetween(LocalDateTime.now(), expiredAt));
        model.setShowRedDot(hasBestCoupon);
        model.setBid(bid);
        return model;
    }

    public int calRoleOffPriceByCouponId(int couponId, int originalPrice) {
        RoleAdoptCouponConfig couponConfig = couponRepository.selectAdoptCouponConfigById(couponId);
        if (Objects.isNull(couponConfig) || !couponConfig.getStatus().equals(CouponStatusType.ONLINE.getCode())) {
            return originalPrice;
        }
        BigDecimal discountRate = new BigDecimal(couponConfig.getExtraInfo().getDiscountRate()).divide(BigDecimal.TEN, 2, RoundingMode.HALF_UP);
        return new BigDecimal(originalPrice).multiply(discountRate).intValue();
    }

    /**
     * 计算可兑换能量药水数
     *
     * @param couponIds
     * @return
     */
    public int calcEnergyBottleCount(List<Integer> couponIds, List<UserAdoptCouponRecord> records) {
        int count = 0;
        if (!CollectionUtils.isEmpty(records)) {
            Map<Integer, RoleAdoptCouponConfig> couponConfigMap = couponRepository.selectAdoptCouponConfigByIds(couponIds)
                    .stream()
                    .filter(couponConfig -> couponConfig.getStatus().equals(CouponStatusType.ONLINE.getCode()))
                    .collect(Collectors.toMap(RoleAdoptCouponConfig::getId, Function.identity()));
            for (UserAdoptCouponRecord record : records) {
                Integer couponId = record.getCouponId();
                count = count + couponConfigMap.get(couponId).getExtraInfo().getEnergyCount();
            }
        } else {
            List<RoleAdoptCouponConfig> couponConfigs = couponRepository.selectAdoptCouponConfigByIds(couponIds)
                    .stream()
                    .filter(couponConfig -> couponConfig.getStatus().equals(CouponStatusType.ONLINE.getCode()))
                    .collect(Collectors.toList());
            for (RoleAdoptCouponConfig couponConfig : couponConfigs) {
                count = count + couponConfig.getExtraInfo().getEnergyCount();
            }
        }
        return count;
    }

    private List<AdoptCouponModel.CouponModel> queryAdoptCouponInfo(List<CouponModel> coupons) {
        if (CollectionUtils.isEmpty(coupons)) {
            return new ArrayList<>();
        }
        List<AdoptCouponModel.CouponModel> couponModels = Lists.newArrayList();
        List<Integer> couponIds = coupons.stream().map(CouponModel::getCouponId).collect(Collectors.toList());
        Map<Integer, RoleGroupAdoptCouponRelation> couponRelationMap = couponRepository.selectRoleGroupAdoptCouponRelationByCouponIds(couponIds)
                .stream()
                .collect(Collectors.toMap(RoleGroupAdoptCouponRelation::getCouponId, v -> v));
        List<Integer> groupIds = couponRelationMap.values().stream().map(RoleGroupAdoptCouponRelation::getRoleGroupId).distinct().collect(Collectors.toList());
        Map<Integer, List<RoleGroupRelation>> groupRelationMap = roleGroupRelationRepository.queryByGroupIdsFromCache(groupIds);
        Map<Integer, Role> roleMap = roleRepository.queryAllFromCache().stream().collect(Collectors.toMap(Role::getId, v -> v));
        for (CouponModel coupon : coupons) {
            AdoptCouponModel.RoleAdoptCouponModel adoptCouponModel = new AdoptCouponModel.RoleAdoptCouponModel().setCouponId(
                            coupon.getRoleAdoptCouponConfig().getId())
                    .setBid(coupon.getBid())
                    .setTipContent(ADOPT_COUPON_TIP)
                    .setExpiryTime(DateUtil.formatDD7(coupon.getExpiredAt()))
                    .setDiscountRate(coupon.getRoleAdoptCouponConfig().getExtraInfo().getDiscountRate())
                    .setEnergyBottleCount(coupon.getRoleAdoptCouponConfig().getExtraInfo().getEnergyCount());

            int roleGroupId = couponRelationMap.get(coupon.getRoleAdoptCouponConfig().getId()).getRoleGroupId();
            AdoptCouponModel.RoleGroupsModel roleGroups = new AdoptCouponModel.RoleGroupsModel();
            roleGroups.setId(roleGroupId);
            List<AdoptCouponModel.RoleModel> roleModels = Lists.newArrayList();
            List<RoleGroupRelation> roleGroupRelations = groupRelationMap.get(roleGroupId);
            for (RoleGroupRelation roleGroupRelation : roleGroupRelations) {
                Role role = roleMap.get(roleGroupRelation.getRoleId());
                AdoptCouponModel.RoleModel roleModel = new AdoptCouponModel.RoleModel();
                roleModel.setId(role.getId());
                roleModel.setName(role.getName());
                roleModel.setImage(role.getImage());
                roleModels.add(roleModel);
            }
            roleGroups.setRoleList(roleModels);

            AdoptCouponModel.CouponModel couponModel = new AdoptCouponModel.CouponModel();
            couponModel.setRoleAdoptCoupon(adoptCouponModel).setRoleGroupsModel(roleGroups);
            couponModels.add(couponModel);
        }
        return couponModels;
    }

    public BizResult<BlindBoxCouponModel> queryBlindBoxCouponList(int userId) {
        List<UserBlindBoxCouponRecord> records = userBlindBoxCouponRecordRepository.queryByUserId(userId)
                .stream()
                .filter(record -> record.getExpiredAt().after(new Date()) && !record.getUsed())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(records)) {
            log.info("this user has no available blind box coupon , uid={}", userId);
            return BizResult.success(new BlindBoxCouponModel());
        }
        // 限定角色组map<couponId,relation>
        Map<Integer, RoleGroupBlindBoxCouponRelation> relationMap = roleGroupBlindBoxCouponRelationRepository.queryByCouponIds(
                        records.stream().map(UserBlindBoxCouponRecord::getCouponId).collect(Collectors.toList()))
                .stream()
                .collect(Collectors.toMap(RoleGroupBlindBoxCouponRelation::getCouponId, v -> v));

        List<BlindBoxCouponModel.BlindBoxCoupon> blindBoxCouponList = records.stream()
                .map(record -> buildBlindBoxModel(record, relationMap.get(record.getCouponId())))
                .collect(Collectors.toList());
        return BizResult.success(new BlindBoxCouponModel().setBlindBoxCouponList(blindBoxCouponList));
    }

    private BlindBoxCouponModel.BlindBoxCoupon buildBlindBoxModel(UserBlindBoxCouponRecord record, RoleGroupBlindBoxCouponRelation relation) {
        BlindBoxCouponModel.BlindBoxCoupon model = new BlindBoxCouponModel.BlindBoxCoupon();
        model.setBid(record.getBid());
        model.setCouponId(record.getCouponId());
        model.setCreatedAt(record.getCreatedAt());
        model.setExpiryTime(DateUtil.formatDD7(record.getExpiredAt()));
        if (null != relation && relation.getRoleGroupId() != 0) {
            model.setRoleGroupId(relation.getRoleGroupId());
            model.setApplyType(CouponConfigType.BlindBoxType.SPECIFIC.getCode());
            model.setDisplayUrl(getDisplayUrl(CouponConfigType.BlindBoxType.SPECIFIC, relation.getRoleGroupId()));
        } else {
            model.setApplyType(CouponConfigType.BlindBoxType.COMMON.getCode());
            model.setDisplayUrl(getDisplayUrl(CouponConfigType.BlindBoxType.COMMON, null));
        }
        return model;
    }

    // apollo获取图片显示链接
    private String getDisplayUrl(CouponConfigType.BlindBoxType type, Integer roleGroupId) {
        BlindBoxCouponImageUrlModel apolloImageUrlModel = apolloConfig.getBlindBoxCouponImageUrlModel();
        if (apolloImageUrlModel == null) {
            log.error("apollo config blind_box_coupon_image_url is null");
            return null;
        }
        switch (type) {
            case COMMON:
                return apolloImageUrlModel.getCommonUrl();
            case SPECIFIC:
                if (roleGroupId == null) {
                    log.error("error in specific getDisplayUrl, roleGroupId is null");
                    throw new IllegalArgumentException("error in specific getDisplayUrl, roleGroupId is null");
                }
                Map<Integer, String> specificUrlMap = apolloImageUrlModel.getSpecificUrls()
                        .stream()
                        .collect(Collectors.toMap(BlindBoxCouponImageUrlModel.RoleGroupImage::getGroupId,
                                BlindBoxCouponImageUrlModel.RoleGroupImage::getImageUrl));
                String imageUrl = specificUrlMap.get(roleGroupId);
                if (imageUrl != null) {
                    return imageUrl;
                }
                log.error("error in getDisplayUrl, roleGroupId not matched, roleGroupId:{}", roleGroupId);
                throw new IllegalArgumentException("error in getDisplayUrl, roleGroupId not found, roleGroupId:" + roleGroupId);
            default:
                log.error("error in getDisplayUrl, type is not supported, type:{}", type);
                throw new IllegalArgumentException("error in getDisplayUrl, type is not supported, type:" + type);
        }
    }
}
