package com.kuaikan.role.game.controller;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kuaikan.role.game.api.bean.Costume;
import com.kuaikan.role.game.api.bean.Story;
import com.kuaikan.role.game.api.bean.UserBlindBoxTargetAward;
import com.kuaikan.role.game.api.bean.UserBlindBoxTargetAwardRecord;
import com.kuaikan.role.game.api.bean.UserInfo;
import com.kuaikan.role.game.api.bean.UserStory;
import com.kuaikan.role.game.api.enums.StoryType;
import com.kuaikan.role.game.common.bean.RoleGroupRelation;
import com.kuaikan.role.game.common.bean.UserStoryTabRedDotSendModel;
import com.kuaikan.role.game.component.RedDotComponent;
import com.kuaikan.role.game.component.RoleGroupComponent;
import com.kuaikan.role.game.dao.rolegame.UserBlindBoxTargetAwardMapper;
import com.kuaikan.role.game.dao.rolegame.UserBlindBoxTargetAwardRecordMapper;
import com.kuaikan.role.game.repository.CostumeRepository;
import com.kuaikan.role.game.repository.RoleGroupRelationRepository;
import com.kuaikan.role.game.repository.StoryRepository;
import com.kuaikan.role.game.repository.UserBlindBoxTargetAwardRepository;
import com.kuaikan.role.game.repository.UserInfoRepository;
import com.kuaikan.role.game.repository.UserStoryRepository;

/**
 * <AUTHOR>
 * @version 2024-11-22
 */
@Slf4j
@RestController
@RequestMapping("/v1/role/game/oldDataHandle")
public class OldDataHandleController {

    @Resource
    private RoleGroupComponent roleGroupComponent;
    @Resource
    private UserInfoRepository userInfoRepository;
    @Resource
    private UserStoryRepository userStoryRepository;
    @Resource
    private StoryRepository storyRepository;
    @Resource
    private RoleGroupRelationRepository roleGroupRelationRepository;
    @Resource
    private RedDotComponent redDotComponent;
    @Resource
    private UserBlindBoxTargetAwardRepository userBlindBoxTargetAwardRepository;

    @Resource
    private CostumeRepository costumeRepository;

    @GetMapping("/blindBox/initCostumeLevel")
    public String blindBoxInitCostumeLevel() {
        final int i = userInfoRepository.countAll();
        final Map<Integer, Costume> costumeMap = costumeRepository.queryAll().stream().collect(Collectors.toMap(Costume::getId, costume -> costume));
        log.info("blindBoxInitCostumeLevel start, total user count: {}", i);
        int pageSize = 1000;
        int count = 0;
        for (int j = 0; j < i; j += pageSize) {
            List<UserInfo> userInfos = userInfoRepository.queryByPage(j, pageSize);
            for (UserInfo userInfo : userInfos) {
                int userId = userInfo.getUserId();
                List<UserBlindBoxTargetAward> userBlindBoxTargetAwards = userBlindBoxTargetAwardRepository.queryByUserId(userId);
                for (UserBlindBoxTargetAward userBlindBoxTargetAward : userBlindBoxTargetAwards) {
                    final Costume costume = costumeMap.get(userBlindBoxTargetAward.getCostumeId());
                    if (costume == null) {
                        log.warn("blindBoxInitCostumeLevel userBlindBoxTargetAward costume is null, costumeId:{}, userId:{}", userBlindBoxTargetAward
                                .getCostumeId(), userId);
                        continue;
                    }
                    if (userBlindBoxTargetAward.getCostumeLevel() == 0) {
                        userBlindBoxTargetAwardRepository.updateAwardCostumeLevel(userId, userBlindBoxTargetAward.getId(),costume.getLevel());
                    }
                }
                List<UserBlindBoxTargetAwardRecord> userBlindBoxTargetAwardRecords = userBlindBoxTargetAwardRepository.queryRecordsByUserId(userId);
                for (UserBlindBoxTargetAwardRecord userBlindBoxTargetAwardRecord : userBlindBoxTargetAwardRecords) {
                    final Costume costume = costumeMap.get(userBlindBoxTargetAwardRecord.getCostumeId());
                    if (costume == null) {
                        log.warn("blindBoxInitCostumeLevel userBlindBoxTargetAwardRecord costume is null, costumeId:{}, userId:{}",
                                userBlindBoxTargetAwardRecord.getCostumeId(), userId);
                        continue;
                    }
                    if (userBlindBoxTargetAwardRecord.getCostumeLevel() == 0) {
                        userBlindBoxTargetAwardRepository.updateRecordCostumeLevel(userId, userBlindBoxTargetAwardRecord.getId(), costume.getLevel());
                    }
                }
                log.info("blindBoxInitCostumeLevel userId:{}, userBlindBoxTargetAwards size:{}, userBlindBoxTargetAwardRecords size:{}", userId,
                        userBlindBoxTargetAwards.size(), userBlindBoxTargetAwardRecords.size());
            }
            count += userInfos.size();
            log.info("blindBoxInitCostumeLevel page:{}, count:{}", j, count);
        }
        log.info("blindBoxInitCostumeLevel end, count:{}", count);
        return "SUCCESS";
    }

    @GetMapping("/userRoleGroup/init")
    public String userRoleGroupInit() {
        if (roleGroupComponent.startSync()) {
            CompletableFuture.runAsync(roleGroupComponent::syncUserRoleGroupData);
            return "sync user role group started.";
        } else {
            return "sync is already in progress.";
        }
    }

    @GetMapping("/userRoleGroup/progress")
    public String getProgress() {
        if (roleGroupComponent.isSyncing()) {
            return "sync progress: " + roleGroupComponent.getProgress() + "%";
        } else {
            return "No sync in progress.";
        }
    }

    @GetMapping("/initAddNewStoryTab")
    public String initAddNewStoryTab() {
        final int i = userInfoRepository.countAll();
        log.info("initAddNewStoryTab start, total user count: {}", i);
        int pageSize = 1000;
        final List<Story> stories = storyRepository.queryAllOnlineStoryListFromCache();
        final Map<Integer, RoleGroupRelation> roleGroupRelationMap = roleGroupRelationRepository.queryAll()
                .stream()
                .collect(Collectors.toMap(RoleGroupRelation::getRoleId, roleGroupRelation -> roleGroupRelation));
        final Map<Integer, Story> storyMap = stories.stream().collect(Collectors.toMap(Story::getId, story -> story));
        int count = 0;
        for (int j = 0; j < i; j += pageSize) {
            List<UserInfo> userInfos = userInfoRepository.queryByPage(j, pageSize);
            Set<UserStoryTabRedDotSendModel> userStoryTabRedDotSendModels = new HashSet<>();
            for (UserInfo userInfo : userInfos) {
                final List<UserStory> userStories = userStoryRepository.queryUserStoriesByUid(userInfo.getUserId())
                        .stream()
                        .filter(UserStory::isShowRedDot)
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(userStories)) {
                    continue;
                }
                for (UserStory userStory : userStories) {
                    final Story story = storyMap.get(userStory.getStoryId());
                    if (story == null) {
                        log.warn("initAddNewStoryTab story is null, storyId:{}, userId:{}", userStory.getStoryId(), userInfo.getUserId());
                        continue;
                    }
                    UserStoryTabRedDotSendModel model = new UserStoryTabRedDotSendModel();
                    model.setUserId(userInfo.getUserId());
                    model.setStoryType(StoryType.of(story.getType()));
                    final RoleGroupRelation roleGroupRelation = roleGroupRelationMap.get(story.getRoleIds().get(0));
                    model.setRoleGroupId(roleGroupRelation.getRoleGroupId());
                    userStoryTabRedDotSendModels.add(model);
                }
            }
            Map<Integer, Map<Integer, Set<StoryType>>> groupedModels = userStoryTabRedDotSendModels.stream()
                    .collect(Collectors.groupingBy(UserStoryTabRedDotSendModel::getUserId, Collectors.groupingBy(UserStoryTabRedDotSendModel::getRoleGroupId,
                            Collectors.mapping(UserStoryTabRedDotSendModel::getStoryType, Collectors.toSet()))));

            groupedModels.forEach((userId, roleGroupMap) -> roleGroupMap.forEach((roleGroupId, storyTypes) -> redDotComponent.sendAddStoryTabEvents(userId,
                    storyTypes, roleGroupId)));
            count += userStoryTabRedDotSendModels.size();
            log.info("initAddNewStoryTab userStoryTabRedDotSendModels size:{}, j:{}, count:{}", userStoryTabRedDotSendModels.size(), j, count);
        }
        log.info("initAddNewStoryTab end, count:{}", count);
        return "SUCCESS";
    }

}
