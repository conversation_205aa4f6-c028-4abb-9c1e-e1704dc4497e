package com.kuaikan.role.game.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;

import com.kuaikan.common.model.RpcResult;
import com.kuaikan.game.gamecard.dubbo.dto.ClientInfoDTO;
import com.kuaikan.game.gamecard.questcontainer.def.dto.QuestContainerDto;
import com.kuaikan.game.gamecard.questcontainer.def.dto.QuestDto;
import com.kuaikan.game.gamecard.questcontainer.def.service.QuestContainerService;
import com.kuaikan.role.game.api.bean.AvgChapter;
import com.kuaikan.role.game.api.bean.MapStory;
import com.kuaikan.role.game.api.bean.UserMapStory;
import com.kuaikan.role.game.api.bo.ImageInfo;
import com.kuaikan.role.game.api.model.CommonScheduleStoryLibModel;
import com.kuaikan.role.game.api.model.map.MapCollectionModel;
import com.kuaikan.role.game.api.model.map.MapStoryModel;
import com.kuaikan.role.game.api.model.map.MapStoryTabModel;
import com.kuaikan.role.game.api.model.map.UnlockMapStoryModel;
import com.kuaikan.role.game.api.rpc.result.TaskModuleConfigModel;
import com.kuaikan.role.game.api.service.CommonMapService;
import com.kuaikan.role.game.api.service.RedDotService;
import com.kuaikan.role.game.api.util.CardBattleUtils;
import com.kuaikan.role.game.common.bean.MapCity;
import com.kuaikan.role.game.common.enums.RedDotEventType;
import com.kuaikan.role.game.component.RedDotComponent;
import com.kuaikan.role.game.component.UserMapStoryComponent;
import com.kuaikan.role.game.repository.AvgRepository;
import com.kuaikan.role.game.repository.MapCityRepository;
import com.kuaikan.role.game.repository.MapStoryRepository;
import com.kuaikan.role.game.repository.UserMapStoryRepository;
import com.kuaikan.role.game.uitl.QuestUtil;

/**
 *<AUTHOR>
 *@date 2025/6/4
 */
@DubboService(version = "1.0", group = "role-game")
@Slf4j
public class CommonMapServiceImpl implements CommonMapService {

    @Resource
    private QuestContainerService questContainerService;
    @Resource
    private MapCityRepository mapCityRepository;
    @Resource
    private MapStoryRepository mapStoryRepository;
    @Resource
    private UserMapStoryRepository userMapStoryRepository;
    @Resource
    private RedDotService redDotService;
    @Resource
    private AvgRepository avgRepository;
    @Resource
    private UserMapStoryComponent userMapStoryComponent;
    @Resource
    private RedDotComponent redDotComponent;

    @Override
    public RpcResult<Void> checkUserMapStory(int userId, int mapId, int chapterId) {
        return RpcResult.success();
    }

    @Override
    public RpcResult<TaskModuleConfigModel.TaskConfigModel> getMapTaskList(ClientInfoDTO clientInfo, int mapId) {
        MapCity mapCity = mapCityRepository.getById(mapId);
        if (mapCity == null || mapCity.getConfig() == null || mapCity.getConfig().getActivityJobId() == null) {
            log.error("getMapTaskList mapCity taskId is null, mapId: {}, mapCity: {}", mapId, mapCity);
            return RpcResult.success(new TaskModuleConfigModel.TaskConfigModel());
        }
        long questContainerId = mapCity.getConfig().getActivityJobId();
        QuestContainerDto userQuestContainerDto = questContainerService.getUserQuestContainerDto(clientInfo, questContainerId);
        if (userQuestContainerDto == null) {
            log.error("getMapTaskList userQuestContainerDto is null, questContainerId:{}, clientInfo: {}", questContainerId, clientInfo);
            return RpcResult.success(new TaskModuleConfigModel.TaskConfigModel());
        }
        // 检查任务时间是否有效
        if (userQuestContainerDto.getStartTime() > System.currentTimeMillis() || userQuestContainerDto.getEndTime() < System.currentTimeMillis()) {
            log.error("getMapTaskList userQuestContainerDto time is invalid, userId: {}, questContainerId: {}, startTime: {}, endTime: {}",
                    clientInfo.getUserId(), questContainerId, userQuestContainerDto.getStartTime(), userQuestContainerDto.getEndTime());
            return RpcResult.success(new TaskModuleConfigModel.TaskConfigModel());
        }
        List<QuestDto> questDtos = userQuestContainerDto.getQuestDtos();
        List<TaskModuleConfigModel.QuestDtoInfo> questDtoInfos = questDtos.stream()
                .map(item -> new TaskModuleConfigModel.QuestDtoInfo().setQuestDto(item))
                .collect(Collectors.toList());
        boolean hadRedDot = QuestUtil.ifHasFinishTask(questDtos);
        TaskModuleConfigModel.TaskConfigModel taskConfigModel = new TaskModuleConfigModel.TaskConfigModel().setTaskId(questContainerId)
                .setModuleName("任务")
                .setShowRedDot(hadRedDot)
                .setQuestInfos(questDtoInfos);

        return RpcResult.success(taskConfigModel);
    }

    @Override
    public RpcResult<MapCollectionModel> getMapCollection(int userId, int mapId) {
        // 查询地图配置
        List<MapStory> mapStories = mapStoryRepository.queryListByMapId(mapId);
        if (CollectionUtils.isEmpty(mapStories)) {
            log.warn("getMapCollection no map stories found for mapId: {}", mapId);
            return RpcResult.success(new MapCollectionModel().setModuleName("地图收集簿"));
        }
        List<UserMapStory> userMapStories = userMapStoryRepository.queryListByUserIdAndMapId(userId, mapId);
        List<Integer> avgChapterIds = mapStories.stream().map(MapStory::getAvgChapterId).collect(Collectors.toList());
        Map<Integer, AvgChapter> avgChapterMap = avgRepository.queryByChapterIdsFromCache(avgChapterIds);
        Map<Integer, UserMapStory> userMapStoryMap = userMapStories.stream().collect(Collectors.toMap(UserMapStory::getMapStoryId, Function.identity()));
        List<CommonScheduleStoryLibModel> commonScheduleStoryLibList = userMapStoryComponent.getCommonScheduleStoryLibList(mapId);
        Map<Integer, List<CommonScheduleStoryLibModel>> unlockScheduleListMap = commonScheduleStoryLibList.stream()
                .collect(Collectors.groupingBy(CommonScheduleStoryLibModel::getStoryLibId, Collectors.toList()));
        // 填充地图收集簿
        List<MapStoryTabModel> tabModelList = new ArrayList<>();
        mapStories.stream().collect(Collectors.groupingBy(MapStory::getTag, Collectors.toList())).forEach((tag, stories) -> {
            MapStoryTabModel mapStoryTabModel = new MapStoryTabModel().setTabName(tag);
            List<MapStoryModel> storyInfos = stories.stream().map(story -> {
                AvgChapter avgChapter = avgChapterMap.get(story.getAvgChapterId());
                if (avgChapter == null) {
                    log.error("getMapCollection avgChapter not found for avgChapterId: {}", story.getAvgChapterId());
                    return null;
                }
                UserMapStory userMapStory = userMapStoryMap.get(story.getId());
                MapStoryModel mapStoryModel = new MapStoryModel();
                mapStoryModel.setTitle(avgChapter.getChapterName());
                mapStoryModel.setCollectionImage(new ImageInfo().setUrl(CardBattleUtils.getImgUrlJoinHost(story.getCoverImage())));
                mapStoryModel.setAvgChapterId(story.getAvgChapterId());
                mapStoryModel.setUnlock(userMapStory != null);
                mapStoryModel.setUnlockCondition(story.getObtainCopywriting());
                mapStoryModel.setUnlockCommonScheduleList(unlockScheduleListMap.getOrDefault(story.getLibraryId(), new ArrayList<>()));
                mapStoryModel.setShowRedDot(userMapStory == null ? false : userMapStory.isShowRedDot());
                // 填充红点信息
                mapStoryModel.setNewRedDot(redDotComponent.hasAddMapStoryEvent(userId, mapId, story.getAvgChapterId()));

                return mapStoryModel;
            }).filter(Objects::nonNull).collect(Collectors.toList());
            boolean tabNewRedDot = redDotComponent.hasAddMapStoryTagEvent(userId, mapId, tag);
            boolean tabHasRedDot = redDotComponent.hasUserAcceptStoryPoint(RedDotEventType.MAP_USER_ACCEPT_STORY_TAG, userId, mapId, tag);
            mapStoryTabModel.setNewRedDot(tabNewRedDot);
            mapStoryTabModel.setShowRedDot(tabHasRedDot);
            mapStoryTabModel.setStoryList(storyInfos);
            mapStoryTabModel.setUserStoryCnt((int) storyInfos.stream().filter(MapStoryModel::isUnlock).count());
            mapStoryTabModel.setTotalStoryCnt(storyInfos.size());
            // 将分组添加到收集簿中
            tabModelList.add(mapStoryTabModel);
        });

        // 地图获得新剧情红点
        boolean newRedDot = redDotComponent.hasUserBroadcastEvent(userId, RedDotEventType.MAP_HOME_ADD_STORY, mapId);
        // 地图上新红点信息
        boolean hasRedDot = redDotComponent.hasUserAcceptStoryPoint(RedDotEventType.MAP_HOME_USER_ACCEPT_STORY,userId, mapId, null);
        return RpcResult.success(
                new MapCollectionModel().setModuleName("地图收集簿").setTabList(tabModelList).setNewRedDot(newRedDot).setShowRedDot(hasRedDot));
    }

    @Override
    public void storyCollectionRedDotClear(int userId, int mapId, List<Integer> avgChapterIds) {
        for (Integer avgChapterId : avgChapterIds) {
            redDotService.clearAddMapStoryEvent(userId, mapId, avgChapterId);
        }
        userMapStoryComponent.clearRedDotByAvgIds(userId, mapId, avgChapterIds);
    }

    @Override
    public void clearMapRedDot(int userId, int mapId) {
        redDotService.clearMapHomeAddStoryAndAcceptStoryEvent(userId, mapId);
    }

    @Override
    public void clearMapTagRedDot(int userId, int mapId, String tag) {
        redDotService.clearMapTagAddStoryAndAcceptStoryEvent(userId, mapId, tag);
    }

    @Override
    public UnlockMapStoryModel unlockMapStoryModelByLibId(int userId, int mapId, int libId) {
        return userMapStoryComponent.unlockMapStoryModelByLibId(userId, mapId, libId);
    }
}
