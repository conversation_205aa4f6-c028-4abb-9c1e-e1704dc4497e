package com.kuaikan.role.game.repository;

import static com.kuaikan.role.game.common.enums.CacheConfig.USER_ROLE_GROUP_INFO;
import static com.kuaikan.role.game.uitl.ThreadPoolConfig.REDIS_EXECUTOR;

import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import com.kuaikan.comic.common.utils.CommonLettuceClusterUtil;
import com.kuaikan.common.redis.KeyGenerator;
import com.kuaikan.common.redis.lettuce.LettuceClusterClient;
import com.kuaikan.common.redis.lettuce.LettuceClusterUtil;
import com.kuaikan.role.game.api.bean.UserRoleGroup;
import com.kuaikan.role.game.api.enums.UserTableEnum;
import com.kuaikan.role.game.common.enums.CacheConfig;
import com.kuaikan.role.game.dao.rolegame.UserRoleGroupMapper;
import com.kuaikan.role.game.uitl.TablePartitionUtil;

/**
 * <AUTHOR>
 * @date 2024/12/9 18:55
 *
 */

@Repository
public class UserRoleGroupRepository {

    @Resource
    private UserRoleGroupMapper userRoleGroupMapper;

    @Resource
    private UserRoleGroupMapper userRoleGroupMapperSlave;

    public int insert(UserRoleGroup record) {
        String tableName = getTableName(record.getUserId());
        int count = userRoleGroupMapper.insert(tableName, record);
        deleteCache(record.getUserId(), record.getRoleGroupId());
        return count;
    }

    public int batchInsert(String tableName, List<UserRoleGroup> userRoleGroupList) {
        int count = userRoleGroupMapper.batchInsert(tableName, userRoleGroupList);
        deleteCache(userRoleGroupList);
        return count;
    }

    public int updateBondPopup(int userId, int roleGroupId, UserRoleGroup.ExtraInfo extraInfo) {
        String tableName = getTableName(userId);
        int count = userRoleGroupMapper.updateExtraInfoByUserIdAndRoleGroupId(tableName, extraInfo, userId, roleGroupId);
        deleteCache(userId, roleGroupId);
        return count;
    }

    public UserRoleGroup queryUserRoleGroupFromCache(int userId, int roleGroupId) {
        return CommonLettuceClusterUtil.get(userId, roleGroupId, USER_ROLE_GROUP_INFO, UserRoleGroup.class, this::queryUserRoleGroupFromDB, REDIS_EXECUTOR);
    }

    public UserRoleGroup queryUserRoleGroupFromDB(int userId, int roleGroupId) {
        String tableName = getTableName(userId);
        return userRoleGroupMapperSlave.queryByUserIdAndRoleGroupId(tableName, userId, roleGroupId);
    }

    private String getTableName(int userId) {
        return TablePartitionUtil.getTableName(UserTableEnum.USER_ROLE_GROUP, userId);
    }

    private void deleteCache(int userId, int roleGroupId) {
        LettuceClusterClient redisClient = LettuceClusterUtil.getClusterClientByName(USER_ROLE_GROUP_INFO.getReadWriteVip());
        String cacheKey = KeyGenerator.generate(CacheConfig.USER_ROLE_GROUP_INFO.getKeyPattern(), userId, roleGroupId);
        redisClient.del(cacheKey);
    }

    private void deleteCache(List<UserRoleGroup> userRoleGroupList) {
        LettuceClusterClient redisClient = LettuceClusterUtil.getClusterClientByName(USER_ROLE_GROUP_INFO.getReadWriteVip());
        List<String> cacheKeys = userRoleGroupList.stream()
                .map(e -> KeyGenerator.generate(CacheConfig.USER_ROLE_GROUP_INFO.getKeyPattern(), e.getUserId(), e.getRoleGroupId()))
                .collect(Collectors.toList());
        redisClient.del(cacheKeys.toArray(new String[0]));
    }
}
