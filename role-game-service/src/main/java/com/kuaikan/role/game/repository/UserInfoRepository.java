package com.kuaikan.role.game.repository;

import static com.kuaikan.role.game.common.enums.CacheConfig.USER_INFO;
import static com.kuaikan.role.game.uitl.ThreadPoolConfig.REDIS_EXECUTOR;

import java.util.List;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Repository;

import com.kuaikan.comic.common.utils.CommonLettuceClusterUtil;
import com.kuaikan.common.redis.KeyGenerator;
import com.kuaikan.common.redis.lettuce.LettuceClusterClient;
import com.kuaikan.common.redis.lettuce.LettuceClusterUtil;
import com.kuaikan.role.game.api.bean.UserInfo;
import com.kuaikan.role.game.common.enums.CacheConfig;
import com.kuaikan.role.game.dao.rolegame.UserInfoMapper;

/**
 * <AUTHOR>
 * @date 2024/2/29
 */
@Repository
@Slf4j
public class UserInfoRepository {

    @Resource
    private UserInfoMapper userInfoMapper;

    @Resource
    private UserInfoMapper userInfoMapperSlave;

    public void setCurrentRole(int uid, int roleId) {
        userInfoMapper.setCurrentRole(uid, roleId);
        deleteCache(uid);
    }

    public void setCurrentRoleGroup(int uid, int roleGroupId) {
        userInfoMapper.setCurrentRoleGroup(uid, roleGroupId);
        deleteCache(uid);
    }

    public void updateSceneStatus(int userId, int sceneStatus) {
        userInfoMapper.updateSceneStatus(userId, sceneStatus);
        deleteCache(userId);
    }

    public void insert(UserInfo record) {
        userInfoMapper.insert(record);
        deleteCache(record.getUserId());
    }

    public UserInfo queryUserInfoByUidFromCache(int uid) {
        return CommonLettuceClusterUtil.get(uid, USER_INFO, UserInfo.class, this::queryUserInfoByUid, REDIS_EXECUTOR);
    }

    public UserInfo queryUserInfoByUid(int uid) {
        return userInfoMapper.queryUserInfoByUid(uid);
    }

    public int countAll() {
        return userInfoMapper.countAll();
    }

    public List<UserInfo> queryByPage(int page, int pageSize) {
        return userInfoMapperSlave.queryByPage(page, pageSize);
    }

    public void updateRoleGroup(int userId, int roleGroupId) {
        userInfoMapper.updateRoleGroup(userId, roleGroupId);
        deleteCache(userId);
    }

    private void deleteCache(int uid) {
        LettuceClusterClient redisClient = LettuceClusterUtil.getClusterClientByName(CacheConfig.USER_INFO.getReadWriteVip());
        String cacheKey = KeyGenerator.generate(CacheConfig.USER_INFO.getKeyPattern(), uid);
        redisClient.del(cacheKey);
        log.debug("delete cache key: {}, {}", cacheKey, uid);
    }

}
