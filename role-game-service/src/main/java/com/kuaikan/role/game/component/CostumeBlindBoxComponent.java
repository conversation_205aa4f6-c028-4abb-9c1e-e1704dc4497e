package com.kuaikan.role.game.component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import cn.hutool.core.lang.Pair;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Range;
import com.google.common.collect.RangeMap;
import com.google.common.collect.TreeRangeMap;

import com.kuaikan.role.game.api.bean.BlindBoxDrawContext;
import com.kuaikan.role.game.api.bean.BlindBoxDrawResult;
import com.kuaikan.role.game.api.bean.CostumePartRelation;
import com.kuaikan.role.game.api.enums.CostumeLevel;
import com.kuaikan.role.game.api.model.CostumeProbabilityModel;
import com.kuaikan.role.game.repository.CostumePartRelationRepository;

/**
 * <AUTHOR>
 * @version 2025-01-20
 */
@Component
@Slf4j
public class CostumeBlindBoxComponent {

    @Resource
    private CostumePartRelationRepository costumePartRelationRepository;

    public BlindBoxDrawResult draw(BlindBoxDrawContext blindBoxDrawContext) {
        log.info("draw costume blind box start, blindBoxDrawContext={}", blindBoxDrawContext);
        final int lotteryNum = blindBoxDrawContext.getGearConfig().getLotteryNum();
        BlindBoxDrawResult blindBoxDrawResult = new BlindBoxDrawResult();
        List<BlindBoxDrawResult.DrawInfo> drawInfos = Lists.newArrayListWithCapacity(lotteryNum);
        blindBoxDrawResult.setDrawInfos(drawInfos);
        final BlindBoxDrawContext.GuaranteedConfig guaranteedConfig = blindBoxDrawContext.getGuaranteedConfig();
        final List<BlindBoxDrawContext.LotteryConfig> lotteryConfigs = Optional.ofNullable(blindBoxDrawContext.getLotteryConfigs())
                .orElse(Collections.emptyList());
        final List<BlindBoxDrawContext.LotteryConfig> lotteryConfigsCanLottery = lotteryConfigs.stream()
                .filter(BlindBoxDrawContext.LotteryConfig::isCanLottery)
                .collect(Collectors.toList());
        // 每个星级对应的概率
        final Map<CostumeLevel, Double> costumeLevelToProbabilityMap = Arrays.stream(CostumeLevel.values())
                .collect(Collectors.toMap(costumeLevel -> costumeLevel, costumeLevel -> getStarPartProbabilityByLevel(costumeLevel, blindBoxDrawContext)));
        // 每个星级对应的装扮
        final Map<Integer, List<BlindBoxDrawContext.LotteryConfig>> levelToLotteriesMap = lotteryConfigsCanLottery.stream()
                .collect(Collectors.groupingBy(BlindBoxDrawContext.LotteryConfig::getLevel));
        final Set<Integer> costumeLevelSet = levelToLotteriesMap.keySet();
        // 保底概率
        final int leastLevel = guaranteedConfig == null ? 0 : guaranteedConfig.getLeastLevel();
        final List<CostumeLevel> equalOrUpperCostumeLevels = CostumeLevel.getEqualOrUpper(leastLevel);
        // 保底概率 最小保底等级的概率，等于小于等于这个等级的概率的和
        Map<CostumeLevel, Double> guaranteedCostumeLevelToProbabilityMap = initGuaranteedProbabilityMap(equalOrUpperCostumeLevels, leastLevel,
                costumeLevelToProbabilityMap);
        final Map<Integer, BlindBoxDrawContext.LotteryConfig> allCostumeConfigMap = lotteryConfigsCanLottery.stream()
                .collect(Collectors.toMap(BlindBoxDrawContext.LotteryConfig::getCostumeId, Function.identity()));
        // 保底概率区间
        final Pair<Double, RangeMap<Double, CostumeLevel>> guarenteedRangeMapPair = initProbabilityMap(guaranteedCostumeLevelToProbabilityMap, costumeLevelSet);
        final Double guaranteedLevelMaxProbability = guarenteedRangeMapPair.getKey();
        final RangeMap<Double, CostumeLevel> guarenteedRangeMap = guarenteedRangeMapPair.getValue();
        log.info("draw costume blind box,guaranteedLevelMaxProbability:{},guaranteedRangeMap:{}", guaranteedLevelMaxProbability, guarenteedRangeMap);
        // 非保底概率
        // 非保底概率区间
        final Pair<Double, RangeMap<Double, CostumeLevel>> levelRangeMapPair = initProbabilityMap(costumeLevelToProbabilityMap, costumeLevelSet);
        final Double levelMaxProbability = levelRangeMapPair.getKey();
        final RangeMap<Double, CostumeLevel> rangeMap = levelRangeMapPair.getValue();
        final BlindBoxDrawContext.UserContext userContext = blindBoxDrawContext.getUserContext();
        for (int i = 0; i < lotteryNum; i++) {
            try {
                final int finalDrawCostumePartId;
                boolean triggerGuaranteed;
                // 确定可抽取装扮池
                if (CollectionUtils.isEmpty(lotteryConfigsCanLottery)) {
                    BlindBoxDrawResult.DrawInfo drawInfo = drawFallback(blindBoxDrawContext);
                    drawInfos.add(drawInfo);
                    log.warn("draw costume blind box, get by fallback, lotteryConfigsCanLottery is empty, blindBoxDrawContext:{}, drawInfo:{}",
                            blindBoxDrawContext, drawInfo);
                    continue;
                }
                CostumeLevel costumeLevel;
                double costumeLevelRandom;
                // 确定星级 nonGuaranteedCount范围0～9，本次抽取要再加1
                if (guaranteedConfig != null && userContext.getNonGuaranteedCount() + 1 >= guaranteedConfig.getCount()) {
                    // 触发保底
                    costumeLevelRandom = guaranteedLevelMaxProbability * new Random().nextDouble();
                    costumeLevel = guarenteedRangeMap.get(costumeLevelRandom);
                    userContext.setNonGuaranteedCount(0);
                    triggerGuaranteed = true;
                    log.info(
                            "draw costume blind box,guaranteed random:{}, guaranteedLevelMaxProbability:{}, guaranteedRangeMap:{}, blindBoxDrawContext:{}, costumeLevel:{}",
                            costumeLevelRandom, guaranteedLevelMaxProbability, guarenteedRangeMap, blindBoxDrawContext, costumeLevel);
                    if (costumeLevel == null) {
                        // 如果保底没有星级，则按无保底去抽
                        costumeLevelRandom = levelMaxProbability * new Random().nextDouble();
                        costumeLevel = rangeMap.get(costumeLevelRandom);
                        triggerGuaranteed = false;
                        log.info(
                                "draw costume blind box,guaranteed but no level costume random:{}, levelMaxProbability:{}, rangeMap:{},blindBoxDrawContext:{},costumeLevel:{}",
                                costumeLevelRandom, levelMaxProbability, rangeMap, blindBoxDrawContext, costumeLevel);
                    }
                } else {
                    // 触发非保底
                    costumeLevelRandom = levelMaxProbability * new Random().nextDouble();
                    costumeLevel = rangeMap.get(costumeLevelRandom);
                    triggerGuaranteed = false;
                    log.info("draw costume blind box,guaranteed not random:{}, levelMaxProbability:{}, rangeMap:{},blindBoxDrawContext:{}", costumeLevelRandom,
                            levelMaxProbability, rangeMap, blindBoxDrawContext);
                    userContext.setNonGuaranteedCount(userContext.getNonGuaranteedCount() + 1);
                }
                if (costumeLevel.getLevel() >= leastLevel) {
                    // 保底星级触发后，保底次数重置
                    userContext.setNonGuaranteedCount(0);
                }
                log.info(
                        "draw costume blind box,blindBoxDrawContext:{}, costumeLevel:{}, triggerGuaranteed:{}, currNonGuaranteedCount:{},levelToLotteriesMap:{}",
                        blindBoxDrawContext, costumeLevel, triggerGuaranteed, userContext.getNonGuaranteedCount(), levelToLotteriesMap);
                final List<BlindBoxDrawContext.LotteryConfig> levelLotteryConfigs = levelToLotteriesMap.get(costumeLevel.getLevel());
                // 确定装扮
                Map<Integer, BlindBoxDrawContext.LotteryConfig> costumeConfigMap = levelLotteryConfigs.stream()
                        .collect(Collectors.toMap(BlindBoxDrawContext.LotteryConfig::getCostumeId, Function.identity()));
                log.debug("draw costume blind box,blindBoxDrawContext:{}, costumeConfigMap:{},levelToLotteriesMap:{}", blindBoxDrawContext, costumeConfigMap,levelToLotteriesMap);
                final CostumeProbabilityModel costumeProbabilityModel = initCostumeProbabilityMap(blindBoxDrawContext, costumeConfigMap, allCostumeConfigMap,
                        costumeLevel);
                final double costumeRandom = new Random().nextDouble() * costumeProbabilityModel.getEnd();
                final Integer costumeId = costumeProbabilityModel.getRangeMap().get(costumeRandom);
                if (costumeId == null) {
                    BlindBoxDrawResult.DrawInfo drawInfo = drawFallback(blindBoxDrawContext);
                    drawInfos.add(drawInfo);
                    log.warn("draw costume blind box, get by probability, costumeId is null, blindBoxDrawContext:{}, drawInfo:{},costumeProbabilityModel:{}",
                            blindBoxDrawContext, drawInfo, costumeProbabilityModel);
                    continue;
                }
                // 确定是否出未获取单品
                final List<CostumePartRelation> costumePartRelations = costumePartRelationRepository.selectByCostumeId(costumeId);
                Set<Integer> allCostumePartIds = costumePartRelations.stream().map(CostumePartRelation::getCostumePartId).collect(Collectors.toSet());
                final Set<Integer> ownedCostumePartIds = Optional.ofNullable(userContext.getOwnedCostumePartIds()).orElse(new HashSet<>());
                //本装扮已获得的单品
                final Set<Integer> acquiredCostumePartIds = allCostumePartIds.stream().filter(ownedCostumePartIds::contains).collect(Collectors.toSet());

                //未获得的单品
                final Set<Integer> notAcquiredCostumePartIds = allCostumePartIds.stream()
                        .filter(costumePartId -> !ownedCostumePartIds.contains(costumePartId))
                        .collect(Collectors.toSet());

                final double notAcquiredCostumePartProbability = blindBoxDrawContext.getNotAcquiredCostumePartProbability();
                final boolean drawAcquiredCostumePart = isDrawAcquiredCostumePart(notAcquiredCostumePartProbability, acquiredCostumePartIds,
                        notAcquiredCostumePartIds);
                // 确定单品
                if (drawAcquiredCostumePart) {
                    // 抽取已获得的单品
                    finalDrawCostumePartId = new ArrayList<>(acquiredCostumePartIds).get(new Random().nextInt(acquiredCostumePartIds.size()));
                    log.info("draw costume blind box,blindBoxDrawContext:{}, draw acquiredCostumePart, costumeId:{}, drewCostumePartId:{},userId:{}",
                            blindBoxDrawContext, costumeId, finalDrawCostumePartId, userContext.getUserId());
                } else {
                    //抽取未获得的单品
                    finalDrawCostumePartId = new ArrayList<>(notAcquiredCostumePartIds).get(new Random().nextInt(notAcquiredCostumePartIds.size()));
                    userContext.getOwnedCostumePartIds().add(finalDrawCostumePartId);
                    // 判断是否会集齐新装扮
                    if (userContext.getOwnedCostumePartIds().containsAll(allCostumePartIds)) {
                        userContext.getOwnedCostumeIds().add(costumeId);
                    }
                    log.info("draw costume blind box,blindBoxDrawContext:{}, draw not acquiredCostumePart, costumeId:{}, drewCostumePartId:{},userId:{}",
                            blindBoxDrawContext, costumeId, finalDrawCostumePartId, userContext.getUserId());
                }
                userContext.getOwnedCostumePartIds().add(finalDrawCostumePartId);
                final Integer targetCostumeId = getTargetCostumeId(blindBoxDrawContext, allCostumeConfigMap, costumeId, costumeLevel);
                userContext.getTargetAwardCostumeIdMap().put(costumeLevel.getLevel(), targetCostumeId);
                // 组装抽取结果
                final BlindBoxDrawResult.DrawInfo drawInfo = buildDrawInfo(finalDrawCostumePartId, costumeProbabilityModel, triggerGuaranteed,
                        blindBoxDrawContext.getUserContext().getNonGuaranteedCount(), !drawAcquiredCostumePart, costumeId, costumeLevel, costumeRandom,
                        costumeLevelRandom, triggerGuaranteed ? guarenteedRangeMap : rangeMap, targetCostumeId);
                log.info("draw costume blind box,blindBoxDrawContext:{} drawInfo:{}", blindBoxDrawContext, drawInfo);
                drawInfos.add(drawInfo);
            } catch (Exception e) {
                final BlindBoxDrawResult.DrawInfo drawInfo = drawFallback(blindBoxDrawContext);
                drawInfos.add(drawInfo);
                log.error("draw costume blind box error, blindBoxDrawContext:{}", blindBoxDrawContext, e);
            }
        }
        log.info("draw costume blind box finish, blindBoxDrawContext:{}, blindBoxDrawResult:{}", blindBoxDrawContext, blindBoxDrawResult);
        return blindBoxDrawResult;
    }

    private static Map<CostumeLevel, Double> initGuaranteedProbabilityMap(List<CostumeLevel> equalOrUpperCostumeLevels, int leastLevel,
                                                                          Map<CostumeLevel, Double> costumeLevelToProbabilityMap) {
        Map<CostumeLevel, Double> guaranteedCostumeLevelToProbabilityMap = Maps.newHashMapWithExpectedSize(equalOrUpperCostumeLevels.size());
        for (CostumeLevel equalOrUpperCostumeLevel : equalOrUpperCostumeLevels) {
            final Integer level = equalOrUpperCostumeLevel.getLevel();
            Double prob = 0.0d;
            if (level == leastLevel) {
                // 从costumeLevelToProbabilityMap获取小于等于这个等级的概率的和
                for (Map.Entry<CostumeLevel, Double> costumeLevelDoubleEntry : costumeLevelToProbabilityMap.entrySet()) {
                    if (costumeLevelDoubleEntry.getKey().getLevel() <= level) {
                        prob += costumeLevelDoubleEntry.getValue();
                    }
                }
            } else {
                prob = costumeLevelToProbabilityMap.get(equalOrUpperCostumeLevel);
            }
            guaranteedCostumeLevelToProbabilityMap.put(equalOrUpperCostumeLevel, prob);
        }
        return guaranteedCostumeLevelToProbabilityMap;
    }

    private BlindBoxDrawResult.DrawInfo buildDrawInfo(int finalDrawCostumePartId, CostumeProbabilityModel costumeProbabilityModel, boolean triggerGuaranteed,
                                                      int currNonGuaranteedCount, Boolean notAcquired, int costumeId, CostumeLevel costumeLevel,
                                                      double costumeRandom, double costumeLevelRandom, RangeMap<Double, CostumeLevel> costumeLevelRangeMap,
                                                      Integer targetCostumeId) {
        BlindBoxDrawResult.DrawInfo drawInfo = new BlindBoxDrawResult.DrawInfo();
        drawInfo.setCostumeId(costumeId);
        drawInfo.setCostumePartId(finalDrawCostumePartId);
        drawInfo.setCostumeProbabilities(costumeProbabilityModel.getCostumeProbabilityMap());
        drawInfo.setCostumeRanges(costumeProbabilityModel.getRangeMap());
        drawInfo.setCostumeRandom(costumeRandom);
        drawInfo.setFallback(false);
        drawInfo.setGuaranteed(triggerGuaranteed);
        drawInfo.setTargetAwardCostumeId(costumeProbabilityModel.getTargetCostumeId());
        drawInfo.setNonGuaranteedCount(currNonGuaranteedCount);
        drawInfo.setNotAcquire(notAcquired);
        drawInfo.setCostumeLevel(costumeLevel);
        drawInfo.setCostumeLevelRandom(costumeLevelRandom);
        drawInfo.setCostumeLevelRanges(costumeLevelRangeMap);
        drawInfo.setNewTargetCostumeId(targetCostumeId);
        return drawInfo;
    }

    /**
     * 装扮概率区间
     * @see <a href="https://wiki.quickcan.com/pages/viewpage.action?pageId=1156417725"/>
     * @see <a href="https://wiki.quickcan.com/pages/viewpage.action?pageId=1190691488"/>
     * @param context
     * @param costumeConfigMap 当前装扮等级下的抽取配置
     * @return
     */
    private CostumeProbabilityModel initCostumeProbabilityMap(BlindBoxDrawContext context, Map<Integer, BlindBoxDrawContext.LotteryConfig> costumeConfigMap,
                                                              Map<Integer, BlindBoxDrawContext.LotteryConfig> allCostumeConfigMap, CostumeLevel costumeLevel) {
        CostumeProbabilityModel costumeProbabilityModel = new CostumeProbabilityModel();
        Map<Integer, Double> costumeProbabilityMap = Maps.newHashMapWithExpectedSize(costumeConfigMap.size());
        RangeMap<Double, Integer> rangeMap = TreeRangeMap.create();
        Set<BlindBoxDrawContext.UpConfig> upConfigs = getUpCostumeConfigs(context.getUpConfigs(), costumeConfigMap);
        Set<Integer> upCostumeIds = upConfigs.stream().map(BlindBoxDrawContext.UpConfig::getCostumeId).collect(Collectors.toSet());
        Map<Integer, Integer> targetAwardCostumeIdMap = context.getUserContext().getTargetAwardCostumeIdMap();
        Integer targetCostumeId = getTargetCostumeId(context, allCostumeConfigMap, targetAwardCostumeIdMap.get(costumeLevel.getLevel()), costumeLevel);
        final BlindBoxDrawContext.TargetConfig targetConfig = context.getTargetConfig();
        final BlindBoxDrawContext.LotteryConfig lotteryConfig = allCostumeConfigMap.get(targetCostumeId);
        double targetProbability = 0.0d;
        Set<Integer> normalCostumeIds = getNormalCostumeIds(costumeConfigMap.values(), upCostumeIds, targetCostumeId);
        double start = 0;
        final double maxProbability = 100;
        if (isCalcTargetAwardProbability(costumeLevel, targetCostumeId, targetConfig, lotteryConfig)) {
            targetProbability = targetConfig.getProbability();
            start = addRangeMapping(targetCostumeId, targetProbability, rangeMap, start);
            costumeProbabilityMap.put(targetCostumeId, targetProbability);
        }
        double leftProbability = maxProbability - targetProbability;
        log.info("initCostumeIdProbabilityMap,context:{}, targetConfig:{}, lotteryConfig:{},targetCostumeId:{},costumeLevel:{},leftProbability:{}", context,
                targetConfig, lotteryConfig, targetCostumeId, costumeLevel, leftProbability);
        if (leftProbability > 0) {
            // 获取不是定轨装扮的 up 装扮
            Set<BlindBoxDrawContext.UpConfig> nonTargetAwardUpConfigs = upConfigs.stream()
                    .filter(upConfig -> !Objects.equals(upConfig.getCostumeId(), targetCostumeId))
                    .collect(Collectors.toSet());
            final Set<Integer> nonTargetAwardUpCostumeIds = nonTargetAwardUpConfigs.stream()
                    .map(BlindBoxDrawContext.UpConfig::getCostumeId)
                    .collect(Collectors.toSet());
            // 非定轨up概率总和
            double nonTargetAwardUpsTotalProbability = calculateTotalUpProbability(upConfigs, nonTargetAwardUpCostumeIds);
            // 所有up概率总和
            double upsTotalProbability = calculateTotalUpProbability(upConfigs, upCostumeIds);
            // 其他装扮的概率和
            double otherCostumesTotalProbability = maxProbability - upsTotalProbability;
            // 计算非定轨装扮的概率总和
            double nonTargetAwardTotalProbability = nonTargetAwardUpsTotalProbability + otherCostumesTotalProbability;
            start = addUpCostumeRangeMappings(nonTargetAwardUpConfigs, rangeMap, start, costumeProbabilityMap, leftProbability, nonTargetAwardTotalProbability);

            // 获取不是定轨（同时也不是 up）的普通装扮
            final Set<Integer> otherNormalCostumeIds = normalCostumeIds.stream()
                    .filter(costumeId -> !normalCostumeIds.contains(targetCostumeId))
                    .collect(Collectors.toSet());
            log.info("initCostumeIdProbabilityMap, leftProb>0, context:{}, otherNormalCostumeIds:{},normalCostumeIds:{},nonTargetAwardNormalCostumesTotalProbability:{},otherCostumesTotalProbability:{},nonTargetAwardTotalProbability:{},upsTotalProbability:{},nonTargetAwardUpCostumeIds:{},nonTargetAwardUpConfigs:{}", context,
                    otherNormalCostumeIds, normalCostumeIds, nonTargetAwardTotalProbability, otherCostumesTotalProbability, nonTargetAwardUpsTotalProbability, upsTotalProbability,nonTargetAwardUpCostumeIds,nonTargetAwardUpConfigs);
            if (CollectionUtils.isNotEmpty(otherNormalCostumeIds)) {
                start = addNormalCostumeProbability(costumeProbabilityMap, rangeMap, start, otherCostumesTotalProbability,
                        otherNormalCostumeIds,nonTargetAwardTotalProbability, leftProbability);
            }
        }
        costumeProbabilityModel.setEnd(start);
        costumeProbabilityModel.setCostumeProbabilityMap(costumeProbabilityMap);
        costumeProbabilityModel.setRangeMap(rangeMap);
        costumeProbabilityModel.setTargetCostumeId(targetCostumeId);
        log.info("initCostumeIdProbabilityMap,context:{}, result:{}", context, costumeProbabilityModel);
        return costumeProbabilityModel;
    }

    private double addNormalCostumeProbability(Map<Integer, Double> costumeProbabilityMap, RangeMap<Double, Integer> rangeMap, double start,double otherCostumesTotalProbability,
                                             final Set<Integer> otherNormalCostumeIds, double nonTargetAwardTotalProbability, double leftProbability) {
        // 每一个普通装扮的概率等于
        BigDecimal divisor = new BigDecimal(nonTargetAwardTotalProbability).multiply(new BigDecimal(otherNormalCostumeIds.size()));
        BigDecimal divideCountProbability = new BigDecimal(otherCostumesTotalProbability).divide(divisor,4,BigDecimal.ROUND_HALF_UP);
        double normalCostumeProbability = new BigDecimal(leftProbability).multiply(divideCountProbability).doubleValue();
        start = addRangeMappings(otherNormalCostumeIds, normalCostumeProbability, rangeMap, start);
        costumeProbabilityMap.putAll(otherNormalCostumeIds.stream().collect(Collectors.toMap(costumeId -> costumeId, costumeId -> normalCostumeProbability)));
        log.debug("addNormalCostumeProbability,otherNormalCostumeIds:{},normalCostumeProbability:{},start:{},divideCountProbability:{}", otherNormalCostumeIds, normalCostumeProbability, start,divideCountProbability);
        return start;
    }

    private static boolean isCalcTargetAwardProbability(CostumeLevel costumeLevel, Integer targetCostumeId, BlindBoxDrawContext.TargetConfig targetConfig,
                                                        BlindBoxDrawContext.LotteryConfig lotteryConfig) {
        return targetCostumeId != null && targetConfig != null && lotteryConfig.getLevel() == costumeLevel.getLevel();
    }

    private Set<BlindBoxDrawContext.UpConfig> getUpCostumeConfigs(List<BlindBoxDrawContext.UpConfig> upConfigs,
                                                                  Map<Integer, BlindBoxDrawContext.LotteryConfig> costumeConfigMap) {
        return upConfigs.stream().filter(upConfig -> costumeConfigMap.containsKey(upConfig.getCostumeId())).collect(Collectors.toSet());
    }

    private Set<Integer> getNormalCostumeIds(Collection<BlindBoxDrawContext.LotteryConfig> costumeConfigs, Set<Integer> upCostumeIds, Integer targetCostumeId) {
        return costumeConfigs.stream()
                .map(BlindBoxDrawContext.LotteryConfig::getCostumeId)
                .filter(costumeId -> !upCostumeIds.contains(costumeId) && !Objects.equals(targetCostumeId, costumeId))
                .collect(Collectors.toSet());
    }

    /**
     * 计算概率时，普通装扮的数量不计算定轨装扮
     * @param costumeConfigs
     * @param upCostumeIds
     * @return
     */
    private int getNormalCostumeCount(Collection<BlindBoxDrawContext.LotteryConfig> costumeConfigs, Set<Integer> upCostumeIds) {
        return (int) costumeConfigs.stream()
                .map(BlindBoxDrawContext.LotteryConfig::getCostumeId)
                .filter(costumeId -> !upCostumeIds.contains(costumeId))
                .count();
    }

    private double calculateTotalUpProbability(Collection<BlindBoxDrawContext.UpConfig> upConfigs, Set<Integer> upCostumeIds) {
        if (CollectionUtils.isEmpty(upConfigs)) {
            return 0;
        }
        return upConfigs.stream()
                .filter(config -> upCostumeIds.contains(config.getCostumeId()))
                .mapToDouble(BlindBoxDrawContext.UpConfig::getProbability)
                .sum();
    }

    private double calculateNormalCostumesTotalProbability(double maxProbability, double upsTotalProbability) {
        return maxProbability - upsTotalProbability;
    }

    private double addRangeMappings(Set<Integer> costumeIds, double probability, RangeMap<Double, Integer> rangeMap, double start) {
        for (Integer costumeId : costumeIds) {
            start = addRangeMapping(costumeId, probability, rangeMap, start);
        }
        return start;
    }

    private double addRangeMapping(Integer costumeId, double probability, RangeMap<Double, Integer> rangeMap, double start) {
        double end = new BigDecimal(start).add(new BigDecimal(probability)).setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue();
        rangeMap.put(Range.closedOpen(start, end), costumeId);
        log.debug("addRangeMapping, costumeId:{}, probability:{}, start:{}, end:{}", costumeId, probability, start, end);
        return end;
    }

    private double addUpCostumeRangeMappings(Collection<BlindBoxDrawContext.UpConfig> upConfigs, RangeMap<Double, Integer> rangeMap, double start,
                                             Map<Integer, Double> costumeProbabilityMap, double totalProbability, double nonTargetAwardTotalProbability) {
        if (CollectionUtils.isEmpty(upConfigs)) {
            return start;
        }
        // 获取up装扮的概率 
        for (BlindBoxDrawContext.UpConfig upConfig : upConfigs) {
            double probability = upConfig.getProbability();
            BigDecimal divideProbabilityBigDecimal = new BigDecimal(probability)
                .divide(new BigDecimal(nonTargetAwardTotalProbability), 4, BigDecimal.ROUND_HALF_UP);
            double finalProbability = new BigDecimal(totalProbability).multiply(divideProbabilityBigDecimal).doubleValue();
            start = addRangeMapping(upConfig.getCostumeId(),finalProbability, rangeMap, start);
            costumeProbabilityMap.put(upConfig.getCostumeId(), finalProbability);
        }
        return start;
    }

    /**
     * 获取定轨装扮，确认是否还处于定轨中
     * @param blindBoxDrawContext
     * @param allCostumeConfigMap
     * @param targetAwardCostumeId 本次尝试定轨的装扮id，可能是本次抽取到的装扮，判断下是否可以加入定轨
     * @return
     */
    private Integer getTargetCostumeId(BlindBoxDrawContext blindBoxDrawContext, Map<Integer, BlindBoxDrawContext.LotteryConfig> allCostumeConfigMap,
                                       Integer targetAwardCostumeId, CostumeLevel costumeLevel) {
        // 本次定轨后最终确认的装扮id
        Integer targetCostumeId = null;
        if (blindBoxDrawContext.getTargetConfig() == null) {
            return null;
        }
        // 判断定轨装扮用户是否已集齐
        final Set<Integer> ownedCostumeIds = blindBoxDrawContext.getUserContext().getOwnedCostumeIds();
        // 已处于之前定轨中的装扮
        Map<Integer, Integer> targetAwardCostumeIdMap = blindBoxDrawContext.getUserContext().getTargetAwardCostumeIdMap();
        final Integer targetAwardCostumeIdFromContext = targetAwardCostumeIdMap.get(costumeLevel.getLevel());
        final int leastLevel = blindBoxDrawContext.getTargetConfig().getLeastLevel();
        if (targetAwardCostumeIdFromContext == null && targetAwardCostumeId == null) {
            log.info("getTargetCostumeId, targetAwardCostumeIdFromContext and targetAwardCostumeId is null, blindBoxDrawContext:{}, targetCostumeId:{}",
                    blindBoxDrawContext, targetCostumeId);
            return null;
        }
        // 找到up 中当前星级的 up 配置
        final List<BlindBoxDrawContext.UpConfig> upConfigs = blindBoxDrawContext.getUpConfigs().stream().filter(upConfig-> allCostumeConfigMap.get(upConfig.getCostumeId()).getLevel() == costumeLevel.getLevel()).collect(Collectors.toList());
        // 优先已处于定轨中的装扮
        if (targetAwardCostumeIdFromContext != null && !ownedCostumeIds.contains(targetAwardCostumeIdFromContext)) {
            targetCostumeId = getValidTargetCostumeId(targetAwardCostumeIdFromContext, allCostumeConfigMap, leastLevel, upConfigs, blindBoxDrawContext);
            if (targetCostumeId != null) {
                log.info(
                        "getTargetCostumeId, use targetAwardCostumeIdFromContext blindBoxDrawContext:{}, targetCostumeId:{}.targetAwardCostumeId:{},targetAwardCostumeIdFromContext:{}",
                        blindBoxDrawContext, targetCostumeId, targetAwardCostumeId, targetAwardCostumeIdFromContext);
                return targetCostumeId;
            }
        }
        // 其次判断本次抽取的装扮是否可以定轨
        if (targetAwardCostumeId != null && !ownedCostumeIds.contains(targetAwardCostumeId)) {
            targetCostumeId = getValidTargetCostumeId(targetAwardCostumeId, allCostumeConfigMap, leastLevel, upConfigs, blindBoxDrawContext);
            if (targetCostumeId != null) {
                log.info(
                        "getTargetCostumeId, use targetAwardCostumeId blindBoxDrawContext:{}, targetCostumeId:{}.targetAwardCostumeId:{},targetAwardCostumeIdFromContext:{}",
                        blindBoxDrawContext, targetCostumeId, targetAwardCostumeId, targetAwardCostumeIdFromContext);
                return targetCostumeId;
            }
        }

        log.info(
                "getTargetCostumeId, blindBoxDrawContext:{}, targetCostumeId:{}，targetAwardCostumeId:{}，targetAwardCostumeIdFromContext:{},allCostumeConfigMap:{}",
                blindBoxDrawContext, targetCostumeId, targetAwardCostumeId, targetAwardCostumeIdFromContext, allCostumeConfigMap);
        return targetCostumeId;
    }

    private static boolean isUpContainsTargetCostume(Integer targetAwardCostumeId, List<BlindBoxDrawContext.UpConfig> upConfigs) {
        return upConfigs.stream().anyMatch(upConfig -> upConfig.getCostumeId() == targetAwardCostumeId);
    }

    private static boolean isGreaterOrEqualLeastLevel(BlindBoxDrawContext.LotteryConfig lotteryConfig, int leastLevel) {
        return lotteryConfig != null && lotteryConfig.getLevel() >= leastLevel;
    }

    private Integer getValidTargetCostumeId(Integer targetCostumeId, Map<Integer, BlindBoxDrawContext.LotteryConfig> allCostumeConfigMap, int leastLevel,
                                            List<BlindBoxDrawContext.UpConfig> upConfigs, BlindBoxDrawContext blindBoxDrawContext) {
        final BlindBoxDrawContext.LotteryConfig lotteryConfig = allCostumeConfigMap.get(targetCostumeId);
        if (isGreaterOrEqualLeastLevel(lotteryConfig, leastLevel)) {
            if (CollectionUtils.isEmpty(upConfigs) || isUpContainsTargetCostume(targetCostumeId, upConfigs)) {
                log.info("getTargetCostumeId, valid targetCostumeId found, blindBoxDrawContext:{}, targetCostumeId:{}", blindBoxDrawContext, targetCostumeId);
                return targetCostumeId;
            }
            // 如果up装扮都已经抽取完毕，则对其他装扮也可进行定轨
            Set<Integer> upCostumeIds = upConfigs.stream().map(BlindBoxDrawContext.UpConfig::getCostumeId).collect(Collectors.toSet());
            if(blindBoxDrawContext.getUserContext().getOwnedCostumeIds().containsAll(upCostumeIds)) {
                log.info("getTargetCostumeId, valid targetCostumeId found, has all upCostumeIds, blindBoxDrawContext:{}, targetCostumeId:{}",
                        blindBoxDrawContext, targetCostumeId);
                return targetCostumeId;
            }

            log.info("getTargetCostumeId, invalid targetCostumeId, upConfigs:{}, blindBoxDrawContext:{}, targetCostumeId:{}", upConfigs, blindBoxDrawContext,
                    targetCostumeId);
        }
        log.info("getTargetCostumeId, invalid targetCostumeId, blindBoxDrawContext:{}, targetCostumeId:{}", blindBoxDrawContext, targetCostumeId);
        return null;
    }

    /**
     * 兜底抽取，只有在抽取异常的时候才会使用
     * @param blindBoxDrawContext
     * @return
     */
    private BlindBoxDrawResult.DrawInfo drawFallback(BlindBoxDrawContext blindBoxDrawContext) {
        final List<BlindBoxDrawContext.LotteryConfig> lotteryConfigs = blindBoxDrawContext.getLotteryConfigs();
        final BlindBoxDrawContext.LotteryConfig lotteryConfig = lotteryConfigs.get(new Random().nextInt(lotteryConfigs.size()));
        final int costumeId = lotteryConfig.getCostumeId();
        final List<CostumePartRelation> costumePartRelations = costumePartRelationRepository.selectByCostumeId(costumeId);
        final List<Integer> costumePartIds = costumePartRelations.stream().map(CostumePartRelation::getCostumePartId).collect(Collectors.toList());
        final Integer costumePartId = costumePartIds.get(new Random().nextInt(costumePartIds.size()));
        blindBoxDrawContext.getUserContext().getOwnedCostumePartIds().add(costumePartId);
        if (new HashSet<>(costumePartIds).containsAll(blindBoxDrawContext.getUserContext().getOwnedCostumePartIds())) {
            blindBoxDrawContext.getUserContext().getOwnedCostumeIds().add(costumeId);
        }
        final BlindBoxDrawContext.GuaranteedConfig guaranteedConfig = blindBoxDrawContext.getGuaranteedConfig();
        final int leastLevel = guaranteedConfig == null ? 0 : guaranteedConfig.getLeastLevel();
        if (lotteryConfig.getLevel() >= leastLevel) {
            blindBoxDrawContext.getUserContext().setNonGuaranteedCount(0);
        } else {
            blindBoxDrawContext.getUserContext().setNonGuaranteedCount(blindBoxDrawContext.getUserContext().getNonGuaranteedCount() + 1);
        }
        BlindBoxDrawResult.DrawInfo drawInfo = new BlindBoxDrawResult.DrawInfo();
        drawInfo.setCostumeId(costumeId);
        drawInfo.setCostumePartId(costumePartId);
        drawInfo.setFallback(true);
        drawInfo.setCostumeLevel(CostumeLevel.getByLevel(lotteryConfig.getLevel()));
        return drawInfo;
    }

    public double getStarPartProbabilityByLevel(CostumeLevel costumeLevel, BlindBoxDrawContext blindBoxDrawContext) {
        switch (costumeLevel) {
            case ONE_STAR:
                return blindBoxDrawContext.getOneStarPartProbability();
            case TWO_STAR:
                return blindBoxDrawContext.getTwoStarPartProbability();
            case THREE_STAR:
                return blindBoxDrawContext.getThreeStarPartProbability();
            case FOUR_STAR:
                return blindBoxDrawContext.getFourStarPartProbability();
            case FIVE_STAR:
                return blindBoxDrawContext.getFiveStarPartProbability();
            default:
                log.warn("unknown costume level, costumeLevel={}", costumeLevel);
                return 0;
        }
    }

    /**
     * 是否抽取到获得过的单品
     */
    private boolean isDrawAcquiredCostumePart(double notAcquiredCostumePartProbability, Set<Integer> ownedCostumePartIds,
                                              Set<Integer> notAcquiredCostumePartIds) {
        if (CollectionUtils.isEmpty(ownedCostumePartIds)) {
            return false;
        }
        if (CollectionUtils.isEmpty(notAcquiredCostumePartIds)) {
            return true;
        }
        return new Random().nextDouble() * 100 > notAcquiredCostumePartProbability;
    }

    /**
     * 获取概率
     * @param costumelevelToProbabilityMap
     * @return pair 的 key 为概率总和，value 为概率区间
     */
    private Pair<Double, RangeMap<Double, CostumeLevel>> initProbabilityMap(Map<CostumeLevel, Double> costumelevelToProbabilityMap,
                                                                            Set<Integer> costumeLevelSet) {
        RangeMap<Double, CostumeLevel> rangeMap = TreeRangeMap.create();
        double start = 0;
        double end = 0;
        for (Map.Entry<CostumeLevel, Double> costumelevelToProbabilityEntry : costumelevelToProbabilityMap.entrySet()) {
            final CostumeLevel costumelevel = costumelevelToProbabilityEntry.getKey();
            if (!costumeLevelSet.contains(costumelevel.getLevel())) {
                continue;
            }
            final Double probability = costumelevelToProbabilityEntry.getValue();
            end = start + probability;
            rangeMap.put(Range.closedOpen(start, end), costumelevel);
            start = end;
        }
        return new Pair<>(end, rangeMap);
    }

}
