package com.kuaikan.role.game.dao.rolegame;

import java.util.Collection;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.kuaikan.role.game.api.bean.CostumePartNum;
import com.kuaikan.role.game.api.bean.UserCostumePart;

/**
 * <AUTHOR>
 * @version 2024-04-23
 */
public interface UserCostumePartMapper {

    int insert(@Param("tableName") String tableName, @Param("record") UserCostumePart userCostumePart);

    int insertBatch(@Param("tableName") String tableName, @Param("records") List<UserCostumePart> userCostumeParts);

    int addBalance(@Param("tableName") String tableName, @Param("userId") int userId, @Param("costumePartId") int costumePartId, @Param("count") int count);

    UserCostumePart queryByUserIdPartId(@Param("tableName") String tableName, @Param("userId") int userId, @Param("costumePartId") int costumePartId);

    List<UserCostumePart> queryByCostumePartIds(@Param("tableName") String tableName, @Param("userId") int userId,
                                                @Param("costumePartIds") Collection<Integer> costumePartIds);

    int updateBalance(@Param("tableName") String tableName, @Param("userId") int userId, @Param("costumePartId") int costumePartId,
                      @Param("balance") int balance);

    List<UserCostumePart> selectByUserId(@Param("tableName") String tableName, @Param("userId") int userId);

    int batchAddBalance(@Param("tableName") String tableName, @Param("userId") int userId, @Param("costumePartNums") List<CostumePartNum> costumePartNums);
}
