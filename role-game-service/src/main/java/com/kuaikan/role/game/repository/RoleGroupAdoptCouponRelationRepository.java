package com.kuaikan.role.game.repository;

import java.util.Collection;
import java.util.List;
import javax.annotation.Resource;

import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import com.beust.jcommander.internal.Lists;

import com.kuaikan.role.game.common.bean.RoleGroupAdoptCouponRelation;
import com.kuaikan.role.game.dao.rolegame.RoleGroupAdoptCouponRelationMapper;

/**
 * <AUTHOR>
 * @date 2024/12/26 11:53
 */

@Repository
public class RoleGroupAdoptCouponRelationRepository {

    @Resource
    private RoleGroupAdoptCouponRelationMapper roleGroupAdoptCouponRelationMapperSlave;

    public RoleGroupAdoptCouponRelation queryRelationByCouponId(int couponId) {
        return roleGroupAdoptCouponRelationMapperSlave.selectByCouponId(couponId);
    }

    public List<RoleGroupAdoptCouponRelation> queryRelationByCouponIds(Collection<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return roleGroupAdoptCouponRelationMapperSlave.selectByCouponIds(ids);
    }
}
