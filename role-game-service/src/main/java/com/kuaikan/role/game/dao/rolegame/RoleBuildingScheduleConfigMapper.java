package com.kuaikan.role.game.dao.rolegame;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.kuaikan.role.game.api.bean.RoleBuildingScheduleConfig;

/**
 * RoleBuildingTirednessMapper
 *
 * <AUTHOR>
 * @since 2024-06-05
 */
public interface RoleBuildingScheduleConfigMapper {

    RoleBuildingScheduleConfig queryByRoleIdAndBuildingId(@Param("roleId") int roleId, @Param("buildingId") int buildingId);

    List<RoleBuildingScheduleConfig> queryByRoleId(@Param("roleId") int roleId);

    List<RoleBuildingScheduleConfig> queryAll();
}
