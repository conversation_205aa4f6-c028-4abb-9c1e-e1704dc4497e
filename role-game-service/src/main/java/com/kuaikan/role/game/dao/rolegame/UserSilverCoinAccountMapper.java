package com.kuaikan.role.game.dao.rolegame;

import org.apache.ibatis.annotations.Param;

import com.kuaikan.role.game.api.bean.UserSilverCoinAccount;

public interface UserSilverCoinAccountMapper {

    int deleteByPrimaryKey(Integer id);

    int insert(UserSilverCoinAccount record);

    int insertSelective(UserSilverCoinAccount record);

    UserSilverCoinAccount selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(UserSilverCoinAccount record);

    int updateByPrimaryKey(UserSilverCoinAccount record);

    UserSilverCoinAccount getUserAccount(@Param("userId") int userId);

    int chargeAccount(@Param("userId") int userId, @Param("amount") int amount, @Param("beforeBalance") int beforeBalance);

    int consumeAccount(@Param("userId") int userId, @Param("amount") int amount, @Param("beforeBalance") int beforeBalance);
}