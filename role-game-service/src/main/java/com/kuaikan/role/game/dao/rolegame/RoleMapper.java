package com.kuaikan.role.game.dao.rolegame;

import java.util.Collection;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.kuaikan.role.game.api.bean.Role;

public interface RoleMapper {

    List<Role> queryRoleListByTopicId(int topicId);

    List<Role> queryByPage(@Param("offset") int offset, @Param("limit") int limit);

    int count();

    List<Role> queryAll();

    List<Role> queryByIds(@Param("ids") Collection<Integer> ids);

    Role queryById(Integer id);

}