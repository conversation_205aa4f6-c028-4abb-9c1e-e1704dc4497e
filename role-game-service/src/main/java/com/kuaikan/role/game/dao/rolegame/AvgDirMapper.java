package com.kuaikan.role.game.dao.rolegame;

import java.util.Collection;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.kuaikan.role.game.api.bean.AvgDir;

public interface AvgDirMapper {

    List<AvgDir> queryAvgDirByNamesAndType(@Param("names") Collection<String> names, @Param("type") int type);

    List<AvgDir> selectByIds(@Param("ids") Collection<Integer> ids);

    List<AvgDir> queryDirByType(@Param("type") int type);
}