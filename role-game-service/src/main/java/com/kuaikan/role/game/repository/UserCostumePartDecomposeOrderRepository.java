package com.kuaikan.role.game.repository;

import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import com.kuaikan.role.game.api.bean.UserCostumePartDecomposeOrder;
import com.kuaikan.role.game.api.enums.UserTableEnum;
import com.kuaikan.role.game.dao.rolegame.UserCostumePartDecomposeOrderMapper;
import com.kuaikan.role.game.uitl.TablePartitionUtil;

/**
 * <AUTHOR>
 * @version 2024-04-23
 */
@Repository
public class UserCostumePartDecomposeOrderRepository {

    @Resource
    private UserCostumePartDecomposeOrderMapper userCostumePartDecomposeOrderMapper;

    public int insert(UserCostumePartDecomposeOrder userCostumePartDecomposeOrder) {
        String tableName = getTableName(userCostumePartDecomposeOrder.getUserId());
        return userCostumePartDecomposeOrderMapper.insert(tableName, userCostumePartDecomposeOrder);
    }

    private String getTableName(int userId) {
        return TablePartitionUtil.getTableName(UserTableEnum.USER_COSTUME_PART_DECOMPOSE_ORDER, userId);
    }
}
