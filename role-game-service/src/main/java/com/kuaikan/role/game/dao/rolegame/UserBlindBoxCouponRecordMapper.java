package com.kuaikan.role.game.dao.rolegame;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.kuaikan.role.game.api.bean.UserBlindBoxCouponRecord;

/**
 * <AUTHOR>
 * @date 2024/12/26 15:42
 */
public interface UserBlindBoxCouponRecordMapper {

    void insertBatch(@Param("tableName") String tableName, @Param("records") List<UserBlindBoxCouponRecord> records);

    List<UserBlindBoxCouponRecord> queryByUserId(@Param("tableName") String tableName, @Param("userId") Integer userId);

    UserBlindBoxCouponRecord queryByBid(@Param("tableName") String tableName, @Param("bid") Long bid);

    int updateUsedByBid(@Param("tableName") String tableName, @Param("used") Boolean used, @Param("bid") Long bid);
}
