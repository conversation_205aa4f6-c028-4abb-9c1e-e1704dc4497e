package com.kuaikan.role.game.service.impl;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;

import com.kuaikan.account.api.model.PassportContext;
import com.kuaikan.comic.common.common.BizResult;

import com.google.common.collect.Lists;



import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.kuaikan.common.model.RpcResult;
import com.kuaikan.common.tools.string.StringUtils;
import com.kuaikan.game.gamecard.base.model.Prize;
import com.kuaikan.game.gamecard.prize.def.service.GameCardPrizeService;
import com.kuaikan.idgenerator.sdk.BufferedIdGenerator;
import com.kuaikan.role.game.api.bean.Building;
import com.kuaikan.role.game.api.bean.BuildingArea;
import com.kuaikan.role.game.api.bean.CityCommonPropertyConfig;
import com.kuaikan.role.game.api.bean.ClientInfo;
import com.kuaikan.role.game.api.bean.CityCommonPropertyConfig;
import com.kuaikan.role.game.api.bean.CommonScheduleSettlementSnapshot;
import com.kuaikan.role.game.api.bean.KeyValueConfig;
import com.kuaikan.role.game.api.bean.CityCommonPropertyConfig;
import com.kuaikan.role.game.api.bean.MapBuildingRelation;
import com.kuaikan.role.game.api.bean.MapStory;
import com.kuaikan.role.game.api.bean.Schedule;
import com.kuaikan.role.game.api.bean.ScheduleGiftConfig;
import com.kuaikan.common.tools.string.StringUtils;
import com.kuaikan.role.game.api.bean.Building;
import com.kuaikan.role.game.api.bean.CommonScheduleSettlementSnapshot;
import com.kuaikan.role.game.api.bean.KeyValueConfig;
import com.kuaikan.role.game.api.bean.Schedule;
import com.kuaikan.role.game.api.bean.ScheduleGiftConfigList;
import com.kuaikan.role.game.api.bean.ScheduleNumericalConfig;
import com.kuaikan.role.game.api.bean.UserMap;
import com.kuaikan.role.game.api.bean.UserMapOngoingSchedule;
import com.kuaikan.role.game.api.common.RoleGameResponse;
import com.kuaikan.role.game.api.common.RoleGameResponse;
import com.kuaikan.role.game.api.common.RoleGameResponse;
import com.kuaikan.role.game.api.enums.ScheduleStatus;
import com.kuaikan.role.game.api.model.CommonScheduleCreateModel;
import com.kuaikan.role.game.api.model.CommonScheduleListModel;
import com.kuaikan.role.game.api.model.CommonScheduleResultModel;
import com.kuaikan.role.game.api.model.CompleteScheduleModel;
import com.kuaikan.role.game.api.rpc.param.CommonScheduleCompleteParam;
import com.kuaikan.role.game.api.rpc.param.CommonScheduleCreateAndCompleteParam;
import com.kuaikan.role.game.api.rpc.param.CommonScheduleCreateParam;
import com.kuaikan.role.game.api.rpc.param.CommonScheduleGuideFinishParam;
import com.kuaikan.role.game.api.rpc.param.CommonScheduleListParam;
import com.kuaikan.role.game.api.rpc.param.CommonScheduleStopParam;
import com.kuaikan.role.game.api.rpc.result.AvgChapterModel;
import com.kuaikan.role.game.api.service.AvgService;
import com.kuaikan.role.game.api.service.CommonScheduleService;
import com.kuaikan.role.game.api.util.GsonUtils;
import com.kuaikan.role.game.common.bean.GuideRecord;
import com.kuaikan.role.game.common.constant.KeyValueConfigKeys;
import com.kuaikan.role.game.common.enums.GuideType;
import com.kuaikan.role.game.common.enums.ScheduleActionType;
import com.kuaikan.role.game.component.SaComponent;
import com.kuaikan.role.game.api.util.GsonUtils;
import com.kuaikan.role.game.common.constant.KeyValueConfigKeys;
import com.kuaikan.role.game.component.SaComponent;
import com.kuaikan.role.game.api.util.GsonUtils;
import com.kuaikan.role.game.common.constant.KeyValueConfigKeys;
import com.kuaikan.role.game.component.SaComponent;
import com.kuaikan.role.game.component.UserCommonScheduleComponent;
import com.kuaikan.role.game.component.UserMapComponent;
import com.kuaikan.role.game.repository.BuildingAreaRepository;
import com.kuaikan.role.game.repository.BuildingRepository;
import com.kuaikan.role.game.repository.GuideRecordRepository;
import com.kuaikan.role.game.repository.BuildingRepository;
import com.kuaikan.role.game.repository.MapElementRepository;
import com.kuaikan.role.game.repository.MapStoryRepository;
import com.kuaikan.role.game.repository.BuildingRepository;
import com.kuaikan.role.game.repository.MapElementRepository;
import com.kuaikan.role.game.repository.ScheduleRepository;
import com.kuaikan.role.game.repository.UserMapFinishScheduleRepository;
import com.kuaikan.role.game.repository.UserMapOngoingScheduleRepository;
import com.kuaikan.role.game.repository.UserMapRepository;
import com.kuaikan.role.game.repository.KeyValueConfigRepository;
import com.kuaikan.role.game.repository.MapBuildingRelationRepository;

@DubboService
@Slf4j
public class CommonScheduleServiceImpl implements CommonScheduleService {

    @Resource
    private UserMapFinishScheduleRepository userMapFinishScheduleRepository;
    @Resource
    private UserMapOngoingScheduleRepository userMapOngoingScheduleRepository;
    @Resource
    private UserMapRepository userMapRepository;
    @Resource
    private SaComponent saComponent;
    @Resource
    private BuildingRepository buildingRepository;
    @Resource
    private MapElementRepository mapElementRepository;

    @Resource
    private UserMapComponent userMapComponent;
    @Resource
    private ScheduleRepository scheduleRepository;
    @Resource
    private UserCommonScheduleComponent userCommonScheduleComponent;
    @Resource
    private GuideRecordRepository guideRecordRepository;

    @Resource
    private BuildingAreaRepository buildingAreaRepository;

    @Resource
    private GameCardPrizeService gameCardPrizeService;

    @Resource
    private MapStoryRepository mapStoryRepository;

    @Resource
    private AvgService avgService;

    @Resource
    private KeyValueConfigRepository keyValueConfigRepository;

    @Resource
    private MapBuildingRelationRepository mapBuildingRelationRepository;

    @Override
    public RpcResult<CommonScheduleListModel> getList(CommonScheduleListParam param) {
        int userId = param.getUserId();
        int mapId = param.getMapId();
        UserMap userMap = userMapRepository.queryByUserIdAndMapId(userId, mapId);
        if (userMap == null) {
            userMap = userMapComponent.initUserMap(userId, mapId);
            log.info("CommonScheduleService getList user {} init map {}", userId, mapId);
        }
        UserMapOngoingSchedule userMapOngoingSchedule = userMapOngoingScheduleRepository.queryByUserIdAndMapId(userId, mapId);
        List<MapBuildingRelation> mapBuildingRelations = mapBuildingRelationRepository.queryByMapId(mapId);
        List<Schedule> schedules = scheduleRepository.queryByBuildingIds(mapBuildingRelations.stream().map(MapBuildingRelation::getBuildingId).collect(Collectors.toList()));
        final List<BuildingArea> buildingAreas = buildingAreaRepository.queryAllFromCache();
        final Map<Integer, BuildingArea> buildingAreaMap = buildingAreas.stream().collect(Collectors.toMap(BuildingArea::getId, Function.identity()));
        CommonScheduleListModel commonScheduleListModel = new CommonScheduleListModel();
        List<MapStory> mapStories = mapStoryRepository.queryListByMapId(mapId);
        Set<Integer> avgChapterIds = mapStories.stream().map(MapStory::getAvgChapterId).collect(Collectors.toSet());
        RpcResult<Map<Integer, AvgChapterModel>> avgChapterBasicInfos = avgService.batchQueryAvgChapterBasicInfo(avgChapterIds);
        if(!avgChapterBasicInfos.isSuccess()){
            log.error("CommonScheduleService getList avgChapterBasicInfos error, userId:{}, mapId:{}", userId, mapId);
            return RpcResult.result(avgChapterBasicInfos.getCode(), avgChapterBasicInfos.getMessage());
        }
        Map<Integer, AvgChapterModel> avgChapterBasicInfoMap = avgChapterBasicInfos.getData();
        Map<Integer, List<MapStory>> mapStoryMap = mapStories.stream().collect(Collectors.groupingBy(MapStory::getLibraryId));
        List<CommonScheduleListModel.CommonScheduleModel> commonScheduleModels = Lists.newArrayList();
        commonScheduleListModel.setSchedules(commonScheduleModels);
        for(Schedule schedule : schedules) {
            CommonScheduleListModel.CommonScheduleModel commonScheduleModel = new CommonScheduleListModel.CommonScheduleModel();
            commonScheduleModel.setScheduleId(schedule.getId());
            commonScheduleModel.setScheduleName(schedule.getName());
            BuildingArea buildingArea = buildingAreaMap.get(schedule.getAreaId());
            if (buildingArea != null) {
                commonScheduleModel.setAreaName(buildingArea.getName());
                commonScheduleModel.setAreaId(buildingArea.getId());
            }
            Building building = buildingRepository.queryByIdFromCache(schedule.getBuildingId());
            if (building != null) {
                commonScheduleModel.setBuildingName(building.getName());
            }
            if (userMapOngoingSchedule != null && userMapOngoingSchedule.getScheduleId() == schedule.getId()) {
                commonScheduleModel.setOngoingSchedule(new CommonScheduleListModel.OngoingScheduleModel().setUserScheduleId(userMapOngoingSchedule.getId())
                        .setStartTime(userMapOngoingSchedule.getStartTime())
                        .setEndTime(userMapOngoingSchedule.getEndTime()));
                if (userMapOngoingSchedule.getEndTime() <= System.currentTimeMillis()) {
                    commonScheduleModel.setStatus(ScheduleStatus.FINISHED.getStatus());
                } else {
                    commonScheduleModel.setStatus(ScheduleStatus.PROCESSING.getStatus());
                }
            }
            commonScheduleModel.setEnergyConsume(schedule.getConfig().getConsumeEnergyPerPeriod() * 60);
            ScheduleNumericalConfig numericalConfig = schedule.getConfig().getNumericalConfig();
            if(numericalConfig!=null){
                List<Long> prizeIds = numericalConfig.getPrizeIds();
                if (CollectionUtils.isNotEmpty(prizeIds)) {
                    Map<Long, Prize> prizeMap;
                    try {
                        prizeMap = gameCardPrizeService.getOnlinePrizeMapByPrizeIdList(prizeIds);
                    } catch (Exception e) {
                        log.error("Exception in getOnlinePrizeMapByPrizeIdList. prizeIds:{}", prizeIds, e);
                        prizeMap = Maps.newHashMap();
                    }
                    List<CommonScheduleListModel.CommonPrizeModel> commonPrizes = Lists.newArrayList();
                    for (Long prizeId : prizeIds) {
                        Prize prize = prizeMap.get(prizeId);
                        if (prize != null) {
                            commonPrizes.add(new CommonScheduleListModel.CommonPrizeModel().setId(prizeId.intValue())
                                    .setName(prize.getName())
                                    .setUnit(prize.getUnit())
                                    .setIcon(prize.getImgUrl()));
                        }
                    }
                    commonScheduleModel.setCommonPrizes(commonPrizes);
                }
                Integer repoId = numericalConfig.getRepoId();
                if (repoId!=null) {
                    List<MapStory> mapStoriesByRepoId = mapStoryMap.get(repoId);
                    if(CollectionUtils.isNotEmpty(mapStoriesByRepoId)){
                        List<CommonScheduleListModel.StoryPrizeModel> storyPrizes = Lists.newArrayList();
                        for(MapStory mapStory : mapStoriesByRepoId){
                            AvgChapterModel avgChapterModel = avgChapterBasicInfoMap.get(mapStory.getAvgChapterId());
                            if(avgChapterModel!=null){
                                storyPrizes.add(new CommonScheduleListModel.StoryPrizeModel().setId(mapStory.getId()).setName(avgChapterModel.getChapterName()));
                            }
                        }
                        commonScheduleModel.setStoryPrizes(storyPrizes);
                    }
                }
            }
            commonScheduleModels.add(commonScheduleModel);
        }
        return RpcResult.success(commonScheduleListModel);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RpcResult<CommonScheduleCreateModel> create(CommonScheduleCreateParam param) {
        int userId = param.getUserId();
        int mapId = param.getMapId();
        int scheduleId = param.getScheduleId();
        int minutes = param.getMinutes();
        return userCommonScheduleComponent.create(userId, mapId, scheduleId, minutes);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RpcResult<CommonScheduleResultModel> complete(CommonScheduleCompleteParam param) {
        int userId = param.getUserId();
        int userScheduleId = param.getUserScheduleId();
        String orderId = String.valueOf(BufferedIdGenerator.getId());
        UserMapOngoingSchedule userMapOngoingSchedule = userMapOngoingScheduleRepository.queryById(userScheduleId);
        if (userMapOngoingSchedule == null) {
            log.error("UserCommonScheduleComponent stopSchedule user {} schedule {} not found", userId, userScheduleId);
            return RpcResult.result(RoleGameResponse.SCHEDULE_NOT_EXIST);
        }
        RpcResult<CommonScheduleResultModel> result = userCommonScheduleComponent.complete(param.getClientInfo(), userId, userMapOngoingSchedule, orderId);
        if (!result.isSuccess()) {
            return RpcResult.result(result.getCode(), result.getMessage());
        }
        trackingData(userId, userMapOngoingSchedule, ScheduleActionType.COMPLETE.getDesc(), 0, 0);
        return RpcResult.success(result.getData());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RpcResult<CommonScheduleResultModel> createAndComplete(CommonScheduleCreateAndCompleteParam param) {
        int userId = param.getUserId();
        int mapId = param.getMapId();
        int scheduleId = param.getScheduleId();
        int minutes = 0;
        String orderId = String.valueOf(BufferedIdGenerator.getId());
        RpcResult<CommonScheduleCreateModel> result = userCommonScheduleComponent.create(userId, mapId, scheduleId, minutes);
        if (!result.isSuccess()) {
            return RpcResult.result(result.getCode(), result.getMessage());
        }
        UserMapOngoingSchedule userMapOngoingSchedule = userMapOngoingScheduleRepository.queryByUserIdAndMapId(userId, mapId);
        if (userMapOngoingSchedule == null) {
            log.error("UserCommonScheduleComponent createAndCompleteSchedule user {} schedule {} not found", userId, mapId);
            return RpcResult.result(RoleGameResponse.SCHEDULE_NOT_EXIST);
        }
        RpcResult<CommonScheduleResultModel> completeResult = userCommonScheduleComponent.complete(param.getClientInfo(), userId, userMapOngoingSchedule,
                orderId);
        if (!completeResult.isSuccess()) {
            return RpcResult.result(completeResult.getCode(), completeResult.getMessage());
        }
        trackingData(userId, userMapOngoingSchedule, ScheduleActionType.COMPLETE.getDesc(), 0, 0);
        return RpcResult.success(completeResult.getData());
    }

    @Override
    public RpcResult<CommonScheduleResultModel> stop(CommonScheduleStopParam param) {
        int userId = param.getUserId();
        int userScheduleId = param.getUserScheduleId();
        String orderId = String.valueOf(BufferedIdGenerator.getId());
        UserMapOngoingSchedule userMapOngoingSchedule = userMapOngoingScheduleRepository.queryById(userScheduleId);
        if (userMapOngoingSchedule == null) {
            log.error("UserCommonScheduleComponent stopSchedule user {} schedule {} not found", userId, userScheduleId);
            return RpcResult.result(RoleGameResponse.SCHEDULE_NOT_EXIST);
        }
        RpcResult<CommonScheduleResultModel> result = userCommonScheduleComponent.stop(userId, userMapOngoingSchedule, orderId);
        if (!result.isSuccess()) {
            return RpcResult.result(result.getCode(), result.getMessage());
        }
        trackingData(userId, userMapOngoingSchedule, ScheduleActionType.STOP.getDesc(), 0, 0);
        return RpcResult.success(result.getData());
    }


    @Override
    public RpcResult<ScheduleGiftConfigList> rewardGear(int mapId) {
        KeyValueConfig keyValueConfig = keyValueConfigRepository.queryByKey(String.format(KeyValueConfigKeys.MAP_SCHEDULE_ACCELERATION_CONFIG,mapId));
        if (keyValueConfig == null) {
            return RpcResult.success();
        }
        List<ScheduleGiftConfig> scheduleGiftConfigs = GsonUtils.tryParseList(keyValueConfig.getValue(), ScheduleGiftConfig.class);
        if (CollectionUtils.isEmpty(scheduleGiftConfigs)) {
            return RpcResult.success();
        }
        ScheduleGiftConfigList scheduleGiftConfigList = new ScheduleGiftConfigList();
        scheduleGiftConfigList.setGearConfigs(scheduleGiftConfigs);
        return RpcResult.success(scheduleGiftConfigList);
    }

    @Override
    public void trackingData(int userId, UserMapOngoingSchedule userMapOngoingSchedule, String actionType, int spendKKB, int spendRecharge) {
        if (userMapOngoingSchedule == null) {
            log.warn("trackingData failed, userMapOngoingSchedule is null, userId:{}", userId);
            return;
        }
        Map<String, Object> properties = new HashMap<>();
        try {
            // 设置基本信息
            properties.put("Action", actionType);
            // 获取并设置日程信息
            Schedule schedule = scheduleRepository.queryByIdFromCache(userMapOngoingSchedule.getScheduleId());
            properties.put("ScheduleName", schedule == null ? StringUtils.EMPTY : schedule.getName());
            Building building = buildingRepository.queryByIdFromCache(userMapOngoingSchedule.getBuildingId());
            properties.put("BuildingName", building != null ? building.getName() : StringUtils.EMPTY);
            List<String> rewardTypeList = Lists.newArrayList();
            List<String> rewardCountList = Lists.newArrayList();
            CommonScheduleSettlementSnapshot settlementSnapshot = userMapOngoingSchedule.getSettlementSnapshot();
            if (settlementSnapshot != null) {
                // 处理通用奖励
                if (CollectionUtils.isNotEmpty(settlementSnapshot.getCommonPrizes())) {
                    for (CommonScheduleSettlementSnapshot.CommonPrize prize : settlementSnapshot.getCommonPrizes()) {
                        if (prize != null && prize.getPrizeName() != null) {
                            rewardTypeList.add(prize.getPrizeName());
                            rewardCountList.add(String.valueOf(settlementSnapshot.getPeriodCount()));
                        }
                    }
                }
                // 处理剧情奖励
                CommonScheduleSettlementSnapshot.StoryPrize storyPrize = settlementSnapshot.getStoryPrize();
                if (storyPrize != null) {
                    rewardTypeList.add("剧情奖励");
                    rewardCountList.add(String.valueOf(settlementSnapshot.getPeriodCount()));
                }
            }
            properties.put("RewardTypeList", rewardTypeList);
            properties.put("RewardCountList", rewardCountList);
            properties.put("SpendKKB", spendKKB);
            properties.put("SpendRecharge", spendRecharge);
            saComponent.uploadEventData(userId, SaComponent.MAP_SCHEDULE, properties);
            log.info("trackingData success, userId:{}, scheduleId:{}, actionType:{}", userId, userMapOngoingSchedule.getScheduleId(), actionType);
        } catch (Exception e) {
            log.error("trackingData error, userId:{}, userMapOngoingSchedule:{}, actionType:{}, error:{}", userId, userMapOngoingSchedule, actionType,
                    e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RpcResult<CommonScheduleResultModel> guideFinish(CommonScheduleGuideFinishParam param) {
        final int userScheduleId = param.getUserScheduleId();
        final int userId = param.getUserId();
        String orderId = String.valueOf(BufferedIdGenerator.getId());
        final ClientInfo clientInfo = new ClientInfo().setUserId(userId).setUserAgent(PassportContext.getUserAgent()).setXDevice(PassportContext.getXDevice());
        log.debug("finishSchedule, userId:{}, userScheduleId:{}", userId, userScheduleId);
        GuideRecord guideRecord = guideRecordRepository.queryByUserIdFromDb(userId);
        if (guideRecord == null) {
            log.error("finishSchedule error, guideRecord is null, userId:{}, userScheduleId:{}", userId, userScheduleId);
            return RpcResult.result(RoleGameResponse.GUIDE_RECORD_FAIL.getCode(), "引导记录为空");
        }
        final Boolean isScheduleAccelerated = guideRecord.getConfig().get(GuideType.FIRST_FREE_ACCELERATE_SCHEDULE.getKey());
        if (isScheduleAccelerated == null || isScheduleAccelerated) {
            log.error("finishSchedule isScheduleAccelerated is null or true, userId:{}, userScheduleId:{}, isScheduleAccelerated:{},guideRecord:{}", userId,
                    userScheduleId, isScheduleAccelerated, guideRecord);
            return RpcResult.result(RoleGameResponse.GUIDE_RECORD_FAIL.getCode(), "无免费加速次数");
        }
        UserMapOngoingSchedule userMapOngoingSchedule = userMapOngoingScheduleRepository.queryById(userScheduleId);
        if (userMapOngoingSchedule == null) {
            log.error("UserCommonScheduleComponent stopSchedule user {} schedule {} not found", userId, userScheduleId);
            return RpcResult.result(RoleGameResponse.SCHEDULE_NOT_EXIST);
        }
        final RpcResult<CommonScheduleResultModel> completeScheduleModelBizResult = userCommonScheduleComponent.complete(clientInfo, userId,
                userMapOngoingSchedule, orderId);
        if (!completeScheduleModelBizResult.isSuccess()) {
            log.error("finishSchedule complete schedule error, userId:{}, userScheduleId:{}, result:{}", userId, userScheduleId,
                    completeScheduleModelBizResult);
            return RpcResult.result(RoleGameResponse.GUIDE_RECORD_FAIL);
        }
        guideRecord.updateConfigByCode(GuideType.FIRST_FREE_ACCELERATE_SCHEDULE.getCode());
        guideRecordRepository.updateGuideRecord(guideRecord);
        return RpcResult.success(completeScheduleModelBizResult.getData());
    }

}
