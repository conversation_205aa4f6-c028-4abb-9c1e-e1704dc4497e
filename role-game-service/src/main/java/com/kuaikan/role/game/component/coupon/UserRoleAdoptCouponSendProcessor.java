package com.kuaikan.role.game.component.coupon;

import static com.kuaikan.role.game.uitl.ThreadPoolConfig.ADOPT_COUPON_SEND_EXECUTOR;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.kuaikan.common.exception.IllegalParamKException;
import com.kuaikan.message.bean.MessageTemplate;
import com.kuaikan.message.service.MessageService;
import com.kuaikan.role.game.api.bean.UserRole;
import com.kuaikan.role.game.repository.UserRoleRepository;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.google.common.collect.Sets;

import com.kuaikan.common.config.Settings;
import com.kuaikan.idgenerator.sdk.BizIdGenerator;
import com.kuaikan.message.bean.MessageActionTarget;
import com.kuaikan.message.enums.MessagePlatform;
import com.kuaikan.message.enums.MessagePushFlag;
import com.kuaikan.message.enums.MessageSource;
import com.kuaikan.message.enums.MessageTemplateFlag;
import com.kuaikan.message.enums.MessageType;
import com.kuaikan.message.model.MessageParam;
import com.kuaikan.role.game.api.bean.BaseCouponConfig;
import com.kuaikan.role.game.api.bean.Role;
import com.kuaikan.role.game.api.bean.RoleAdoptCouponConfig;
import com.kuaikan.role.game.api.bean.UserAdoptCouponRecord;
import com.kuaikan.role.game.api.bean.UserCouponBasicInfo;
import com.kuaikan.role.game.api.enums.UserCouponSourceType;
import com.kuaikan.role.game.api.enums.UserTableEnum;
import com.kuaikan.role.game.common.bean.RedDotEventInfo;
import com.kuaikan.role.game.common.bean.RedDotUserRecord;
import com.kuaikan.role.game.common.bean.RoleGroupAdoptCouponRelation;
import com.kuaikan.role.game.common.bean.RoleGroupRelation;
import com.kuaikan.role.game.common.enums.RedDotEventType;
import com.kuaikan.role.game.common.enums.RedDotRecordStatus;
import com.kuaikan.role.game.component.LockComponent;
import com.kuaikan.role.game.component.RedDotComponent;
import com.kuaikan.role.game.repository.RoleAdoptCouponConfigRepository;
import com.kuaikan.role.game.repository.RoleGroupAdoptCouponRelationRepository;
import com.kuaikan.role.game.repository.RoleGroupRelationRepository;
import com.kuaikan.role.game.repository.RoleRepository;
import com.kuaikan.role.game.repository.UserAdoptCouponRecordRepository;
import com.kuaikan.role.game.uitl.DateUtil;
import com.kuaikan.role.game.uitl.TablePartitionUtil;

/**
 * <AUTHOR>
 * @date 2024/12/25 16:12
 * @description 领养券发放设置
 */

@Slf4j
@Component
public class UserRoleAdoptCouponSendProcessor extends AbstractUserCouponSendProcessor {

    /** 领养券-发放用户表mongo前缀 */
    public static final String TABLE_PREFIX = "user_coupon_adopt_role_";

    /** 快看小助手 */
    public static final int MSG_SEND_USER_ID_STAG = 3293155;
    public static final int MSG_SEND_USER_ID_PROD = 26653435;

    /** button */
    public static final String TARGET_GUIDE_BUTTON = "button";
    public static final String TARGET_GUIDE_NAME = "立即使用";
    public static final Integer TARGET_ACTION_TYPE = 18;

    /** 跳转链接 */
    public static final String TARGET_WEB_URL_STAG = "https://rolegame.quickcan.cn/character-game-main-client/index.html?backgroundColor=ffecce&conf2fullscreen=1&conf2scrollwhitearea=1&statusbar=0&autoOpen=cabinet&blockAutoClose=1&alpha_screen=1";
    public static final String TARGET_WEB_URL_PROD = "https://rolegame.kuaikanmanhua.com/character-game-main-client/index.html?backgroundColor=ffecce&conf2fullscreen=1&conf2scrollwhitearea=1&statusbar=0&autoOpen=cabinet&blockAutoClose=1&alpha_screen=1";

    @Resource
    private RoleRepository roleRepository;
    @Resource
    private RoleGroupRelationRepository roleGroupRelationRepository;
    @Resource
    private UserAdoptCouponRecordRepository userAdoptCouponRecordRepository;
    @Resource
    private RoleAdoptCouponConfigRepository roleAdoptCouponConfigRepository;
    @Resource
    private RoleGroupAdoptCouponRelationRepository roleGroupAdoptCouponRelationRepository;
    @Resource
    private LockComponent lockComponent;
    @Resource
    private RedDotComponent redDotComponent;
    @Resource
    private UserRoleRepository userRoleRepository;
    @Resource
    private MessageService messageService;

    @Override
    protected String generateCollectionName(Integer couponId) {
        RoleAdoptCouponConfig roleAdoptCouponConfig = roleAdoptCouponConfigRepository.selectById(couponId);
        if (roleAdoptCouponConfig == null) {
            log.error("generate role adopt collection name error, config is null, couponId={}", couponId);
            return null;
        }
        return TABLE_PREFIX + DateUtil.getTargetDate(roleAdoptCouponConfig.getCreatedAt());
    }

    @Override
    protected String getCouponRecordTableName(Integer userId) {
        return TablePartitionUtil.getTableName(UserTableEnum.USER_ADOPT_COUPON_RECORD, userId);
    }

    @Override
    protected BaseCouponConfig getCouponBasicInfo(Integer couponId) {
        return roleAdoptCouponConfigRepository.selectById(couponId);
    }

    /** 如果发消息则返回消息参数 */
    @Override
    protected MessageTemplate buildMsgTemplate(Integer couponId, Integer sourceType, Integer sendCount) {
        RoleAdoptCouponConfig roleAdoptCouponConfig = roleAdoptCouponConfigRepository.selectById(couponId);
        Boolean relatedMsg = roleAdoptCouponConfig.getExtraInfo().getRelatedMsg();
        if (!Boolean.TRUE.equals(relatedMsg)) {
            return null;
        }
        // build message param
        int validDays = roleAdoptCouponConfig.getExtraInfo().getValidDays();
        String discountRate = roleAdoptCouponConfig.getExtraInfo().getDiscountRate();
        int roleGroupId = roleGroupAdoptCouponRelationRepository.queryRelationByCouponId(couponId).getRoleGroupId();
        List<Integer> roleIds = roleGroupRelationRepository.queryByGroupIdFromCache(roleGroupId)
                .stream()
                .map(RoleGroupRelation::getRoleId)
                .collect(Collectors.toList());
        List<String> roleNames = roleRepository.getRoleListByIds(roleIds).stream().map(Role::getName).collect(Collectors.toList());

        MessageParam msgParam = new MessageParam();
        msgParam.setMessagePlatform(MessagePlatform.ALL);
        msgParam.setMessageType(MessageType.TO_TARGETUSER);
        if (UserCouponSourceType.getByCode(sourceType) == UserCouponSourceType.SYSTEM) {
            msgParam.setTitle(String.format("送您一张%s、%s的角色领养折扣券", roleNames.get(0), roleNames.get(1)));
            msgParam.setDescription(String.format("领养第2个角色可享受%s折！有效期%s天，尽快使用哦～", discountRate, validDays));
        } else if (UserCouponSourceType.getByCode(sourceType) == UserCouponSourceType.WISH) {
            msgParam.setTitle(String.format("送您一张%s、%s的角色领养折扣券", roleNames.get(0), roleNames.get(1)));
            msgParam.setDescription(String.format("领养第2个角色可享受%s折！有效期%s天，多余的折扣券可兑换为行动力药水。", discountRate, validDays));
        } else if (UserCouponSourceType.getByCode(sourceType) == UserCouponSourceType.PRIZE) {
            msgParam.setTitle(String.format("恭喜获得%s、%s 角色领养折扣券", roleNames.get(0), roleNames.get(1)));
            msgParam.setDescription(String.format("领养第二个角色享%s折优惠，有效期%s天。重复获得的折扣券还可兑换行动力药水哦~", discountRate, validDays));
        }
        msgParam.setNotifyAt(System.currentTimeMillis());
        msgParam.setPushFlag(MessagePushFlag.PUSH);
        msgParam.setMessageSource(MessageSource.NORMAL.getCode());
        msgParam.setTemplateFlag(MessageTemplateFlag.NORMAL);
        MessageActionTarget actionTarget = new MessageActionTarget();
        actionTarget.setActionType(TARGET_ACTION_TYPE);
        actionTarget.setTargetGuideName(TARGET_GUIDE_NAME);
        actionTarget.setTargetGuide(TARGET_GUIDE_BUTTON);
        actionTarget.setTargetWebUrl(Settings.getEnvironment().isProd() ? TARGET_WEB_URL_PROD : TARGET_WEB_URL_STAG);
        msgParam.setMessageActionTarget(actionTarget);
        msgParam.setSendUserId(Settings.getEnvironment().isProd() ? MSG_SEND_USER_ID_PROD : MSG_SEND_USER_ID_STAG);
        MessageTemplate messageTemplate;
        try {
            messageTemplate = messageService.addMessageV1(msgParam);
        } catch (IllegalParamKException e) {
            log.error("error in messageService.addMessageV1, messageParam:{}, error:{}", msgParam, e);
            return null;
        }
        return messageTemplate;
    }

    @Override
    protected UserCouponBasicInfo buildUserCouponBasicInfo(Integer userId, BaseCouponConfig baseCouponConfig, Integer sourceType) {
        RoleAdoptCouponConfig roleAdoptCouponConfig = (RoleAdoptCouponConfig) baseCouponConfig;
        int validDays = roleAdoptCouponConfig.getExtraInfo().getValidDays();
        Date expiredAt = DateUtil.getDayEnd(new Date(System.currentTimeMillis() + validDays * 24 * 60 * 60 * 1000L));
        return new UserAdoptCouponRecord().setUserId(userId)
                .setCouponId(roleAdoptCouponConfig.getId())
                .setSource(sourceType)
                .setExpiredAt(expiredAt)
                .setUsed(Boolean.FALSE)
                .setBid(BizIdGenerator.getId());
    }

    @Override
    protected void sendCouponToUsers(Map<String, Set<UserCouponBasicInfo>> userRecordsMap) {
        userRecordsMap.forEach((tableName, records) -> {
            log.info("开始向{}表插入用户领养券记录,用户数量为{}", tableName, records.size());
            List<UserAdoptCouponRecord> recordList = records.stream().map(UserAdoptCouponRecord.class::cast).collect(Collectors.toList());
            userAdoptCouponRecordRepository.insertBatch(tableName, recordList);
        });
    }

    @Override
    protected RedDotEventInfo queryRedDotEventInfo(Integer couponId) {
        return redDotComponent.sendP2PEventInfo(RedDotEventType.ROLE_ADOPT_COUPON, couponId);
    }

    @Override
    protected void sendRedDotToUsers(Map<String, Set<Integer>> userRedDotMap, Integer couponId, RedDotEventInfo redDotEventInfo) {
        if (userRedDotMap.isEmpty() || couponId == null) {
            return;
        }
        // userRedDotMap 处理
        Map<String, Set<Integer>> bestCouponUserIdMap = getBestCouponUserIdMap(userRedDotMap, couponId);
        bestCouponUserIdMap.forEach((tableName, userIds) -> {
            List<RedDotUserRecord> redDotUserRecords = userIds.stream()
                    .map(userId -> new RedDotUserRecord().setUserId(userId)
                            .setEventTypeId(redDotEventInfo.getEventTypeId())
                            .setEventId(redDotEventInfo.getId())
                            .setSubDimId(couponId)
                            .setVersion(0)
                            .setStatus(RedDotRecordStatus.VALID.getCode()))
                    .collect(Collectors.toList());
            if (!redDotUserRecords.isEmpty()) {
                log.info("开始向{}表插入用户领养券红点记录,用户数量为{}", tableName, redDotUserRecords.size());
                redDotComponent.addUserRecordBatch(tableName, redDotUserRecords);
            }
        });
    }

    @Override
    protected RedDotEventInfo getRedDotEventInfo(Integer couponId) {
        return redDotComponent.sendP2PEventInfo(RedDotEventType.ROLE_ADOPT_COUPON, couponId);
    }

    @Override
    protected ThreadPoolExecutor getTaskExecutor() {
        return ADOPT_COUPON_SEND_EXECUTOR;
    }

    @Override
    protected int updateCouponConfigProcess(int couponId, int progressType) {
        return roleAdoptCouponConfigRepository.updateCouponConfigProcess(couponId, progressType);
    }

    @Override
    protected boolean lockForSendCoupon(int couponId) {
        return lockComponent.lockForUserSendCoupon(couponId);
    }

    @Override
    protected void unlockForSendCoupon(int couponId) {
        lockComponent.unlockForUserSendCoupon(couponId);
    }

    /** 获取当前折扣券是为最优的用户ids */
    private Map<String, Set<Integer>> getBestCouponUserIdMap(Map<String, Set<Integer>> userRedDotMap, Integer couponId) {
        // userRedDotMap , key = 表名， value = 这个表中需要插入红点的用户列表
        int currentRoleGroupId = roleGroupAdoptCouponRelationRepository.queryRelationByCouponId(couponId).getRoleGroupId();
        double currentDiscountRate = Double.parseDouble(roleAdoptCouponConfigRepository.selectById(couponId).getExtraInfo().getDiscountRate());
        List<Integer> groupRoleIds = roleGroupRelationRepository.queryRoleIdsByGroupId(currentRoleGroupId).stream().map(RoleGroupRelation::getRoleId).collect(Collectors.toList());

        // userRedDotMap 取出所有的用户id列表 userids
        List<Integer> userIds = userRedDotMap.values().stream().flatMap(Set::stream).collect(Collectors.toList());
        // 查询所有用户拥有的角色Map<integer,List<UserRole>> userRoleMap，key = 用户id，value = 用户所拥有的角色列表
        Map<Integer, List<UserRole>> userRoleMap = userRoleRepository.queryByUserIdsFromDB(userIds);
        // 循环 userRoleMap，如果用户两个角色都领养了，userIds 中剔除该用户
        userRoleMap.entrySet().stream()
                .filter(entry -> !entry.getValue().stream()
                        .map(UserRole::getRoleId)
                        .collect(Collectors.toSet())
                        .containsAll(groupRoleIds))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        // 未完全领养用户
        userIds = new ArrayList<>(userRoleMap.keySet());
        // 1、有历史折扣券的用户：判断是否最优券
        Map<Integer, List<UserAdoptCouponRecord>> historyRecordMap = userAdoptCouponRecordRepository.batchSelectByUserIds(userIds);
        // 2、无历史折扣券的用户：当前就是最优券
        Set<Integer> noCouponHistoryUsers = Sets.newHashSet(userIds);
        noCouponHistoryUsers.removeAll(historyRecordMap.keySet());
        // 处理有历史券用户
        List<Integer> allCouponIds = historyRecordMap.values().stream()
                .flatMap(List::stream)
                .map(UserAdoptCouponRecord::getCouponId)
                .collect(Collectors.toList());
        Map<Integer, Integer> couponRelationMap = roleGroupAdoptCouponRelationRepository.queryRelationByCouponIds(allCouponIds).stream()
                .collect(Collectors.toMap(RoleGroupAdoptCouponRelation::getCouponId, RoleGroupAdoptCouponRelation::getRoleGroupId));
        Map<Integer, List<Integer>> historyCouponIdMap = historyRecordMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().stream()
                        .map(UserAdoptCouponRecord::getCouponId)
                        // 排除新发的券
                        .filter(id -> !Objects.equals(id, couponId))
                        .filter(id -> couponRelationMap.get(id) == currentRoleGroupId)
                        .collect(Collectors.toList())));
        // historyCouponIdMap 中取出所有的历史券id，查询couponConfig，Map<Integer,config> couponconfigMap, key = 券id, value = 券model
        List<Integer> historyCouponIds = historyCouponIdMap.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());
        Map<Integer, RoleAdoptCouponConfig> couponConfigMap = roleAdoptCouponConfigRepository.selectByIds(historyCouponIds).stream()
                .collect(Collectors.toMap(RoleAdoptCouponConfig::getId, config -> config));
        // 最优券:如果当前券折扣数最低则是最优券;如果用户没有历史券则当前券是最优券
        Map<Integer, List<Integer>> bestCouponUserMap = historyCouponIdMap.entrySet()
                .stream()
                .filter(entry -> entry.getValue()
                        .stream()
                        .allMatch(id -> Double.parseDouble(couponConfigMap.get(id).getExtraInfo().getDiscountRate()) > currentDiscountRate))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        // 无券用户+最优券用户合并
        Set<Integer> bestCouponUserIds = new HashSet<>(CollectionUtils.union(bestCouponUserMap.keySet(), noCouponHistoryUsers));
        // userRedDotMap过滤出最优券用户,发红点
        return userRedDotMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().stream()
                        .filter(bestCouponUserIds::contains)
                        .collect(Collectors.toSet())));
    }
}
