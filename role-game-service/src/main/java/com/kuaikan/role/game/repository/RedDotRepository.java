package com.kuaikan.role.game.repository;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.kuaikan.role.game.common.enums.RedDotEventType;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Repository;

import com.kuaikan.role.game.api.enums.UserTableEnum;
import com.kuaikan.role.game.common.bean.RedDotDisplayInfo;
import com.kuaikan.role.game.common.bean.RedDotDisplayInfoExample;
import com.kuaikan.role.game.common.bean.RedDotEventInfo;
import com.kuaikan.role.game.common.bean.RedDotEventInfoExample;
import com.kuaikan.role.game.common.bean.RedDotUserRecord;
import com.kuaikan.role.game.common.dao.RedDotDisplayInfoMapper;
import com.kuaikan.role.game.common.dao.RedDotEventInfoMapper;
import com.kuaikan.role.game.common.dao.RedDotUserRecordMapper;
import com.kuaikan.role.game.uitl.TablePartitionUtil;

/**
 *
 * <AUTHOR>
 * @date 2024/7/5
 */
@Slf4j
@Repository
public class RedDotRepository {

    @Resource
    private RedDotEventInfoMapper redDotEventInfoMapper;

    @Resource
    private RedDotUserRecordMapper redDotUserRecordMapper;

    @Resource
    private RedDotEventInfoMapper redDotEventInfoMapperSlave;

    @Resource
    private RedDotUserRecordMapper redDotUserRecordMapperSlave;

    @Resource
    private RedDotDisplayInfoMapper redDotDisplayInfoMapperSlave;

    public RedDotUserRecord getUserRecordByUserIdAndEventIdFromMaster(Integer userId, Integer eventId) {
        return redDotUserRecordMapper.getUserRecordByUserIdAndEventId(getTableName(userId), userId, eventId);
    }

    public RedDotUserRecord getUserRecordByUserIdAndEventId(Integer userId, Integer eventId) {
        return redDotUserRecordMapperSlave.getUserRecordByUserIdAndEventId(getTableName(userId), userId, eventId);
    }

    public void updateUserRecord(RedDotUserRecord userRecord) {
        redDotUserRecordMapper.updateByPrimaryKeySelective(getTableName(userRecord.getUserId()), userRecord);
    }

    public void updateUserRecordStatusBatch(int userId, int status, List<Integer> recordIds) {
        redDotUserRecordMapper.updateStatusBatch(getTableName(userId), userId, status, recordIds);
    }

    public void addUserRecord(RedDotUserRecord userRecord) {
        redDotUserRecordMapper.insertSelective(getTableName(userRecord.getUserId()), userRecord);
    }

    public void addUserRecordBatch(String tableName, List<RedDotUserRecord> userRecordList) {
        if (CollectionUtils.isEmpty(userRecordList)) {
            return;
        }
        redDotUserRecordMapper.insertBatch(tableName, userRecordList);
    }

    public void addUserRecordBatch(int userId, List<RedDotUserRecord> userRecordList) {
        redDotUserRecordMapper.insertBatch(getTableName(userId), userRecordList);
    }

    public List<RedDotUserRecord> getUserRecordByUserIdAndeEentTypeIdAndStatus(Integer userId, Integer eventTypeId, Integer status) {
        String tableName = getTableName(userId);
        return redDotUserRecordMapper.getByUserIdAndEventTypeIdAndStatus(tableName, userId, eventTypeId, status);
    }

    public int updateStatusByUserIdAndEventTypeId(Integer userId, Integer eventTypeId, Integer status) {
        String tableName = getTableName(userId);
        return redDotUserRecordMapper.updateStatusByUserIdAndEventTypeId(tableName, userId, eventTypeId, status);
    }

    public List<RedDotEventInfo> getEventsByPageId(Integer pageId) {
        RedDotDisplayInfoExample query = new RedDotDisplayInfoExample();
        query.createCriteria().andPageIdEqualTo(pageId);
        List<RedDotDisplayInfo> redDotEventDisplayInfos = redDotDisplayInfoMapperSlave.selectByExample(query);
        if (redDotEventDisplayInfos.isEmpty()) {
            return Collections.emptyList();
        }
        List<Integer> eventTypeIds = ListUtils.emptyIfNull(redDotEventDisplayInfos)
                .stream()
                .map(RedDotDisplayInfo::getEventTypeId)
                .collect(Collectors.toList());
        RedDotEventInfoExample queryEvent = new RedDotEventInfoExample();
        queryEvent.createCriteria().andEventTypeIdIn(eventTypeIds);
        return redDotEventInfoMapperSlave.selectByExample(queryEvent);
    }

    public List<RedDotUserRecord> getUserRecordByUserIdAndEventIds(Integer userId, List<Integer> eventIdList) {
        if (CollectionUtils.isEmpty(eventIdList)) {
            return new ArrayList<>();
        }
        return redDotUserRecordMapperSlave.getUserRecordByUserIdAndEventIds(getTableName(userId), userId, eventIdList);
    }

    public void addEvent(RedDotEventInfo eventInfo) {
        redDotEventInfoMapper.insertSelective(eventInfo);
    }

    public List<RedDotEventInfo> getEventByEventTypeId(Integer eventTypeId) {
        RedDotEventInfoExample query = new RedDotEventInfoExample();
        query.createCriteria().andEventTypeIdEqualTo(eventTypeId);
        List<RedDotEventInfo> redDotEventInfos = redDotEventInfoMapperSlave.selectByExample(query);
        return redDotEventInfos;
    }

    public List<RedDotEventInfo> getEventByEventTypeIdAndCreatedAt(Integer eventTypeId, Date createdAt) {
        RedDotEventInfoExample query = new RedDotEventInfoExample();
        query.createCriteria().andEventTypeIdEqualTo(eventTypeId).andCreatedAtGreaterThan(createdAt);
        List<RedDotEventInfo> redDotEventInfos = redDotEventInfoMapperSlave.selectByExample(query);
        return redDotEventInfos;
    }

    public RedDotEventInfo getEventByEventTypeIdAndSubDimId(Integer eventTypeId, Integer subDimId) {
        RedDotEventInfoExample query = new RedDotEventInfoExample();
        query.createCriteria().andEventTypeIdEqualTo(eventTypeId).andSubIdEqualTo(subDimId);
        List<RedDotEventInfo> redDotEventInfos = redDotEventInfoMapperSlave.selectByExample(query);
        if (redDotEventInfos.isEmpty()) {
            return null;
        }
        if (redDotEventInfos.size() > 1) {
            log.error("more than one event for eventTypeId:{}, subDimId:{}", eventTypeId, subDimId);
        }
        return redDotEventInfos.get(0);
    }

    public RedDotEventInfo getEventByEventTypeIdAndThirdId(Integer eventTypeId, Long thirdId) {
        RedDotEventInfoExample query = new RedDotEventInfoExample();
        query.createCriteria().andEventTypeIdEqualTo(eventTypeId).andThirdIdEqualTo(thirdId);
        List<RedDotEventInfo> redDotEventInfos = redDotEventInfoMapperSlave.selectByExample(query);
        if (redDotEventInfos.isEmpty()) {
            return null;
        }
        if (redDotEventInfos.size() > 1) {
            log.error("more than one event for eventTypeId:{}, thirdId:{}", eventTypeId, thirdId);
        }
        return redDotEventInfos.get(0);
    }

    public Map<Integer, RedDotEventInfo> getEventByEventTypeIdAndSubDimIds(Integer eventTypeId, Collection<Integer> subDimIds) {
        if (CollectionUtils.isEmpty(subDimIds)) {
            return new HashMap<>();
        }
        RedDotEventInfoExample query = new RedDotEventInfoExample();
        query.createCriteria().andEventTypeIdEqualTo(eventTypeId).andSubIdIn(subDimIds);
        return redDotEventInfoMapperSlave.selectByExample(query).stream().collect(Collectors.toMap(RedDotEventInfo::getSubId, Function.identity()));
    }

    public List<RedDotEventInfo> getEventByEventTypeIdsAndSubDimId(List<Integer> eventTypeIds, Integer subDimId) {
        RedDotEventInfoExample query = new RedDotEventInfoExample();
        query.createCriteria().andEventTypeIdIn(ListUtils.emptyIfNull(eventTypeIds)).andSubIdEqualTo(subDimId);
        return redDotEventInfoMapperSlave.selectByExample(query);
    }

    public void updateEvent(RedDotEventInfo event) {
        redDotEventInfoMapper.updateByPrimaryKey(event);
    }

    private String getTableName(int userId) {
        return TablePartitionUtil.getTableName(UserTableEnum.RED_DOT_USER_RECORD, userId);
    }

    public RedDotEventInfo getUserRecordByUserIdAndEventIdAndThirdId(RedDotEventType eventType, Integer subId, Long thirdId) {
        RedDotEventInfoExample query = new RedDotEventInfoExample();
        query.createCriteria()
                .andEventTypeIdEqualTo(eventType.getCode())
                .andSubIdEqualTo(subId)
                .andThirdIdEqualTo(thirdId);
        List<RedDotEventInfo> redDotEventInfos = redDotEventInfoMapperSlave.selectByExample(query);
        if (redDotEventInfos.isEmpty()) {
            return null;
        }
        if (redDotEventInfos.size() > 1) {
            log.error("more than one event for eventTypeId:{}, subId: {}, thirdId:{}", eventType.getCode(), subId, thirdId);
        }
        return redDotEventInfos.get(0);
    }
}
