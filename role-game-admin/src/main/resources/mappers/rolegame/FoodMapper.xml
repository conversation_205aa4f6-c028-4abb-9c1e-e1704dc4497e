<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kuaikan.role.game.admin.dao.FoodMapper">
    <resultMap id="resultMap" type="com.kuaikan.role.game.common.bean.Food">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="config" property="config" typeHandler="com.kuaikan.common.db.mybatis.JsonTypeHandler"
                javaType="com.kuaikan.role.game.common.bean.Food$Config"/>
        <result column="order_num" property="orderNum"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="column">
        id
        , name, config, order_num, created_at, updated_at
    </sql>

    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.kuaikan.role.game.common.bean.Food"
            useGeneratedKeys="true">
        insert into food (name, config, order_num)
        values (#{name},
                #{config,typeHandler=com.kuaikan.common.db.mybatis.JsonTypeHandler,javaType=com.kuaikan.role.game.common.bean.Food$Config}, #{orderNum})
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.kuaikan.role.game.common.bean.Food">
        update food
        set name      = #{name},
            config    = #{config,typeHandler=com.kuaikan.common.db.mybatis.JsonTypeHandler,javaType=com.kuaikan.role.game.common.bean.Food$Config},
            order_num = #{orderNum}
        where id = #{id}
    </update>

    <update id="updateOrderNumGreaterThanOrEqualToNewOrderNum">
        UPDATE food
        SET order_num = order_num + 1
        WHERE order_num &gt;= #{orderNum}
    </update>

    <select id="queryAll" resultMap="resultMap">
        select
        <include refid="column"/>
        from food
        order by order_num asc, created_at desc
    </select>

    <select id="queryByPage" resultMap="resultMap">
        select
        <include refid="column"/>
        from food
        order by order_num asc, created_at desc
        limit #{offset}, #{limit}
    </select>

    <select id="queryById" resultMap="resultMap">
        select
        <include refid="column"/>
        from food
        where id = #{id}
    </select>
</mapper>