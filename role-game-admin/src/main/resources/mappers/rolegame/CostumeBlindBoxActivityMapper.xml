<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kuaikan.role.game.admin.dao.CostumeBlindBoxActivityMapper">
    <resultMap id="BaseResultMap" type="com.kuaikan.role.game.api.bean.CostumeBlindBoxActivity">
        <id column="id" property="id"/>
        <result column="order_num" property="orderNum"/>
        <result column="name" property="name"/>
        <result column="role_group_id" property="roleGroupId"/>
        <result column="start_at" property="startAt"/>
        <result column="end_at" property="endAt"/>
        <result column="config" property="config" typeHandler="com.kuaikan.common.db.mybatis.JsonTypeHandler"
                javaType="com.kuaikan.role.game.api.bean.CostumeBlindBoxActivity$Config"/>
        <result column="status" property="status"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, order_num, `name`, role_group_id, start_at, end_at, config, `status`,
        created_at, updated_at
    </sql>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" useGeneratedKeys="true" parameterType="com.kuaikan.role.game.api.bean.CostumeBlindBoxActivity">
        insert into costume_blind_box_activity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNum != null">
                order_num,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="roleGroupId != null">
                role_group_id,
            </if>
            <if test="startAt != null">
                start_at,
            </if>
            <if test="endAt != null">
                end_at,
            </if>
            <if test="config != null">
                config,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNum != null">
                #{orderNum},
            </if>
            <if test="name != null">
                #{name},
            </if>
            <if test="roleGroupId != null">
                #{roleGroupId},
            </if>
            <if test="startAt != null">
                #{startAt},
            </if>
            <if test="endAt != null">
                #{endAt},
            </if>
            <if test="config != null">
                #{config, typeHandler=com.kuaikan.common.db.mybatis.JsonTypeHandler, javaType=com.kuaikan.role.game.api.bean.CostumeBlindBoxActivity$Config},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.kuaikan.role.game.api.bean.CostumeBlindBoxActivity">
        update costume_blind_box_activity
        <set>
            <if test="orderNum != null">
                order_num = #{orderNum},
            </if>
            <if test="name != null">
                `name` = #{name},
            </if>
            <if test="roleGroupId != null">
                role_group_id = #{roleGroupId},
            </if>
            <if test="startAt != null">
                start_at = #{startAt},
            </if>
            <if test="endAt != null">
                end_at = #{endAt},
            </if>
            <if test="config != null">
                config = #{config, typeHandler=com.kuaikan.common.db.mybatis.JsonTypeHandler, javaType=com.kuaikan.role.game.api.bean.CostumeBlindBoxActivity$Config},
            </if>
            <if test="status != null">
                `status` = #{status},
            </if>
        </set>
        where id = #{id}
    </update>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from costume_blind_box_activity
        where id = #{id}
    </select>
    <select id="count" resultType="java.lang.Integer">
        select count(1)
        from costume_blind_box_activity
    </select>
    <select id="queryByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from costume_blind_box_activity
        order by id desc
        limit #{offset}, #{limit}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from costume_blind_box_activity
        where id = #{id}
    </delete>

</mapper>