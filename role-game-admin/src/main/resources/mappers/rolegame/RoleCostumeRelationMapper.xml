<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kuaikan.role.game.admin.dao.RoleCostumeRelationMapper">
    <resultMap id="resultMap" type="com.kuaikan.role.game.api.bean.RoleCostumeRelation">
        <id column="id" property="id"/>
        <result column="role_id" property="roleId"/>
        <result column="costume_id" property="costumeId"/>
        <result column="config" property="config" typeHandler="com.kuaikan.common.db.mybatis.JsonTypeHandler"
                javaType="com.kuaikan.role.game.api.bean.RoleCostumeRelation$Config"/>
        <result column="order_num" property="orderNum"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>
    <sql id="column">
        id
        , role_id, costume_id,config, order_num,created_at, updated_at
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="resultMap">
        select
        <include refid="column"/>
        from role_costume_relation
        where id = #{id}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from role_costume_relation
        where id = #{id}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.kuaikan.role.game.api.bean.RoleCostumeRelation"
            useGeneratedKeys="true">
        insert into role_costume_relation (role_id, costume_id, config, order_num, created_at,
                                           updated_at)
        values (#{roleId}, #{costumeId},
                #{config, typeHandler=com.kuaikan.common.db.mybatis.JsonTypeHandler, javaType=com.kuaikan.role.game.api.bean.RoleCostumeRelation$Config},
                #{orderNum}, #{createdAt},
                #{updatedAt})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.kuaikan.role.game.api.bean.RoleCostumeRelation" useGeneratedKeys="true">
        insert into role_costume_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roleId != null">
                role_id,
            </if>
            <if test="costumeId != null">
                costume_id,
            </if>
            <if test="config != null">
                config,
            </if>
            <if test="orderNum != null">
                order_num,
            </if>
            <if test="createdAt != null">
                created_at,
            </if>
            <if test="updatedAt != null">
                updated_at,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roleId != null">
                #{roleId},
            </if>
            <if test="costumeId != null">
                #{costumeId},
            </if>
            <if test="config != null">
                #{config,typeHandler=com.kuaikan.common.db.mybatis.JsonTypeHandler, javaType=com.kuaikan.role.game.api.bean.RoleCostumeRelation$Config},
            </if>
            <if test="orderNum != null">
                #{orderNum},
            </if>
            <if test="createdAt != null">
                #{createdAt},
            </if>
            <if test="updatedAt != null">
                #{updatedAt},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.kuaikan.role.game.api.bean.RoleCostumeRelation">
        update role_costume_relation
        <set>
            <if test="roleId != null">
                role_id = #{roleId},
            </if>
            <if test="costumeId != null">
                costume_id = #{costumeId},
            </if>
            <if test="config != null">
                config =
                #{config, typeHandler=com.kuaikan.common.db.mybatis.JsonTypeHandler, javaType=com.kuaikan.role.game.api.bean.RoleCostumeRelation$Config},
            </if>
            <if test="orderNum != null">
                order_num = #{orderNum},
            </if>
            <if test="createdAt != null">
                created_at = #{createdAt},
            </if>
            <if test="updatedAt != null">
                updated_at = #{updatedAt},
            </if>
        </set>
        where id = #{id}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.kuaikan.role.game.api.bean.RoleCostumeRelation">
        update role_costume_relation
        set role_id    = #{roleId},
            costume_id = #{costumeId},
            config     = #{config, typeHandler=com.kuaikan.common.db.mybatis.JsonTypeHandler, javaType=com.kuaikan.role.game.api.bean.RoleCostumeRelation$Config},
            order_num  = #{orderNum},
            created_at = #{createdAt},
            updated_at = #{updatedAt}
        where id = #{id}
    </update>
    <select id="queryByRoleIds" parameterType="java.util.List" resultMap="resultMap">
        select
        <include refid="column"/>
        from role_costume_relation
        where role_id in
        <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </select>

    <select id="intersectCostumeIdByRoles" resultType="java.lang.Integer">
        select
        costume_id
        from role_costume_relation
        where role_id in
        <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
        group by costume_id
        HAVING COUNT(DISTINCT role_id) = #{roleIdsSize}
    </select>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into role_costume_relation (role_id, costume_id)
        values
        <foreach collection="records" item="item" separator=",">
            (#{item.roleId}, #{item.costumeId})
        </foreach>
    </insert>
    <select id="queryByRoleId" parameterType="java.lang.Integer" resultMap="resultMap">
        select
        <include refid="column"/>
        from role_costume_relation
        where role_id = #{roleId}
    </select>
    <select id="queryByCostumeIds" parameterType="java.util.List" resultMap="resultMap">
        select
        <include refid="column"/>
        from role_costume_relation
        where costume_id in
        <foreach collection="costumeIds" item="costumeId" open="(" separator="," close=")">
            #{costumeId}
        </foreach>
    </select>
    <select id="countByRoleId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        select count(1)
        from role_costume_relation
        where role_id = #{roleId}
    </select>
    <select id="queryPageByRoleId" parameterType="com.kuaikan.role.game.api.bean.RoleCostumeRelation"
            resultMap="resultMap">
        select
        <include refid="column"/>
        from role_costume_relation
        where role_id = #{roleId}
        order by order_num,id desc
        limit #{offset}, #{limit}
    </select>

    <update id="updateOrderNumGreaterThanOrEqualToNewOrderNum">
        UPDATE role_costume_relation
        SET order_num = order_num + 1
        WHERE order_num &gt;= #{orderNum}
          AND role_id = #{roleId}
    </update>

    <update id="updateBatchConfig" parameterType="java.util.List">
        <foreach collection="records" item="item" index="index" separator=";">
            update role_costume_relation
            set config =
            #{item.config,typeHandler=com.kuaikan.common.db.mybatis.JsonTypeHandler,javaType=com.kuaikan.role.game.api.bean.RoleCostumeRelation$Config}
            where costume_id = #{item.costumeId}
            AND role_id = #{item.roleId}
        </foreach>
    </update>

    <select id="queryCostumeIdsByRoleId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        select costume_id from role_costume_relation where role_id = #{roleId}
    </select>
</mapper>