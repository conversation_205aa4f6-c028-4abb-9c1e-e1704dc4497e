<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kuaikan.role.game.admin.dao.RoleGroupMapper">

    <resultMap id="resultMap" type="com.kuaikan.role.game.api.bean.RoleGroup">
        <id column="id" property="id"/>
        <result column="default_scene_id" property="defaultSceneId"/>
        <result column="config" property="config" typeHandler="com.kuaikan.common.db.mybatis.JsonTypeHandler"
                javaType="com.kuaikan.role.game.api.bean.RoleGroup$Config"/>
        <result column="order_num" property="orderNum"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="columns">
        id
        ,
        default_scene_id,
        config,
        order_num,
        created_at,
        updated_at
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="roleGroup.id">
        insert into role_group
            (`default_scene_id`, `order_num`)
        values (#{roleGroup.defaultSceneId}, #{roleGroup.orderNum})
    </insert>

    <update id="update">
        update role_group
        set default_scene_id=#{roleGroup.defaultSceneId},
            order_num=#{roleGroup.orderNum}
        where id = #{roleGroup.id}
    </update>

    <select id="queryByPage" resultMap="resultMap">
        select
        <include refid="columns"/>
        from role_group
        order by order_num asc, id desc
        limit #{offset},#{pageSize}
    </select>

    <select id="queryAll" resultMap="resultMap">
        select
        <include refid="columns"/>
        from role_group
        order by order_num , id
    </select>

    <select id="queryById" resultMap="resultMap">
        select
        <include refid="columns"/>
        from role_group
        where id=#{id}
    </select>
    <select id="queryByIds" resultMap="resultMap">
        select
        <include refid="columns"/>
        from role_group
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="queryBySceneId" resultMap="resultMap">
        select
        <include refid="columns"/>
        from role_group
        where default_scene_id=#{sceneId}
    </select>

    <update id="updateOrderNumGreaterThanOrEqualToNewOrderNum">
        UPDATE role_group
        SET order_num = order_num + 1
        WHERE order_num &gt;= #{orderNum}
    </update>

    <update id="updateConfigById">
        update role_group
        set config=#{updatedConfig,typeHandler=com.kuaikan.common.db.mybatis.JsonTypeHandler,
            javaType=com.kuaikan.role.game.api.bean.RoleGroup$Config}
        where id=#{id}
    </update>

</mapper>

