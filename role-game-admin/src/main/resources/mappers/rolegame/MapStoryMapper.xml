<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kuaikan.role.game.admin.dao.rolegame.MapStoryMapper">

    <resultMap id="MapStoryResultMap" type="com.kuaikan.role.game.api.bean.MapStory">
        <id column="id" property="id"/>
        <result column="map_id" property="mapId"/>
        <result column="library_id" property="libraryId"/>
        <result column="tag" property="tag"/>
        <result column="avg_chapter_id" property="avgChapterId"/>
        <result column="cover_img" property="coverImage"/>
        <result column="weight" property="weight"/>
        <result column="obtain_copywriting" property="obtainCopywriting"/>
        <result column="status" property="status"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , map_id, library_id, tag, avg_chapter_id, cover_img, weight, obtain_copywriting, status, created_at, updated_at
    </sql>

    <insert id="insert" parameterType="com.kuaikan.role.game.api.bean.MapStory">
        INSERT INTO map_story (map_id, library_id, tag, avg_chapter_id, cover_img, weight, obtain_copywriting, status, created_at,
                               updated_at)
        VALUES (#{mapId}, #{libraryId}, #{tag}, #{avgChapterId}, #{coverImage}, #{weight}, #{obtainCopywriting}, #{status},
                #{createdAt}, #{updatedAt})
    </insert>

    <select id="selectByMapId" resultMap="MapStoryResultMap" parameterType="int">
        SELECT <include refid="Base_Column_List"/>
        FROM map_story
        WHERE map_id = #{mapId}
    </select>

    <select id="selectByMapAndChapterId" resultMap="MapStoryResultMap">
        select
        <include refid="Base_Column_List"/>
        from map_story
        where map_id = #{mapId}
        and avg_chapter_id = #{chapterId}
    </select>

    <update id="updateById">
        update map_story
        set library_id         = #{libraryId},
            cover_img          = #{coverImage},
            tag                = #{tag},
            weight             = #{weight},
            obtain_copywriting = #{obtainCopywriting},
            status             = #{status},
            updated_at         = #{updatedAt}
        where id = #{id}
    </update>
</mapper>