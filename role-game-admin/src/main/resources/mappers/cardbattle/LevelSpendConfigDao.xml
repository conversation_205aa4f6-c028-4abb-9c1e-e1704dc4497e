<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kuaikan.role.game.admin.battle.dao.LevelSpendConfigDao">

  <resultMap id="BaseResultMap"
    type="com.kuaikan.role.game.api.bean.cardbattle.CardBattleCardLevelSpendConfig">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="level" jdbcType="INTEGER" property="level"/>
    <result column="rare_code" jdbcType="INTEGER" property="rareCode"/>
    <result column="coin_spend" jdbcType="INTEGER" property="coinSpend"/>
    <result column="exp_spend" jdbcType="INTEGER" property="expSpend"/>
  </resultMap>

  <sql id="Base_Column_List">
    id
    , level, rare_code, coin_spend, exp_spend
  </sql>

  <select id="selectAll" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM card_battle_card_level_spend_config
    ORDER BY rare_code, level
  </select>

  <insert id="batchInsert" parameterType="java.util.List">
    INSERT INTO card_battle_card_level_spend_config (level, rare_code, coin_spend, exp_spend)
    VALUES
    <foreach collection="list" item="record" separator=",">
      (#{record.level,jdbcType=INTEGER}, #{record.rareCode,jdbcType=INTEGER},
      #{record.coinSpend,jdbcType=INTEGER},#{record.expSpend,jdbcType=INTEGER})
    </foreach>
    ON DUPLICATE KEY UPDATE coin_spend = VALUES(coin_spend), exp_spend = VALUES(exp_spend)
  </insert>

</mapper>