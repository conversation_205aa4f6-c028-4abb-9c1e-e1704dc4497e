<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kuaikan.role.game.admin.battle.dao.MonsterModelConfigDao">

    <resultMap id="BaseResultMap"
               type="com.kuaikan.role.game.api.bean.cardbattle.CardBattleMonsterModelConfig">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="model_id" jdbcType="INTEGER" property="modelId"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="atk" jdbcType="INTEGER" property="atk"/>
        <result column="hp" jdbcType="INTEGER" property="hp"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , model_id, `type`, atk, hp
    </sql>

    <select id="getAllConfigs" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM card_battle_monster_model_config
        where status = 0
    </select>

    <update id="updateInvalidStatusByIds" parameterType="java.util.List">
        UPDATE card_battle_monster_model_config
        SET status = 1
        WHERE id IN
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO card_battle_monster_model_config (model_id, type, atk, hp, status)
        VALUES
        <foreach collection="list" item="record" separator=",">
            (#{record.modelId,jdbcType=INTEGER},#{record.type,jdbcType=INTEGER},
            #{record.atk,jdbcType=INTEGER}, #{record.hp,jdbcType=INTEGER},
            #{record.status,jdbcType=INTEGER})
        </foreach>
        ON DUPLICATE KEY UPDATE `type` = VALUES(`type`), atk = VALUES(atk), hp = VALUES(hp),
        status = VALUES(status)
    </insert>

    <select id="selectByModelId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM card_battle_monster_model_config
        where status = 0
        and model_id = #{modelId}
    </select>

</mapper>