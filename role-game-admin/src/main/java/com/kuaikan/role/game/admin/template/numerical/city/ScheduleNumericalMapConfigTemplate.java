package com.kuaikan.role.game.admin.template.numerical.city;

import com.alibaba.excel.read.listener.ReadListener;
import com.google.common.collect.Lists;
import com.kuaikan.account.api.model.PassportContext;
import com.kuaikan.role.game.admin.model.excel.ScheduleNumericalExcelData;
import com.kuaikan.role.game.admin.repository.MapBuildingRelationRepository;
import com.kuaikan.role.game.admin.repository.ScheduleRepository;
import com.kuaikan.role.game.admin.utils.excel.listener.ScheduleNumericalExcelDataListener;
import com.kuaikan.role.game.api.bean.MapBuildingRelation;
import com.kuaikan.role.game.api.bean.Schedule;
import com.kuaikan.role.game.api.bean.ScheduleNumericalConfig;
import com.kuaikan.role.game.api.bo.FailMessage;
import com.kuaikan.role.game.api.enums.CityConfigType;
import groovy.util.logging.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class ScheduleNumericalMapConfigTemplate extends MapConfigTemplate<ScheduleNumericalExcelData> {

    @Resource
    private ScheduleRepository scheduleRepository;

    @Resource
    private MapBuildingRelationRepository mapBuildingRelationRepository;

    public static final int ROW_START = 2;
    public static final String COLUMN_SCHEDULE_ID = "A";
    public static final String COLUMN_SCHEDULE_NAME = "B";
    public static final String COLUMN_SCHEDULE_REMARK = "C";
    public static final String COLUMN_SCHEDULE_CONSUME = "D";
    public static final String COLUMN_SCHEDULE_REWARD_IDS = "E";
    public static final String COLUMN_SCHEDULE_STORY_ID = "F";
    public static final String COLUMN_SCHEDULE_PROBABILITY = "G";

    @Override
    protected int getHeadRowNumber() {
        return ROW_START;
    }

    @Override
    protected Class<ScheduleNumericalExcelData> getExcelDataClass() {
        return ScheduleNumericalExcelData.class;
    }

    @Override
    protected ReadListener<ScheduleNumericalExcelData> createExcelListener(List<ScheduleNumericalExcelData> excelDataList) {
        return new ScheduleNumericalExcelDataListener(excelDataList);
    }

    @Override
    protected List<FailMessage> validate() {
        List<FailMessage> failMessages = Lists.newArrayList();
        for (int i = 0; i < super.excelDataList.size(); i++) {
            int row = i + ROW_START + 1;
            final ScheduleNumericalExcelData excelData = super.excelDataList.get(i);
            final Integer id = excelData.getId();
            if (Objects.isNull(id)) {
                failMessages.add(newMessage(row, COLUMN_SCHEDULE_ID, "日程ID为空"));
            }

            Schedule schedule = scheduleRepository.queryById(id);
            if (Objects.isNull(schedule)) {
                failMessages.add(newMessage(row, COLUMN_SCHEDULE_ID, "未查询到日程 id="+id));
            }

            String name = excelData.getName();
            if (StringUtils.isBlank(name)) {
                failMessages.add(newMessage(row, COLUMN_SCHEDULE_NAME, "日程名称不能为空"));
            }

            String remark = excelData.getRemark();

            String consume = excelData.getConsume();
            if (StringUtils.isBlank(consume)) {
                failMessages.add(newMessage(row, COLUMN_SCHEDULE_CONSUME, "日程消耗行动力数为空"));
            } else if (!StringUtils.isNumeric(consume)) {
                failMessages.add(newMessage(row, COLUMN_SCHEDULE_CONSUME, "日程消耗行动力数必须为数字"));
            }

            String rewardIds = excelData.getRewardIds();
            if (StringUtils.isBlank(rewardIds)) {
                failMessages.add(newMessage(row, COLUMN_SCHEDULE_REWARD_IDS, "奖励Id为空"));
            }

            String[] split = rewardIds.split(",");
            for (String rewardId : split) {
                if (!StringUtils.isNumeric(rewardId.trim())) {
                    failMessages.add(newMessage(row, COLUMN_SCHEDULE_REWARD_IDS, "奖励Id <"+ rewardId +"> 不是数字"));
                }
            }

            Integer storyId = excelData.getStoryId();
            if (Objects.isNull(storyId)) {
                failMessages.add(newMessage(row, COLUMN_SCHEDULE_STORY_ID, "剧情Id为空"));
            }

            Integer probability = excelData.getProbability();
            if (Objects.isNull(probability)) {
                failMessages.add(newMessage(row, COLUMN_SCHEDULE_PROBABILITY, "出发概率为空"));
            }
        }
        return failMessages;
    }

    @Override
    public void save() {
        for (ScheduleNumericalExcelData excelData : super.excelDataList) {
            Integer id = excelData.getId();
            Schedule schedule = scheduleRepository.queryById(id);
            Schedule.Config config = schedule.getConfig();
            ScheduleNumericalConfig convert = excelData.convert();
            config.setNumericalConfig(convert);
            scheduleRepository.update(schedule);

            MapBuildingRelation relation = new MapBuildingRelation();
            relation.setMapId(this.mapId);
            relation.setBuildingId(schedule.getBuildingId());
            mapBuildingRelationRepository.insertOrUpdate(relation);
        }
    }

    @Override
    public CityConfigType getCityConfigType() {
        // 日程配置
        return CityConfigType.SCHEDULE_NUMERICAL;
    }
}
