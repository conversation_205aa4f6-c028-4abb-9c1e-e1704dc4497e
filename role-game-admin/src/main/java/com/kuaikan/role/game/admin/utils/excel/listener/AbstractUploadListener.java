package com.kuaikan.role.game.admin.utils.excel.listener;

import lombok.extern.slf4j.Slf4j;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.enums.RowTypeEnum;
import com.alibaba.excel.read.listener.ReadListener;

/**
 *
 * <AUTHOR>
 * @date 2023/2/17
 */
@Slf4j
public class AbstractUploadListener<T> implements ReadListener<T> {

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        ReadListener.super.onException(exception, context);
    }

    @Override
    public void invoke(T data, AnalysisContext analysisContext) {
        //是否忽略空白行设置为false (参考：com.alibaba.excel.read.processor.DefaultAnalysisEventProcessor.endRow)
        log.debug("AbstractUploadListener invoke row is {}", analysisContext.readRowHolder().getRowIndex());
        analysisContext.readWorkbookHolder().setIgnoreEmptyRow(true);
    }

    @Override
    public boolean hasNext(AnalysisContext context) {
        if (RowTypeEnum.EMPTY.equals(context.readRowHolder().getRowType())) {
            //遇到第一个空行直接结束
            //解析结束的工作
            doAfterAllAnalysed(context);
            return false;
        }
        return ReadListener.super.hasNext(context);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("data analyse complete");
    }

}
