package com.kuaikan.role.game.admin.model.view;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.bo.ImageInfo;

/**
 *
 * <AUTHOR>
 * @date 2024/6/21
 */
@Data
@Accessors(chain = true)
public class BuildingMapView {

    /**
     * id
     */
    private Integer id;

    /**
     * 名称
     */
    private String name;

    /**
     * 关联建筑id
     */
    private Integer buildingId;

    /**
     * npc数量
     */
    private Integer npcNum;

    /**
     * 装饰物数量
     */
    private Integer decorationNum;

    /**
     * 图片地址
     */
    private String imgUrl;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 更新时间
     */
    private Long updatedAt;

    /**
     * 状态
     */
    private Integer status;

}
