package com.kuaikan.role.game.admin.repository;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import com.google.common.collect.Lists;

import com.kuaikan.common.redis.KeyGenerator;
import com.kuaikan.common.redis.lettuce.LettuceClusterClient;
import com.kuaikan.common.redis.lettuce.LettuceClusterUtil;
import com.kuaikan.role.game.admin.dao.BuildingAreaMapper;
import com.kuaikan.role.game.api.bean.BuildingArea;
import com.kuaikan.role.game.common.enums.CacheConfig;

/**
 * BuildingAreaRepository
 *
 * <AUTHOR>
 * @since 2024-06-07
 */
@Repository
public class BuildingAreaRepository {

    @Resource
    private BuildingAreaMapper buildingAreaMapper;

    public int batchInsert(List<BuildingArea> buildingAreas) {
        final int i = buildingAreaMapper.batchInsert(buildingAreas);
        deleteCache(buildingAreas.stream().map(BuildingArea::getBuildingId).collect(Collectors.toList()));
        return i;
    }

    public int deleteByBuildingId(int buildingId) {
        final int i = buildingAreaMapper.deleteByBuildingId(buildingId);
        deleteCache(Lists.newArrayList(buildingId));
        return i;
    }

    public int update(BuildingArea buildingArea) {
        final int update = buildingAreaMapper.update(buildingArea);
        deleteCache(Lists.newArrayList(buildingArea.getBuildingId()));
        return update;
    }

    public List<BuildingArea> queryByBuildingId(int buildingId) {
        return buildingAreaMapper.queryByBuildingId(buildingId);
    }

    public List<BuildingArea> queryByBuildingIds(Collection<Integer> buildingIds) {
        if (CollectionUtils.isEmpty(buildingIds)) {
            return new ArrayList<>();
        }
        return buildingAreaMapper.queryByBuildingIds(buildingIds);
    }

    public int insertBatch(List<BuildingArea> buildingAreas) {
        if (CollectionUtils.isEmpty(buildingAreas)) {
            return 0;
        }
        final int i = buildingAreaMapper.batchInsert(buildingAreas);
        deleteCache(buildingAreas.stream().map(BuildingArea::getBuildingId).collect(Collectors.toList()));
        return i;
    }

    public BuildingArea queryByAreaId(int areaId) {
        return buildingAreaMapper.queryByAreaId(areaId);
    }

    public int deleteByAreaId(int areaId) {
        final int i = buildingAreaMapper.deleteByAreaId(areaId);
        final BuildingArea buildingArea = buildingAreaMapper.queryByAreaId(areaId);
        if (Objects.nonNull(buildingArea)) {
            deleteCache(Lists.newArrayList(buildingArea.getBuildingId()));
        }
        return i;
    }

    private void deleteCache(Collection<Integer> buildingIds) {
        List<String> deleteKeys = new ArrayList<>();
        final LettuceClusterClient redisClient = LettuceClusterUtil.getClusterClientByName(CacheConfig.BUILDING_AREA_INFOS.getReadWriteVip());
        String cacheKey = KeyGenerator.generate(CacheConfig.BUILDING_AREA_INFOS.getKeyPattern());
        deleteKeys.add(cacheKey);
        if (CollectionUtils.isNotEmpty(deleteKeys)) {
            for (Integer buildingId : buildingIds) {
                String cacheKey2 = KeyGenerator.generate(CacheConfig.BUILDING_AREA_BY_BUILDING_INFOS.getKeyPattern(), buildingId);
                deleteKeys.add(cacheKey2);
            }
        }
        redisClient.del(deleteKeys.toArray(new String[0]));
    }

}
