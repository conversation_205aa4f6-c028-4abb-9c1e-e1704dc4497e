package com.kuaikan.role.game.admin.model.param;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/31
 */
@Data
@Accessors(chain = true)
public class AvgFileQueryParam {

    private String name;

    private Integer topicId;

    private Integer resource;

    private int status;

    private List<Integer> types;

    private String orderByAndSort;

    private String innerOrderByAndSort;

    private int page;

    private int pageSize;

}
