package com.kuaikan.role.game.admin.biz;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.kuaikan.admin.base.bean.Operation;
import com.kuaikan.admin.base.common.PageView;
import com.kuaikan.admin.base.utils.OperateLogUtils;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.model.RpcResult;
import com.kuaikan.common.util.JsonUtils;
import com.kuaikan.idgenerator.sdk.BufferedIdGenerator;
import com.kuaikan.role.game.admin.common.OperationConstants;
import com.kuaikan.role.game.admin.common.RoleGameResponse;
import com.kuaikan.role.game.admin.model.param.SpiritStoneUpdateParam;
import com.kuaikan.role.game.admin.model.view.SpiritStoneView;
import com.kuaikan.role.game.admin.repository.RoleRepository;
import com.kuaikan.role.game.admin.repository.RoleSpiritStoneConfigRepository;
import com.kuaikan.role.game.api.bean.Role;
import com.kuaikan.role.game.api.bean.RoleSpiritStoneConfig;
import com.kuaikan.role.game.api.enums.RewardType;
import com.kuaikan.role.game.api.enums.SpiritStoneEnum;
import com.kuaikan.role.game.api.rpc.param.AssignRewardParam;
import com.kuaikan.role.game.api.service.AssignRewardService;

/**
 * <AUTHOR>
 * @date 2025/3/5 17:14
 */

@Slf4j
@Service
public class SpiritStoneBiz {

    @Resource
    private RoleSpiritStoneConfigRepository roleSpiritStoneConfigRepository;
    @Resource
    private RoleRepository roleRepository;
    @Resource
    private AssignRewardService assignRewardService;

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> update(SpiritStoneUpdateParam param) {
        if (!param.isParamIllegal()) {
            log.error("update spiritStone config param is illegal, param:{}", param);
            return BizResult.result(RoleGameResponse.PARAM_ILLEGAL.getCode(), "参数不合法");
        }
        RoleSpiritStoneConfig updateConfig = roleSpiritStoneConfigRepository.selectByPrimaryKey(param.getId());
        if (updateConfig == null) {
            log.error("update spiritStone config not exist, id:{}", param.getId());
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "配置不存在");
        }
        RoleSpiritStoneConfig.Config oldConfig = updateConfig.getConfig();
        updateConfig.setConfig(param.toConfig()).setUpdatedAt(new Date());
        roleSpiritStoneConfigRepository.updateByPrimaryKeySelective(updateConfig);
        Operation operation = Operation.of(OperationConstants.OperateSubType.ROLE_SPIRIT_STONE_UPDATE)
                .add("id", param.getId())
                .add("oldData", JsonUtils.writeValueAsString(oldConfig))
                .add("newData", JsonUtils.writeValueAsString(param.toConfig()));
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<PageView<SpiritStoneView>> list(Integer pageNum, Integer pageSize) {
        List<Role> roleList = roleRepository.queryAll();
        Set<Integer> allRoleIds = roleList.stream().map(Role::getId).collect(Collectors.toSet());
        int total = allRoleIds.size();
        Set<Integer> initializedRoleIds = roleSpiritStoneConfigRepository.queryAll().stream().map(RoleSpiritStoneConfig::getRoleId).collect(Collectors.toSet());
        allRoleIds.removeAll(initializedRoleIds);
        int insertCount = roleSpiritStoneConfigRepository.insertList(RoleSpiritStoneConfig.init(allRoleIds));
        if (insertCount != allRoleIds.size()) {
            log.error("init role spirit stone config error, insert:{}, required:{}", insertCount, allRoleIds.size());
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "初始化角色漫灵石失败");
        }
        if (insertCount > 0) {
            log.info("init role spirit stone config success, insertCount:{}, initRoleIds:{}", insertCount, allRoleIds);
            Operation operation = Operation.of(OperationConstants.OperateSubType.ROLE_SPIRIT_STONE_INIT)
                    .add("insertCount", insertCount)
                    .add("initRoleIds", JsonUtils.writeValueAsString(allRoleIds));
            OperateLogUtils.asyncRecord(operation);
        }
        int offset = (pageNum - 1) * pageSize;
        List<RoleSpiritStoneConfig> configs = roleSpiritStoneConfigRepository.queryByPage(offset, pageSize);
        Map<Integer, Role> roleMap = roleList.stream().collect(Collectors.toMap(Role::getId, Function.identity()));
        List<SpiritStoneView> views = configs.stream().map(config -> SpiritStoneView.valueOf(config, roleMap)).collect(Collectors.toList());
        return BizResult.success(PageView.form(total, views));
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> present(int userId, int roleId, int amount) {
        if (amount < 1) {
            log.error("present spirit stone error, userId:{}, roleId:{}, amount:{}", userId, roleId, amount);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "赠送数量不能小于1");
        }
        RoleSpiritStoneConfig roleSpiritStoneConfig = roleSpiritStoneConfigRepository.queryByRoleId(roleId);
        if (roleSpiritStoneConfig == null) {
            log.error("present spirit stone error, roleId:{} spirit stone config not exist", roleId);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色漫灵石配置不存在");
        }
        AssignRewardParam assignRewardParam = new AssignRewardParam().setType(RewardType.SPIRIT_STONE.getCode())
                .setAwardSource(SpiritStoneEnum.GIFT.getType())
                .setUserId(userId)
                .setRelatedId(roleSpiritStoneConfig.getId())
                .setNum(amount)
                .setBid(BufferedIdGenerator.getId());
        log.info("present spirit stone, param:{}", assignRewardParam);
        RpcResult<Void> rpcResult = assignRewardService.assignRewardAndRecord(assignRewardParam);
        if (!rpcResult.isSuccess()) {
            log.error("present spirit stone error, param:{}, result:{}", assignRewardParam, rpcResult);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "赠送漫灵石失败");
        }
        return BizResult.success();
    }
}
