package com.kuaikan.role.game.admin.model.view;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.bean.Role;

/**
 * <AUTHOR>
 * @date 2025/3/24 12:22
 */

@Data
@Accessors(chain = true)
public class AdoptAnimationView implements Serializable {

    private static final long serialVersionUID = 7205398187893097209L;

    private Integer roleId;
    private String greetingText;
    private String expectedCpText;
    private List<String> entranceAnimation;
    private List<String> greetingAnimation;
    private List<String> expectedCpAnimation;

    public static AdoptAnimationView valueOf(Role.Config.AdoptAnimation adoptAnimation, int roleId) {
        if (adoptAnimation == null) {
            return null;
        }
        return new AdoptAnimationView().setGreetingText(adoptAnimation.getGreetingText())
                .setExpectedCpText(adoptAnimation.getExpectedCpText())
                .setEntranceAnimation(adoptAnimation.getEntranceAnimation())
                .setGreetingAnimation(adoptAnimation.getGreetingAnimation())
                .setExpectedCpAnimation(adoptAnimation.getExpectedCpAnimation());
    }
}
