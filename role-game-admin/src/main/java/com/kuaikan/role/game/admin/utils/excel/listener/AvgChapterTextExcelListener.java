package com.kuaikan.role.game.admin.utils.excel.listener;

import java.util.List;

import com.alibaba.excel.context.AnalysisContext;

import com.kuaikan.role.game.admin.model.excel.AvgChapterTextExcel;

/**
 * <AUTHOR>
 */
public class AvgChapterTextExcelListener extends AbstractUploadListener<AvgChapterTextExcel> {

    private final List<AvgChapterTextExcel> excelDTOList;

    public AvgChapterTextExcelListener(List<AvgChapterTextExcel> excelDTOList) {
        this.excelDTOList = excelDTOList;
    }

    @Override
    public void invoke(AvgChapterTextExcel dto, AnalysisContext analysisContext) {
        super.invoke(dto, analysisContext);
        excelDTOList.add(dto);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
    }
}
