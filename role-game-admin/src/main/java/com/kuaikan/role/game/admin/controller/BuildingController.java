package com.kuaikan.role.game.admin.controller;

import java.util.Map;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.kuaikan.role.game.admin.biz.BuildingBiz;
import com.kuaikan.role.game.admin.model.param.BuildingAddOrUpdateParam;
import com.kuaikan.role.game.admin.utils.ResponseUtils;

/**
 * <AUTHOR>
 * @date 2024/6/13
 */
@Slf4j
@RestController
@RequestMapping("/v2/admin/role/game/building")
public class BuildingController {

    @Resource
    private BuildingBiz buildingBiz;

    @PostMapping("addOrUpdate")
    public Map<String, Object> addOrUpdate(@RequestBody BuildingAddOrUpdateParam addOrUpdateParam) {
        return ResponseUtils.valueOf(buildingBiz.addOrUpdate(addOrUpdateParam));
    }

    @GetMapping("list")
    public Map<String, Object> list(@RequestParam(name = "pageNum", defaultValue = "1") int pageNum,
                                    @RequestParam(name = "pageSize", defaultValue = "20") int pageSize) {
        return ResponseUtils.valueOf(buildingBiz.list(pageNum, pageSize));
    }

    @GetMapping("all")
    public Map<String, Object> all() {
        return ResponseUtils.valueOf(buildingBiz.all());
    }

    @PostMapping("delete")
    public Map<String, Object> delete(@RequestParam(name = "id") int id) {
        return ResponseUtils.valueOf(buildingBiz.delete(id));
    }


    @PostMapping("deleteArea")
    public Map<String, Object> deleteArea(@RequestParam(name = "areaId") int areaId) {
        return ResponseUtils.valueOf(buildingBiz.deleteArea(areaId));
    }

    @PostMapping("updateSort")
    public Map<String, Object> updateSort(@RequestParam(name = "id") int id, @RequestParam(name = "order") int order) {
        return ResponseUtils.valueOf(buildingBiz.updateSort(id, order));
    }

}
