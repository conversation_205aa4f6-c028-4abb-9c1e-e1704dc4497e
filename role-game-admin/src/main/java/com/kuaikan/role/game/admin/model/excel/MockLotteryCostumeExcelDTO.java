package com.kuaikan.role.game.admin.model.excel;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import org.springframework.beans.BeanUtils;

import com.alibaba.excel.annotation.ExcelProperty;

import com.kuaikan.role.game.api.model.MockLotteryResultModel;

/**
 * <AUTHOR>
 * @date 2025/1/23 16:01
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MockLotteryCostumeExcelDTO implements Serializable {

    private static final long serialVersionUID = -3574907255497320171L;

    @ExcelProperty(value = "抽数", index = 0)
    private int lotteryCount;

    @ExcelProperty(value = "角色名称", index = 1)
    private String roleName;

    @ExcelProperty(value = "星级", index = 2)
    private String starLevel;

    @ExcelProperty(value = "装扮ID", index = 3)
    private int costumeId;

    @ExcelProperty(value = "装扮名称", index = 4)
    private String costumeName;

    @ExcelProperty(value = "装扮单品ID", index = 5)
    private int costumePartId;

    @ExcelProperty(value = "装扮单品名称", index = 6)
    private String costumePartName;

    @ExcelProperty(value = "是否触发定轨", index = 7)
    private String triggerTarget;

    @ExcelProperty(value = "各装扮抽取概率", index = 8)
    private String costumeProbabilities;

    @ExcelProperty(value = "各装扮抽取区间", index = 9)
    private String costumeRanges;

    @ExcelProperty(value = "装扮抽取随机数", index = 10)
    private double costumeRandom;

    @ExcelProperty(value = "是否是兜底策略抽取", index = 11)
    private String fallback;

    @ExcelProperty(value = "是否是保底抽取", index = 12)
    private String guaranteed;

    @ExcelProperty(value = "未保底次数", index = 13)
    private int nonGuaranteedCount;

    @ExcelProperty(value = "各装扮等级区间", index = 14)
    private String costumeLevelRanges;

    public static MockLotteryCostumeExcelDTO fromResultModel(MockLotteryResultModel resultModel) {
        MockLotteryCostumeExcelDTO excelDTO = new MockLotteryCostumeExcelDTO();
        BeanUtils.copyProperties(resultModel, excelDTO);
        return excelDTO;
    }
}