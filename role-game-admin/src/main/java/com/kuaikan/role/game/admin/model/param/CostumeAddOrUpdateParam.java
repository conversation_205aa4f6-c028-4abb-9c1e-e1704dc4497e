package com.kuaikan.role.game.admin.model.param;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.bean.Costume;
import com.kuaikan.role.game.api.bo.ImageInfo;

/**
 * <AUTHOR>
 * @date 2024/2/26
 */
@Data
@Accessors(chain = true)
public class CostumeAddOrUpdateParam implements Serializable {

    private static final long serialVersionUID = 6589914096629058676L;

    private int id;

    private String name;

    private int roleId;

    private String obtainCopywriting;

    private ImageInfo thumbnail;

    private ImageInfo icon;

    private Integer activityId;

    private SpineMaterialParam actionMaterial;

    private Integer level;

    private int cpCostumeId;

    private int relatedStoryId;

    public Costume.Config toCostumeConfig() {
        return new Costume.Config()
                .setObtainCopywriting(obtainCopywriting)
                .setThumbnail(thumbnail)
                .setIcon(icon)
                .setActivityId(activityId)
                .setCpCostumeId(cpCostumeId)
                .setRelatedStoryId(relatedStoryId);
    }

}

