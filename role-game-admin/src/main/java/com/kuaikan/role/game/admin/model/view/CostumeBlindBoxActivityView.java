package com.kuaikan.role.game.admin.model.view;

import java.io.Serializable;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.bean.CostumeBlindBoxActivity;

@Data
@Accessors(chain = true)
public class CostumeBlindBoxActivityView implements Serializable {

    private static final long serialVersionUID = -5347053280125198775L;

    private int id;

    private ImageInfoView image;

    private int orderNum;

    private String name;

    private int roleGroupId;

    private long startAt;

    private long endAt;

    private int status;

    private SpineMaterialView spine;

    private ImageInfoView drawRewardBtn;

    private String drawCopywriting;

    private ImageInfoView entryImage;

    private List<RoleConfigView> roleConfigs;

    private int scaleRatio;

    private int freeCount;

    /** 概率up装扮 */
    private UpCostumeView upCostume;

    private List<RewardView> rewards;

    private Integer defaultRuleId;

    private BlindBoxProbabilityView.ExptRuleView exptRules;

    @Data
    @Accessors(chain = true)
    public static class RewardView {

        private int needNum;

        private String name;

        private int type;

        private int rewardId;

        private int rewardNum;

        private ImageInfoView rewardImage;

        private String awardName;

        private String activityName;

        private Long prizeBagId;

        public static RewardView valueOf(CostumeBlindBoxActivity.Reward reward) {
            if (reward == null) {
                return null;
            }
            RewardView rewardView = new RewardView();
            rewardView.setNeedNum(reward.getNeedNum());
            rewardView.setName(reward.getName());
            rewardView.setType(reward.getType());
            Optional.ofNullable(reward.getRewardId()).ifPresent(rewardView::setRewardId);
            Optional.ofNullable(reward.getRewardNum()).ifPresent(rewardView::setRewardNum);
            Optional.ofNullable(reward.getRewardImage()).ifPresent(imageInfo -> rewardView.setRewardImage(ImageInfoView.valueOf(imageInfo)));
            Optional.ofNullable(reward.getAwardName()).ifPresent(rewardView::setAwardName);
            Optional.ofNullable(reward.getActivityName()).ifPresent(rewardView::setActivityName);
            Optional.ofNullable(reward.getPrizeBagId()).ifPresent(rewardView::setPrizeBagId);
            return rewardView;
        }
    }

    @Data
    @Accessors(chain = true)
    public static class UpCostumeView implements Serializable {

        private static final long serialVersionUID = 6655762184925708775L;
        private List<Integer> upCostumeIds;
        private List<ContentView> contents;

        public static UpCostumeView valueOf(CostumeBlindBoxActivity.UpCostume upCostume) {
            if (upCostume == null) {
                return null;
            }
            UpCostumeView upCostumeView = new UpCostumeView();
            upCostumeView.setUpCostumeIds(upCostume.getUpCostumeIds());
            upCostumeView.setContents(upCostume.getContents().stream().map(ContentView::valueOf).collect(Collectors.toList()));
            return upCostumeView;
        }
    }

    @Data
    @Accessors(chain = true)
    public static class ContentView implements Serializable {

        private static final long serialVersionUID = 869625341735143395L;
        private Integer type;
        private String content;

        public static ContentView valueOf(CostumeBlindBoxActivity.Content content) {
            if (content == null) {
                return null;
            }
            ContentView contentView = new ContentView();
            contentView.setType(content.getType());
            contentView.setContent(content.getContent());
            return contentView;
        }
    }

    @Data
    @Accessors(chain = true)
    public static class RoleConfigView implements Serializable {

        private static final long serialVersionUID = 5411392450380652288L;
        private int id;

        private String name;

        private int costumeId;

        private String animation;

        private CoordinateView coordinate;

        public static RoleConfigView valueOf(CostumeBlindBoxActivity.RoleConfig roleConfig) {
            if (roleConfig == null) {
                return null;
            }
            RoleConfigView roleConfigView = new RoleConfigView();
            roleConfigView.setId(roleConfig.getId());
            roleConfigView.setName(roleConfig.getName());
            roleConfigView.setCostumeId(roleConfig.getCostumeId());
            roleConfigView.setAnimation(roleConfig.getAnimation());
            roleConfigView.setCoordinate(CoordinateView.valueOf(roleConfig.getCoordinate()));
            return roleConfigView;
        }
    }

    @Data
    @Accessors(chain = true)
    public static class CoordinateView implements Serializable {

        private static final long serialVersionUID = -2834628693074304795L;
        private int x;
        private int y;

        public static CoordinateView valueOf(CostumeBlindBoxActivity.Coordinate coordinate) {
            if (coordinate == null) {
                return null;
            }
            CoordinateView coordinateView = new CoordinateView();
            coordinateView.setX(coordinate.getX());
            coordinateView.setY(coordinate.getY());
            return coordinateView;
        }
    }

    public static CostumeBlindBoxActivityView valueOf(CostumeBlindBoxActivity activity) {
        if (activity == null) {
            return null;
        }
        CostumeBlindBoxActivityView costumeBlindBoxActivityView = new CostumeBlindBoxActivityView().setId(activity.getId())
                .setOrderNum(activity.getOrderNum())
                .setName(activity.getName())
                .setRoleGroupId(activity.getRoleGroupId())
                .setStartAt(activity.getStartAt())
                .setEndAt(activity.getEndAt())
                .setStatus(activity.getStatus());

        CostumeBlindBoxActivity.Config config = activity.getConfig();
        if (config != null) {
            costumeBlindBoxActivityView.setImage(ImageInfoView.valueOf(config.getActivityImage()))
                    .setRoleConfigs(config.getRoleConfigs().stream().map(RoleConfigView::valueOf).collect(Collectors.toList()))
                    .setSpine(SpineMaterialView.valueOf(config.getSpine()))
                    .setDrawRewardBtn(ImageInfoView.valueOf(config.getDrawRewardBtn()))
                    .setDrawCopywriting(config.getDrawCopywriting())
                    .setEntryImage(ImageInfoView.valueOf(config.getEntryImage()))
                    .setScaleRatio(config.getScaleRatio())
                    .setFreeCount(config.getFreeCount())
                    .setUpCostume(UpCostumeView.valueOf(config.getUpCostume()))
                    .setDefaultRuleId(config.getDefaultRuleId())
                    .setExptRules(BlindBoxProbabilityView.ExptRuleView.valueOf(config.getExptRules()));
            if (config.getRewards() != null) {
                costumeBlindBoxActivityView.setRewards(config.getRewards().stream().map(RewardView::valueOf).collect(Collectors.toList()));
            }
        }
        return costumeBlindBoxActivityView;
    }
}
