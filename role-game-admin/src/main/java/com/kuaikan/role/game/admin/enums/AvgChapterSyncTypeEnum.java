package com.kuaikan.role.game.admin.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/1/13
 */
@Getter
@AllArgsConstructor
public enum AvgChapterSyncTypeEnum {

    INIT(0, "initial", "数字资产查询"),
    UPDATE(1, "update", "更新段落配置"),
    ;

    private final int code;
    private final String type;
    private final String desc;

    public static AvgChapterSyncTypeEnum getByCode(int code) {
        for (AvgChapterSyncTypeEnum status : AvgChapterSyncTypeEnum.values()) {
            if (code == status.getCode()) {
                return status;
            }
        }
        return null;
    }
}
