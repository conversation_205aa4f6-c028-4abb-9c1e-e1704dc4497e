package com.kuaikan.role.game.admin.model.param;

import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class BuildingMapAddOrUpdateParam {

    /**
     * id
     */
    private Integer id;

    /**
     * 名称
     */
    private String name;

    /**
     * 关联建筑id
     */
    private Integer buildingId;


    List<BuildingAreaMapParam> buildingAreaMapParamList;

}
