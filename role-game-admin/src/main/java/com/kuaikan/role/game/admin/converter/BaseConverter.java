package com.kuaikan.role.game.admin.converter;

import java.util.Date;

import com.kuaikan.comic.utils.CdnUtil;
import com.kuaikan.role.game.admin.model.view.ImageInfoView;
import com.kuaikan.role.game.api.bo.ImageInfo;
import com.kuaikan.role.game.api.enums.CommonStatus;

/**
 *
 * <AUTHOR>
 * @date 2024/6/24
 */
public interface BaseConverter {

    /**
     * 时间转换
     * @param date date
     * @return 时间戳
     */
    default Long dateToTimestamp(Date date) {
        return date != null ? date.getTime() : null;
    }

    default String convertStatus(CommonStatus status) {
        return status != null ? status.getDesc() : null;
    }

    default Integer convertStatusId(CommonStatus status) {
        return status != null ? status.getCode() : null;
    }

    default ImageInfoView toImageInfoView(ImageInfo imageInfo) {
        return ImageInfoView.valueOf(imageInfo);
    }

    default String toImageUrl(ImageInfo imageInfo) {
        if (imageInfo == null || imageInfo.getUrl() == null) {
            return null;
        }
        return CdnUtil.getDefaultDomainWithBackSlash() + imageInfo.getUrl();
    }

}
