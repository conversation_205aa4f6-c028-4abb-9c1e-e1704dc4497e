package com.kuaikan.role.game.admin.component.battle.excel.valid;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Collectors;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.metadata.data.ReadCellData;

import com.kuaikan.role.game.api.enums.cardbattle.CardBattleCardAttrEnum;
import com.kuaikan.role.game.api.enums.cardbattle.CardBattleCardRarityEnum;

/**
 * excel注解校验
 *
 * <AUTHOR>
 * @date 2024/6/26
 */
public class ExcelImportValid {

    /**
     * Excel导入字段校验
     *
     * @param object 校验的JavaBean 其属性须有自定义注解
     */
    public static void valid(Object object, int rowNumber) {
        Field[] fields = object.getClass().getDeclaredFields();
        for (Field field : fields) {
            //设置可访问
            field.setAccessible(true);
            //属性的值
            Object fieldValue = null;
            try {
                fieldValue = field.get(object);
            } catch (IllegalAccessException e) {
                throw new RuntimeException("导入参数检查失败");
            }

            boolean isExcelProperty = field.isAnnotationPresent(ExcelProperty.class);
            if (!isExcelProperty) {
                continue;
            }
            int columnNumber = field.getAnnotation(ExcelProperty.class).index();

            //是否包含必填校验注解
            boolean isExcelValid = field.isAnnotationPresent(ExcelNotNullValid.class);
            if (isExcelValid && Objects.isNull(fieldValue)) {
                throw new RuntimeException("NULL" + field.getAnnotation(ExcelNotNullValid.class).message());
            }

            boolean isAttrCodeValid = field.isAnnotationPresent(AttrCodeValid.class);
            if (isAttrCodeValid) {
                Integer value = (Integer) fieldValue;
                if (CardBattleCardAttrEnum.findByCode(value) == null) {
                    throw new ExcelDataConvertException(rowNumber, columnNumber, new ReadCellData<>(CellDataTypeEnum.STRING, String.valueOf(value)), null,
                            "属性填写不正确，范围为:" + Arrays.stream(CardBattleCardAttrEnum.values())
                                    .map(CardBattleCardAttrEnum::getCode)
                                    .collect(Collectors.toList()));
                }
            }

            boolean isRareCodeValid = field.isAnnotationPresent(RareCodeValid.class);
            if (isRareCodeValid) {
                Integer value = (Integer) fieldValue;
                if (CardBattleCardRarityEnum.parseByCode(value) == null) {
                    throw new ExcelDataConvertException(rowNumber, columnNumber, new ReadCellData<>(CellDataTypeEnum.STRING, String.valueOf(value)), null,
                            "稀有度填写不正确，范围为:" + Arrays.stream(CardBattleCardRarityEnum.values())
                                    .map(CardBattleCardRarityEnum::getCode)
                                    .collect(Collectors.toList()));
                }
            }
        }
    }

}
