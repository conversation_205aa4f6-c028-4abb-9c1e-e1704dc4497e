package com.kuaikan.role.game.admin.biz;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;

import com.kuaikan.admin.base.bean.Operation;
import com.kuaikan.admin.base.common.PageView;
import com.kuaikan.admin.base.utils.OperateLogUtils;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.tools.serialize.JsonUtils;
import com.kuaikan.role.game.admin.common.OperationConstants;
import com.kuaikan.role.game.admin.model.param.BuildingAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.view.BuildingView;
import com.kuaikan.role.game.admin.repository.BuildingAreaRepository;
import com.kuaikan.role.game.admin.repository.BuildingRepository;
import com.kuaikan.role.game.admin.repository.ScheduleRepository;
import com.kuaikan.role.game.admin.repository.UserRoleOngoingScheduleRepository;
import com.kuaikan.role.game.api.bean.Building;
import com.kuaikan.role.game.api.bean.BuildingArea;
import com.kuaikan.role.game.api.bean.Schedule;
import com.kuaikan.role.game.common.bean.BuildingMap;
import com.kuaikan.role.game.common.dao.BuildingMapMapper;

/**
 * <AUTHOR>
 * @date 2024/6/13
 */
@Service
public class BuildingBiz {

    @Resource
    private BuildingRepository buildingRepository;

    @Resource
    private BuildingAreaRepository buildingAreaRepository;

    @Resource
    private ScheduleRepository scheduleRepository;

    @Resource
    private UserRoleOngoingScheduleRepository userRoleOngoingScheduleRepository;

    @Resource
    private BuildingMapMapper buildingMapMapper;

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> addOrUpdate(BuildingAddOrUpdateParam addOrUpdateParam) {
        int buildingId = addOrUpdateParam.getId();
        Building oldBuilding = null;
        if (buildingId > 0) {
            Building building = buildingRepository.queryById(buildingId);
            if (building == null) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "建筑不存在");
            }
            oldBuilding = building;
            building.setDimensionType(addOrUpdateParam.getDimensionType()).setName(addOrUpdateParam.getName()).setType(addOrUpdateParam.getType());
            buildingRepository.update(building);
            List<BuildingArea> buildingAreasParams = Optional.ofNullable(addOrUpdateParam.getAreaParams())
                    .orElse(Lists.newArrayList())
                    .stream()
                    .map(item -> new BuildingArea().setId(item.getId()).setBuildingId(building.getId()).setName(item.getName()))
                    .collect(Collectors.toList());
            List<BuildingArea> updateList = Lists.newArrayList();
            List<BuildingArea> insertList = Lists.newArrayList();
            buildingAreasParams.forEach(item -> {
                if (item.getId() > 0) {
                    updateList.add(item);
                } else {
                    insertList.add(item);
                }
            });

            buildingAreaRepository.insertBatch(insertList);
            for (BuildingArea buildingArea : updateList) {
                buildingAreaRepository.update(buildingArea);
            }

        } else {
            Building building = new Building().setName(addOrUpdateParam.getName())
                    .setType(addOrUpdateParam.getType())
                    .setDimensionType(addOrUpdateParam.getDimensionType());
            buildingRepository.insert(building);
            buildingId = building.getId();
            List<BuildingArea> buildingAreas = Optional.ofNullable(addOrUpdateParam.getAreaParams())
                    .orElse(Lists.newArrayList())
                    .stream()
                    .map(item -> new BuildingArea().setBuildingId(building.getId()).setName(item.getName()))
                    .collect(Collectors.toList());
            buildingAreaRepository.insertBatch(buildingAreas);
        }
        Operation operation = Operation.of(OperationConstants.OperateSubType.BUILDING_ADD_OR_UPDATE)
                .add("id", buildingId)
                .add("oldData", JsonUtils.toJson(oldBuilding))
                .add("newData", JsonUtils.toJson(addOrUpdateParam));
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    public BizResult<PageView<BuildingView>> list(int pageNum, int pageSize) {
        int total = buildingRepository.count();
        List<Building> buildings = buildingRepository.queryByPage((pageNum - 1) * pageSize, pageSize);
        final List<BuildingView> buildingViews = getBuildingViews(buildings);
        return BizResult.success(PageView.form(total, buildingViews));
    }

    public BizResult<List<BuildingView>> all() {
        final List<Building> buildings = buildingRepository.queryAll();
        final List<BuildingView> buildingViews = getBuildingViews(buildings);
        return BizResult.success(buildingViews);
    }

    @NotNull
    private List<BuildingView> getBuildingViews(List<Building> buildings) {
        Map<Integer, List<BuildingArea>> buildingAreaMap = buildingAreaRepository.queryByBuildingIds(
                buildings.stream().map(Building::getId).collect(Collectors.toList())).stream().collect(Collectors.groupingBy(BuildingArea::getBuildingId));
        List<BuildingView> buildingViews = buildings.stream().map(item -> {
            BuildingView buildingView = new BuildingView().setId(item.getId())
                    .setName(item.getName())
                    .setType(item.getType())
                    .setDimensionType(item.getDimensionType())
                    .setOrderNum(item.getOrderNum());
            List<BuildingArea> buildingAreas = buildingAreaMap.get(item.getId());
            if (buildingAreas != null) {
                List<BuildingView.AreaView> areaViews = buildingAreas.stream()
                        .map(buildingArea -> new BuildingView.AreaView().setId(buildingArea.getId()).setName(buildingArea.getName()))
                        .collect(Collectors.toList());
                buildingView.setAreaList(areaViews);
            }
            return buildingView;
        }).collect(Collectors.toList());
        return buildingViews;
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> delete(int id) {
        Building building = buildingRepository.queryById(id);
        if (building == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "建筑不存在");
        }
        List<Integer> areaIds = buildingAreaRepository.queryByBuildingId(id).stream().map(BuildingArea::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(areaIds)) {
            List<Integer> scheduleIdList = scheduleRepository.queryByAreaIds(areaIds).stream().map(Schedule::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(scheduleIdList)) {
                int onGoingCount = userRoleOngoingScheduleRepository.countByScheduleIds(scheduleIdList);
                if (onGoingCount > 0) {
                    return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "建筑区域下有进行中的日程，不能删除");
                }
            }
        }

        List<BuildingMap> buildingMaps = buildingMapMapper.selectByBuildingId(id);
        if (CollectionUtils.isNotEmpty(buildingMaps)) {
            String errorMessage = String.format("建筑已被关联内景地图%s，不能删除",
                    StringUtils.join(buildingMaps.stream().map(BuildingMap::getName).collect(Collectors.toList()), ","));
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), errorMessage);
        }
        buildingRepository.delete(id);
        buildingAreaRepository.deleteByBuildingId(id);
        scheduleRepository.deleteByBuildingId(id);
        Operation operation = Operation.of(OperationConstants.OperateSubType.BUILDING_DELETE).add("id", id);
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> deleteArea(int areaId) {
        BuildingArea buildingArea = buildingAreaRepository.queryByAreaId(areaId);
        if (buildingArea == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "建筑区域不存在");
        }
        int buildingId = buildingArea.getBuildingId();
        Schedule schedule = scheduleRepository.queryByBuildingIdAndAreaId(buildingId, areaId);
        if (schedule != null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "该区域下有日程，不能删除");
        }
        buildingAreaRepository.deleteByAreaId(areaId);
        Operation operation = Operation.of(OperationConstants.OperateSubType.BUILDING_AREA_DELETE).add("id", buildingId).add("areaId", areaId);
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> updateSort(int buildingId, int order) {
        List<Building> buildings = buildingRepository.queryAll();
        Building building = buildings.stream().filter(item -> item.getId() == buildingId).findAny().orElse(null);
        if (building == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "建筑不存在");
        }
        if (order == 0) {
            building.setOrderNum(order);
            buildingRepository.updateByPrimaryKeySelective(building);
            return BizResult.success();
        }
        int oldOrder = building.getOrderNum();
        if (order > oldOrder) {
            buildingRepository.updateOrderNumGreaterThanOrEqualToNewOrderNum(order);
            building.setOrderNum(order);
            buildingRepository.updateByPrimaryKeySelective(building);
        } else if (order < oldOrder) {
            Building sameOrderRole = buildings.stream().filter(item -> item.getOrderNum() == order).findAny().orElse(null);
            if (sameOrderRole != null) {
                sameOrderRole.setOrderNum(oldOrder);
                buildingRepository.updateByPrimaryKeySelective(sameOrderRole);
            }
            building.setOrderNum(order);
            buildingRepository.updateByPrimaryKeySelective(building);
        }
        Operation operation = Operation.of(OperationConstants.OperateSubType.BUILDING_ORDER_UPDATE)
                .add("id", buildingId)
                .add("oldOrder", oldOrder)
                .add("newOrder", order);
        OperateLogUtils.asyncRecord(operation);
        buildings.forEach(item -> buildingRepository.deleteCache(item));
        return BizResult.success();
    }

}
