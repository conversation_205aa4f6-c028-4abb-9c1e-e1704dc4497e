package com.kuaikan.role.game.admin.repository;

import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.Index;
import org.springframework.stereotype.Repository;

import com.kuaikan.idgenerator.sdk.BizIdGenerator;
import com.kuaikan.role.game.api.bean.UserCouponTaskIndex;

/**
 * <AUTHOR>
 * @date 2024/10/30 20:55
 * @description： 后台折扣券消息索引
 */
@Repository
public class UserCouponTaskRepository {

    @Resource
    private MongoTemplate mongoTemplatePrimary;

    private static final String USER_ID = "userId";
    private static final String INDEX_NAME = "uid_unique_index";

    public void batchToSave(List<Integer> receiveUserIds, String collectionName, Integer couponId) {
        if (CollectionUtils.isEmpty(receiveUserIds) || StringUtils.isBlank(collectionName) || couponId == null) {
            return;
        }
        long now = System.currentTimeMillis();
        List<UserCouponTaskIndex> documentsToInsert = receiveUserIds.stream()
                .map(userId -> new UserCouponTaskIndex().setUserId(userId)
                        .setCouponId(couponId)
                        .setCreatedAt(now)
                        .setUpdatedAt(now)
                        .setBid(BizIdGenerator.getId()))
                .collect(Collectors.toList());
        mongoTemplatePrimary.insert(documentsToInsert, collectionName);
    }

    public void createIndex(String collectionName) {
        if (StringUtils.isBlank(collectionName)) {
            return;
        }
        mongoTemplatePrimary.indexOps(collectionName).ensureIndex(new Index().on(USER_ID, Sort.Direction.ASC).unique().background().named(INDEX_NAME));
    }
}
