package com.kuaikan.role.game.admin.common;

import com.kuaikan.common.ResponseCodeMsg;

/**
 * <AUTHOR>
 * @version 2024-02-29
 */
public class RoleGameResponse extends ResponseCodeMsg {

    protected RoleGameResponse(int code, String message) {
        super(code, message);
    }

    public static final RoleGameResponse PARAM_ILLEGAL = new RoleGameResponse(1001, "参数错误");

    public static final RoleGameResponse ROLE_NOT_EXIST = new RoleGameResponse(2000, "角色不存在");
    public static final RoleGameResponse SCENE_NOT_EXIST = new RoleGameResponse(2001, "场景不存在");

    public static final RoleGameResponse ROLE_SCENE_RELATION_NOT_EXIST = new RoleGameResponse(2002, "角色场景关联不存在");
    public static final RoleGameResponse ROLE_COSTUME_RELATION_NOT_EXIST = new RoleGameResponse(2003, "角色装扮关联不存在");
    public static final RoleGameResponse MATERIAL_ALL_PUBLISH = new RoleGameResponse(2004, "不存在未发布物料");
    public static final RoleGameResponse ROLE_STORY_RELATION_NOT_EXIST = new RoleGameResponse(2005, "角色剧情关联不存在");
    public static final RoleGameResponse AVG_STORY_FILE_EXIT = new RoleGameResponse(2006, "avg配置文件义已存在");

    public static final RoleGameResponse STUFF_NOT_EXIST = new RoleGameResponse(2005, "材料不存在");

    public static final RoleGameResponse COSTUME_PART_NOT_EXIST = new RoleGameResponse(2006, "装扮单品不存在");

    public static final RoleGameResponse COSTUME_PART_NOT_FOR_ONE_COSTUME = new RoleGameResponse(2007, "装扮单品不属于同一装扮");
    public static final RoleGameResponse COSTUME_PART_DECOMPOSE_RATE_ILLEGAL = new RoleGameResponse(2007, "单品分解比例不符合");
    public static final RoleGameResponse COSTUME_NOT_EXIST = new RoleGameResponse(2008, "装扮不存在");
    public static final RoleGameResponse COSTUME_PART_CAN_NOT_INSERT_OR_DELETE = new RoleGameResponse(2009, "装扮单品不能新增或删除");
    public static final RoleGameResponse COSTUME_PART_RELATION_NOT_EXIST = new RoleGameResponse(2010, "装扮单品关联不存在");
    public static final RoleGameResponse AVG_CHAPTER_EXIT = new RoleGameResponse(2011, "avg文件重名");
    public static final RoleGameResponse ACTION_STORY_CONFIG_EXCEL_FAIL = new RoleGameResponse(2012, "动作剧情配置excel异常");
    public static final RoleGameResponse ROLE_EXCEL_CONFIG_FAIL = new RoleGameResponse(2013, "角色Excel内容配置错误");
    public static final RoleGameResponse ITEM_CONFIG_EXIST = new RoleGameResponse(2014, "该道具已经存在");
    public static final RoleGameResponse ITEM_CONFIG_NOT_EXIST = new RoleGameResponse(2015, "该道具不存在");

    public static final RoleGameResponse COMMON_VIDEO_TASK_NOT_EXIT = new RoleGameResponse(2016, "视频任务不存在");
    public static final RoleGameResponse COMMON_VIDEO_TASK_NOT_SUCCESS = new RoleGameResponse(2017, "视频任务未完成");
    public static final RoleGameResponse COMMON_VIDEO_TASK_BUCKET_ERROR = new RoleGameResponse(2018, "不支持的bucket类型");
}
