package com.kuaikan.role.game.admin.dao;

import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

import com.kuaikan.role.game.common.bean.RoleGroupInteractiveActionRelation;

/**
 * 互动动作mapper
 * <AUTHOR>
 * @date 2024/9/12 20:33
 */
public interface RoleGroupInteractiveActionRelationMapper {

    int insert(RoleGroupInteractiveActionRelation record);

    List<RoleGroupInteractiveActionRelation> queryByRoleGroupIds(@Param("roleGroupIds") Collection<Integer> roleGroupIds);

    List<RoleGroupInteractiveActionRelation> queryByRoleGroupId(@Param("roleGroupId") Integer roleGroupId);

    int updateOrderNumByRoleGroupIdAndActionId(@Param("orderNum") Integer orderNum, @Param("roleGroupId") Integer roleGroupId,
                                               @Param("actionId") Integer actionId);

    int updateOrderNumGreaterThanOrEqualToNewOrderNum(@Param("orderNum") Integer orderNum, @Param("roleGroupId") Integer roleGroupId);

    List<RoleGroupInteractiveActionRelation> queryPageByGroupId(@Param("roleGroupId") int roleGroupId, @Param("offset") int offset, @Param("limit") int limit);

    Integer countByRoleGroupId(@Param("roleGroupId") Integer roleGroupId);

    List<RoleGroupInteractiveActionRelation> queryByActionIds(@Param("actionIds") List<Integer> actionIds);
}
