package com.kuaikan.role.game.admin.model.param;

import java.io.Serializable;
import java.util.Objects;
import java.util.stream.Stream;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.bean.RoleSpiritStoneConfig;
import com.kuaikan.role.game.api.bo.ImageInfo;

/**
 * <AUTHOR>
 * @date 2025/3/5 19:06
 */

@Data
@Accessors(chain = true)
public class SpiritStoneUpdateParam implements Serializable {

    private static final long serialVersionUID = -3942641586355323882L;

    private Integer id;
    private ImageInfo image;
    private Integer amount;
    private Integer worth;
    /** 卡片后台活动id */
    private Integer activityId;

    public boolean isParamIllegal() {
        return Stream.of(id, image, amount, worth).allMatch(Objects::nonNull);
    }

    public RoleSpiritStoneConfig.Config toConfig() {
        RoleSpiritStoneConfig.Config config = new RoleSpiritStoneConfig.Config();
        config.setImage(image);
        config.setAmount(amount);
        config.setWorth(worth);
        config.setActivityId(activityId);
        return config;
    }
}
