package com.kuaikan.role.game.admin.remote;

import com.kuaikan.role.game.admin.model.dto.DigitalAssetBaseRespDTO;
import feign.HeaderMap;
import feign.Headers;
import feign.Param;
import feign.RequestLine;

import java.util.Map;

/**
 * 数字资产api
 *
 * <AUTHOR>
 * @since 2025/1/10
 */
public interface DigitalAssetApi {
    /**
     * 数字资产接口
     *
     * @param methodCode  接口方法
     * @param headerMap   请求头
     * @param body json body
     * @return
     */
    @Headers({"Content-Type: application/json;charset=utf-8"})
    @RequestLine("POST /v1/zichan/partner/{code}")
    DigitalAssetBaseRespDTO doExchange(@Param("code") String methodCode, @HeaderMap Map<String, String> headerMap, String body);
}
