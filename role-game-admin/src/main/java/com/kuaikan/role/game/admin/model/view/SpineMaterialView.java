package com.kuaikan.role.game.admin.model.view;

import java.util.List;
import java.util.Set;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.comic.utils.CdnUtil;
import com.kuaikan.role.game.api.bean.SpineMaterial;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Data
@Accessors(chain = true)
public class SpineMaterialView {

    private Set<String> animations;

    private String jsonFileKey;

    private String atlasFileKey;

    @Deprecated
    private String pngFileKey;

    private List<String> pngFileKeys;

    private String originZipFileKey;

    private String name;

    public static SpineMaterialView valueOf(SpineMaterial spineMaterial) {
        if (spineMaterial == null) {
            return null;
        }
        return new SpineMaterialView().setAnimations(spineMaterial.getAnimations())
                .setJsonFileKey(spineMaterial.getJsonFileKey())
                .setAtlasFileKey(spineMaterial.getAtlasFileKey())
                .setPngFileKey(spineMaterial.getPngFileKey())
                .setPngFileKeys(spineMaterial.getPngFileKeys())
                .setOriginZipFileKey(CdnUtil.getDefaultDomainWithBackSlash() + spineMaterial.getOriginZipFileKey())
                .setName(spineMaterial.getName());
    }

}
