package com.kuaikan.role.game.admin.template.numerical;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import com.kuaikan.role.game.admin.model.excel.ScheduleExcelData;
import com.kuaikan.role.game.admin.repository.ScheduleRepository;
import com.kuaikan.role.game.admin.utils.NumericalUtil;
import com.kuaikan.role.game.admin.utils.excel.listener.AbstractUploadListener;
import com.kuaikan.role.game.admin.utils.excel.listener.ScheduleNumberExcelListener;
import com.kuaikan.role.game.api.bean.Schedule;
import com.kuaikan.role.game.api.bo.FailMessage;
import com.kuaikan.role.game.api.enums.NumericalConfigType;

/**
 * ScheduleNumericalTemplate
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Service
public class ScheduleNumericalTemplate extends NumericalTemplate<ScheduleExcelData> {

    private static final int ROW_START = 2;

    @Resource
    private ScheduleRepository scheduleRepository;

    @Override
    protected Class<ScheduleExcelData> getExcelDataClass() {
        return ScheduleExcelData.class;
    }

    @Override
    protected AbstractUploadListener createExcelListener(List<ScheduleExcelData> excelDataList) {
        return new ScheduleNumberExcelListener(excelDataList);
    }

    @Override
    protected int getHeadRowNumber() {
        return ROW_START;
    }

    @Override
    protected List<FailMessage> validate(List<ScheduleExcelData> dataList) {
        List<Schedule> scheduleList = scheduleRepository.queryAll();
        Set<Integer> exitScheduleId = scheduleList.stream().map(Schedule::getId).collect(Collectors.toSet());
        List<FailMessage> failMessages = new ArrayList<>();
        for (int i = 0; i < dataList.size(); i++) {
            ScheduleExcelData scheduleExcelData = dataList.get(i);
            String id = scheduleExcelData.getId();
            int row = i + ROW_START + 1;
            if (!NumericalUtil.isInteger(id)) {
                failMessages.add(newMessage(row, "A", "日程ID不是整数"));
            }
            String consumeEnergy = scheduleExcelData.getConsumeEnergyPerPeriod();
            if (!NumericalUtil.isFloat(consumeEnergy)) {
                failMessages.add(newMessage(row, "D", "消耗行动力数不是小数"));
            }
            String consumeMinute = scheduleExcelData.getConsumeMinute();
            if (!NumericalUtil.isInteger(consumeMinute) || Integer.parseInt(consumeMinute) <= 0) {
                failMessages.add(newMessage(row, "E", "消耗时长（分钟）不是正整数"));
            }
            String silverCoinChange = scheduleExcelData.getSilverCoinChangePerPeriod();
            if (!NumericalUtil.isFloat(silverCoinChange)) {
                failMessages.add(newMessage(row, "F", "银币变化值不是小数"));
            }
            String dimensionExp = scheduleExcelData.getDimensionExpPerPeriod();
            if (!NumericalUtil.isFloat(dimensionExp)) {
                failMessages.add(newMessage(row, "G", "五维属性变化值不是小数"));
            }
            if (!exitScheduleId.contains(Integer.parseInt(scheduleExcelData.getId()))) {
                failMessages.add(newMessage(row, "A", "日程id不存在"));
            }
        }
        Set<Integer> importIds = dataList.stream().map(item -> Integer.valueOf(item.getId())).collect(Collectors.toSet());
        exitScheduleId.removeAll(importIds);
        if (CollectionUtils.isNotEmpty(exitScheduleId)) {
            String errorMessage = StringUtils.join(exitScheduleId, ",") + "日程id数据未导入";
            failMessages.add(newMessage(errorMessage));
        }
        failMessages.forEach(item -> item.setNumericalConfigType(getNumericalConfigType().getDesc()));
        return failMessages;
    }

    @Override
    protected void save(List<ScheduleExcelData> dataList) {
        Map<Integer, ScheduleExcelData> scheduleId2DataMap = dataList.stream()
                .collect(Collectors.toMap(key -> Integer.parseInt(key.getId()), scheduleExcelData -> scheduleExcelData));
        List<Schedule> scheduleList = scheduleRepository.queryAll();
        for (Schedule schedule : scheduleList) {
            ScheduleExcelData scheduleExcelData = scheduleId2DataMap.get(schedule.getId());
            Schedule.Config config = schedule.getConfig();
            int consumeMinute = NumberUtils.toInt(scheduleExcelData.getConsumeMinute());
            config.setConsumeMinute(consumeMinute);
            float consumeEnergyPerPeriod = NumberUtils.toFloat(scheduleExcelData.getConsumeEnergyPerPeriod());
            config.setConsumeEnergyPerPeriod(consumeEnergyPerPeriod);
            float silverCoinChangePerPeriod = NumberUtils.toFloat(scheduleExcelData.getSilverCoinChangePerPeriod());
            config.setSilverCoinChangePerPeriod(silverCoinChangePerPeriod);
            float dimensionExpPerPeriod = NumberUtils.toFloat(scheduleExcelData.getDimensionExpPerPeriod());
            config.setDimensionExpPerPeriod(dimensionExpPerPeriod);
            int totalPeriod = (int) TimeUnit.MINUTES.toSeconds(consumeMinute) / 5;
            config.setConsumeEnergy(Math.round(consumeEnergyPerPeriod * totalPeriod));
            config.setSilverCoinChange(Math.round(silverCoinChangePerPeriod * totalPeriod));
            config.setDimensionExp(Math.round(dimensionExpPerPeriod * totalPeriod));
            scheduleRepository.update(schedule);
        }
    }

    @Override
    protected NumericalConfigType getNumericalConfigType() {
        return NumericalConfigType.SCHEDULE;
    }
}
