package com.kuaikan.role.game.admin.template.reader;

import java.util.List;

import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import com.alibaba.excel.read.listener.ReadListener;
import com.google.common.collect.Lists;

import com.kuaikan.role.game.admin.model.excel.RoleReactionBubbleExcelData;
import com.kuaikan.role.game.admin.utils.excel.listener.RoleReactionBubbleExcelListener;
import com.kuaikan.role.game.api.bean.RoleBubbleConfig;
import com.kuaikan.role.game.api.bo.FailMessage;
import com.kuaikan.role.game.api.enums.ExcelReaderType;
import com.kuaikan.role.game.common.enums.RoleBubbleContentType;
import com.kuaikan.role.game.common.enums.RoleBubbleStyle;

/**
 * <AUTHOR>
 * @version 2024-11-14
 */
@Component
public class RoleReactionBubbleExcelReaderTemplate extends ExcelReader<RoleReactionBubbleExcelData, RoleBubbleConfig.Bubble> {

    @Override
    protected Class<RoleReactionBubbleExcelData> getExcelDataClass() {
        return RoleReactionBubbleExcelData.class;
    }

    @Override
    protected ReadListener createExcelListener(List<RoleReactionBubbleExcelData> excelDataList) {
        return new RoleReactionBubbleExcelListener(excelDataList);
    }

    @Override
    protected List<FailMessage> validate(List<RoleReactionBubbleExcelData> dataList) {
        List<FailMessage> failMessages = Lists.newArrayList();
        for (int i = 0; i < dataList.size(); i++) {
            int row = i + getHeadRowNumber() + 1;
            RoleReactionBubbleExcelData excelData = dataList.get(i);
            if (RoleBubbleStyle.getByCode(NumberUtils.toInt(excelData.getBubbleStyle())) == null) {
                failMessages.add(newMessage(row, "D", "气泡样式不对"));
            }
            if (RoleBubbleContentType.getByCode(NumberUtils.toInt(excelData.getBubbleContentType())) == null) {
                failMessages.add(newMessage(row, "E", "气泡内容类型不对"));
            }
        }
        return failMessages;
    }

    @Override
    protected List<RoleBubbleConfig.Bubble> read(List<RoleReactionBubbleExcelData> dataList) {
        List<RoleBubbleConfig.Bubble> bubbleList = Lists.newArrayList();
        for (RoleReactionBubbleExcelData excelData : dataList) {
            RoleBubbleConfig.Bubble bubble = new RoleBubbleConfig.Bubble();
            bubble.setAnimation(excelData.getAnimation());
            bubble.setStyle(NumberUtils.toInt(excelData.getBubbleStyle()));
            bubble.setContentType(NumberUtils.toInt(excelData.getBubbleContentType()));
            bubble.setContent(excelData.getBubbleContent());
            bubbleList.add(bubble);
        }
        return bubbleList;
    }

    @Override
    protected ExcelReaderType getExcelReaderType() {
        return ExcelReaderType.REACTION;
    }

    protected int getHeadRowNumber() {
        return 3;
    }
}
