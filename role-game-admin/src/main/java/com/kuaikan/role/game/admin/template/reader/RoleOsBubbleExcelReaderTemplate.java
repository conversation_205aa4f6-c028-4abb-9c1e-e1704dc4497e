package com.kuaikan.role.game.admin.template.reader;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import com.alibaba.excel.read.listener.ReadListener;
import com.google.common.collect.Lists;

import com.kuaikan.role.game.admin.model.excel.RoleOsBubbleExcelData;
import com.kuaikan.role.game.admin.utils.NumericalUtil;
import com.kuaikan.role.game.admin.utils.excel.listener.RoleOsBubbleExcelListener;
import com.kuaikan.role.game.api.bean.RoleBubbleConfig;
import com.kuaikan.role.game.api.bo.FailMessage;
import com.kuaikan.role.game.api.enums.ExcelReaderType;
import com.kuaikan.role.game.common.enums.RoleBubbleContentType;
import com.kuaikan.role.game.common.enums.RoleBubbleStyle;

/**
 * RoleOsExcelReaderTemplate
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
@Slf4j
@Component
public class RoleOsBubbleExcelReaderTemplate extends ExcelReader<RoleOsBubbleExcelData, RoleBubbleConfig.Bubble> {

    @Override
    protected Class<RoleOsBubbleExcelData> getExcelDataClass() {
        return RoleOsBubbleExcelData.class;
    }

    @Override
    protected ReadListener createExcelListener(List<RoleOsBubbleExcelData> excelDataList) {
        return new RoleOsBubbleExcelListener(excelDataList);
    }

    @Override
    protected List<FailMessage> validate(List<RoleOsBubbleExcelData> dataList) {
        List<FailMessage> failMessages = Lists.newArrayList();
        for (int i = 0; i < dataList.size(); i++) {
            int row = i + getHeadRowNumber() + 1;
            RoleOsBubbleExcelData excelData = dataList.get(i);
            failMessages.addAll(checkColumnNumber(excelData, row));
            if (RoleBubbleStyle.getByCode(NumberUtils.toInt(excelData.getBubbleStyle())) == null) {
                failMessages.add(newMessage(row, "F", "气泡样式不对"));
            }
            if (RoleBubbleContentType.getByCode(NumberUtils.toInt(excelData.getBubbleContentType())) == null) {
                failMessages.add(newMessage(row, "G", "气泡内容类型不对"));
            }
        }
        return failMessages;
    }

    private List<FailMessage> checkColumnNumber(RoleOsBubbleExcelData data, int row) {
        List<FailMessage> failMessages = Lists.newArrayList();
        if (!NumericalUtil.isInteger(data.getElementId())) {
            failMessages.add(newMessage(row, "C", "元素id不是整数"));
        }
        return failMessages;
    }

    @Override
    protected List<RoleBubbleConfig.Bubble> read(List<RoleOsBubbleExcelData> dataList) {
        List<RoleBubbleConfig.Bubble> bubbleList = Lists.newArrayList();
        for (RoleOsBubbleExcelData excelData : dataList) {
            RoleBubbleConfig.Bubble bubble = new RoleBubbleConfig.Bubble();
            bubble.setElementId(NumberUtils.toInt(excelData.getElementId()));
            bubble.setAnimation(excelData.getAnimation());
            bubble.setStyle(NumberUtils.toInt(excelData.getBubbleStyle()));
            bubble.setContentType(NumberUtils.toInt(excelData.getBubbleContentType()));
            bubble.setContent(excelData.getBubbleContent());
            bubbleList.add(bubble);
        }
        return bubbleList;
    }

    @Override
    protected ExcelReaderType getExcelReaderType() {
        return ExcelReaderType.ROLE_OS_BUBBLE;
    }

    protected int getHeadRowNumber() {
        return 3;
    }
}
