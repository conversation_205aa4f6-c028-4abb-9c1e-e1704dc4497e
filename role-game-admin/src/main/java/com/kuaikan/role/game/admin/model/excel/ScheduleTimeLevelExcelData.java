package com.kuaikan.role.game.admin.model.excel;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.alibaba.excel.annotation.ExcelProperty;

import com.kuaikan.role.game.common.bean.ScheduleTimeLevelNumerical;

/**
 * <AUTHOR>
 * @date 2024/9/9 09:39
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScheduleTimeLevelExcelData implements Serializable {

    private static final long serialVersionUID = 7457621444376076304L;

    @ExcelProperty(value = "ID", index = 0)
    private String id;

    @ExcelProperty(value = "日程档位分钟", index = 1)
    private String level;

    public static ScheduleTimeLevelNumerical valueOf(ScheduleTimeLevelExcelData excelData) {
        ScheduleTimeLevelNumerical scheduleTimeLevelNumerical = new ScheduleTimeLevelNumerical();
        scheduleTimeLevelNumerical.setId(Integer.parseInt(excelData.getId()));
        scheduleTimeLevelNumerical.setLevel(Integer.valueOf(excelData.getLevel()));
        return scheduleTimeLevelNumerical;
    }

}
