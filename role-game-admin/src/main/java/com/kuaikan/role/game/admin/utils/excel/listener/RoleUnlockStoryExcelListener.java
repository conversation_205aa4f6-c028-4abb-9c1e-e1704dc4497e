package com.kuaikan.role.game.admin.utils.excel.listener;

import java.util.List;

import com.alibaba.excel.context.AnalysisContext;

import com.kuaikan.role.game.admin.model.excel.RoleUnlockStoryExcelData;

/**
 * <AUTHOR>
 * @date 2024/9/24 16:42
 */
public class RoleUnlockStoryExcelListener extends AbstractUploadListener<RoleUnlockStoryExcelData> {

    private final List<RoleUnlockStoryExcelData> excelDataList;

    public RoleUnlockStoryExcelListener(List<RoleUnlockStoryExcelData> excelDataList) {
        this.excelDataList = excelDataList;
    }

    @Override
    public void invoke(RoleUnlockStoryExcelData dto, AnalysisContext analysisContext) {
        super.invoke(dto, analysisContext);
        excelDataList.add(dto);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
    }
}
