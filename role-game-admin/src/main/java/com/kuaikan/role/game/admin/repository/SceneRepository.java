package com.kuaikan.role.game.admin.repository;

import java.util.Collection;
import java.util.List;
import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import com.kuaikan.role.game.admin.dao.SceneMapper;
import com.kuaikan.role.game.api.bean.Scene;

/**
 *
 * <AUTHOR>
 * @date 2024/2/28
 */
@Repository
public class SceneRepository {

    @Resource
    private SceneMapper sceneMapper;

    public int insert(Scene record) {
        return sceneMapper.insert(record);
    }

    public Scene selectByPrimaryKey(Integer id) {
        return sceneMapper.selectByPrimaryKey(id);
    }

    public Scene selectByName(String name) {
        return sceneMapper.selectByName(name);
    }

    public List<Scene> queryByPage(int offset, int limit) {
        return sceneMapper.queryByPage(offset, limit);
    }

    public List<Scene> queryBySceneIds(Collection<Integer> sceneIds) {
        return sceneMapper.queryBySceneIds(sceneIds);
    }

    public int count() {
        return sceneMapper.count();
    }

    public List<Scene> queryAll() {
        return sceneMapper.queryAll();
    }

    public int updateByPrimaryKeySelective(Scene record) {
        return sceneMapper.updateByPrimaryKeySelective(record);
    }

    public int deleteByPrimaryKey(Integer id) {
        return sceneMapper.deleteByPrimaryKey(id);
    }
}
