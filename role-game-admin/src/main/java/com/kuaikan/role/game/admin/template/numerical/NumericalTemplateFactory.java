package com.kuaikan.role.game.admin.template.numerical;

import java.util.Map;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import com.google.common.collect.ImmutableMap;

import com.kuaikan.role.game.api.enums.NumericalConfigType;

/**
 * NumericalTemplateFactory
 *
 * <AUTHOR>
 * @since 2024-07-02
 */
@Slf4j
@Service
public class NumericalTemplateFactory implements ApplicationContextAware {

    private static Map<NumericalConfigType, NumericalTemplate> map;

    public NumericalTemplate getInstance(NumericalConfigType numericalType) {
        return map.get(numericalType);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        ImmutableMap.Builder<NumericalConfigType, NumericalTemplate> builder = ImmutableMap.builder();
        for (NumericalTemplate numericalTemplate : applicationContext.getBeansOfType(NumericalTemplate.class).values()) {
            builder.put(numericalTemplate.getNumericalConfigType(), numericalTemplate);
        }
        map = builder.build();
    }
}
