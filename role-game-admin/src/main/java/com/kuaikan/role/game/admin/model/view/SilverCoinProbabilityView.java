package com.kuaikan.role.game.admin.model.view;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.bean.SilverCoinConfig;

/**
 * <AUTHOR>
 * @date 2024/4/19
 */
@Data
@Accessors(chain = true)
public class SilverCoinProbabilityView implements Serializable {

    private static final long serialVersionUID = -563479081722522921L;
    /**
     * 1星未获得剧情掉落银币数量
     */
    private int oneStarNotObtainedDroppedQuantity;
    /**
     * 2星未获得剧情掉落银币数量
     */
    private int twoStarNotObtainedDroppedQuantity;
    /**
     * 3星未获得剧情掉落银币数量
     */
    private int threeStarNotObtainedDroppedQuantity;
    /**
     * 已经获得剧情掉落银币数量
     */
    private int obtainedDroppedQuantity;
    /**
     * 活动id
     */
    private String activityId;
    /**
     * 银币用途描述
     */
    private String usageDesc;

    public static SilverCoinProbabilityView valueOf(SilverCoinConfig silverCoinConfig) {
        if (silverCoinConfig == null) {
            return null;
        }
        return new SilverCoinProbabilityView().setOneStarNotObtainedDroppedQuantity(silverCoinConfig.getOneStarNotObtainedDroppedQuantity())
                .setTwoStarNotObtainedDroppedQuantity(silverCoinConfig.getTwoStarNotObtainedDroppedQuantity())
                .setThreeStarNotObtainedDroppedQuantity(silverCoinConfig.getThreeStarNotObtainedDroppedQuantity())
                .setObtainedDroppedQuantity(silverCoinConfig.getObtainedDroppedQuantity())
                .setActivityId(silverCoinConfig.getActivityId())
                .setUsageDesc(silverCoinConfig.getUsageDesc());
    }
}
