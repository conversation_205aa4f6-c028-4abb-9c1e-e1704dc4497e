package com.kuaikan.role.game.admin.utils.excel.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.kuaikan.role.game.admin.model.excel.StoryMapExcelData;

import java.util.List;

public class MapStoryCityListener extends AbstractUploadListener<StoryMapExcelData>{

    private List<StoryMapExcelData> excelDataList;

    public MapStoryCityListener(List<StoryMapExcelData> excelDataList) {
        this.excelDataList = excelDataList;
    }

    @Override
    public void invoke(StoryMapExcelData dto, AnalysisContext analysisContext) {
        super.invoke(dto, analysisContext);
        excelDataList.add(dto);
    }
}
