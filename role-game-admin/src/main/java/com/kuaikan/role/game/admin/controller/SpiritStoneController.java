package com.kuaikan.role.game.admin.controller;

import java.util.Map;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.kuaikan.role.game.admin.biz.SpiritStoneBiz;
import com.kuaikan.role.game.admin.model.param.SpiritStoneUpdateParam;
import com.kuaikan.role.game.admin.utils.ResponseUtils;

/**
 * <AUTHOR>
 * @date 2025/3/5 17:12
 */

@Slf4j
@RestController
@RequestMapping("/v2/admin/role/game/spiritStone")
public class SpiritStoneController {

    @Resource
    private SpiritStoneBiz spiritStoneBiz;

    @GetMapping("/list")
    public Map<String, Object> list(@RequestParam(name = "pageNum", defaultValue = "1") Integer pageNum,
                                    @RequestParam(name = "pageSize", defaultValue = "20") Integer pageSize) {
        return ResponseUtils.valueOf(spiritStoneBiz.list(pageNum, pageSize));
    }

    @PostMapping("/update")
    public Map<String, Object> update(@RequestBody SpiritStoneUpdateParam param) {
        return ResponseUtils.valueOf(spiritStoneBiz.update(param));
    }

    @PostMapping("/present")
    public Map<String, Object> present(@RequestParam(name = "userId") int userId,
                                       @RequestParam(name = "roleId") int roleId,
                                       @RequestParam(name = "amount") int amount) {
        return ResponseUtils.valueOf(spiritStoneBiz.present(userId, roleId, amount));
    }
}
