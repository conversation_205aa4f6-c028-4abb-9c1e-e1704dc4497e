package com.kuaikan.role.game.admin.model.view.battle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import lombok.Data;

import com.kuaikan.common.tools.serialize.JsonUtils;
import com.kuaikan.role.game.api.bean.cardbattle.BattleActivityConfig;
import com.kuaikan.role.game.api.bean.cardbattle.BattleActivityConfig.DungeonBasicConfig;
import com.kuaikan.role.game.api.bean.cardbattle.BondActivityExtraConfig;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleLimitedTimeActivityConfig;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleLotteryActivityConfig;
import com.kuaikan.role.game.api.enums.cardbattle.CardBattleExploreTypeEnum;

@Data
public class BattleExploreView implements Serializable {

    private static final long serialVersionUID = 1791077782256245725L;

    private String id;
    private String name;
    private String subTitle;
    // @see CardBattleExploreTypeEnum
    private Integer type;
    private String banner;
    // 副本活动id列表
    private List<DungeonBasicConfig> battleDungeonConfig;
    // 战斗管卡id列表
    private List<BattleActivityConfig.BattleTaskBasicConfig> battleTaskConfig;
    // 限定专题
    private boolean limitTopic;
    // 专题id
    private List<Integer> topicIds;
    //专题名称
    private List<String> topicNames;
    // 限定加成专题列表
    private List<Integer> limitedBoostTopicIds;
    private BigDecimal limitedBoostRatio;
    // 生效时间
    private Long startTime;
    private Long endTime;
    private long offlineTime;
    // 额外配置信息
    private BattleExtraInfoView extraInfoView;
    // 顺序
    private int order;
    // 活动状态, 0:未开始, 1:进行中, 2:已结束
    private int status;
    // 限时活动额外信息
    private CardBattleLimitedTimeActivityConfig limitedTimeActivityView;
    private BondActivityExtraConfig bondActivityView;
    private CardBattleLotteryActivityConfig lotteryActivityView;
    // 白名单开关
    private boolean whiteListSwitch;
    // 白名单
    private String whiteList;
    // 资源位图片
    private String resourceImg;
    // 点击交互
    private String clickInteractive;
    // 变更记录
    private List<BattleActivityConfig.OperatorRecord> operatorRecords;

    public static BattleExploreView convert(BattleActivityConfig battleActivityConfig) {
        BattleExploreView dto = new BattleExploreView();
        dto.setId(battleActivityConfig.getId());
        dto.setName(battleActivityConfig.getName());
        dto.setSubTitle(battleActivityConfig.getSubTitle());
        dto.setType(battleActivityConfig.getType());
        dto.setBanner(battleActivityConfig.getBanner());
        dto.setBattleDungeonConfig(battleActivityConfig.getBattleDungeonConfig());
        dto.setBattleTaskConfig(battleActivityConfig.getBattleTaskConfig());
        dto.setLimitTopic(battleActivityConfig.isLimitTopic());
        dto.setTopicIds(Optional.ofNullable(battleActivityConfig.getTopicIds()).orElse(new ArrayList<>()));
        dto.setLimitedBoostTopicIds(battleActivityConfig.getLimitedBoostTopicIds());
        dto.setLimitedBoostRatio(battleActivityConfig.getLimitedBoostRatio());
        dto.setStartTime(battleActivityConfig.getStartTime());
        dto.setEndTime(battleActivityConfig.getEndTime());
        dto.setOfflineTime(battleActivityConfig.getOfflineTime());
        dto.setOrder(battleActivityConfig.getOrder());
        dto.setStatus(battleActivityConfig.getStatus());
        if (CardBattleExploreTypeEnum.LIMITED_TIME_EXPLORE.getCode().equals(battleActivityConfig.getType())) {
            dto.setLimitedTimeActivityView(JsonUtils.fromJson(battleActivityConfig.getExtraInfo(), CardBattleLimitedTimeActivityConfig.class));
        } else if (CardBattleExploreTypeEnum.BOND.getCode().equals(battleActivityConfig.getType())) {
            dto.setBondActivityView(JsonUtils.fromJson(battleActivityConfig.getExtraInfo(), BondActivityExtraConfig.class));
        } else if (CardBattleExploreTypeEnum.LIMITED_TIME_LOTTERY.getCode().equals(battleActivityConfig.getType())
                || CardBattleExploreTypeEnum.LIMITED_TIME_EXPLORE_V2.getCode().equals(battleActivityConfig.getType())) {
            dto.setLotteryActivityView(JsonUtils.fromJson(battleActivityConfig.getExtraInfo(), CardBattleLotteryActivityConfig.class));
        } else {
            dto.setExtraInfoView(JsonUtils.fromJson(battleActivityConfig.getExtraInfo(), BattleExtraInfoView.class));
        }
        dto.setWhiteListSwitch(battleActivityConfig.isWhiteListSwitch());
        dto.setWhiteList(battleActivityConfig.getWhiteList());
        dto.setResourceImg(battleActivityConfig.getResourceImg());
        dto.setClickInteractive(battleActivityConfig.getClickInteractive());
        dto.setOperatorRecords(battleActivityConfig.getOperatorRecords());
        return dto;
    }

}
