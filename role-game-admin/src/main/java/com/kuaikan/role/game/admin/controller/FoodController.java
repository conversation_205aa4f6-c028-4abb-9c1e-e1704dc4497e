package com.kuaikan.role.game.admin.controller;

import java.util.Map;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.kuaikan.role.game.admin.biz.FoodBiz;
import com.kuaikan.role.game.admin.model.param.FoodAddOrUpdateParam;
import com.kuaikan.role.game.admin.utils.ResponseUtils;

/**
 * <AUTHOR>
 * @version 2024-10-30
 */
@RequestMapping("/v2/admin/role/game/food")
@RestController
public class FoodController {

    @Resource
    private FoodBiz foodBiz;

    @PostMapping("addOrUpdate")
    public Map<String, Object> addOrUpdate(@RequestBody FoodAddOrUpdateParam param) {
        return ResponseUtils.valueOf(foodBiz.addOrUpdate(param));
    }

    @GetMapping("list")
    public Map<String, Object> list(@RequestParam(name = "pageNum", defaultValue = "1") int pageNum,
                                    @RequestParam(name = "pageSize", defaultValue = "20") int pageSize) {
        return ResponseUtils.valueOf(foodBiz.list(pageNum, pageSize));
    }

    @PostMapping("updateSort")
    public Map<String, Object> updateSort(@RequestParam(name = "id") int id, @RequestParam(name = "order") int order) {
        return ResponseUtils.valueOf(foodBiz.updateSort(id, order));
    }

}
