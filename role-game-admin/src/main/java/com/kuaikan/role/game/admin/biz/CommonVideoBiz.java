package com.kuaikan.role.game.admin.biz;

import static com.kuaikan.role.game.admin.common.RoleGameResponse.COMMON_VIDEO_TASK_BUCKET_ERROR;
import static com.kuaikan.role.game.admin.common.RoleGameResponse.COMMON_VIDEO_TASK_NOT_EXIT;
import static com.kuaikan.role.game.admin.common.RoleGameResponse.COMMON_VIDEO_TASK_NOT_SUCCESS;

import java.util.Date;
import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.comic.utils.CdnUtil;
import com.kuaikan.idgenerator.sdk.BizIdGenerator;
import com.kuaikan.role.game.admin.model.view.CommonVideoView;
import com.kuaikan.role.game.admin.repository.AvgRepository;
import com.kuaikan.role.game.api.bean.CommonVideo;
import com.kuaikan.role.game.api.enums.BucketType;
import com.kuaikan.role.game.api.enums.CommonVideoStatus;

@Component
public class CommonVideoBiz {

    @Resource
    private AvgRepository avgRepository;

    public BizResult<String> submitTask(Integer bizType, Integer bucketType, String videoKey) {
        Date now = new Date();
        String videoId = String.valueOf(BizIdGenerator.getId());
        BucketType bucketTypeEnum = BucketType.getByCode(bucketType);
        if (bucketTypeEnum == BucketType.UNKNOWN) {
            return BizResult.result(COMMON_VIDEO_TASK_BUCKET_ERROR);
        }

        CommonVideo commonVideo = new CommonVideo().setVideoId(videoId)
                .setBizType(bizType)
                .setBucketType(bucketType)
                .setStatus(CommonVideoStatus.WAITING.getCode())
                .setOriginVideo(new CommonVideo.VideoInfo().setVideoKey(videoKey))
                .setCreatedAt(now)
                .setUpdatedAt(now);
        avgRepository.insertCommonVideo(commonVideo);
        return BizResult.success(videoId);
    }

    public BizResult<CommonVideoView> queryTaskResult(String videoId) {
        CommonVideo commonVideo = avgRepository.queryCommonVideoByVideoId(videoId);
        if (commonVideo == null) {
            return BizResult.result(COMMON_VIDEO_TASK_NOT_EXIT);
        }
        int status = commonVideo.getStatus();
        if (CommonVideoStatus.SUCCESS.getCode() != status) {
            return BizResult.result(COMMON_VIDEO_TASK_NOT_SUCCESS);
        }
        return BizResult.success(CommonVideoView.valueOf(commonVideo));
    }
}
