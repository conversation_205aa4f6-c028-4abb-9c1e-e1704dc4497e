package com.kuaikan.role.game.admin.model.view;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.bean.Stuff;

/**
 * <AUTHOR>
 * @version 2024-04-19
 */
@Data
@Accessors(chain = true)
@ApiModel("材料")
public class StuffView implements Serializable {

    private static final long serialVersionUID = -2782494970487639814L;
    @ApiModelProperty("材料id")
    private int id;
    @ApiModelProperty("材料名称")
    private String name;
    @ApiModelProperty("材料图片")
    private ImageInfoView stuffImage;
    @ApiModelProperty("分解率")
    private Double decomposeRate;

    public static StuffView valueOf(Stuff stuff) {
        return new StuffView().setId(stuff.getId())
                .setName(stuff.getName())
                .setStuffImage(ImageInfoView.valueOf(stuff.getConfig().getStuffImage()))
                .setDecomposeRate(stuff.getConfig().getDecomposeRate());
    }
}
