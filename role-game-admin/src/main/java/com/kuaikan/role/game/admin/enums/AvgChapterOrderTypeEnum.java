package com.kuaikan.role.game.admin.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 * <AUTHOR>
 * @date 2024/8/12
 */
@Getter
@AllArgsConstructor
public enum AvgChapterOrderTypeEnum {
    // 段落ID升序、段落ID降序、段落名称升序、段落名称降序、修改日期升序、修改日期降序
    CHAPTER_ID_ASC(1, "段落ID升序", "chapterId", true),
    CHAPTER_ID_DESC(2, "段落ID降序", "chapterId", false),
    CHAPTER_NAME_ASC(3, "段落名称升序", "chapterName", true),
    CHAPTER_NAME_DESC(4, "段落名称降序", "chapterName", false),
    UPDATED_AT_ASC(5, "修改日期升序", "updatedAt", true),
    UPDATED_AT_DESC(6, "修改日期降序", "updatedAt", false);

    private int code;
    private String desc;
    private String field;
    private boolean asc;

    public static AvgChapterOrderTypeEnum getByCode(int code) {
        for (AvgChapterOrderTypeEnum status : AvgChapterOrderTypeEnum.values()) {
            if (code == status.getCode()) {
                return status;
            }
        }
        return null;
    }
}
