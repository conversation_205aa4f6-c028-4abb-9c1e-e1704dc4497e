package com.kuaikan.role.game.admin.template.reader;

import java.io.File;
import java.io.IOException;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.ReadListener;
import com.google.common.collect.Lists;

import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.role.game.api.bo.ExcelReaderResult;
import com.kuaikan.role.game.api.bo.FailMessage;
import com.kuaikan.role.game.api.enums.ExcelReaderType;

/**
 * ExcelReader
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
public abstract class ExcelReader<T, K> {

    public static final int HEADER_COUNT = 1;

    @Transactional(rollbackFor = Exception.class)
    public BizResult<ExcelReaderResult> readExcelConfig(File excelFile) throws IOException {

        List<T> dataList = convertData(excelFile);

        List<FailMessage> failMessages = validate(dataList);

        if (CollectionUtils.isNotEmpty(failMessages)) {
            return BizResult.result(ExcelReaderResult.valueOf(failMessages), ResponseCodeMsg.BAD_REQUEST);
        }
        ExcelReaderResult excelReaderResult = new ExcelReaderResult();
        List<K> readDataList = read(dataList);
        excelReaderResult.setResultDataList(readDataList);
        return BizResult.success(excelReaderResult);
    }

    // 读取excel文件，转成mapList，每一个map是一行数据
    protected List<T> convertData(File excelFile) throws IOException {
        List<T> excelDataList = Lists.newArrayList();
        EasyExcel.read(excelFile, getExcelDataClass(), createExcelListener(excelDataList)).sheet().headRowNumber(getHeadRowNumber()).doRead();
        return excelDataList;
    }

    protected int getHeadRowNumber() {
        return HEADER_COUNT;
    }

    protected abstract Class<T> getExcelDataClass();

    protected abstract ReadListener createExcelListener(List<T> excelDataList);

    // 校验数据有效性，无效的数据返回failMessage，说明哪行哪列出问题了
    protected abstract List<FailMessage> validate(List<T> dataList);

    // 保存数据
    protected abstract List<K> read(List<T> dataList);

    protected abstract ExcelReaderType getExcelReaderType();

    protected FailMessage newMessage(String message) {
        return newMessage(0, "", message);
    }

    protected FailMessage newMessage(int row, String column, String message) {
        return new FailMessage().setExcelReaderType(getExcelReaderType().getDesc()).setRow(row).setColumn(column).setMessage(message);
    }
}
