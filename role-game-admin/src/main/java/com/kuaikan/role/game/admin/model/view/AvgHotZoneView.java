package com.kuaikan.role.game.admin.model.view;

import com.kuaikan.cdn.core.CdnHandler;
import com.kuaikan.role.game.api.bean.AvgHotZone;
import com.kuaikan.role.game.api.enums.AvgFileResourceType;
import com.kuaikan.role.game.api.enums.AvgFileStatusType;
import com.kuaikan.role.game.api.enums.AvgHotZoneType;
import com.kuaikan.role.game.api.enums.CdnPayType;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Data
@Accessors(chain = true)
public class AvgHotZoneView {

    private int id;
    private String name;
    private String imageKey;
    private HotZoneConfigView config;
    private Integer status;
    private Integer resource;
    private Integer topicId;
    // 别名
    private List<String> nickNames;

    @Data
    @Accessors(chain = true)
    public static class HotZoneConfigView {
        @Deprecated
        private List<List<List<Integer>>> hotZone;
        private List<HotZoneView> hotZoneV2;
        private ContextView context;

        public static HotZoneConfigView valueOf(AvgHotZone.Config hotZoneConfig) {
            HotZoneConfigView hotZoneConfigView = new HotZoneConfigView();
            if (Objects.isNull(hotZoneConfig)) {
                return hotZoneConfigView;
            }
            List<HotZoneView> hotZoneV2 = null;
            if (CollectionUtils.isNotEmpty(hotZoneConfig.getHotZoneV2())) {
                hotZoneV2 = hotZoneConfig.getHotZoneV2().stream().map(HotZoneView::valueOf).collect(Collectors.toList());
            }
            return hotZoneConfigView
                    .setHotZone(hotZoneConfig.getHotZone())
                    .setHotZoneV2(hotZoneV2)
                    .setContext(ContextView.valueOf(hotZoneConfig.getContext()));
        }
    }

    @Data
    @Accessors(chain = true)
    public static class HotZoneView{
        /**
         * 热区类型
         * @see AvgHotZoneType
         */
        private int styleType;
        /**
         * 热区坐标,左上点
         */
        private PointAxisView leftUpAxis;
        /**
         * 热区坐标,右下点
         */
        private PointAxisView rightDownAxis;
        /**
         * 校验点，styleType为滑动时才会有值
         */
        private List<PointAxisView> checkPoints;
        /**
         * 曲线点，多个离散点构成一条平滑曲线，styleType为滑动时才会有值
         */
        private List<PointAxisView> linePoints;

        public static HotZoneView valueOf(AvgHotZone.HotZone hotZone) {
            if (Objects.isNull(hotZone)) {
                return null;
            }
             List<PointAxisView> checkPoints =null;
            if (CollectionUtils.isNotEmpty(hotZone.getCheckPoints())){
                checkPoints = hotZone.getCheckPoints().stream().map(PointAxisView::valueOf).collect(Collectors.toList());
            }
            List<PointAxisView> linePoints = null;
            if (CollectionUtils.isNotEmpty(hotZone.getLinePoints())){
                linePoints = hotZone.getLinePoints().stream().map(PointAxisView::valueOf).collect(Collectors.toList());
            }
            return new HotZoneView()
                    .setStyleType(hotZone.getStyleType())
                    .setLeftUpAxis(PointAxisView.valueOf(hotZone.getLeftUpAxis()))
                    .setRightDownAxis(PointAxisView.valueOf(hotZone.getRightDownAxis()))
                    .setCheckPoints(checkPoints)
                    .setLinePoints(linePoints);
        }
    }

    @Data
    @Accessors(chain = true)
    public static class PointAxisView{
        private int x;
        private int y;

        public static PointAxisView valueOf(AvgHotZone.PointAxis pointAxis) {
            if (Objects.isNull(pointAxis)) {
                return null;
            }
            return new PointAxisView()
                    .setX(pointAxis.getX())
                    .setY(pointAxis.getY());
        }
    }

    @Data
    @Accessors(chain = true)
    public static class ContextView{
        private String hint;
        /**
         * 提示文本框的左上角坐标
         */
        private PointAxisView leftUpAxis;
        /**
         * 提示文本框的右下角坐标
         */
        private PointAxisView rightDownAxis;

        public static ContextView valueOf(AvgHotZone.Context context) {
            if (Objects.isNull(context)) {
                return null;
            }
            return new ContextView()
                    .setHint(context.getHint())
                    .setLeftUpAxis(PointAxisView.valueOf(context.getLeftUpAxis()))
                    .setRightDownAxis(PointAxisView.valueOf(context.getRightDownAxis()));
        }
    }

    public static AvgHotZoneView valueOf(AvgHotZone avgHotZone, String domain, List<String> aliasList) {
        // 别名
        List<String> nickNames = CollectionUtils.isNotEmpty(aliasList) ? aliasList.stream()
                .filter(alias -> StringUtils.isNotBlank(alias) && !alias.equals(avgHotZone.getName()))
                .collect(Collectors.toList()) : Collections.emptyList();
        // imageKey
        String imageKey = Strings.isNotEmpty(avgHotZone.getImageKey()) ? CdnHandler.getEncryptionUrl(domain + avgHotZone.getImageKey(),
                CdnPayType.PAY.getCode()) : "";

        AvgHotZoneView view = new AvgHotZoneView();
        view.setId(avgHotZone.getId());
        view.setName(avgHotZone.getName());
        view.setImageKey(imageKey);
        view.setNickNames(nickNames);
        view.setResource(Optional.ofNullable(avgHotZone.getResource()).orElse(AvgFileResourceType.UNKNOW.getCode()));
        view.setStatus(Optional.ofNullable(avgHotZone.getStatus()).orElse(AvgFileStatusType.ONLINE.getCode()));
        view.setTopicId(null != avgHotZone.getConfig() ? avgHotZone.getConfig().getTopicId() : null);
        view.setConfig(HotZoneConfigView.valueOf(avgHotZone.getConfig()));
        return view;
    }

}
