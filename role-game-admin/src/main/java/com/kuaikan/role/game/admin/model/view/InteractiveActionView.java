package com.kuaikan.role.game.admin.model.view;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.experimental.Accessors;

import org.apache.commons.collections4.CollectionUtils;

import com.kuaikan.comic.utils.CdnUtil;
import com.kuaikan.role.game.api.bean.Role;
import com.kuaikan.role.game.api.bean.InteractiveAction;
import com.kuaikan.role.game.api.bo.ImageInfo;
import com.kuaikan.role.game.api.enums.InteractiveActionUnlockTypeEnum;

/**
 *
 * 互动动作view
 *
 * <AUTHOR>
 * @date 2024/08/30
 */
@Data
@Accessors(chain = true)
public class InteractiveActionView implements Serializable {

    private static final long serialVersionUID = 8514839571690894502L;

    /**
     * 排序
     */
    private Integer orderNum;

    /**
     * 羁绊值加成等阶(可能会根据配置校验)
     */
    private int emotionBondLevel;

    /**
     * 动作id
     */
    private int id;

    /**
     * 动作名称
     */
    private String name;

    /**
     * 关联角色id
     */
    private List<Integer> roleIds;

    /**
     * 关联角色名称
     */
    private List<String> roleNames;

    /**
     * 动画配置
     */
    private AnimationConfigView animationConfig;

    /**
     * 动作图片
     */
    private ImageInfoView actionImage;

    /**
     * 解锁条件{@link InteractiveActionUnlockTypeEnum}
     */
    private int unlockType;

    /**
     * 条件参数：
     * 阅读漫画xx话
     * 羁绊等级xx级
     * 一起做日程xx分钟
     * 初始解锁无解锁条件
     */
    private Integer unlockCondition;

    /**
     * 有效状态（1:未上架、2:已上架、3:已下架）
     */
    private int status;

    /**
     * 创建时间
     */
    private Date createTime;

    public static InteractiveActionView valueOf(InteractiveAction interactiveAction, Integer orderNum, Map<Integer, Role> roleMap) {
        if (null == interactiveAction) {
            return null;
        }
        InteractiveActionView interactiveActionView = new InteractiveActionView();
        interactiveActionView.setOrderNum(orderNum);
        interactiveActionView.setId(interactiveAction.getId());
        interactiveActionView.setName(interactiveAction.getName());
        interactiveActionView.setRoleIds(new ArrayList<>(roleMap.keySet()));
        interactiveActionView.setRoleNames(roleMap.values().stream().map(Role::getName).collect(Collectors.toList()));
        interactiveActionView.setEmotionBondLevel(interactiveAction.getEmotionBondLevel());
        interactiveActionView.setUnlockType(interactiveAction.getConfig().getUnlockType());
        interactiveActionView.setUnlockCondition(interactiveAction.getConfig().getUnlockCondition());
        interactiveActionView.setStatus(interactiveAction.getStatus());
        interactiveActionView.setCreateTime(interactiveAction.getCreatedAt());
        interactiveActionView.setActionImage(interactiveAction.getConfig().getActionImage());
        interactiveActionView.setAnimationConfig(interactiveAction.getConfig().getAnimationConfig(), roleMap);
        return interactiveActionView;
    }

    public void setActionImage(ImageInfo actionImage) {
        this.actionImage = ImageInfoView.valueOf(actionImage);
    }

    /**
     * 动画配置
     */
    public void setAnimationConfig(InteractiveAction.Config.AnimationConfig animationConfig, Map<Integer, Role> roleMap) {
        this.animationConfig = AnimationConfigView.valueOf(animationConfig, roleMap);
    }

    @Data
    @Accessors(chain = true)
    public static class AnimationConfigView {

        private String configFileUrl;

        private String configFileKey;

        private String configFileName;

        private List<RoleAnimationConfigView> roleAnimationConfigs;

        private int spacingX;

        private int spacingY;

        private int upperLayerRoleId;

        private String host;

        public static AnimationConfigView valueOf(InteractiveAction.Config.AnimationConfig animationConfig, Map<Integer, Role> roleMap) {
            if (null == animationConfig) {
                return null;
            }
            AnimationConfigView animationConfigView = new AnimationConfigView();
            animationConfigView.setSpacingX(animationConfig.getSpacingX());
            animationConfigView.setSpacingY(animationConfig.getSpacingY());
            animationConfigView.setUpperLayerRoleId(animationConfig.getUpperLayerRoleId());
            animationConfigView.setConfigFileUrl(animationConfig.getConfigFileUrl());
            animationConfigView.setConfigFileKey(animationConfig.getConfigFileKey());
            animationConfigView.setConfigFileName(animationConfig.getConfigFileName());
            animationConfigView.setHost(CdnUtil.getDefaultDomainWithBackSlash());
            List<InteractiveAction.Config.RoleAnimationConfig> roleAnimationConfigs = animationConfig.getRoleAnimationConfigs();
            if (CollectionUtils.isNotEmpty(roleAnimationConfigs)) {
                animationConfigView.setRoleAnimationConfigs(
                        roleAnimationConfigs.stream().map(item -> RoleAnimationConfigView.valueOf(item, roleMap)).collect(Collectors.toList()));
            }
            return animationConfigView;
        }
    }

    @Data
    @Accessors(chain = true)
    public static class RoleAnimationConfigView {

        private int roleId;

        private String roleName;

        private List<DialogueConfigView> dialogueConfigs;

        @Data
        @Accessors(chain = true)
        private static class DialogueConfigView {

            private String animation;
            private Integer bubbleType;
            private String word;
            private Double animationStartSecond;
            private Double wordStartSecond;
        }

        public static RoleAnimationConfigView valueOf(InteractiveAction.Config.RoleAnimationConfig roleAnimationConfig, Map<Integer, Role> roleMap) {
            if (null == roleAnimationConfig) {
                return null;
            }
            Role role = roleMap.get(roleAnimationConfig.getRoleId());
            RoleAnimationConfigView view = new RoleAnimationConfigView();
            view.setRoleId(roleAnimationConfig.getRoleId());
            view.setRoleName(role == null ? null : role.getName());
            List<InteractiveAction.Config.DialogueConfig> dialogueConfigs = roleAnimationConfig.getDialogueConfigs();
            if (CollectionUtils.isNotEmpty(dialogueConfigs)) {
                List<RoleAnimationConfigView.DialogueConfigView> dialogueConfigViews = dialogueConfigs.stream().map(item -> {
                    RoleAnimationConfigView.DialogueConfigView dialogueConfigView = new RoleAnimationConfigView.DialogueConfigView();
                    dialogueConfigView.setAnimation(item.getAnimation());
                    dialogueConfigView.setBubbleType(item.getBubbleType());
                    dialogueConfigView.setWord(item.getWord());
                    dialogueConfigView.setAnimationStartSecond(item.getAnimationStartSecond());
                    dialogueConfigView.setWordStartSecond(item.getWordStartSecond());
                    return dialogueConfigView;
                }).collect(Collectors.toList());
                view.setDialogueConfigs(dialogueConfigViews);
            }
            return view;
        }

    }

}
