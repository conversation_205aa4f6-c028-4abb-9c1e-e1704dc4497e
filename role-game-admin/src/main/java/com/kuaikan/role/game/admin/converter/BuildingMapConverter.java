package com.kuaikan.role.game.admin.converter;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import com.kuaikan.role.game.admin.model.param.BuildingAreaMapParam;
import com.kuaikan.role.game.admin.model.param.BuildingMapAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.view.BuildingAreaMapDetailView;
import com.kuaikan.role.game.admin.model.view.BuildingMapDetailView;
import com.kuaikan.role.game.admin.model.view.BuildingMapView;
import com.kuaikan.role.game.common.bean.BuildingAreaMap;
import com.kuaikan.role.game.common.bean.BuildingMap;

/**
 *
 * <AUTHOR>
 * @date 2024/6/20
 */
@Mapper
public interface BuildingMapConverter extends BaseConverter {

    BuildingMap toBuildingMap(BuildingMapAddOrUpdateParam param);

    @Mappings({
            @Mapping(target = ".", source = "config"),
            @Mapping(target = "imgUrl", source = "config.imageInfo"),
    })
    BuildingMapView toBuildingMapView(BuildingMap buildingMap);

    @Mapping(target = ".", source = "config")
    BuildingAreaMapDetailView toBuildingAreaMapDetailView(BuildingAreaMap buildingAreaMap);

    @Mappings({@Mapping(target = "buildingAreaMapDetailViewList", source = "buildingAreaMapList") })
    BuildingMapDetailView toBuildingMapDetailView(BuildingMap buildingMap);

    List<BuildingAreaMap> toBuildingAreaMapList(List<BuildingAreaMapParam> buildingAreaMapParamList);

    @Mappings({ @Mapping(target = "config", source = ".")})
    BuildingAreaMap toBuildingAreaMap(BuildingAreaMapParam param);

}
