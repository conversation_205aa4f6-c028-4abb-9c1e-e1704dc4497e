package com.kuaikan.role.game.admin.model.view;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.bean.Costume;
import com.kuaikan.role.game.api.bean.RoleCostumeRelation;
import com.kuaikan.role.game.api.bean.RoleGroupCostumeBlindBoxConfig;

/**
 * <AUTHOR>
 * @version 2024-06-06
 */
@Data
@Accessors(chain = true)
public class RoleCostumeConfigView implements Serializable {

    private static final long serialVersionUID = -8286719419689822877L;

    private List<Config> configs;

    @Data
    @Accessors(chain = true)
    public static class Config implements Serializable {

        private static final long serialVersionUID = -8558872520546024444L;

        private int id;
        private String name;
        private String roleName;
        /**
         * @see com.kuaikan.role.game.api.enums.CostumeLevel
         */
        private int level;
        /**
         * 抽取概率
         */
        @Deprecated
        private double lotteryProbability;
        /**
         * 能否抽取
         */
        private boolean canLottery;
        /**
         * 能否合成
         */
        private boolean canCompose;

        public static Config valueOf(Costume costume, RoleCostumeRelation.Config relationConfig, String roleName) {
            if (costume == null) {
                return null;
            }
            Config config = new Config();
            config.setId(costume.getId());
            config.setName(costume.getName());
            config.setRoleName(roleName);
            config.setLevel(costume.getLevel());
            if (relationConfig != null) {
                config.setLotteryProbability(relationConfig.getLotteryProbability());
                config.setCanLottery(relationConfig.isCanLottery());
                config.setCanCompose(relationConfig.isCanCompose());
            }
            return config;
        }

        public static Config valueOf(Costume costume, RoleGroupCostumeBlindBoxConfig.Config costumeConfig, String roleName) {
            if (costume == null) {
                return null;
            }
            Config config = new Config();
            config.setId(costume.getId());
            config.setName(costume.getName());
            config.setRoleName(roleName);
            config.setLevel(costume.getLevel());

            if (costumeConfig != null) {
                config.setLotteryProbability(costumeConfig.getProbability());
                config.setCanLottery(costumeConfig.isCanLottery());
                config.setCanCompose(costumeConfig.isCanCompose());
            }
            return config;
        }
    }
}
