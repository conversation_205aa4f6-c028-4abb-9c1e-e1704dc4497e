package com.kuaikan.role.game.admin.repository;

import com.kuaikan.role.game.admin.dao.rolegame.MapBuildingRelationMapper;
import com.kuaikan.role.game.api.bean.MapBuildingRelation;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;

@Repository
public class MapBuildingRelationRepository {

    @Resource
    private MapBuildingRelationMapper mapBuildingRelationMapper;

    public void insertOrUpdate(MapBuildingRelation mapBuildingRelation) {
        MapBuildingRelation relation = mapBuildingRelationMapper.selectByCityAndBuildingId(mapBuildingRelation.getMapId(), mapBuildingRelation.getBuildingId());
        Date now = new Date();
        if (relation != null) {
            relation.setUpdatedAt(now);
            mapBuildingRelationMapper.updateByPrimaryKey(relation);
        } else {
            mapBuildingRelation.setUpdatedAt(now);
            mapBuildingRelation.setCreatedAt(now);
            mapBuildingRelationMapper.insert(mapBuildingRelation);
        }

    }
}
