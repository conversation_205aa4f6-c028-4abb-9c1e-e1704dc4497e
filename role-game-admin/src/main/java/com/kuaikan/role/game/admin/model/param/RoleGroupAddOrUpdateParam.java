package com.kuaikan.role.game.admin.model.param;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * RoleGroupAddOrUpdateParam
 *
 * <AUTHOR>
 * @since 2024-06-25
 */
@Data
@Accessors(chain = true)
public class RoleGroupAddOrUpdateParam implements Serializable {

    private static final long serialVersionUID = -5715932320548642863L;
    private int id;
    private List<RoleGroupRelationParam> roles;

    @Data
    @Accessors(chain = true)
    public static class RoleGroupRelationParam implements Serializable {

        private static final long serialVersionUID = 2499511135272606016L;
        private int roleId;
        private int orderNum;
        private boolean emotionBondSwitch;
    }
}
