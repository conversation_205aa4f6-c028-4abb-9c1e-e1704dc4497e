package com.kuaikan.role.game.admin.component.battle.excel.listener;

import java.util.Arrays;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import com.kuaikan.role.game.admin.common.BattleUploadType;
import com.kuaikan.role.game.admin.model.excel.CardModelConfigExcelDto;

/**
 * CardModelExcelBasicListener
 *
 * <AUTHOR>
 * @date 2024/6/20
 */
@Slf4j
@Component
public class CardModelExcelBasicListener extends AbstractUploadBasicListener<CardModelConfigExcelDto> {

  private final List<String> headers = Arrays.asList("modelId", "rareCode", "cost", "atk", "hp");

  public CardModelExcelBasicListener() {
  }

  @Override
  public int getUploadType() {
    return BattleUploadType.CARD_MODEL;
  }

  @Override
  protected List<String> getRealNeedHeaders() {
    return headers;
  }
}
