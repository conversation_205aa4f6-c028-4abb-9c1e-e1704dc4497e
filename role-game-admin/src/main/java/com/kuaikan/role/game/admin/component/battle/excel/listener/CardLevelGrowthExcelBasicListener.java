package com.kuaikan.role.game.admin.component.battle.excel.listener;

import java.util.Arrays;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import com.kuaikan.role.game.admin.common.BattleUploadType;
import com.kuaikan.role.game.admin.model.excel.CardLevelGrowthConfigExcelDto;

/**
 * CardModelExcelBasicListener
 *
 * <AUTHOR>
 * @date 2024/6/20
 */
@Slf4j
@Component
public class CardLevelGrowthExcelBasicListener extends AbstractUploadBasicListener<CardLevelGrowthConfigExcelDto> {

    private final List<String> headers = Arrays.asList("level", "rareN", "rareR", "rareSr", "rareSsr", "rareUr", "rareSp", "rareSsp");

    @Override
    public int getUploadType() {
        return BattleUploadType.CARD_LEVEL_GROWTH;
    }

    @Override
    protected List<String> getRealNeedHeaders() {
        return headers;
    }
}
