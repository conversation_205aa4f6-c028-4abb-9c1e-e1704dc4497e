package com.kuaikan.role.game.admin.model.excel;

import java.io.Serializable;

import lombok.Data;

import org.springframework.beans.BeanUtils;

import com.alibaba.excel.annotation.ExcelProperty;

import com.kuaikan.role.game.admin.component.battle.excel.valid.RareCodeValid;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleCardModelConfig;

/**
 * 卡牌模板 excel dto
 *
 * <AUTHOR>
 * @date 2024/6/20
 */
@Data
public class CardModelConfigExcelDto implements Serializable {

    // 模板id
    @ExcelProperty(value = { "模板ID", "整数", "modelId" }, index = 0)
    private int modelId;
    // 模板稀有度
    @RareCodeValid
    @ExcelProperty(value = { "稀有度", "整数", "rareCode" }, index = 1)
    private int rareCode;
    // 行动消耗点数
    @ExcelProperty(value = { "战斗点数", "整数", "cost" }, index = 2)
    private int cost;
    // 攻击力
    @ExcelProperty(value = { "攻击力", "整数", "atk" }, index = 3)
    private int atk;
    // 血量
    @ExcelProperty(value = { "血量", "整数", "hp" }, index = 4)
    private int hp;

    public static CardBattleCardModelConfig toCardModelConfig(CardModelConfigExcelDto excelDto) {
        CardBattleCardModelConfig cardBattleCardRareConfig = new CardBattleCardModelConfig();
        BeanUtils.copyProperties(excelDto, cardBattleCardRareConfig);
        // 状态设置为0有效
        cardBattleCardRareConfig.setStatus(0);
        return cardBattleCardRareConfig;
    }

    public static CardModelConfigExcelDto fromCardModelConfig(CardBattleCardModelConfig cardModelConfig) {
        CardModelConfigExcelDto excelDto = new CardModelConfigExcelDto();
        BeanUtils.copyProperties(cardModelConfig, excelDto);
        return excelDto;
    }

}
