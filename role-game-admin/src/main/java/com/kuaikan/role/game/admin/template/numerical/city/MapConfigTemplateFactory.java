package com.kuaikan.role.game.admin.template.numerical.city;

import com.google.common.collect.ImmutableMap;
import com.kuaikan.role.game.api.enums.CityConfigType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
public class MapConfigTemplateFactory implements ApplicationContextAware {
    private static Map<CityConfigType, MapConfigTemplate<?>> map;

    public MapConfigTemplate<?> getInstance(CityConfigType numericalType) {
        return map.get(numericalType);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        ImmutableMap.Builder<CityConfigType, MapConfigTemplate<?>> builder = ImmutableMap.builder();
        for (MapConfigTemplate<?> mapConfigTemplate : applicationContext.getBeansOfType(MapConfigTemplate.class).values()) {
            builder.put(mapConfigTemplate.getCityConfigType(), mapConfigTemplate);
        }
        map = builder.build();
    }
}
