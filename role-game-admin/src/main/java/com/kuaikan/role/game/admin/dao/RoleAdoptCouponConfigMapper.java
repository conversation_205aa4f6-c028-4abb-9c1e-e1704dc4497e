package com.kuaikan.role.game.admin.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.kuaikan.role.game.api.bean.RoleAdoptCouponConfig;

/**
 * <AUTHOR>
 * @date 2024/10/28 19:18
 */
public interface RoleAdoptCouponConfigMapper {

    int insert(RoleAdoptCouponConfig record);

    RoleAdoptCouponConfig selectByPrimaryKey(@Param("id") Integer id);

    int updateProgressById(@Param("progress") Integer progress, @Param("id") Integer id);

    List<RoleAdoptCouponConfig> queryByPage(@Param("offset") int offset, @Param("limit") int limit);

    int count();

    int updateByPrimaryKeySelective(RoleAdoptCouponConfig couponConfig);

    List<RoleAdoptCouponConfig> queryAll();

}
