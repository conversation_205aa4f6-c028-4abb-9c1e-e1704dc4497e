package com.kuaikan.role.game.admin.dao.mongo;

import javax.annotation.Resource;

import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import com.mongodb.client.result.UpdateResult;

import com.kuaikan.role.game.api.bean.StoryLetter;
import com.kuaikan.role.game.api.model.StoryLetterContent;

/**
 *
 * <AUTHOR>
 * @date 2024/2/28
 */
@Service
public class LetterContentDAO {

    @Resource
    private MongoTemplate mongoTemplatePrimary;

    private static final String STORY_ID = "storyId";

    private static final String LETTER_CONTENT = "letterContent";

    private static final String MATERIAL_MD5 = "materialMd5";

    private static final String CREATED_AT = "createdAt";

    private static final String UPDATED_AT = "updatedAt";

    public UpdateResult upsert(int storyId, StoryLetterContent storyLetterContent, String materialMd5) {
        Query query = new Query(Criteria.where(STORY_ID).is(storyId));
        Update update = new Update();
        update.setOnInsert(STORY_ID, storyId);
        update.setOnInsert(CREATED_AT, System.currentTimeMillis());
        update.set(LETTER_CONTENT, storyLetterContent);
        update.set(MATERIAL_MD5, materialMd5);
        update.set(UPDATED_AT, System.currentTimeMillis());
        return mongoTemplatePrimary.upsert(query, update, StoryLetter.class);
    }

    public StoryLetter findByStoryId(int storyId) {
        Query query = new Query(Criteria.where(STORY_ID).is(storyId));
        return mongoTemplatePrimary.findOne(query, StoryLetter.class);
    }

}
