package com.kuaikan.role.game.admin.repository;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import com.kuaikan.role.game.api.enums.CommonStatus;
import com.kuaikan.role.game.common.bean.BuildingAreaMap;
import com.kuaikan.role.game.common.bean.BuildingMap;
import com.kuaikan.role.game.common.dao.BuildingAreaMapMapper;
import com.kuaikan.role.game.common.dao.BuildingMapMapper;
import com.kuaikan.role.game.common.exception.BusinessException;

/**
 *
 * <AUTHOR>
 */
@Repository
public class BuildingMapRepository {

    @Resource
    private BuildingMapMapper buildingMapMapper;

    @Resource
    private BuildingAreaMapMapper buildingAreaMapMapper;

    public PageInfo<BuildingMap> selectSimpleBuildingMapList(String name, String creator, CommonStatus status, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<BuildingMap> buildingMaps = buildingMapMapper.selectList(name, creator, status);
        PageInfo<BuildingMap> pageInfo = new PageInfo<>(buildingMaps);
        return pageInfo;
    }

    public List<BuildingMap> selectBuildingMapList(String name, String creator, CommonStatus status) {
        List<BuildingMap> buildingMaps = buildingMapMapper.selectList(name, creator, status);
        List<Integer> buildingMapIds = buildingMaps.stream().map(BuildingMap::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(buildingMapIds)) {
            return buildingMaps;
        }
        List<BuildingAreaMap> buildingAreaMaps = buildingAreaMapMapper.selectByBuildingMapIds(buildingMapIds);
        if (CollectionUtils.isEmpty(buildingAreaMaps)) {
            return buildingMaps;
        }
        buildingMaps.forEach(buildingMap -> {
            List<BuildingAreaMap> buildingAreaMapList = buildingAreaMaps.stream()
                    .filter(buildingAreaMap -> buildingAreaMap.getBuildingMapId().equals(buildingMap.getId()))
                    .collect(Collectors.toList());
            buildingMap.setBuildingAreaMapList(buildingAreaMapList);
        });
        return buildingMaps;
    }
    public BuildingMap selectBuildingMapDetailById(Integer buildingMapId) {
        BuildingMap buildingMap = buildingMapMapper.selectByPrimaryKey(buildingMapId);
        if (buildingMap == null) {
            return null;
        }
        List<BuildingAreaMap> buildingAreaMaps = buildingAreaMapMapper.selectByBuildingMapIdAndBuildingAreaIds(buildingMapId, null);
        if (CollectionUtils.isEmpty(buildingAreaMaps)) {
            return null;
        }
        buildingMap.setBuildingAreaMapList(buildingAreaMaps);
        return buildingMap;
    }

    public Optional<BuildingMap> selectSimpleBuildingMapById(Integer id) {
        BuildingMap buildingMap = buildingMapMapper.selectByPrimaryKey(id);
        return Optional.ofNullable(buildingMap);
    }

    public int updateStatus(Integer id, CommonStatus status, String userName) {
        BuildingMap buildingMap = new BuildingMap().setId(id).setStatus(status).setUpdater(userName);
        return buildingMapMapper.updateByPrimaryKeySelective(buildingMap);
    }

    @Transactional(rollbackFor = Exception.class)
    public void insertBuildingMap(BuildingMap buildingMap, String userName) {
        if (CollectionUtils.isEmpty(buildingMap.getBuildingAreaMapList())) {
            throw new BusinessException("建筑区域不能为空");
        }
        buildingMap.setCreator(userName)
                .setUpdater(userName)
                .setStatus(CommonStatus.NOT_ONLINE);
        buildingMapMapper.insertSelective(buildingMap);
        buildingMap.getBuildingAreaMapList().forEach(buildingAreaMap -> {
            buildingAreaMap.setBuildingMapId(buildingMap.getId())
                    .setCreator(userName)
                    .setUpdater(userName)
                    .setStatus(CommonStatus.ONLINE);
            buildingAreaMapMapper.insertSelective(buildingAreaMap);
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateBuildingMap(BuildingMap buildingMap, String userName) {
        List<BuildingAreaMap> buildingAreaMapList = buildingMap.getBuildingAreaMapList();
        if (CollectionUtils.isEmpty(buildingAreaMapList)) {
            throw new BusinessException("建筑区域不能为空");
        }
        buildingMap.setUpdater(userName);
        buildingMapMapper.updateByPrimaryKeySelective(buildingMap);
        List<Integer> addBuildingAreaIds = buildingAreaMapList.stream()
                .filter(buildingAreaMap -> buildingAreaMap.getId() == null)
                .map(BuildingAreaMap::getBuildingAreaId)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(addBuildingAreaIds)) {
            List<BuildingAreaMap> oldBuildingAreaMaps = buildingAreaMapMapper.selectByBuildingMapIdAndBuildingAreaIds(buildingMap.getId(), addBuildingAreaIds);
            if (CollectionUtils.isNotEmpty(oldBuildingAreaMaps)) {
                throw new BusinessException("新增的建筑区域已存在");
            }
        }
        buildingAreaMapList.forEach(buildingAreaMap -> {
            if (buildingAreaMap.getId() == null) {
                buildingAreaMap.setBuildingMapId(buildingMap.getId())
                        .setCreator(userName)
                        .setUpdater(userName);
                buildingAreaMapMapper.insertSelective(buildingAreaMap);
            } else {
                buildingAreaMap.setUpdater(userName);
                buildingAreaMapMapper.updateByPrimaryKeySelective(buildingAreaMap);
            }
        });
    }
}
