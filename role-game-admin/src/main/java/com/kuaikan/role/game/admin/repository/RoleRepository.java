package com.kuaikan.role.game.admin.repository;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;

import com.kuaikan.role.game.admin.dao.RoleGroupMapper;
import com.kuaikan.role.game.admin.dao.RoleGroupRelationMapper;
import com.kuaikan.role.game.admin.utils.DateUtil;
import com.kuaikan.role.game.common.bean.RoleGroupRelation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import com.kuaikan.common.redis.KeyGenerator;
import com.kuaikan.common.redis.lettuce.LettuceClusterClient;
import com.kuaikan.common.redis.lettuce.LettuceClusterUtil;
import com.kuaikan.role.game.admin.dao.RoleMapper;
import com.kuaikan.role.game.api.bean.Role;
import com.kuaikan.role.game.common.enums.CacheConfig;
import org.testng.collections.Lists;

/**
 * <AUTHOR>
 * @date 2024/2/26
 */
@Repository
public class RoleRepository {

    @Resource
    private RoleMapper roleMapper;
    @Resource
    private RoleGroupRelationMapper roleGroupRelationMapper;

    public List<Role> queryRoleListByTopicId(int topicId) {
        return roleMapper.queryRoleListByTopicId(topicId);
    }

    public int insert(Role record) {
        return roleMapper.insertSelective(record);
    }

    public int updateByPrimaryKeySelective(Role record) {
        return roleMapper.updateByPrimaryKeySelective(record);
    }

    public Role queryById(int id) {
        return roleMapper.selectByPrimaryKey(id);
    }

    public List<Role> queryByNameLike(String name) {
        return roleMapper.queryByNameLike(name);
    }

    public List<Role> queryByPage(int offset, int limit) {
        return roleMapper.queryByPage(offset, limit);
    }

    public int count() {
        return roleMapper.count();
    }

    public List<Role> queryAll() {
        return roleMapper.queryAll();
    }

    public List<Role> queryByIds(Collection<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return roleMapper.queryByIds(ids);
    }

    public int updateOrderNumGreaterThanOrEqualToNewOrderNum(int orderNum) {
        return roleMapper.updateOrderNumGreaterThanOrEqualToNewOrderNum(orderNum);
    }

    public List<Role> queryByCostumeId(int costumeId) {
        return roleMapper.queryByCostumeId(costumeId);
    }

    public List<Role> queryBySceneId(int sceneId) {
        return roleMapper.queryBySceneId(sceneId);
    }

    public void batchUpdateConfig(List<Role> roles) {
        roleMapper.batchUpdateConfig(roles);
        deleteCache(roles);
    }

    private void deleteCache(List<Role> roles) {
        if (CollectionUtils.isEmpty(roles)) {
            return;
        }
        List<String> keys = Lists.newArrayList();
        LettuceClusterClient redisClient = LettuceClusterUtil.getClusterClientByName(CacheConfig.ALL_ROLE_INFO.getReadWriteVip());
        keys.add(CacheConfig.ALL_ROLE_INFO.getKeyPattern());
        List<String> allRoleInfoKeys = roles.stream().map(e -> KeyGenerator.generate(CacheConfig.ROLE_INFO.getKeyPattern(), e.getId())).collect(Collectors.toList());
        keys.addAll(allRoleInfoKeys);

        redisClient.del(keys.toArray(new String[0]));
    }

    public int queryByGroupIdAndRoleId(int groupId, int roleId) {
        List<Integer> roles = roleMapper.queryByGroupId(groupId);
        return roles.get(0) == roleId ? roles.get(1) : roles.get(0);
    }

}
