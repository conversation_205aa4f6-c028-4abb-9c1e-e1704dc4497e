package com.kuaikan.role.game.admin.dao;

import com.kuaikan.role.game.api.bean.AvgFileChapterRelation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/31
 */
public interface AvgFileChapterRelationMapper {

    AvgFileChapterRelation selectByPrimaryKey(Integer id);

    int deleteByIds(@Param("ids") List<Integer> ids);

    void batchInsert(@Param("relations") List<AvgFileChapterRelation> relations);

    List<AvgFileChapterRelation> queryByTypeAndFileIds(@Param("type")int type, @Param("fileIds") List<Integer> fileIds);

    List<AvgFileChapterRelation> queryByChapterId(@Param("chapterId") Integer chapterId);

    AvgFileChapterRelation queryByTypeAndFileIdAndChapterId(@Param("fileId") Integer fileId, @Param("chapterId") Integer chapterId, @Param("type") int type);
}
