package com.kuaikan.role.game.admin.model.view;

import java.io.Serializable;
import java.util.Collection;
import java.util.Date;

import com.kuaikan.role.game.api.bean.SpineMaterial;
import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.bean.Scene;

/**
 * <AUTHOR>
 * @date 2024/2/27
 */
@Data
@Accessors(chain = true)
public class SceneView implements Serializable {

    private static final long serialVersionUID = 7219320577055097897L;

    private Integer id;

    private String name;
    /**
     * 获取文案
     */
    private String obtainCopywriting;
    /**
     * 活动id
     */
    private int activityId;
    /**
     * 缩略图
     */
    private ImageInfoView thumbnail;
    /**
     * 大图
     */
    private ImageInfoView largeImage;
    /**
     * 角标
     */
    private ImageInfoView cornerMark;
    /**
     * 场景状态（1:未上架、2:已上架、3:已下架）
     */
    private int status;

    private  Collection<RoleGroupView> roleGroups;

    private Integer orderNum;

    private Boolean isDefault;

    private Float[] rgba;

    private Date createTime;

    /**
     * 大图spine
     */
    private SpineMaterialView largeImageSpineMaterial;

    public static SceneView valueOf(Scene scene,  Integer orderNum, Boolean isDefault) {
        SceneView sceneView = new SceneView();
        sceneView.setId(scene.getId());
        sceneView.setName(scene.getName());
        sceneView.setObtainCopywriting(scene.getConfig().getObtainCopywriting());
        sceneView.setActivityId(scene.getConfig().getActivityId());
        sceneView.setThumbnail(ImageInfoView.valueOf(scene.getConfig().getThumbnail()));
        sceneView.setLargeImage(ImageInfoView.valueOf(scene.getConfig().getLargeImage()));
        sceneView.setCornerMark(ImageInfoView.valueOf(scene.getConfig().getCornerMark()));
        sceneView.setStatus(scene.getStatus());
        sceneView.setOrderNum(orderNum);
        sceneView.setCreateTime(scene.getCreatedAt());
        sceneView.setIsDefault(isDefault);
        sceneView.setRgba(scene.getConfig().getRgba());
        sceneView.setLargeImageSpineMaterial(SpineMaterialView.valueOf(scene.getConfig().getLargeImageSpineMaterial()));
        return sceneView;
    }

    @Data
    public static class SceneConfigView implements Serializable {

        private static final long serialVersionUID = -615405484915091351L;
        /**
         * 获取文案
         */
        private String obtainCopywriting;
        /**
         * 活动id
         */
        private int activityId;
        /**
         * 缩略图
         */
        private ImageInfoView thumbnailUrl;
        /**
         * 大图
         */
        private ImageInfoView largeImageUrl;
        /**
         * 角标
         */
        private ImageInfoView cornerMarkUrl;

    }

}
