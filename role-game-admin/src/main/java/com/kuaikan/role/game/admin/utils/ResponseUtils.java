package com.kuaikan.role.game.admin.utils;

import java.util.Map;

import com.google.common.collect.Maps;

import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.ResponseType;

/**
 * <AUTHOR>
 * @date 2024/2/26
 */
public class ResponseUtils {

    public static Map<String, Object> success() {

        return buildResponse(ResponseType.SUCCESS.getCode(), ResponseType.SUCCESS.getMsg(), null);
    }

    public static Map<String, Object> success(Object data) {
        return buildResponse(ResponseType.SUCCESS.getCode(), ResponseType.SUCCESS.getMsg(), data);
    }

    public static Map<String, Object> fail(ResponseType responseType) {
        return buildResponse(responseType.getCode(), responseType.getMsg(), null);
    }

    public static Map<String, Object> fail(ResponseType responseType, Object... args) {
        String message = String.format(responseType.getMsg(), args);
        return buildResponse(responseType.getCode(), message, null);
    }

    public static Map<String, Object> response(ResponseType responseType) {
        return buildResponse(responseType.getCode(), responseType.getMsg(), null);
    }

    public static Map<String, Object> valueOf(BizResult bizResult) {
        return buildResponse(bizResult.getCode(), bizResult.getMessage(), bizResult.getData());
    }

    public static Map<String, Object> buildResponse(int code, String msg, Object data) {
        Map<String, Object> response = Maps.newHashMap();
        response.put("code", code);
        response.put("message", msg);
        if (data != null) {
            response.put("data", data);
        }
        return response;
    }

}
