package com.kuaikan.role.game.admin.utils.excel.listener;

import java.util.List;

import com.alibaba.excel.context.AnalysisContext;

import com.kuaikan.role.game.admin.model.excel.BondLevelUnlockStoryExcelData;

/**
 * <AUTHOR>
 * @version 2024-10-30
 */
public class BondLevelUnlockStoryExcelListener extends AbstractUploadListener<BondLevelUnlockStoryExcelData> {

    private final List<BondLevelUnlockStoryExcelData> excelDataList;

    public BondLevelUnlockStoryExcelListener(List<BondLevelUnlockStoryExcelData> excelDataList) {
        this.excelDataList = excelDataList;
    }

    @Override
    public void invoke(BondLevelUnlockStoryExcelData dto, AnalysisContext analysisContext) {
        super.invoke(dto, analysisContext);
        excelDataList.add(dto);
    }
}
