package com.kuaikan.role.game.admin.template.numerical;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;

import com.kuaikan.auth.interceptor.AuthContext;
import com.kuaikan.common.util.JsonUtils;
import com.kuaikan.role.game.admin.model.excel.ScheduleGiftExcelData;
import com.kuaikan.role.game.admin.repository.KeyValueConfigRepository;
import com.kuaikan.role.game.admin.utils.excel.listener.ScheduleGiftExcelListener;
import com.kuaikan.role.game.api.bean.KeyValueConfig;
import com.kuaikan.role.game.api.bean.ScheduleGiftConfig;
import com.kuaikan.role.game.api.bo.FailMessage;
import com.kuaikan.role.game.api.enums.NumericalConfigType;
import com.kuaikan.role.game.common.constant.KeyValueConfigKeys;

/**
 * ScheduleGiftNumericalTemplate
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Service
@Slf4j
public class ScheduleGiftNumericalTemplate extends NumericalTemplate<ScheduleGiftExcelData> {

    public static final int ROW_START = 2;
    public static final String COLUMN_GEAR_ID = "B";

    public static final String COLUMN_REMAINING_SECOND = "C";

    public static final String COLUMN_GIFT_ID = "D";

    public static final String COLUMN_UNIT_PRICE = "E";
    @Resource
    private KeyValueConfigRepository keyValueConfigRepository;

    @Override
    protected int getHeadRowNumber() {
        return ROW_START;
    }

    @Override
    protected Class<ScheduleGiftExcelData> getExcelDataClass() {
        return ScheduleGiftExcelData.class;
    }

    @Override
    protected ScheduleGiftExcelListener createExcelListener(List<ScheduleGiftExcelData> excelDataList) {
        return new ScheduleGiftExcelListener(excelDataList);
    }

    @Override
    protected List<FailMessage> validate(List<ScheduleGiftExcelData> dataList) {
        List<FailMessage> failMessages = Lists.newArrayList();
        Set<Integer> remainingSecondSet = new HashSet<>();
        for (int i = 0; i < dataList.size(); i++) {
            int row = i + ROW_START + 1;
            final ScheduleGiftExcelData scheduleGiftExcelData = dataList.get(i);
            final String gear = scheduleGiftExcelData.getGearId();
            if (StringUtils.isEmpty(gear)) {
                failMessages.add(newMessage(row, COLUMN_GEAR_ID, "档位为空"));
            } else if (!StringUtils.isNumeric(gear)) {
                failMessages.add(newMessage(row, COLUMN_GEAR_ID, "档位必须为整数"));
            }
            final String remainingSecond = scheduleGiftExcelData.getRemainingSecond();
            if (remainingSecond == null) {
                failMessages.add(newMessage(row, COLUMN_REMAINING_SECOND, "剩余时间为空"));
            } else if (!StringUtils.isNumeric(remainingSecond)) {
                failMessages.add(newMessage(row, COLUMN_REMAINING_SECOND, "剩余时间必须为整数"));
            } else if (Integer.parseInt(remainingSecond) <= 0) {
                failMessages.add(newMessage(row, COLUMN_REMAINING_SECOND, "剩余时间必须大于0"));
            } else {
                final boolean add = remainingSecondSet.add(Integer.parseInt(remainingSecond));
                if (!add) {
                    failMessages.add(newMessage(row, COLUMN_REMAINING_SECOND, "剩余时间重复"));
                }
            }
            if (StringUtils.isEmpty(scheduleGiftExcelData.getGiftId())) {
                failMessages.add(newMessage(row, COLUMN_GIFT_ID, "礼物id为空"));
            }
            if (StringUtils.isEmpty(scheduleGiftExcelData.getUnitPrice())) {
                failMessages.add(newMessage(row, COLUMN_UNIT_PRICE, "单价为空"));
            } else if (!StringUtils.isNumeric(scheduleGiftExcelData.getUnitPrice())) {
                failMessages.add(newMessage(row, COLUMN_UNIT_PRICE, "单价必须为整数"));
            }
        }
        return failMessages;
    }

    @Override
    protected void save(List<ScheduleGiftExcelData> dataList) {
        log.info("save schedule gift config, size: {}", dataList.size());
        final int size = dataList.size();
        List<ScheduleGiftConfig> scheduleGiftConfigs = Lists.newArrayListWithExpectedSize(size);
        for (ScheduleGiftExcelData scheduleGiftExcelData : dataList) {
            ScheduleGiftConfig scheduleGiftConfig = new ScheduleGiftConfig().setGiftId(scheduleGiftExcelData.getGiftId())
                    .setGearId(Integer.parseInt(scheduleGiftExcelData.getGearId()))
                    .setRemainingSecond(Integer.parseInt(scheduleGiftExcelData.getRemainingSecond()))
                    .setUnitPrice(Integer.parseInt(scheduleGiftExcelData.getUnitPrice()));
            scheduleGiftConfigs.add(scheduleGiftConfig);
        }
        KeyValueConfig keyValueConfig = new KeyValueConfig();
        keyValueConfig.setKey(KeyValueConfigKeys.SCHEDULE_GIFT_CONFIG);
        keyValueConfig.setValue(JsonUtils.writeValueAsString(scheduleGiftConfigs));
        keyValueConfig.setUpdater(AuthContext.getCurrentUser().getName());
        final KeyValueConfig oldKeyValueConfig = keyValueConfigRepository.queryByKey(KeyValueConfigKeys.SCHEDULE_GIFT_CONFIG);
        if (oldKeyValueConfig != null) {
            keyValueConfig.setId(oldKeyValueConfig.getId());
            keyValueConfigRepository.updateByKey(keyValueConfig);
        } else {
            keyValueConfigRepository.insert(keyValueConfig);
        }
    }

    @Override
    protected NumericalConfigType getNumericalConfigType() {
        return NumericalConfigType.SCHEDULE_GIFT;
    }
}
