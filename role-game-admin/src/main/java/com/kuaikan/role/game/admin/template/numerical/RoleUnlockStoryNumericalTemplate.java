package com.kuaikan.role.game.admin.template.numerical;

import static com.kuaikan.role.game.common.constant.KeyValueConfigKeys.ROLE_UNLOCK_STORY_CONFIG;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.alibaba.excel.read.listener.ReadListener;
import com.google.common.collect.Lists;

import com.kuaikan.auth.interceptor.AuthContext;
import com.kuaikan.common.tools.serialize.JsonUtils;
import com.kuaikan.role.game.admin.model.excel.RoleUnlockStoryExcelData;
import com.kuaikan.role.game.admin.repository.KeyValueConfigRepository;
import com.kuaikan.role.game.admin.repository.RoleRepository;
import com.kuaikan.role.game.admin.repository.StoryRepository;
import com.kuaikan.role.game.admin.utils.excel.listener.RoleUnlockStoryExcelListener;
import com.kuaikan.role.game.api.bean.KeyValueConfig;
import com.kuaikan.role.game.api.bean.Role;
import com.kuaikan.role.game.api.bean.Story;
import com.kuaikan.role.game.api.bo.FailMessage;
import com.kuaikan.role.game.api.enums.NumericalConfigType;

/**
 * <AUTHOR>
 * @date 2024/9/24 16:35
 */
@Service
public class RoleUnlockStoryNumericalTemplate extends NumericalTemplate<RoleUnlockStoryExcelData> {

    @Resource
    private RoleRepository roleRepository;

    @Resource
    private StoryRepository storyRepository;

    @Resource
    private KeyValueConfigRepository keyValueConfigRepository;

    @Override
    protected Class<RoleUnlockStoryExcelData> getExcelDataClass() {
        return RoleUnlockStoryExcelData.class;
    }

    @Override
    protected ReadListener createExcelListener(List<RoleUnlockStoryExcelData> excelDataList) {
        return new RoleUnlockStoryExcelListener(excelDataList);
    }

    @Override
    protected List<FailMessage> validate(List<RoleUnlockStoryExcelData> dataList) {
        List<FailMessage> failMessages = Lists.newArrayList();
        // 获取所有角色ID列表
        Set<Integer> roleIds = roleRepository.queryAll().stream().map(Role::getId).collect(Collectors.toSet());
        // 获取所有剧情ID列表
        Set<Integer> storyIds = storyRepository.queryAll().stream().map(Story::getId).collect(Collectors.toSet());
        // 校验A列角色ID是否存在，校验D列剧情ID是否存在
        for (int i = 0, dataListSize = dataList.size(); i < dataListSize; i++) {
            int row = i + getHeadRowNumber() + 1;
            RoleUnlockStoryExcelData data = dataList.get(i);
            if (!roleIds.contains(Integer.parseInt(data.getRoleId()))) {
                failMessages.add(newMessage(row, "A", "角色ID不存在"));
            }
            if (!storyIds.contains(Integer.parseInt(data.getStoryId()))) {
                failMessages.add(newMessage(row, "D", "剧情ID不存在"));
            }
        }
        return failMessages;
    }

    @Override
    protected void save(List<RoleUnlockStoryExcelData> dataList) {
        KeyValueConfig roleUnlockStoryConfig = new KeyValueConfig().setKey(ROLE_UNLOCK_STORY_CONFIG)
                .setValue(JsonUtils.toJson(dataList.stream().map(RoleUnlockStoryExcelData::valueOf).toArray()))
                .setUpdater(AuthContext.getCurrentUser().getName());
        KeyValueConfig queryByKey = keyValueConfigRepository.queryByKey(ROLE_UNLOCK_STORY_CONFIG);
        if (queryByKey == null) {
            keyValueConfigRepository.insert(roleUnlockStoryConfig);
        } else {
            keyValueConfigRepository.updateByKey(roleUnlockStoryConfig);
        }
    }

    @Override
    protected NumericalConfigType getNumericalConfigType() {
        return NumericalConfigType.ROLE_UNLOCK_STORY;
    }
}
