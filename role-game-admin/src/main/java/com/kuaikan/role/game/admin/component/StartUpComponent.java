package com.kuaikan.role.game.admin.component;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import javax.annotation.Resource;

import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import com.kuaikan.role.game.admin.repository.AvgRepository;
import com.kuaikan.role.game.api.bean.AvgDir;

@Component
public class StartUpComponent implements CommandLineRunner {

    private final ConcurrentHashMap<Integer, Integer> avgDirMap = new ConcurrentHashMap<>();

    @Resource
    private AvgRepository avgRepository;

    @Override
    public void run(String... args) {
        // 初始化avgDirMap
        List<AvgDir> roots = avgRepository.queryAvgDirByName("root");
        roots.forEach(root -> avgDirMap.put(root.getType(), root.getId()));
    }

    public Integer getRootAvgDirId(Integer type) {
        return avgDirMap.get(type);
    }
}
