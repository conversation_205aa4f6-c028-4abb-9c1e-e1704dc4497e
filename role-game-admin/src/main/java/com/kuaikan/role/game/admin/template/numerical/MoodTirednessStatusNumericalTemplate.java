package com.kuaikan.role.game.admin.template.numerical;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import com.kuaikan.auth.interceptor.AuthContext;
import com.kuaikan.common.tools.serialize.JsonUtils;
import com.kuaikan.role.game.admin.model.excel.MoodTirednessStatusExcelData;
import com.kuaikan.role.game.admin.repository.KeyValueConfigRepository;
import com.kuaikan.role.game.admin.utils.FunctionUtils;
import com.kuaikan.role.game.admin.utils.NumericalUtil;
import com.kuaikan.role.game.admin.utils.excel.listener.AbstractUploadListener;
import com.kuaikan.role.game.admin.utils.excel.listener.MoodTirednessStatusExcelListener;
import com.kuaikan.role.game.api.bean.KeyValueConfig;
import com.kuaikan.role.game.api.bo.FailMessage;
import com.kuaikan.role.game.api.enums.NumericalConfigType;
import com.kuaikan.role.game.api.enums.RoleStatusType;
import com.kuaikan.role.game.common.bean.MoodTirednessStatusConfig;
import com.kuaikan.role.game.common.constant.KeyValueConfigKeys;

/**
 * MoodTirednessStatusNumericalTemplate
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Service
public class MoodTirednessStatusNumericalTemplate extends NumericalTemplate<MoodTirednessStatusExcelData> {

    @Resource
    private KeyValueConfigRepository keyValueConfigRepository;

    @Override
    protected Class<MoodTirednessStatusExcelData> getExcelDataClass() {
        return MoodTirednessStatusExcelData.class;
    }

    @Override
    protected AbstractUploadListener createExcelListener(List<MoodTirednessStatusExcelData> excelDataList) {
        return new MoodTirednessStatusExcelListener(excelDataList);
    }

    @Override
    protected List<FailMessage> validate(List<MoodTirednessStatusExcelData> dataList) {
        List<FailMessage> failMessages = Lists.newArrayList();
        for (int i = 0; i < dataList.size(); i++) {
            int row = i + getHeadRowNumber() + 1;
            MoodTirednessStatusExcelData data = dataList.get(i);
            if (!NumericalUtil.isInteger(data.getIdStr())) {
                failMessages.add(getTypeFailMessage(row, "A"));
            }
            if (!NumericalUtil.isInteger(data.getTypeStr())) {
                failMessages.add(getTypeFailMessage(row, "B"));
            }
            if (!NumericalUtil.isInteger(data.getRoleStatusCategoryStr())) {
                failMessages.add(getTypeFailMessage(row, "C"));
            } else {
                data.setRoleStatusCategory(NumberUtils.toInt(data.getRoleStatusCategoryStr()));
            }
            if (!NumericalUtil.isInteger(data.getUpperLimitValueStr())) {
                failMessages.add(getTypeFailMessage(row, "D"));
            } else {
                data.setUpperLimitValue(NumberUtils.toInt(data.getUpperLimitValueStr()));
            }
        }

        Set<Integer> configedTypeIdSet = dataList.stream().map(MoodTirednessStatusExcelData::getRoleStatusCategory).collect(Collectors.toSet());

        // 检查dataList中是否有重复的配置，并指出具体是哪项重复
        List<Integer> roleStatusCategoryList = dataList.stream().map(MoodTirednessStatusExcelData::getRoleStatusCategory).collect(Collectors.toList());
        if (roleStatusCategoryList.size() != configedTypeIdSet.size()) {
            Set<Integer> duplicateSet = Sets.newHashSet();
            for (Integer roleStatusCategory : roleStatusCategoryList) {
                RoleStatusType roleStatusType = RoleStatusType.getByCode(roleStatusCategory);
                if (roleStatusType == null) {
                    String message = "未知的配置项：" + roleStatusCategory;
                    failMessages.add(newMessage(message));
                } else if (!duplicateSet.add(roleStatusCategory)) {
                    String message = "重复的配置：" + roleStatusType.getDesc();
                    failMessages.add(newMessage(message));
                }
            }
        }

        // 检查具有相同type值得配置项的upperLimitValue是否是递增的，且取值范围为【0-100】
        Map<Integer, Integer> type2upperLimitMap = FunctionUtils.toMap(dataList, MoodTirednessStatusExcelData::getRoleStatusCategory,
                MoodTirednessStatusExcelData::getUpperLimitValue);
        RoleStatusType.getAllMainType().forEach(mainType -> {
            List<RoleStatusType> sameTypeList = Arrays.stream(RoleStatusType.values())
                    .filter(roleStatusType -> roleStatusType.getType() == mainType)
                    .sorted(Comparator.comparingInt(RoleStatusType::getCode))
                    .collect(Collectors.toList());

            int previous = -1;
            for (RoleStatusType roleStatusType : sameTypeList) {
                int row = roleStatusType.getCode() + getHeadRowNumber();
                Integer upperLimitValue = type2upperLimitMap.get(roleStatusType.getCode());
                if (upperLimitValue != null) {
                    if (upperLimitValue < 0 || upperLimitValue > 100) {
                        String message = roleStatusType.getDesc() + "取值范围应为【0-100】";
                        failMessages.add(newMessage(row, "D", message));
                    }
                    if (previous >= upperLimitValue) {
                        String message = roleStatusType.getDesc() + "配置值应高于上一项配置值";
                        failMessages.add(newMessage(row, "D", message));
                    }
                    previous = upperLimitValue;
                } else {
                    String message = "缺少配置：" + roleStatusType.getDesc();
                    failMessages.add(newMessage(message));
                }
            }
            if (previous != 100) {
                String message = sameTypeList.get(sameTypeList.size() - 1).getDesc() + "配置值应为100";
                FailMessage failMessage = new FailMessage().setNumericalConfigType(NumericalConfigType.MOOD_TIREDNESS_STATUS.getDesc()).setMessage(message);
                failMessages.add(failMessage);
            }

        });
        return failMessages;
    }

    private static FailMessage getTypeFailMessage(int row, String column) {
        String message = "配置值应为整数";
        FailMessage failMessage = new FailMessage().setNumericalConfigType(NumericalConfigType.MOOD_TIREDNESS_STATUS.getDesc())
                .setRow(row)
                .setColumn(column)
                .setMessage(message);
        return failMessage;
    }

    @Override
    protected void save(List<MoodTirednessStatusExcelData> dataList) {
        Map<Integer, Integer> key2upperMap = FunctionUtils.toMap(dataList, MoodTirednessStatusExcelData::getRoleStatusCategory,
                MoodTirednessStatusExcelData::getUpperLimitValue);
        MoodTirednessStatusConfig moodTirednessStatusConfig = new MoodTirednessStatusConfig();
        moodTirednessStatusConfig.setEmoNormalSeperator(key2upperMap.get(RoleStatusType.MOOD_DEPRESSED.getCode()));
        moodTirednessStatusConfig.setNormalHapperSeperator(key2upperMap.get(RoleStatusType.MOOD_NORMAL.getCode()));
        moodTirednessStatusConfig.setNormalTiredSeperator(key2upperMap.get(RoleStatusType.TIREDNESS_NORMAL.getCode()));
        moodTirednessStatusConfig.setTiredExhaustedSeperator(key2upperMap.get(RoleStatusType.TIREDNESS_TIRED.getCode()));
        KeyValueConfig oldConfig = keyValueConfigRepository.queryByKey(KeyValueConfigKeys.MOOD_TIREDNESS_STATUS_CONF);
        KeyValueConfig config = new KeyValueConfig().setKey(KeyValueConfigKeys.MOOD_TIREDNESS_STATUS_CONF)
                .setUpdater(AuthContext.getCurrentUser().getName())
                .setValue(JsonUtils.toJson(moodTirednessStatusConfig));
        if (oldConfig == null) {
            keyValueConfigRepository.insert(config);
        } else {
            keyValueConfigRepository.updateByKey(config);
        }
    }

    @Override
    protected NumericalConfigType getNumericalConfigType() {
        return NumericalConfigType.MOOD_TIREDNESS_STATUS;
    }
}
