package com.kuaikan.role.game.admin.model.param;

import java.io.Serializable;
import java.util.Objects;
import java.util.stream.Stream;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.bean.BlindBoxCouponConfig;

/**
 * <AUTHOR>
 * @date 2024/12/23 19:54
 * @description 创建盲盒券
 */

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@NonNull
public class BlindBoxCouponAddParam extends BaseCouponAddParam implements Serializable {

    private static final long serialVersionUID = -8990630032176294666L;

    /**
     * 盲盒券名称
     */
    private String name;

    /**
     * 关联角色组
     * 为空：通用券
     * 非空：特定券
     */
    private Integer roleGroupId;

    /**
     * 盲盒券有效期
     */
    private Integer validDays;

    /**
     * 是否关联消息
     */
    private Boolean relatedMsg;

    // notNull
    public boolean isParamIllegal() {
        return Stream.of(name, validDays, relatedMsg).allMatch(Objects::nonNull);
    }

    // toExtraInfo
    public BlindBoxCouponConfig.ExtraInfo toExtraInfo() {
        return new BlindBoxCouponConfig.ExtraInfo().setName(name).setValidDays(validDays).setRelatedMsg(relatedMsg);
    }
}