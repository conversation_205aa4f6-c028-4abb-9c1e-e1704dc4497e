package com.kuaikan.role.game.admin.model.view;

import java.util.Map;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.bean.Costume;
import com.kuaikan.role.game.api.bean.Role;
import com.kuaikan.role.game.api.bean.SpineMaterial;

/**
 * <AUTHOR>
 * @date 2024/2/26
 */
@Data
@Accessors(chain = true)
public class CostumeView implements java.io.Serializable {

    private static final long serialVersionUID = -5040355299623424353L;
    private int id;

    private Integer orderNum;

    private String name;

    private String roleName;

    private ImageInfoView thumbnail;

    private int status;

    private Boolean isDefault;

    private Integer roleId;

    private String obtainCopywriting;

    private ImageInfoView icon;

    private Integer activityId;

    private int level;

    private SpineMaterialView actionMaterial;

    private int cpCostumeId;

    private int relatedStoryId;

    public static CostumeView valueOf(Costume costume) {
        return new CostumeView().setId(costume.getId())
                .setName(costume.getName())
                .setLevel(costume.getLevel());
    }

    public static CostumeView valueOf(Costume costume, Map<Integer, Integer> costumeId2RoleIdMap, Map<Integer, Role> roleMap) {
        Integer roleId = costumeId2RoleIdMap.getOrDefault(costume.getId(), null);
        String roleName = roleId != null ? roleMap.get(roleId).getName() : null;

        CostumeView costumeView = new CostumeView().setId(costume.getId())
                .setName(costume.getName())
                .setStatus(costume.getStatus())
                .setThumbnail(ImageInfoView.valueOf(costume.getConfig().getThumbnail()))
                .setRoleName(roleName)
                .setRoleId(roleId)
                .setObtainCopywriting(costume.getConfig().getObtainCopywriting())
                .setCpCostumeId(costume.getConfig().getCpCostumeId())
                .setRelatedStoryId(costume.getConfig().getRelatedStoryId())
                .setIcon(ImageInfoView.valueOf(costume.getConfig().getIcon()))
                .setActivityId(costume.getConfig().getActivityId())
                .setLevel(costume.getLevel());
        SpineMaterial actionSpineMaterial = costume.getConfig().getActionSpineMaterial();
        if (actionSpineMaterial != null) {
            costumeView.setActionMaterial(SpineMaterialView.valueOf(actionSpineMaterial));
        }
        return costumeView;
    }

    public static CostumeView valueOf(Costume costume, Map<Integer, Integer> costume2RoleIdMap, Map<Integer, Role> roleMap, int orderNum,
                                      boolean isDefault) {
        return valueOf(costume, costume2RoleIdMap, roleMap).setOrderNum(orderNum).setIsDefault(isDefault);
    }

}
