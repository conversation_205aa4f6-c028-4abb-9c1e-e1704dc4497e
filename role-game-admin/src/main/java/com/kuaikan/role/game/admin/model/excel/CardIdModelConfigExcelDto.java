package com.kuaikan.role.game.admin.model.excel;

import java.io.Serializable;

import lombok.Data;

import org.springframework.beans.BeanUtils;

import com.alibaba.excel.annotation.ExcelProperty;

import com.kuaikan.role.game.admin.component.battle.excel.valid.AttrCodeValid;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleCardIdModelConfig;

/**
 * 卡牌id模板关系 excel dto
 *
 * <AUTHOR>
 * @date 2024/6/20
 */
@Data
public class CardIdModelConfigExcelDto implements Serializable {

    // 卡牌id
    @ExcelProperty(value = { "卡牌ID", "字符串", "cardId" }, index = 0)
    private String cardId;
    // 卡牌类型
    @ExcelProperty(value = { "卡牌类型", "整数", "cardType" }, index = 1)
    private int cardType;
    // 模板id
    @ExcelProperty(value = { "模板ID", "整数", "modelId" }, index = 2)
    private int modelId;
    // 属性
    @AttrCodeValid
    @ExcelProperty(value = { "属性", "整数", "attr" }, index = 3)
    private int attr;
    // 专题id
    @ExcelProperty(value = { "专题ID(只展示，修改无效)", "整数", "topicId" }, index = 4)
    private int topicId;

    public static CardBattleCardIdModelConfig toCardIdModelConfig(CardIdModelConfigExcelDto excelDto) {
        CardBattleCardIdModelConfig config = new CardBattleCardIdModelConfig();
        BeanUtils.copyProperties(excelDto, config);
        // 状态设置为0有效
        config.setStatus(0);
        return config;
    }

    public static CardIdModelConfigExcelDto fromCardIdModelConfig(CardBattleCardIdModelConfig config) {
        CardIdModelConfigExcelDto excelDto = new CardIdModelConfigExcelDto();
        BeanUtils.copyProperties(config, excelDto);
        return excelDto;
    }

}
