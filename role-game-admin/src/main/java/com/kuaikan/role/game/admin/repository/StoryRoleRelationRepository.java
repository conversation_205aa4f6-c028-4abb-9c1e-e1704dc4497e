package com.kuaikan.role.game.admin.repository;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import com.kuaikan.role.game.admin.dao.StoryRoleRelationMapper;
import com.kuaikan.role.game.api.bean.StoryRoleRelation;

/**
 * <AUTHOR>
 * @version 2024-03-12
 */
@Repository
public class StoryRoleRelationRepository {

    @Resource
    private StoryRoleRelationMapper storyRoleRelationMapper;

    public int insert(StoryRoleRelation record) {
        return storyRoleRelationMapper.insert(record);
    }

    public int insertBatch(List<StoryRoleRelation> records) {
        return storyRoleRelationMapper.insertBatch(records);
    }

    public int deleteByStoryId(Integer storyId) {
        return storyRoleRelationMapper.deleteByStoryId(storyId);
    }

    public StoryRoleRelation selectByPrimaryKey(Integer id) {
        return storyRoleRelationMapper.selectByPrimaryKey(id);
    }

    public List<StoryRoleRelation> selectByStoryId(int storyId) {
        return storyRoleRelationMapper.selectByStoryId(storyId);
    }

    public List<StoryRoleRelation> selectByStoryIds(Collection<Integer> storyIds) {
        if (CollectionUtils.isEmpty(storyIds)) {
            return Collections.emptyList();
        }
        return storyRoleRelationMapper.selectByStoryIds(storyIds);
    }

    public List<StoryRoleRelation> selectByRoleId(int itemId) {
        return storyRoleRelationMapper.selectByRoleId(itemId);
    }

    public List<StoryRoleRelation> selectByRoleIds(Collection<Integer> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return Collections.emptyList();
        }
        return storyRoleRelationMapper.selectByRoleIds(roleIds);
    }

    public int updateByPrimaryKeySelective(StoryRoleRelation record) {
        return storyRoleRelationMapper.updateByPrimaryKeySelective(record);
    }

    public int countByRoleId(int roleId) {
        return storyRoleRelationMapper.countByRoleId(roleId);
    }

    public List<StoryRoleRelation> queryByPageAndRoleId(int roleId, int offset, int limit) {
        return storyRoleRelationMapper.queryByPageAndRoleId(roleId, offset, limit);
    }

    public int updateOrderNum(int orderNum, int roleId, int storyId) {
        return storyRoleRelationMapper.updateOrderNum(orderNum, roleId, storyId);
    }

    public int updateOrderNumGreaterThanOrEqualToNewOrderNum(int newOrderNum, int roleId) {
        return storyRoleRelationMapper.updateOrderNumGreaterThanOrEqualToNewOrderNum(newOrderNum, roleId);
    }

}
