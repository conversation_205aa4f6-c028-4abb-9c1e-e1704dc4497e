package com.kuaikan.role.game.admin.template.numerical;

import java.util.ArrayList;
import java.util.Collection;
import java.util.EnumSet;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;

import com.kuaikan.role.game.admin.model.bo.ScheduleMoodTirednessBO;
import com.kuaikan.role.game.admin.model.excel.RoleMoodTirednessExcelData;
import com.kuaikan.role.game.admin.repository.BuildingRepository;
import com.kuaikan.role.game.admin.repository.RoleBuildingScheduleConfigRepository;
import com.kuaikan.role.game.admin.repository.RoleRepository;
import com.kuaikan.role.game.admin.repository.ScheduleRepository;
import com.kuaikan.role.game.admin.utils.NumericalUtil;
import com.kuaikan.role.game.admin.utils.excel.listener.AbstractUploadListener;
import com.kuaikan.role.game.admin.utils.excel.listener.RoleMoodTirednessExcelDataListener;
import com.kuaikan.role.game.api.bean.Building;
import com.kuaikan.role.game.api.bean.Role;
import com.kuaikan.role.game.api.bean.RoleBuildingScheduleConfig;
import com.kuaikan.role.game.api.bean.Schedule;
import com.kuaikan.role.game.api.bo.FailMessage;
import com.kuaikan.role.game.api.enums.BuildingType;
import com.kuaikan.role.game.api.enums.NumericalConfigType;
import com.kuaikan.role.game.api.enums.ScheduleType;

/**
 * RoleMoodTirednessNumericalTemplate
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Service
public class RoleMoodTirednessNumericalTemplate extends NumericalTemplate<RoleMoodTirednessExcelData> {

    private static final int ROW_START = 2;
    @Resource
    private RoleBuildingScheduleConfigRepository roleBuildingScheduleConfigRepository;

    @Resource
    private BuildingRepository buildingRepository;
    @Resource
    private ScheduleRepository scheduleRepository;

    @Resource
    private RoleRepository roleRepository;

    private List<ScheduleMoodTirednessBO> scheduleMoodTirednessBOList;

    @Override
    protected Class<RoleMoodTirednessExcelData> getExcelDataClass() {
        return RoleMoodTirednessExcelData.class;
    }

    @Override
    protected int getHeadRowNumber() {
        return ROW_START;
    }

    @Override
    protected AbstractUploadListener createExcelListener(List<RoleMoodTirednessExcelData> excelDataList) {
        return new RoleMoodTirednessExcelDataListener(excelDataList);
    }

    Splitter SPLITTER_1 = Splitter.on("#").trimResults().omitEmptyStrings();
    Splitter SPLITTER_2 = Splitter.on("|").trimResults().omitEmptyStrings();

    private static final EnumSet<BuildingType> BUILDING_TYPES = EnumSet.of(BuildingType.MAIN_BUILDING, BuildingType.WORK_BUILDING,
            BuildingType.ENTERTAINMENT_BUILDING);

    @Override
    protected List<FailMessage> validate(List<RoleMoodTirednessExcelData> dataList) {
        Map<Integer, Building> buildingMap = buildingRepository.queryAll().stream().filter(item -> {
            BuildingType byCode = BuildingType.getByCode(item.getType());
            return BUILDING_TYPES.contains(byCode);
        }).collect(Collectors.toMap(Building::getId, building -> building));
        Map<Integer, Role> roleMap = roleRepository.queryAll().stream().collect(Collectors.toMap(Role::getId, role -> role));

        List<FailMessage> failMessages = new ArrayList<>();
        List<ScheduleMoodTirednessBO> scheduleMoodTirednessBOList = new ArrayList<>();
        for (int i = 0; i < dataList.size(); i++) {
            RoleMoodTirednessExcelData item = dataList.get(i);
            int row = i + ROW_START + 1;
            String buildingIdStr = item.getBuildingId();
            if (!NumberUtils.isDigits(buildingIdStr)) {
                failMessages.add(newMessage(row, "A", "建筑id不是数字"));
                continue;
            }
            int buildingId = Integer.parseInt(buildingIdStr);
            Building building = buildingMap.get(buildingId);
            if (building == null) {
                failMessages.add(newMessage(row, "A", "建筑id不存在"));
                continue;
            }
            BuildingType buildingType = BuildingType.getByCode(building.getType());
            String roleIdsGroup = item.getRoleIdsGroup();
            if (StringUtils.isBlank(roleIdsGroup)) {
                failMessages.add(newMessage(row, "C", "角色ID为空"));
                continue;
            }
            List<String> roleIdsList = SPLITTER_1.splitToList(roleIdsGroup);
            Set<Integer> roleIdsSet = new HashSet<>();
            for (String roleIds : roleIdsList) {
                List<String> roleIdList = SPLITTER_2.splitToList(roleIds);
                for (String roleId : roleIdList) {
                    if (!NumericalUtil.isInteger(roleId)) {
                        failMessages.add(newMessage(row, "C", "角色ID不是数字"));
                        continue;
                    }
                    int roleIdInt = Integer.parseInt(roleId);
                    if (roleIdsSet.contains(roleIdInt)) {
                        failMessages.add(newMessage(row, "C", "角色ID重复"));
                        continue;
                    }
                    roleIdsSet.add(roleIdInt);
                    Role role = roleMap.get(roleIdInt);
                    if (role == null) {
                        failMessages.add(newMessage(row, "C", "角色ID不存在"));
                    }
                }
            }
            int groupSize = CollectionUtils.size(roleIdsList);
            ScheduleMoodTirednessBO scheduleMoodTirednessBO = new ScheduleMoodTirednessBO();
            scheduleMoodTirednessBO.setBuildingId(buildingId).setBuildingType(buildingType.getCode());
            if (buildingType == BuildingType.MAIN_BUILDING) {
                if (StringUtils.isBlank(item.getGrowUpLevelOneMoodGroup())) {
                    failMessages.add(newMessage(row, "E", "一阶成长日程心情值变化不能为空"));
                    continue;
                }
                if (StringUtils.isBlank(item.getGrowUpLevelOneTirednessGroup())) {
                    failMessages.add(newMessage(row, "J", "一阶成长活动角色疲劳值变化不能为空"));
                    continue;
                }
                List<String> growUpLevelOneMoodListGroup = SPLITTER_1.splitToList(item.getGrowUpLevelOneMoodGroup());
                List<String> growUpLevelOneTirednessListGroup = SPLITTER_1.splitToList(item.getGrowUpLevelOneTirednessGroup());

                if (CollectionUtils.size(growUpLevelOneMoodListGroup) != groupSize) {
                    failMessages.add(newMessage(row, "E", "一阶成长日程心情值变化#格式不正确"));
                    continue;
                }
                if (CollectionUtils.size(growUpLevelOneTirednessListGroup) != groupSize) {
                    failMessages.add(newMessage(row, "J", "一阶成长活动角色疲劳值变化#格式不正确"));
                    continue;
                }
                List<ScheduleMoodTirednessBO.Config> configList = new ArrayList<>();
                for (int j = 0; j < groupSize; j++) {
                    String roleIds = roleIdsList.get(j);
                    List<Integer> roleIdList = SPLITTER_2.splitToList(roleIds).stream().map(NumberUtils::toInt).collect(Collectors.toList());
                    if (!NumericalUtil.isFloat(growUpLevelOneMoodListGroup.get(j))) {
                        failMessages.add(newMessage(row, "E", "一阶成长日程心情值变化不是小数"));
                        continue;
                    }
                    if (!NumericalUtil.isFloat(growUpLevelOneTirednessListGroup.get(j))) {
                        failMessages.add(newMessage(row, "J", "一阶成长活动角色疲劳值变化不是小数"));
                        continue;
                    }
                    float growUpLevelOneMoodPerPeriod = NumberUtils.toFloat(growUpLevelOneMoodListGroup.get(j));
                    float growUpLevelOneTirednessPerPeriod = NumberUtils.toFloat(growUpLevelOneTirednessListGroup.get(j));

                    ScheduleMoodTirednessBO.Config config = new ScheduleMoodTirednessBO.Config().setRoleIds(roleIdList)
                            .setGrowUpLevelOneMoodPerPeriod(growUpLevelOneMoodPerPeriod)
                            .setGrowUpLevelOneTirednessPerPeriod(growUpLevelOneTirednessPerPeriod);
                    configList.add(config);
                }
                scheduleMoodTirednessBO.setConfigList(configList);

            } else if (buildingType == BuildingType.WORK_BUILDING) {
                if (StringUtils.isBlank(item.getWorkMoodGroup())) {
                    failMessages.add(newMessage(row, "H", "打工日程心情值变化不能为空"));
                    continue;
                }
                if (StringUtils.isBlank(item.getWorkTirednessGroup())) {
                    failMessages.add(newMessage(row, "M", "打工活动疲劳值变化不能为空"));
                    continue;
                }

                List<String> workMoodListGroup = SPLITTER_1.splitToList(item.getWorkMoodGroup());
                List<String> workTirednessListGroup = SPLITTER_1.splitToList(item.getWorkTirednessGroup());
                if (CollectionUtils.size(workMoodListGroup) != groupSize) {
                    failMessages.add(newMessage(row, "H", "打工日程心情值变化#格式不正确"));
                    continue;
                }

                if (CollectionUtils.size(workTirednessListGroup) != groupSize) {
                    failMessages.add(newMessage(row, "M", "打工活动疲劳值变化#格式不正确"));
                    continue;
                }
                List<ScheduleMoodTirednessBO.Config> configList = new ArrayList<>();
                for (int j = 0; j < groupSize; j++) {
                    String roleIds = roleIdsList.get(j);
                    List<Integer> roleIdList = SPLITTER_2.splitToList(roleIds).stream().map(NumberUtils::toInt).collect(Collectors.toList());
                    if (!NumericalUtil.isFloat(workMoodListGroup.get(j))) {
                        failMessages.add(newMessage(row, "H", "打工日程心情值变化不是小数"));
                        continue;
                    }
                    if (!NumericalUtil.isFloat(workTirednessListGroup.get(j))) {
                        failMessages.add(newMessage(row, "M", "打工活动疲劳值变化不是小数"));
                        continue;
                    }
                    float workMoodPerPeriod = NumberUtils.toFloat(workMoodListGroup.get(j));
                    float workTirednessPerPeriod = NumberUtils.toFloat(workTirednessListGroup.get(j));
                    ScheduleMoodTirednessBO.Config config = new ScheduleMoodTirednessBO.Config().setRoleIds(roleIdList)
                            .setWorkMoodPerPeriod(workMoodPerPeriod)
                            .setWorkTirednessPerPeriod(workTirednessPerPeriod);
                    configList.add(config);
                }
                scheduleMoodTirednessBO.setConfigList(configList);
            } else if (buildingType == BuildingType.ENTERTAINMENT_BUILDING) {
                if (StringUtils.isBlank(item.getEntertainmentMoodGroup())) {
                    failMessages.add(newMessage(row, "I", "娱乐日程心情值变化不能为空"));
                    continue;
                }
                if (StringUtils.isBlank(item.getEntertainmentTirednessGroup())) {
                    failMessages.add(newMessage(row, "N", "娱乐活动疲劳值变化不能为空"));
                    continue;
                }
                List<String> entertainmentMoodGroup = SPLITTER_1.splitToList(item.getEntertainmentMoodGroup());
                List<String> entertainmentTirednessGroup = SPLITTER_1.splitToList(item.getEntertainmentTirednessGroup());
                if (CollectionUtils.size(entertainmentMoodGroup) != groupSize) {
                    failMessages.add(newMessage(row, "I", "娱乐日程心情值变化#格式不正确"));
                    continue;
                }
                if (CollectionUtils.size(entertainmentTirednessGroup) != groupSize) {
                    failMessages.add(newMessage(row, "N", "娱乐活动疲劳值变化#格式不正确"));
                    continue;
                }
                List<ScheduleMoodTirednessBO.Config> configList = new ArrayList<>();
                for (int j = 0; j < groupSize; j++) {
                    String roleIds = roleIdsList.get(j);
                    List<Integer> roleIdList = SPLITTER_2.splitToList(roleIds).stream().map(NumberUtils::toInt).collect(Collectors.toList());
                    if (!NumericalUtil.isFloat(entertainmentMoodGroup.get(j))) {
                        failMessages.add(newMessage(row, "I", "娱乐日程心情值变化不是小数"));
                        continue;
                    }
                    if (!NumericalUtil.isFloat(entertainmentTirednessGroup.get(j))) {
                        failMessages.add(newMessage(row, "N", "娱乐活动疲劳值变化不是小数"));
                        continue;
                    }
                    float entertainmentMoodPerPeriod = NumberUtils.toFloat(entertainmentMoodGroup.get(j));
                    float entertainmentTirednessPerPeriod = NumberUtils.toFloat(entertainmentTirednessGroup.get(j));
                    ScheduleMoodTirednessBO.Config config = new ScheduleMoodTirednessBO.Config().setRoleIds(roleIdList)
                            .setEntertainmentMoodPerPeriod(entertainmentMoodPerPeriod)
                            .setEntertainmentTirednessPerPeriod(entertainmentTirednessPerPeriod);
                    configList.add(config);
                }
                scheduleMoodTirednessBO.setConfigList(configList);
            } else {
                failMessages.add(newMessage(row, "A", "建筑类型不存在"));
                continue;

            }
            scheduleMoodTirednessBOList.add(scheduleMoodTirednessBO);
        }
        Set<Integer> roleIdSet = scheduleMoodTirednessBOList.stream()
                .map(ScheduleMoodTirednessBO::getConfigList)
                .flatMap(Collection::stream)
                .map(ScheduleMoodTirednessBO.Config::getRoleIds)
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());
        Set<Integer> dbAllRoleIds = roleMap.keySet();
        //判断是否有不存在的角色id
        if (!roleIdSet.containsAll(dbAllRoleIds)) {
            Set<Integer> roleIdSetCopy = roleIdSet;
            Set<Integer> dbAllRoleIdsCopy = dbAllRoleIds;
            dbAllRoleIds.removeAll(roleIdSet);
            failMessages.add(newMessage("表格中缺失的角色id" + StringUtils.join(dbAllRoleIds, ",")));

            roleIdSetCopy.removeAll(dbAllRoleIdsCopy);
            failMessages.add(newMessage(StringUtils.join(roleIdSetCopy, ",") + "id对应的角色不存在"));
        }

        Set<Integer> dbAllBuildingIds = buildingMap.keySet();
        Set<Integer> importBuild = scheduleMoodTirednessBOList.stream().map(ScheduleMoodTirednessBO::getBuildingId).collect(Collectors.toSet());
        if (!importBuild.containsAll(dbAllBuildingIds)) {
            dbAllBuildingIds.removeAll(importBuild);
            failMessages.add(newMessage("表格中缺失的建筑id" + StringUtils.join(dbAllBuildingIds, ",")));
        }
        this.scheduleMoodTirednessBOList = scheduleMoodTirednessBOList;
        return failMessages;
    }

    @Override
    protected void save(List<RoleMoodTirednessExcelData> dataList) {
        List<Schedule> allSchedules = scheduleRepository.queryAll();
        Map<Integer, Schedule> buildingIdScheduleMap = allSchedules.stream()
                .collect(Collectors.toMap(e -> e.getBuildingId(), e -> e, (newVal, oldVal) -> oldVal));
        Map<Integer, Building> buildingMap = buildingRepository.queryAll().stream().collect(Collectors.toMap(Building::getId, Function.identity()));
        List<ScheduleMoodTirednessBO> scheduleMoodTirednessBOList = this.scheduleMoodTirednessBOList;
        List<RoleBuildingScheduleConfig> roleBuildingScheduleConfigs = new ArrayList<>();
        for (ScheduleMoodTirednessBO scheduleMoodTirednessBO : scheduleMoodTirednessBOList) {
            for (ScheduleMoodTirednessBO.Config config : scheduleMoodTirednessBO.getConfigList()) {
                for (Integer roleId : config.getRoleIds()) {
                    List<ScheduleMoodTirednessBO.Config> configList = scheduleMoodTirednessBO.getConfigList();
                    ScheduleMoodTirednessBO.Config importConfig = configList.stream()
                            .filter(item -> item.getRoleIds().contains(roleId))
                            .findFirst()
                            .orElse(null);
                    if (importConfig == null) {
                        continue;
                    }
                    int buildingId = scheduleMoodTirednessBO.getBuildingId();
                    int type = buildingMap.get(buildingId).getType();
                    Schedule schedule = buildingIdScheduleMap.get(buildingId);
                    int schedulePeriodCount = 0;
                    if (schedule != null) {
                        schedulePeriodCount = (int) TimeUnit.MINUTES.toSeconds(schedule.getConfig().getConsumeMinute()) / 5;
                    }
                    RoleBuildingScheduleConfig.ScheduleTirednessAndMood scheduleConfig = new RoleBuildingScheduleConfig.ScheduleTirednessAndMood();
                    if (type == BuildingType.MAIN_BUILDING.getCode()) {
                        scheduleConfig.setScheduleType(ScheduleType.GROW_UP_LEVEL_ONE.getType());
                        scheduleConfig.setTirednessPerPeriod(importConfig.getGrowUpLevelOneTirednessPerPeriod());
                        scheduleConfig.setMoodPerPeriod(importConfig.getGrowUpLevelOneMoodPerPeriod());
                        scheduleConfig.setTiredness(Math.round(importConfig.getGrowUpLevelOneTirednessPerPeriod() * schedulePeriodCount));
                        scheduleConfig.setMood(Math.round(importConfig.getGrowUpLevelOneMoodPerPeriod() * schedulePeriodCount));

                    } else if (type == BuildingType.WORK_BUILDING.getCode()) {
                        scheduleConfig.setScheduleType(ScheduleType.WORK.getType());
                        scheduleConfig.setTirednessPerPeriod(importConfig.getWorkTirednessPerPeriod());
                        scheduleConfig.setMoodPerPeriod(importConfig.getWorkMoodPerPeriod());
                        scheduleConfig.setTiredness(Math.round(importConfig.getWorkTirednessPerPeriod() * schedulePeriodCount));
                        scheduleConfig.setMood(Math.round(importConfig.getWorkMoodPerPeriod() * schedulePeriodCount));
                    } else if (type == BuildingType.ENTERTAINMENT_BUILDING.getCode()) {
                        scheduleConfig.setScheduleType(ScheduleType.ENTERTAINMENT.getType());
                        scheduleConfig.setTirednessPerPeriod(importConfig.getEntertainmentTirednessPerPeriod());
                        scheduleConfig.setMoodPerPeriod(importConfig.getEntertainmentMoodPerPeriod());
                        scheduleConfig.setTiredness(Math.round(importConfig.getEntertainmentTirednessPerPeriod() * schedulePeriodCount));
                        scheduleConfig.setMood(Math.round(importConfig.getEntertainmentMoodPerPeriod() * schedulePeriodCount));
                    }
                    RoleBuildingScheduleConfig roleBuildingScheduleConfig = new RoleBuildingScheduleConfig().setBuildingId(buildingId)
                            .setRoleId(roleId)
                            .setConfigs(Lists.newArrayList(scheduleConfig));
                    roleBuildingScheduleConfigs.add(roleBuildingScheduleConfig);
                }
            }
        }
        roleBuildingScheduleConfigRepository.batchDelete(roleBuildingScheduleConfigRepository.queryAll());
        roleBuildingScheduleConfigRepository.batchInsert(roleBuildingScheduleConfigs);
        this.scheduleMoodTirednessBOList = null;
    }

    @Override
    protected NumericalConfigType getNumericalConfigType() {
        return NumericalConfigType.ROLE_MOOD_AND_TIREDNESS;
    }
}
