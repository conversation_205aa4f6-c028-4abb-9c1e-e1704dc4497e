package com.kuaikan.role.game.admin.dao;

import java.util.Collection;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.kuaikan.role.game.api.bean.AvgDir;

public interface AvgDirMapper {

    List<AvgDir> queryAvgDirByName(String name);

    List<AvgDir> queryAvgDirByNameAndType(@Param("name") String name, @Param("type") int type);

    void insertSelective(AvgDir avgDir);

    int updateByPrimaryKeySelective(AvgDir avgDir);

    List<AvgDir> queryAvgDirByTypeAndParentId(@Param("type") int type, @Param("parentId") int parentId, @Param("orderByAndSort") String orderByAndSort);

    List<AvgDir> queryAvgDirList(@Param("type") int type, @Param("parentId") int parentId, @Param("orderByAndSort") String orderByAndSort,
                                 @Param("name") String name, @Param("topicId") Integer topicId, @Param("resource") Integer resource, @Param("status") Integer status);

    List<AvgDir> queryDirByTypeAndParentId(@Param("type") int type, @Param("parentId") int parentId);

    List<AvgDir> queryDirByType(@Param("type") int type);

    AvgDir selectByPrimaryKey(int id);

    void deleteByPrimaryKey(int id);

    List<AvgDir> queryAvgDirByNames(List<String> dirNames);

    List<AvgDir> queryAvgDirByNamesAndType(@Param("dirNames") Collection<String> dirNames, @Param("type") int type);

    List<AvgDir> queryAvgByIds(@Param("ids") Collection<Integer> ids);

    void batchUpdateByPrimaryKey(@Param("avgDirs") Collection<AvgDir> avgDirs);

    List<AvgDir> queryAll();

    // query avg dir list
    List<AvgDir> queryDirList(@Param("type") int type, @Param("name") String name, @Param("topicId") Integer topicId, @Param("resource") Integer resource,
                              @Param("status") Integer status);

    List<AvgDir> queryAvgDirByTypes(@Param("types") Collection<Integer> types);
}