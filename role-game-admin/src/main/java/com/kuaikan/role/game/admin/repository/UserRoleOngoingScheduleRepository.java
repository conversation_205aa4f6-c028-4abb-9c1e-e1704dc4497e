package com.kuaikan.role.game.admin.repository;

import java.util.Collection;
import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import com.kuaikan.role.game.admin.dao.UserRoleOngoingScheduleMapper;

/**
 * <AUTHOR>
 * @date 2024/7/9
 */
@Repository
public class UserRoleOngoingScheduleRepository {

    @Resource
    private UserRoleOngoingScheduleMapper userRoleOngoingScheduleMapper;

    public int countByScheduleIds(Collection<Integer> scheduleIds) {
        return userRoleOngoingScheduleMapper.countByScheduleIds(scheduleIds);
    }

}
