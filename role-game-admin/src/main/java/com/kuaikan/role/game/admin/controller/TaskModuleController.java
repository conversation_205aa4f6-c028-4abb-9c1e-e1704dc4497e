package com.kuaikan.role.game.admin.controller;

import java.util.Map;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kuaikan.role.game.admin.biz.TaskModuleBiz;
import com.kuaikan.role.game.admin.model.param.TaskModuleAddOrUpdateParam;
import com.kuaikan.role.game.admin.utils.ResponseUtils;

@Slf4j
@RestController
@RequestMapping("/v2/admin/role/game/taskModule")
public class TaskModuleController {

    @Resource
    private TaskModuleBiz taskModuleBiz;

    @PostMapping("/addOrUpdate")
    public Map<String, Object> addOrUpdate(@RequestBody TaskModuleAddOrUpdateParam param) {
        return ResponseUtils.valueOf(taskModuleBiz.addOrUpdate(param));
    }

    @GetMapping("/detail")
    public Map<String, Object> detail() {
        return ResponseUtils.valueOf(taskModuleBiz.detail());
    }

}
