package com.kuaikan.role.game.admin.controller;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.python.antlr.ast.Str;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.role.game.admin.biz.CommonSettingBiz;
import com.kuaikan.role.game.admin.model.view.CommonSettingView;
import com.kuaikan.role.game.admin.utils.ResponseUtils;
import com.kuaikan.role.game.common.enums.CommonSettingType;

/**
 * CommonSettingController
 *
 * <AUTHOR>
 * @since 2024/7/16
 */
@Slf4j
@RestController
@RequestMapping("/v2/admin/role/game/common/setting")
public class CommonSettingController {

    @Resource
    private CommonSettingBiz commonSettingBiz;

    @PostMapping("/upload")
    public Map<String, Object> update(@RequestParam(value = "file", required = false) MultipartFile file,
                                      @RequestParam(value = "fileName", required = false) String fileName,
                                      @RequestParam(value = "textContent", required = false) String textContent, @RequestParam(value = "key") String key) {
        if (!CommonSettingType.isValidKey(key)) {
            return ResponseUtils.valueOf(BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "key对应的设置不存在"));
        }
        if (file != null && StringUtils.isNotEmpty(textContent)) {
            return ResponseUtils.valueOf(BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "file和textContent不能同时存在"));
        }
        if (file == null && StringUtils.isEmpty(textContent)) {
            return ResponseUtils.valueOf(BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "file和textContent不能同时不存在"));
        }
        CommonSettingType type = CommonSettingType.getByKey(key);
        BizResult<Void> bizResult;
        if (type == CommonSettingType.HOME_PAGE_BGM || type == CommonSettingType.CITY_BGM) {
            if (file == null) {
                return ResponseUtils.valueOf(BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "file不能为空"));
            }
            if (StringUtils.isEmpty(fileName)) {
                return ResponseUtils.valueOf(BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "fileName不能为空"));
            }
            bizResult = commonSettingBiz.updateFile(file, type, fileName);
        } else {
            if (StringUtils.isEmpty(textContent)) {
                return ResponseUtils.valueOf(BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "textContent不能为空"));
            }
            bizResult = commonSettingBiz.updateText(textContent, type);
        }
        return ResponseUtils.valueOf(bizResult);
    }

    @GetMapping("")
    public Map<String, Object> get() {
        return ResponseUtils.valueOf(commonSettingBiz.get());
    }
}
