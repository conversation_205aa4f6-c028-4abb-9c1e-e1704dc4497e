package com.kuaikan.role.game.admin.model.excel;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * CostumeStuffExcalData
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CostumeStuffExcelData implements Serializable {

    private static final long serialVersionUID = -7215516861233876107L;
    @ExcelProperty(value = "costumeId", index = 0)
    private String costumeId;
    @ExcelProperty(value = "costumePartId", index = 1)
    private String costumePartId;
    @ExcelProperty(value = "stuffIds", index = 3)
    private String stuffIds;
    @ExcelProperty(value = "stuffNums", index = 4)
    private String stuffNums;
    @ExcelProperty(value = "silverCoinNum", index = 6)
    private String silverCoinNum;
    @ExcelProperty(value = "obtainText", index = 7)
    private String obtainText;
    @ExcelProperty(value = "url", index = 9)
    private String url;
}
