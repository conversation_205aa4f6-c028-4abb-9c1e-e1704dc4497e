package com.kuaikan.role.game.admin.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.kuaikan.role.game.api.bean.ItemConfig;

/**
 * ItemConfigMapper
 *
 * <AUTHOR>
 * @since 2024/9/4
 */
public interface ItemConfigMapper {

    ItemConfig queryByItemId(@Param("itemId") int itemId, @Param("itemType") int itemType);

    List<ItemConfig> queryAll();

    void insert(ItemConfig itemConfig);

    void update(ItemConfig itemConfig);

    void delete();
}
