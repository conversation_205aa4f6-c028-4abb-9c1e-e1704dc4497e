package com.kuaikan.role.game.admin.model.param;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

import com.kuaikan.role.game.api.bean.CostumeBlindBoxActivity;
import com.kuaikan.role.game.api.bean.SpineMaterial;
import com.kuaikan.role.game.api.bo.ImageInfo;

@Data
@Accessors(chain = true)
public class ActivityAddOrUpdateParam implements Serializable {

    private static final long serialVersionUID = 6399307603214586279L;

    private int id;

    private ImageInfo image;

    private int orderNum;

    private String name;

    private int roleGroupId;

    private long startAt;

    private long endAt;

    /**
     * 背景 spine
     */
    private SpineMaterial spine;

    /**
     * 抽取奖励按钮
     */
    private ImageInfo drawRewardBtn;

    private String drawCopywriting;

    private ImageInfo entryImage;

    private List<CostumeBlindBoxActivity.RoleConfig> roleConfigs;

    private int scaleRatio;

    private int freeCount;

    /** 概率up装扮 */
    private CostumeBlindBoxActivity.UpCostume upCostume;

    private List<CostumeBlindBoxActivity.Reward> rewards;

    /** 概率规则 */
    private Integer defaultRuleId;

    /** 概率实验 */
    private CostumeBlindBoxActivity.ExptRule exptRules;

    public CostumeBlindBoxActivity.Config toCostumeBlindBoxActivityConfig() {
        return new CostumeBlindBoxActivity.Config()
                .setActivityImage(image)
                .setSpine(spine)
                .setDrawRewardBtn(drawRewardBtn)
                .setDrawCopywriting(drawCopywriting)
                .setEntryImage(entryImage)
                .setRoleConfigs(roleConfigs)
                .setScaleRatio(scaleRatio)
                .setFreeCount(freeCount)
                .setUpCostume(upCostume)
                .setDefaultRuleId(defaultRuleId)
                .setExptRules(null != exptRules ? exptRules : new CostumeBlindBoxActivity.ExptRule())
                .setRewards(rewards);
    }
}
