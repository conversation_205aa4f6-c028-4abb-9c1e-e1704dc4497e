package com.kuaikan.role.game.admin.template.numerical.city;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.read.listener.ReadListener;

import com.kuaikan.common.redis.KeyGenerator;
import com.kuaikan.common.redis.lettuce.LettuceClusterClient;
import com.kuaikan.common.redis.lettuce.LettuceClusterUtil;
import com.kuaikan.role.game.admin.model.excel.StoryMapExcelData;
import com.kuaikan.role.game.admin.repository.MapStoryRepository;
import com.kuaikan.role.game.admin.utils.excel.listener.MapStoryCityListener;
import com.kuaikan.role.game.api.bean.MapStory;
import com.kuaikan.role.game.api.bo.FailMessage;
import com.kuaikan.role.game.api.enums.CityConfigType;
import com.kuaikan.role.game.api.service.RedDotService;
import com.kuaikan.role.game.common.enums.CacheConfig;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class StoryMapConfigTemplate extends MapConfigTemplate<StoryMapExcelData> {

    @Resource
    private MapStoryRepository mapStoryRepository;

    @Resource
    private RedDotService redDotService;

    public static final String COLUMN_STORY_TAG = "A";
    public static final String COLUMN_STORY_LIBRARY_ID = "B";
    public static final String COLUMN_STORY_AVG_CHAPTER_ID = "C";
    public static final String COLUMN_STORY_WEIGHT = "D";

    @Override
    protected ReadListener<StoryMapExcelData> createExcelListener(List<StoryMapExcelData> excelDataList) {
        return new MapStoryCityListener(excelDataList);
    }

    @Override
    protected List<FailMessage> validate() {

        List<FailMessage> failMessages = new ArrayList<>();
        if (CollectionUtil.isEmpty(excelDataList)) {
            failMessages.add(newMessage("excel数据为空"));
        }

        for (int i = 0; i < excelDataList.size(); i++) {
            StoryMapExcelData excelData = excelDataList.get(i);
            int row = i + getHeadRowNumber() + 1;

            String tag = excelData.getTag();
            Integer libraryId = excelData.getLibraryId();
            Integer avgChapterId = excelData.getAvgChapterId();
            Integer weight = excelData.getWeight();

            if (StringUtils.isBlank(tag)) {
                failMessages.add(newMessage(row, COLUMN_STORY_TAG, "剧情类型不能为空"));
            }

            if (Objects.isNull(libraryId)) {
                failMessages.add(newMessage(row, COLUMN_STORY_LIBRARY_ID, "剧情库Id不能为空"));
            }

            if (Objects.isNull(avgChapterId)) {
                failMessages.add(newMessage(row, COLUMN_STORY_AVG_CHAPTER_ID, "剧情Id不能为空"));
            }

            if (Objects.isNull(weight)) {
                failMessages.add(newMessage(row, COLUMN_STORY_WEIGHT, "权重不能为空"));
            }
        }


        return failMessages;
    }

    @Override
    protected Class<StoryMapExcelData> getExcelDataClass() {
        return StoryMapExcelData.class;
    }

    @Override
    protected void save() {
        List<MapStory> mapStories = mapStoryRepository.selectByMapId(mapId);
        Map<String, StoryMapExcelData> excelDataMap = excelDataList.stream().collect(Collectors.toMap(e -> mapId + "_" + e.getAvgChapterId(), Function.identity()));

        for (MapStory mapStory : mapStories) {
            String key = mapStory.getMapId() + "_" + mapStory.getAvgChapterId();
            if (!excelDataMap.containsKey(key)) {
                mapStoryRepository.logicDelete(mapStory);
            }
        }


        AtomicReference<Boolean> addStory = new AtomicReference<>(false);
        Set<String> newDotTags = new HashSet<>();
        excelDataList.stream().map(StoryMapExcelData::convert).forEach(mapStory -> {
            MapStory mapStoryDb = mapStoryRepository.selectByMapAndChapterId(mapId, mapStory.getAvgChapterId());
            if (Objects.isNull(mapStoryDb)) {
                mapStory.setMapId(mapId);
                mapStory.setStatus(0);
                mapStoryRepository.insert(mapStory);

                // 剧情上新
                redDotService.sendAddMapStoryEvent(mapId, mapStory.getAvgChapterId());
                // 地图红点上新
                addStory.set(true);
                // tag 上新
                newDotTags.add(mapStory.getTag());
            } else {
                mapStory.setId(mapStoryDb.getId());
                mapStory.setMapId(mapId);
                mapStory.setStatus(0);
                mapStoryRepository.updateById(mapStory);
            }

        });

        // 清理配置缓存
        deleteConfigCache(mapId);
        // 地图上新红点
        if (addStory.get()) {
            redDotService.sendMapHomeAddStoryEvent(mapId);
        }

        // tag上新
        for (String tag : newDotTags) {
            redDotService.sendAddMapStoryTagEvent(mapId, tag);
        }
    }

    @Override
    protected CityConfigType getCityConfigType() {
        return CityConfigType.STORY;
    }

    private void deleteConfigCache(int mapId) {
        LettuceClusterClient redisClient = LettuceClusterUtil.getClusterClientByName(CacheConfig.COMMON_MAP_STORY_CONFIG_LIST.getReadWriteVip());
        String cacheKey = KeyGenerator.generate(CacheConfig.COMMON_MAP_STORY_CONFIG_LIST.getKeyPattern(), mapId);
        redisClient.del(cacheKey);
    }
}
